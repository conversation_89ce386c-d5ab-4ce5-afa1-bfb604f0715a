#!/usr/bin/env python3
"""
AreTomo3 GUI Professional Installation Script
Installs the package in a clean, professional manner for distribution.
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path


def run_command(cmd, check=True, capture_output=True):
    """Run a command and return the result."""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(
            cmd, shell=True, check=check, capture_output=capture_output, text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        raise


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def clean_build_artifacts():
    """Clean any existing build artifacts."""
    print("🧹 Cleaning build artifacts...")
    artifacts = ["build", "dist", "*.egg-info", "__pycache__"]
    for pattern in artifacts:
        run_command(f"rm -rf {pattern}", check=False)
    
    # Clean Python cache files
    run_command("find . -name '*.pyc' -delete", check=False)
    run_command("find . -name '__pycache__' -type d -exec rm -rf {} +", check=False)
    print("✅ Build artifacts cleaned")


def verify_package_structure():
    """Verify the package has the correct structure."""
    print("🔍 Verifying package structure...")
    
    required_files = [
        "setup.py",
        "pyproject.toml", 
        "requirements.txt",
        "README.md",
        "aretomo3_gui/__init__.py",
        "aretomo3_gui/main.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ Package structure verified")
    return True


def build_package():
    """Build the package."""
    print("📦 Building package...")
    
    # Install build dependencies
    run_command("pip install build wheel setuptools")
    
    # Build the package
    run_command("python -m build")
    
    # Verify build artifacts exist
    if not Path("dist").exists():
        print("❌ Build failed - no dist directory created")
        return False
    
    dist_files = list(Path("dist").glob("*"))
    if not dist_files:
        print("❌ Build failed - no files in dist directory")
        return False
    
    print(f"✅ Package built successfully. Files: {[f.name for f in dist_files]}")
    return True


def test_installation():
    """Test the installation in a temporary environment."""
    print("🧪 Testing installation...")
    
    # Find the wheel file
    wheel_files = list(Path("dist").glob("*.whl"))
    if not wheel_files:
        print("❌ No wheel file found for testing")
        return False
    
    wheel_file = wheel_files[0]
    print(f"Testing wheel: {wheel_file}")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Testing in temporary directory: {temp_dir}")
        
        # Install the package
        try:
            run_command(f"pip install {wheel_file}")
            
            # Test import
            result = run_command("python -c 'import aretomo3_gui; print(\"Import successful\")'")
            if result.returncode != 0:
                print("❌ Package import failed")
                return False
            
            # Test entry points
            result = run_command("aretomo3-gui --version", check=False)
            if result.returncode != 0:
                print("⚠️  Entry point test failed, but package imports correctly")
            else:
                print("✅ Entry point test successful")
            
            print("✅ Installation test successful")
            return True
            
        except Exception as e:
            print(f"❌ Installation test failed: {e}")
            return False


def create_installation_instructions():
    """Create installation instructions."""
    print("📝 Creating installation instructions...")
    
    instructions = """# AreTomo3 GUI Installation Instructions

## System Requirements
- Python 3.8 or higher
- PyQt6 compatible system
- Minimum 4GB RAM
- Graphics card with OpenGL support (recommended)

## Installation Methods

### Method 1: Install from Wheel (Recommended)
```bash
pip install dist/aretomo3_gui-*.whl
```

### Method 2: Install from Source
```bash
pip install .
```

### Method 3: Development Installation
```bash
pip install -e .
```

## Verify Installation
```bash
# Test import
python -c "import aretomo3_gui; print('Installation successful')"

# Launch GUI
aretomo3-gui

# Or alternative command
at3gui
```

## Dependencies
The package will automatically install required dependencies:
- PyQt6 (GUI framework)
- numpy (numerical computing)
- matplotlib (plotting)
- mrcfile (MRC file handling)
- And other scientific computing libraries

## Troubleshooting

### PyQt6 Installation Issues
If PyQt6 fails to install:
```bash
# On Ubuntu/Debian
sudo apt-get install python3-pyqt6

# On CentOS/RHEL
sudo yum install python3-qt6

# On macOS with Homebrew
brew install pyqt6
```

### Permission Issues
If you get permission errors:
```bash
pip install --user dist/aretomo3_gui-*.whl
```

## Support
For issues and support, please check the documentation or create an issue in the project repository.
"""
    
    with open("INSTALLATION.md", "w") as f:
        f.write(instructions)
    
    print("✅ Installation instructions created: INSTALLATION.md")


def main():
    """Main installation process."""
    print("🚀 AreTomo3 GUI Professional Package Builder")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Verify package structure
    if not verify_package_structure():
        sys.exit(1)
    
    # Clean build artifacts
    clean_build_artifacts()
    
    # Build package
    if not build_package():
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("⚠️  Installation test failed, but package was built")
        print("   Manual testing may be required")
    
    # Create installation instructions
    create_installation_instructions()
    
    print("\n🎉 Package creation completed successfully!")
    print("\nNext steps:")
    print("1. Test the package: pip install dist/aretomo3_gui-*.whl")
    print("2. Verify functionality: aretomo3-gui --version")
    print("3. Run tests: python -m pytest tests/")
    print("4. Distribute the wheel file in dist/ directory")


if __name__ == "__main__":
    main()
