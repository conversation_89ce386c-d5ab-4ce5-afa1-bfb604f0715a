#!/usr/bin/env python3
"""
AreTomo3 GUI Installation Verification Script
Comprehensive testing to ensure the package works correctly after installation.
"""

import sys
import os
import importlib
import subprocess
import tempfile
from pathlib import Path


class InstallationVerifier:
    """Comprehensive installation verification."""
    
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.warnings = 0
    
    def log_test(self, test_name, passed, message=""):
        """Log test result."""
        if passed:
            print(f"✅ {test_name}")
            self.passed_tests += 1
        else:
            print(f"❌ {test_name}: {message}")
            self.failed_tests += 1
    
    def log_warning(self, test_name, message):
        """Log warning."""
        print(f"⚠️  {test_name}: {message}")
        self.warnings += 1
    
    def test_python_version(self):
        """Test Python version compatibility."""
        version_ok = sys.version_info >= (3, 8)
        self.log_test(
            "Python Version Check",
            version_ok,
            f"Python 3.8+ required, found {sys.version}"
        )
        return version_ok
    
    def test_core_imports(self):
        """Test core package imports."""
        core_modules = [
            "aretomo3_gui",
            "aretomo3_gui.main",
            "aretomo3_gui.gui.main_window",
        ]
        
        all_passed = True
        for module in core_modules:
            try:
                importlib.import_module(module)
                self.log_test(f"Import {module}", True)
            except ImportError as e:
                self.log_test(f"Import {module}", False, str(e))
                all_passed = False
        
        return all_passed
    
    def test_dependencies(self):
        """Test required dependencies."""
        dependencies = [
            ("PyQt6", "PyQt6"),
            ("numpy", "numpy"),
            ("matplotlib", "matplotlib"),
            ("mrcfile", "mrcfile"),
            ("psutil", "psutil"),
            ("Pillow", "PIL"),
            ("scipy", "scipy"),
            ("h5py", "h5py"),
            ("tifffile", "tifffile"),
            ("imageio", "imageio"),
            ("PyYAML", "yaml"),
        ]
        
        all_passed = True
        for dep_name, import_name in dependencies:
            try:
                importlib.import_module(import_name)
                self.log_test(f"Dependency {dep_name}", True)
            except ImportError as e:
                self.log_test(f"Dependency {dep_name}", False, str(e))
                all_passed = False
        
        return all_passed
    
    def test_entry_points(self):
        """Test console entry points."""
        entry_points = ["aretomo3-gui", "at3gui"]
        
        all_passed = True
        for entry_point in entry_points:
            try:
                result = subprocess.run(
                    [entry_point, "--version"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    self.log_test(f"Entry point {entry_point}", True)
                else:
                    self.log_test(f"Entry point {entry_point}", False, "Non-zero exit code")
                    all_passed = False
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                self.log_test(f"Entry point {entry_point}", False, str(e))
                all_passed = False
        
        return all_passed
    
    def test_gui_components(self):
        """Test GUI components (without actually showing GUI)."""
        try:
            # Test QApplication creation
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            
            # Create QApplication if it doesn't exist
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            self.log_test("QApplication Creation", True)
            
            # Test main window import
            from aretomo3_gui.gui.main_window import AreTomoGUI
            self.log_test("Main Window Import", True)
            
            # Test window creation (but don't show it)
            try:
                window = AreTomoGUI()
                self.log_test("Main Window Creation", True)
                window.close()  # Clean up
            except Exception as e:
                self.log_test("Main Window Creation", False, str(e))
                return False
            
            return True
            
        except Exception as e:
            self.log_test("GUI Components", False, str(e))
            return False
    
    def test_file_operations(self):
        """Test file operations and utilities."""
        try:
            # Test configuration loading
            from aretomo3_gui.core.config import config
            self.log_test("Configuration Loading", True)
            
            # Test utility functions
            from aretomo3_gui.utils import file_utils
            self.log_test("Utility Functions", True)
            
            return True
            
        except Exception as e:
            self.log_test("File Operations", False, str(e))
            return False
    
    def test_sample_data_access(self):
        """Test access to sample data if available."""
        sample_data_path = Path("sample_data")
        if sample_data_path.exists():
            self.log_test("Sample Data Available", True)
            return True
        else:
            self.log_warning("Sample Data", "Not found (optional)")
            return True
    
    def run_comprehensive_verification(self):
        """Run all verification tests."""
        print("🔍 AreTomo3 GUI Installation Verification")
        print("=" * 50)
        
        # Core tests
        tests = [
            self.test_python_version,
            self.test_core_imports,
            self.test_dependencies,
            self.test_entry_points,
            self.test_gui_components,
            self.test_file_operations,
            self.test_sample_data_access,
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                print(f"❌ Test {test.__name__} crashed: {e}")
                self.failed_tests += 1
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Verification Summary")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"⚠️  Warnings: {self.warnings}")
        
        if self.failed_tests == 0:
            print("\n🎉 Installation verification PASSED!")
            print("AreTomo3 GUI is ready to use.")
            return True
        else:
            print(f"\n💥 Installation verification FAILED!")
            print(f"Please fix {self.failed_tests} failed test(s) before using.")
            return False


def main():
    """Main verification process."""
    verifier = InstallationVerifier()
    success = verifier.run_comprehensive_verification()
    
    if success:
        print("\n🚀 Quick Start:")
        print("  aretomo3-gui                 # Launch GUI")
        print("  python -m aretomo3_gui       # Alternative launch")
        print("  aretomo3-gui --help          # Show help")
        return 0
    else:
        print("\n🔧 Troubleshooting:")
        print("  pip install --upgrade aretomo3-gui")
        print("  pip install PyQt6")
        print("  Check system requirements")
        return 1


if __name__ == "__main__":
    sys.exit(main())
