# EER File Support in AreTomo3 GUI

This document outlines the removal of direct EER file support from AreTomo3 GUI.

## Status

As of the latest version, direct integration of EER file reading capabilities, previously reliant on EerReaderLib, has been **removed** from the AreTomo3 GUI project.

## Rationale

The decision to remove EER support was made due to the following factors:
- **Maintenance Complexity:** Integrating and maintaining a C++ library (EerReaderLib) across multiple platforms added significant complexity to the build and distribution process.
- **Build Issues:** Ensuring consistent compilation and linking of EerReaderLib, especially on Linux and macOS, proved challenging and time-consuming.
- **Focus on Core Functionality:** To streamline development and focus on the core features of AreTomo3 GUI, it was decided to simplify the project's dependencies.
- **Alternative Workflows:** Users can still process EER files using external tools to convert them into formats compatible with AreTomo3 GUI (e.g., MRC stacks) before importing them.

## Impact on Users

- The GUI will no longer attempt to directly read or process `.eer` files.
- Any specific UI elements or options related to EER processing have been removed.
- Users wishing to work with EER data will need to pre-process their files using other software (e.g., tools from Thermo Fisher, IMOD, Relion, MotionCor2) to convert them into a supported format like MRC.

## For Developers

- The `EerReaderLib-master` directory and its contents have been removed from the `src/` directory.
- All Python code related to EER reading (e.g., `src/aretomo3_gui/utils/eer_reader.py`) has been removed or disabled.
- Build scripts (`setup.py`, `scripts/setup_at3gui.sh`) no longer include steps for compiling or bundling EerReaderLib.
- CMake is no longer a build dependency for the project itself (though some Python dependencies might still require it).
- Test cases specific to EER reading functionality have been removed.

## Future Considerations

While direct EER support is currently removed, future integration might be considered if a more stable and easily maintainable solution becomes available, or if there is strong community demand. This could involve:
- Python-native EER reading libraries, if they mature.
- A more standardized and widely adopted EER library distribution method.

Users are encouraged to use established external tools for EER to MRC conversion in the meantime.