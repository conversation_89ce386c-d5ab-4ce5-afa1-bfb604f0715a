# 🚀 AreTomo3 GUI Installation Guide

A comprehensive guide for installing the professional AreTomo3 GUI application.

## 🎯 Quick Start (Recommended)

### Method 1: Professional Installation Script
The easiest way to install AreTomo3 GUI:

```bash
# Navigate to the project directory
cd /mnt/HDD/ak_devel/AT3GUI_working

# Run the professional installer
./scripts/deployment/install.sh
```

**What this does:**
- ✅ Creates isolated virtual environment
- ✅ Installs all dependencies automatically
- ✅ Sets up desktop launcher
- ✅ Runs installation tests
- ✅ Creates command-line shortcuts

### Method 2: Python Package Installation
For Python developers:

```bash
# Navigate to project directory
cd /mnt/HDD/ak_devel/AT3GUI_working

# Install in development mode
pip install -e .
```

### Method 3: Virtual Environment (Manual)
For complete control:

```bash
# Create virtual environment
python3 -m venv at3gui_env
source at3gui_env/bin/activate  # Linux/macOS
# OR
at3gui_env\Scripts\activate     # Windows

# Install the package
cd /mnt/HDD/ak_devel/AT3GUI_working
pip install -e .
```

## 📁 Installation Methods

### 1. Full Setup with Virtual Environment
Creates an isolated environment with all dependencies:
```bash
cd /mnt/HDD/ak_devel/AT3Gui
./scripts/setup_at3gui.sh
```

**Features:**
- ✅ Isolated Python environment
- ✅ Desktop shortcuts
- ✅ Comprehensive testing
- 📍 Installs to: Current directory (customizable)

### 2. Simple Python Installation
Direct installation in current environment:
```bash
cd /mnt/HDD/ak_devel/AT3Gui
python scripts/install.py
```

**Features:**
- ✅ Quick installation
- ⚠️ Uses current Python environment

### 3. Development Installation
For developers and contributors:
```bash
cd /mnt/HDD/ak_devel/AT3Gui
pip install -e .[dev]
```

**Features:**
- ✅ Editable installation
- ✅ Development tools included
- ✅ Changes reflected immediately

### 4. Custom Location Installation
Copy source to preferred location first:
```bash
cp -r /mnt/HDD/ak_devel/AT3Gui ~/my-at3gui
cd ~/my-at3gui
./scripts/setup_at3gui.sh
```

## 🔧 System Requirements

### Minimum Requirements
- **Python 3.8+** (Python 3.9+ recommended)
- **4GB RAM** (8GB+ recommended for large datasets)
- **2GB disk space** (more for data processing)
- **OpenGL support** (for 3D visualization)

### Required Python Packages
All automatically installed via pip:
- **PyQt6** - Modern GUI framework
- **NumPy** - Numerical computing
- **Matplotlib** - Plotting and visualization
- **mrcfile** - MRC file format support
- **psutil** - System monitoring

### Optional Dependencies
- **AreTomo3** - For tomographic reconstruction
- **IMOD** - For additional processing tools
- **MotionCor3** - For motion correction

### Platform-Specific Setup

#### Ubuntu/Debian
```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install -y python3 python3-pip python3-venv

# For GUI support
sudo apt-get install -y python3-pyqt6 python3-pyqt6.qtwidgets
```

#### CentOS/RHEL/Fedora
```bash
# Install Python and pip
sudo dnf install -y python3 python3-pip

# Enable GUI support
sudo dnf install -y python3-qt6
```

#### macOS
```bash
# Install Python (if not already installed)
brew install python@3.9

# PyQt6 will be installed automatically via pip
```

#### Windows
- **Python 3.8+** from [python.org](https://python.org)
- All dependencies installed automatically via pip

## 📦 What Gets Installed

### Core Components
- **AreTomo3 GUI Application** - Professional graphical interface
- **Enhanced Analysis Tools** - Comprehensive result analysis
- **Live Processing Monitor** - Real-time file monitoring
- **Parameter Management** - Advanced parameter configuration with help system
- **Backup System** - Automated project backups

### GUI Features
- **📊 Enhanced Analysis Tab** - Multi-tab analysis with plots and statistics
- **⚙️ Enhanced Parameters Tab** - Help buttons for all parameters
- **🔴 Live Processing Tab** - Real-time monitoring and processing
- **🏠 Main Control Center** - Organized project management

## 🚀 Launching the Application

### After Installation
```bash
# Method 1: Python module
python -m aretomo3_gui

# Method 2: Direct script (if in PATH)
aretomo3-gui

# Method 3: From project directory
cd /mnt/HDD/ak_devel/AT3GUI_working
python -m aretomo3_gui
```

### Desktop Launcher
If installed with the professional installer, look for:
- **Linux**: Applications menu → Science → AreTomo3 GUI
- **Windows**: Start menu → AreTomo3 GUI
- **macOS**: Applications folder → AreTomo3 GUI

## ✅ Verification

### 1. Basic Import Test
```bash
python -c "import aretomo3_gui; print('✅ AreTomo3 GUI installed successfully')"
```

### 2. GUI Launch Test
```bash
# Test GUI creation (requires display)
python -c "
import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'  # For headless testing
from aretomo3_gui.gui.main_window import AreTomo3MainWindow
from PyQt6.QtWidgets import QApplication
app = QApplication([])
window = AreTomo3MainWindow()
print(f'✅ GUI created with {window.tab_widget.count()} tabs')
app.quit()
"
```

### 3. Full Application Test
```bash
# Launch the full application (requires display)
python -m aretomo3_gui --help
```

## 🔧 Installation Options

### Setup Script Options
```bash
./scripts/setup_at3gui.sh --help

Options:
  --skip-deps      Skip system dependency installation
  --skip-test      Skip installation testing
  --install-dir    Custom installation directory
  --help           Show help message
```

### Python Installer Options
```bash
python scripts/install.py --help

Options:
  --no-test        Skip testing
  --no-shortcut    Skip desktop shortcuts
  --no-venv        Install in current environment
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. Python Version Issues
```bash
# Check Python version
python3 --version  # Should be 3.8+

# If too old, install newer Python:
# Ubuntu/Debian: sudo apt install python3.9
# macOS: brew install python@3.9
# Windows: Download from python.org
```

#### 2. PyQt6 Installation Issues
```bash
# If PyQt6 fails to install
pip install --upgrade pip setuptools wheel
pip install PyQt6

# Alternative: use system package manager
# Ubuntu: sudo apt install python3-pyqt6
# Fedora: sudo dnf install python3-qt6
```

#### 3. Import Errors
```bash
# Check if package is properly installed
python -c "import sys; print(sys.path)"
python -c "import aretomo3_gui; print(aretomo3_gui.__file__)"

# If import fails, reinstall in development mode
cd /mnt/HDD/ak_devel/AT3GUI_working
pip install -e .
```

#### 4. GUI Display Issues
```bash
# For headless systems, use offscreen platform
export QT_QPA_PLATFORM=offscreen
python -m aretomo3_gui

# For X11 forwarding issues
export DISPLAY=:0
xhost +local:
```

#### 5. Permission Errors
```bash
# Use virtual environment (recommended)
python3 -m venv at3gui_env
source at3gui_env/bin/activate
pip install -e .

# OR use user installation
pip install --user -e .
```

### Advanced Troubleshooting

#### Dependency Conflicts
```bash
# Create clean environment
python3 -m venv clean_env
source clean_env/bin/activate
pip install --upgrade pip
cd /mnt/HDD/ak_devel/AT3GUI_working
pip install -e .
```

#### Debug Installation
```bash
# Run installation with verbose output
pip install -e . -v

# Check installed packages
pip list | grep -E "(PyQt6|numpy|matplotlib|mrcfile)"
```

#### Clean Reinstall
```bash
# Remove existing installation
pip uninstall aretomo3-gui

# Clean pip cache
pip cache purge

# Reinstall
cd /mnt/HDD/ak_devel/AT3GUI_working
pip install -e .
```

## 🔄 Updating

### Update Installation
```bash
cd /mnt/HDD/ak_devel/AT3GUI_working
git pull  # If using git
pip install -e . --upgrade
```

### Development Updates
```bash
cd /mnt/HDD/ak_devel/AT3GUI_working
git pull
pip install -e .  # Reinstall in development mode
```

## 🗑️ Uninstalling

### Development Installation
```bash
# Remove package
pip uninstall aretomo3-gui

# Remove desktop launcher (if created)
rm -f ~/.local/share/applications/aretomo3-gui.desktop  # Linux
```

### Virtual Environment Installation
```bash
# Remove entire virtual environment
rm -rf at3gui_env

# Remove desktop launcher
rm -f ~/.local/share/applications/aretomo3-gui.desktop  # Linux
```

### Complete Cleanup
```bash
# Remove package
pip uninstall aretomo3-gui

# Clean pip cache
pip cache purge

# Remove any remaining files
rm -f ~/.local/share/applications/aretomo3-gui.desktop
```

## 📞 Getting Help

### Documentation
- **Installation Guide**: `docs/user/INSTALLATION.md` (this file)
- **User Guide**: `docs/user/USER_GUIDE.md`
- **Developer Guide**: `docs/developer/DEVELOPER_GUIDE.md`
- **API Reference**: `docs/api/API_REFERENCE.md`

### Quick Tests
```bash
# Test basic functionality
cd /mnt/HDD/ak_devel/AT3GUI_working
python scripts/development/validate_professional_project.py

# Test in clean environment
python scripts/development/clean_environment_test.py
```

### Support Information
- **Project Location**: `/mnt/HDD/ak_devel/AT3GUI_working`
- **Backup System**: `backups_AT3GUI/create_backup.sh`
- **Log Files**: `src/logs/aretomo3_gui_*.log`

### Before Reporting Issues
1. ✅ Check this troubleshooting section
2. ✅ Run the verification tests above
3. ✅ Check log files for errors
4. ✅ Include system information:
   - Operating system and version
   - Python version (`python3 --version`)
   - PyQt6 version (`pip show PyQt6`)
   - Error messages and stack traces

---

## 🎉 Success!

After successful installation, you should be able to:

✅ **Launch the GUI**: `python -m aretomo3_gui`
✅ **Access all tabs**: Main, Parameters, Analysis, Live Processing
✅ **Use help system**: Click ℹ️ buttons for parameter help
✅ **Monitor processing**: Real-time file monitoring and analysis
✅ **Manage projects**: Automated backups and session management

**Happy cryo-EM processing with AreTomo3 GUI!** 🧬🔬