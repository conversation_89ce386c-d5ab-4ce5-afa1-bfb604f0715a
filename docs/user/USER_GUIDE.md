# 📖 AT3GUI User Guide

Welcome to the comprehensive user guide for AreTomo3 GUI (AT3GUI). This guide will help you get the most out of AT3GUI for your tomography processing needs.

## 🎯 Table of Contents

- [Getting Started](#-getting-started)
- [Interface Overview](#-interface-overview)
- [Basic Workflow](#-basic-workflow)
- [Advanced Features](#-advanced-features)
- [Batch Processing](#-batch-processing)
- [Configuration](#-configuration)
- [Tips and Best Practices](#-tips-and-best-practices)
- [Troubleshooting](#-troubleshooting)

## 🚀 Getting Started

### First Launch

After installation, launch AT3GUI using one of these methods:

```bash
# Using the new launcher script
./scripts/launch.sh

# Direct launch from installation directory
cd /AT3GUI && ./scripts/launch.sh

# Using desktop shortcut (if created during installation)
# Click on AT3GUI icon in your applications menu or desktop
```

### Initial Setup

1. **Check Dependencies**: AT3GUI will automatically verify that AreTomo3 is installed and accessible
2. **Configure Paths**: Set up default paths for input/output directories in Preferences
3. **Test Installation**: Run a small test dataset to verify everything works correctly

## 🖥️ Interface Overview

### Main Window Components

#### 1. **Menu Bar**
- **File**: Open, save, recent projects, preferences
- **Tools**: Batch processing, utilities, plugins
- **View**: Layout options, zoom controls, panel visibility
- **Help**: Documentation, about, check for updates

#### 2. **Input Panel** (Left Side)
- **File Selection**: Choose MRC/ST files for processing
- **Basic Parameters**: Tilt angle, pixel size, voltage
- **Quick Preview**: Thumbnail view of selected files

#### 3. **Processing Panel** (Center)
- **Parameter Configuration**: Detailed AreTomo3 settings
- **Processing Controls**: Start, stop, pause operations
- **Progress Monitoring**: Real-time processing status

#### 4. **Output Panel** (Right Side)
- **Results Display**: Processed tomogram previews
- **Log Viewer**: Processing messages and errors
- **Export Options**: Save formats and locations

#### 5. **Status Bar** (Bottom)
- **System Status**: CPU, memory, disk usage
- **Processing Queue**: Number of pending jobs
- **Quick Stats**: Success/failure counts

## 📋 Basic Workflow

### Single File Processing

1. **Select Input File**
   ```
   File → Open → Select your MRC/ST file
   ```

2. **Configure Parameters**
   - Set tilt angle range (e.g., -60° to +60°)
   - Specify pixel size (e.g., 2.7 Å)
   - Choose voltage (e.g., 300 kV)

3. **Advanced Settings** (Optional)
   - Alignment parameters
   - CTF correction options
   - Reconstruction settings

4. **Start Processing**
   ```
   Click "Process" button or press Ctrl+R
   ```

5. **Monitor Progress**
   - Watch the progress bar
   - Check log messages
   - Preview intermediate results

6. **Review Results**
   - Examine the reconstructed tomogram
   - Check alignment quality
   - Save or export results

### Project-Based Workflow

1. **Create New Project**
   ```
   File → New Project → Specify project directory
   ```

2. **Add Multiple Files**
   - Drag and drop files into the project
   - Use batch file selection tools
   - Import from directory structures

3. **Configure Batch Settings**
   - Set common parameters for all files
   - Define per-file overrides if needed
   - Configure output naming schemes

4. **Queue Processing**
   - Review processing queue
   - Adjust priority if needed
   - Start batch processing

5. **Manage Results**
   - Organize output files
   - Generate processing reports
   - Archive completed projects

## 🔧 Advanced Features

### Custom Parameter Profiles

Create and save parameter sets for different data types:

1. **Create Profile**
   ```
   Tools → Parameter Profiles → New Profile
   ```

2. **Configure Settings**
   - Set all processing parameters
   - Add descriptive name and notes
   - Save for future use

3. **Apply Profile**
   - Select saved profile from dropdown
   - Modify parameters if needed
   - Process with consistent settings

### Plugin System

Extend AT3GUI functionality with plugins:

1. **Install Plugins**
   ```
   Tools → Plugin Manager → Install from file/repository
   ```

2. **Popular Plugins**
   - **Quality Assessment**: Automated result evaluation
   - **Data Visualization**: Enhanced viewing tools
   - **Export Formats**: Additional output options
   - **Integration Tools**: Connect with other software

### Scripting Interface

Automate tasks with the built-in scripting system:

```python
# Example automation script
import at3gui

# Load project
project = at3gui.load_project("my_project.at3gui")

# Configure processing
project.set_parameter("tilt_angle", (-60, 60))
project.set_parameter("pixel_size", 2.7)

# Process all files
project.process_all()

# Generate report
project.export_report("processing_summary.pdf")
```

## 📦 Batch Processing

### Setting Up Batch Jobs

1. **Prepare Data Structure**
   ```
   project_root/
   ├── raw_data/
   │   ├── dataset_001.st
   │   ├── dataset_002.st
   │   └── dataset_003.st
   ├── metadata/
   │   └── processing_params.csv
   └── output/
   ```

2. **Configure Batch Parameters**
   - Import parameter file (CSV/Excel)
   - Set common processing options
   - Define output directory structure

3. **Queue Management**
   - Add files to processing queue
   - Set processing priorities
   - Configure parallel processing

4. **Monitoring and Control**
   - Real-time progress tracking
   - Pause/resume capabilities
   - Error handling and recovery

### Batch Processing Best Practices

- **Resource Management**: Monitor CPU and memory usage
- **Error Handling**: Set up automatic retry policies
- **Progress Tracking**: Use detailed logging
- **Result Validation**: Implement quality checks

## ⚙️ Configuration

### User Preferences

Access via `File → Preferences`:

#### General Settings
- **Default Directories**: Input, output, temporary files
- **Interface Theme**: Light, dark, system default
- **Language**: Interface localization options
- **Update Checking**: Automatic update notifications

#### Processing Settings
- **CPU Cores**: Number of parallel processes
- **Memory Limit**: Maximum RAM usage
- **Temporary Storage**: Location for intermediate files
- **AreTomo3 Path**: Custom installation location

#### Advanced Options
- **Logging Level**: Debug, info, warning, error
- **Plugin Directories**: Custom plugin locations
- **Network Settings**: Proxy configuration
- **Performance Tuning**: Optimization parameters

### Configuration Files

AT3GUI stores settings in:
- **User Config**: `~/.config/at3gui/settings.json`
- **Project Config**: `project_directory/.at3gui/config.json`
- **System Config**: `/etc/at3gui/global.conf` (admin settings)

## 💡 Tips and Best Practices

### Data Preparation
- **File Naming**: Use consistent, descriptive names
- **Directory Structure**: Organize data logically
- **Metadata**: Include acquisition parameters
- **Backups**: Keep copies of original data

### Processing Optimization
- **Parameter Testing**: Start with small datasets
- **Resource Monitoring**: Watch system performance
- **Quality Control**: Validate results regularly
- **Documentation**: Keep processing logs

### Workflow Efficiency
- **Templates**: Create reusable parameter sets
- **Automation**: Use batch processing for similar data
- **Shortcuts**: Learn keyboard shortcuts
- **Organization**: Use project-based workflows

### Quality Assurance
- **Visual Inspection**: Always check reconstructions
- **Alignment Validation**: Review alignment parameters
- **Consistency Checks**: Compare similar datasets
- **Error Analysis**: Investigate failed processing

## 🔧 Troubleshooting

### Common Issues

#### Installation Problems
```bash
# Check installation
./scripts/launch.sh --verbose

# Verify dependencies
python -c "import aretomo3_gui; print('OK')"

# Reinstall if needed
./scripts/uninstall.sh
./scripts/install.sh
```

#### Processing Errors
- **Memory Issues**: Reduce parallel processes or increase system RAM
- **File Access**: Check file permissions and paths
- **Parameter Conflicts**: Validate processing parameters
- **AreTomo3 Errors**: Check AreTomo3 installation and logs

#### Performance Issues
- **Slow Processing**: Check CPU usage and available memory
- **Disk Space**: Ensure sufficient storage for temporary files
- **Network Issues**: Verify file access over network shares

### Getting Help

1. **Built-in Help**: Press F1 or use Help menu
2. **Documentation**: Check `docs/` directory
3. **Log Files**: Review `logs/` for error details
4. **Community Support**: Visit project repository for issues
5. **Professional Support**: Contact development team

### Reporting Issues

When reporting problems, include:
- AT3GUI version and installation method
- Operating system and version
- Complete error messages
- Steps to reproduce the issue
- Sample data if possible

## 📚 Additional Resources

### Documentation
- **API Reference**: `docs/API_REFERENCE.md`
- **Developer Guide**: `docs/DEVELOPER_GUIDE.md`
- **Troubleshooting**: `docs/TROUBLESHOOTING.md`
- **Changelog**: `docs/CHANGELOG.md`

### External Resources
- **AreTomo3 Documentation**: Official AreTomo3 guides
- **Tomography Best Practices**: Scientific literature
- **Video Tutorials**: Online tutorial series
- **User Forums**: Community discussions

---

## 🎉 Conclusion

AT3GUI provides a powerful, user-friendly interface for AreTomo3 processing. This guide covers the essential features and workflows, but AT3GUI offers many more capabilities to explore.

For advanced usage, custom plugins, and development information, see the [Developer Guide](DEVELOPER_GUIDE.md).

**Happy Processing!** 🚀

---

*Last updated: May 28, 2025*  
*Version: Latest*
