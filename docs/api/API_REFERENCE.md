# 📚 AT3GUI API Reference

This document provides comprehensive API documentation for AT3GUI components, modules, and interfaces.

## 🎯 Table of Contents

- [Core Modules](#-core-modules)
- [GUI Components](#-gui-components)
- [Tools and Utilities](#-tools-and-utilities)
- [Plugin System](#-plugin-system)
- [Configuration API](#-configuration-api)
- [Event System](#-event-system)
- [Erro<PERSON> Handling](#-error-handling)

## 🧠 Core Modules

### aretomo3_gui.core.processing

Main processing pipeline for AreTomo3 operations.

#### ProcessingPipeline

```python
class ProcessingPipeline:
    """Main processing pipeline for AreTomo3 operations."""
    
    def __init__(self, parameters: ProcessingParameters)
    def process(self, input_file: Path) -> ProcessingResult
    def process_batch(self, input_files: List[Path]) -> List[ProcessingResult]
    def set_status_callback(self, callback: Callable[[str, int], None])
    def cancel_processing(self) -> bool
```

**Parameters:**
- `parameters`: ProcessingParameters object containing all processing settings
- `input_file`: Path to input MRC/ST file
- `input_files`: List of input files for batch processing
- `callback`: Function called with (status_message, progress_percentage)

**Returns:**
- `ProcessingResult`: Object containing processing results and metadata

**Example:**
```python
from aretomo3_gui.core.processing import ProcessingPipeline
from aretomo3_gui.core.parameters import ProcessingParameters

# Create parameters
params = ProcessingParameters()
params.set_parameter("tilt_angle", (-60, 60))
params.set_parameter("pixel_size", 2.7)

# Create and run pipeline
pipeline = ProcessingPipeline(params)
result = pipeline.process("input.mrc")

if result.success:
    print(f"Processing completed: {result.output_file}")
else:
    print(f"Processing failed: {result.error_message}")
```

#### ProcessingResult

```python
class ProcessingResult:
    """Container for processing results."""
    
    @property
    def success(self) -> bool
    @property
    def output_file(self) -> Optional[Path]
    @property
    def error_message(self) -> Optional[str]
    @property
    def processing_time(self) -> float
    @property
    def metadata(self) -> Dict[str, Any]
```

### aretomo3_gui.core.parameters

Parameter management and validation system.

#### ProcessingParameters

```python
class ProcessingParameters:
    """Container for all processing parameters with validation."""
    
    def __init__(self)
    def set_parameter(self, name: str, value: Any) -> None
    def get_parameter(self, name: str) -> Any
    def remove_parameter(self, name: str) -> None
    def validate(self) -> List[str]
    def to_dict(self) -> Dict[str, Any]
    def from_dict(self, data: Dict[str, Any]) -> None
    def save(self, file_path: Path) -> None
    def load(self, file_path: Path) -> None
```

**Standard Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `tilt_angle` | Tuple[float, float] | Min/max tilt angles | (-60, 60) |
| `pixel_size` | float | Pixel size in Angstroms | 2.7 |
| `voltage` | int | Acceleration voltage in kV | 300 |
| `cs` | float | Spherical aberration in mm | 2.7 |
| `amplitude_contrast` | float | Amplitude contrast | 0.1 |
| `defocus_range` | Tuple[float, float] | Defocus search range | (-5.0, -0.5) |

**Example:**
```python
from aretomo3_gui.core.parameters import ProcessingParameters

params = ProcessingParameters()

# Set basic parameters
params.set_parameter("tilt_angle", (-45, 45))
params.set_parameter("pixel_size", 3.0)
params.set_parameter("voltage", 200)

# Validate parameters
errors = params.validate()
if errors:
    print("Validation errors:", errors)

# Save/load parameters
params.save("my_parameters.json")
loaded_params = ProcessingParameters()
loaded_params.load("my_parameters.json")
```

### aretomo3_gui.core.project

Project management for organizing processing workflows.

#### Project

```python
class Project:
    """Project management for AT3GUI workflows."""
    
    def __init__(self, project_path: Path)
    @classmethod
    def create(cls, name: str, base_path: Path) -> 'Project'
    @classmethod
    def load(cls, project_file: Path) -> 'Project'
    
    def add_file(self, file_path: Path, parameters: Optional[ProcessingParameters] = None) -> None
    def remove_file(self, file_path: Path) -> None
    def get_files(self) -> List[Path]
    def set_global_parameters(self, parameters: ProcessingParameters) -> None
    def process_all(self, parallel: bool = True) -> Iterator[ProcessingResult]
    def save(self) -> None
    def export_report(self, output_path: Path) -> None
```

**Example:**
```python
from aretomo3_gui.core.project import Project
from aretomo3_gui.core.parameters import ProcessingParameters

# Create new project
project = Project.create("MyProject", Path("/path/to/projects"))

# Add files
project.add_file(Path("dataset1.mrc"))
project.add_file(Path("dataset2.mrc"))

# Set parameters
params = ProcessingParameters()
params.set_parameter("pixel_size", 2.7)
project.set_global_parameters(params)

# Process all files
for result in project.process_all():
    print(f"Processed: {result.output_file}")

# Save project
project.save()
```

## 🖥️ GUI Components

### aretomo3_gui.gui.main_window

Main application window and UI coordination.

#### MainWindow

```python
class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self)
    def open_file(self, file_path: Optional[Path] = None) -> None
    def open_project(self, project_path: Optional[Path] = None) -> None
    def start_processing(self) -> None
    def stop_processing(self) -> None
    def show_preferences(self) -> None
    def update_status(self, message: str) -> None
```

**Signals:**
```python
# File operations
file_opened = pyqtSignal(str)  # file_path
project_opened = pyqtSignal(str)  # project_path

# Processing events
processing_started = pyqtSignal()
processing_finished = pyqtSignal(bool)  # success
progress_updated = pyqtSignal(int)  # percentage

# UI events
status_updated = pyqtSignal(str)  # message
```

### aretomo3_gui.gui.widgets

Custom widgets for specialized functionality.

#### ParameterPanel

```python
class ParameterPanel(QWidget):
    """Widget for parameter input and validation."""
    
    def __init__(self, parameters: Optional[ProcessingParameters] = None)
    def set_parameters(self, parameters: ProcessingParameters) -> None
    def get_parameters(self) -> ProcessingParameters
    def validate_inputs(self) -> List[str]
    def reset_to_defaults(self) -> None
```

**Signals:**
```python
parameters_changed = pyqtSignal()
validation_error = pyqtSignal(list)  # error_messages
```

#### ProgressWidget

```python
class ProgressWidget(QWidget):
    """Widget for displaying processing progress."""
    
    def __init__(self)
    def set_progress(self, percentage: int) -> None
    def set_status(self, message: str) -> None
    def set_processing_file(self, file_path: str) -> None
    def reset(self) -> None
```

#### ResultViewer

```python
class ResultViewer(QWidget):
    """Widget for displaying processing results."""
    
    def __init__(self)
    def display_result(self, result: ProcessingResult) -> None
    def display_image(self, image_path: Path) -> None
    def clear_display(self) -> None
    def export_image(self, output_path: Path) -> None
```

## 🔧 Tools and Utilities

### aretomo3_gui.tools.aretomo3

Interface to AreTomo3 processing engine.

#### AreTomo3Interface

```python
class AreTomo3Interface:
    """Interface to AreTomo3 processing engine."""
    
    def __init__(self, executable_path: Optional[Path] = None)
    def check_installation(self) -> bool
    def get_version(self) -> str
    def process(self, input_file: Path, parameters: ProcessingParameters) -> ProcessingResult
    def generate_command(self, input_file: Path, parameters: ProcessingParameters) -> List[str]
```

**Example:**
```python
from aretomo3_gui.tools.aretomo3 import AreTomo3Interface
from aretomo3_gui.core.parameters import ProcessingParameters

# Create interface
interface = AreTomo3Interface()

# Check installation
if not interface.check_installation():
    print("AreTomo3 not found!")
    exit(1)

print(f"AreTomo3 version: {interface.get_version()}")

# Process file
params = ProcessingParameters()
result = interface.process("input.mrc", params)
```

### aretomo3_gui.utils.file_utils

File operation utilities.

#### Functions

```python
def validate_input_file(file_path: Path) -> bool
def get_file_info(file_path: Path) -> Dict[str, Any]
def create_output_path(input_path: Path, suffix: str = "_processed") -> Path
def backup_file(file_path: Path, backup_dir: Optional[Path] = None) -> Path
def cleanup_temporary_files(temp_dir: Path) -> None
```

**Example:**
```python
from aretomo3_gui.utils.file_utils import validate_input_file, get_file_info

file_path = Path("dataset.mrc")

if validate_input_file(file_path):
    info = get_file_info(file_path)
    print(f"File size: {info['size']} bytes")
    print(f"Dimensions: {info['dimensions']}")
```

### aretomo3_gui.utils.logging_utils

Logging configuration and utilities.

#### Functions

```python
def setup_logging(level: str = "INFO", log_file: Optional[Path] = None) -> None
def get_logger(name: str) -> logging.Logger
def log_processing_start(file_path: Path, parameters: ProcessingParameters) -> None
def log_processing_result(result: ProcessingResult) -> None
```

**Example:**
```python
from aretomo3_gui.utils.logging_utils import setup_logging, get_logger

# Setup logging
setup_logging(level="DEBUG", log_file=Path("at3gui.log"))

# Get logger
logger = get_logger(__name__)
logger.info("Processing started")
```

## 🔌 Plugin System

### aretomo3_gui.core.plugin_base

Base class for plugin development.

#### PluginBase

```python
class PluginBase:
    """Base class for AT3GUI plugins."""
    
    name: str = "Unknown Plugin"
    version: str = "1.0.0"
    description: str = ""
    author: str = ""
    
    def __init__(self)
    def initialize(self, app_context: 'ApplicationContext') -> None
    def finalize(self) -> None
    def get_menu_items(self) -> List[Tuple[str, str, Callable]]
    def get_toolbar_items(self) -> List[Tuple[str, str, Callable]]
    def get_widgets(self) -> List[Tuple[str, QWidget]]
```

**Example Plugin:**
```python
from aretomo3_gui.core.plugin_base import PluginBase
from PyQt5.QtWidgets import QMessageBox

class ExamplePlugin(PluginBase):
    name = "Example Plugin"
    version = "1.0.0"
    description = "Example plugin for demonstration"
    
    def get_menu_items(self):
        return [
            ("Tools", "Example Action", self.example_action)
        ]
    
    def example_action(self):
        QMessageBox.information(None, "Plugin", "Hello from plugin!")
```

### Plugin Manager

```python
class PluginManager:
    """Manages plugin loading and lifecycle."""
    
    def __init__(self)
    def load_plugins(self, plugin_dir: Path) -> None
    def get_loaded_plugins(self) -> List[PluginBase]
    def enable_plugin(self, plugin_name: str) -> None
    def disable_plugin(self, plugin_name: str) -> None
```

## ⚙️ Configuration API

### aretomo3_gui.core.config

Configuration management system.

#### Configuration

```python
class Configuration:
    """Application configuration management."""
    
    def __init__(self, config_file: Optional[Path] = None)
    def get(self, key: str, default: Any = None) -> Any
    def set(self, key: str, value: Any) -> None
    def save(self) -> None
    def load(self) -> None
    def reset_to_defaults(self) -> None
```

**Standard Configuration Keys:**

| Key | Type | Description | Default |
|-----|------|-------------|---------|
| `ui.theme` | str | UI theme name | "default" |
| `ui.language` | str | Interface language | "en" |
| `processing.max_cores` | int | Max CPU cores to use | 4 |
| `processing.temp_dir` | str | Temporary file directory | "/tmp" |
| `paths.aretomo3` | str | AreTomo3 executable path | "AreTomo3" |
| `paths.default_input` | str | Default input directory | "." |
| `paths.default_output` | str | Default output directory | "./output" |

**Example:**
```python
from aretomo3_gui.core.config import Configuration

config = Configuration()

# Get configuration values
theme = config.get("ui.theme", "default")
max_cores = config.get("processing.max_cores", 4)

# Set configuration values
config.set("ui.theme", "dark")
config.set("processing.max_cores", 8)

# Save configuration
config.save()
```

## 📡 Event System

### Event Types

```python
from enum import Enum

class EventType(Enum):
    """Event types for AT3GUI event system."""
    
    PROCESSING_STARTED = "processing_started"
    PROCESSING_FINISHED = "processing_finished"
    PROGRESS_UPDATED = "progress_updated"
    FILE_OPENED = "file_opened"
    PROJECT_OPENED = "project_opened"
    PARAMETER_CHANGED = "parameter_changed"
    ERROR_OCCURRED = "error_occurred"
```

### Event Manager

```python
class EventManager:
    """Central event management system."""
    
    def __init__(self)
    def subscribe(self, event_type: EventType, callback: Callable) -> None
    def unsubscribe(self, event_type: EventType, callback: Callable) -> None
    def emit(self, event_type: EventType, data: Any = None) -> None
```

**Example:**
```python
from aretomo3_gui.core.events import EventManager, EventType

def on_processing_finished(result):
    print(f"Processing finished: {result.success}")

event_manager = EventManager()
event_manager.subscribe(EventType.PROCESSING_FINISHED, on_processing_finished)

# Emit event
event_manager.emit(EventType.PROCESSING_FINISHED, processing_result)
```

## ❌ Error Handling

### Custom Exceptions

```python
class AT3GUIError(Exception):
    """Base exception for AT3GUI."""
    pass

class ProcessingError(AT3GUIError):
    """Error during processing operations."""
    pass

class ValidationError(AT3GUIError):
    """Parameter validation error."""
    pass

class ConfigurationError(AT3GUIError):
    """Configuration-related error."""
    pass

class PluginError(AT3GUIError):
    """Plugin-related error."""
    pass
```

### Error Context Manager

```python
from contextlib import contextmanager

@contextmanager
def error_context(operation: str):
    """Context manager for consistent error handling."""
    try:
        yield
    except Exception as e:
        logger.error(f"Error in {operation}: {e}")
        raise ProcessingError(f"Failed to {operation}: {e}") from e
```

**Example:**
```python
from aretomo3_gui.core.exceptions import error_context, ProcessingError

try:
    with error_context("process file"):
        result = pipeline.process("input.mrc")
except ProcessingError as e:
    print(f"Processing failed: {e}")
```

---

## 🎯 Quick Reference

### Common Import Patterns

```python
# Core functionality
from aretomo3_gui.core.processing import ProcessingPipeline
from aretomo3_gui.core.parameters import ProcessingParameters
from aretomo3_gui.core.project import Project

# GUI components
from aretomo3_gui.gui.main_window import MainWindow
from aretomo3_gui.gui.widgets import ParameterPanel, ProgressWidget

# Tools and utilities
from aretomo3_gui.tools.aretomo3 import AreTomo3Interface
from aretomo3_gui.utils.file_utils import validate_input_file
from aretomo3_gui.utils.logging_utils import setup_logging

# Configuration and events
from aretomo3_gui.core.config import Configuration
from aretomo3_gui.core.events import EventManager, EventType
```

### Typical Workflow

```python
# Setup
from aretomo3_gui.core import ProcessingPipeline, ProcessingParameters
from aretomo3_gui.utils.logging_utils import setup_logging

setup_logging(level="INFO")

# Create parameters
params = ProcessingParameters()
params.set_parameter("tilt_angle", (-60, 60))
params.set_parameter("pixel_size", 2.7)

# Process file
pipeline = ProcessingPipeline(params)
result = pipeline.process("input.mrc")

# Check result
if result.success:
    print(f"Success: {result.output_file}")
else:
    print(f"Error: {result.error_message}")
```

---

*Last updated: May 28, 2025*  
*Version: Latest*
