# Dashboard Integration Guide

## 🔗 Integrating the Analysis Dashboard with AreTomo3 GUI

This guide explains how to integrate the new Analysis Dashboard with the main AreTomo3 GUI application.

## 📋 Integration Steps

### 1. **Main Window Integration**

Add the web server to your main window class:

```python
from aretomo3_gui.web.api_server import AreTomo3<PERSON><PERSON><PERSON><PERSON>
from aretomo3_gui.core.realtime_processor import RealTimeProcessor

class AreTomo3MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Initialize real-time processor
        self.realtime_processor = RealTimeProcessor()
        
        # Initialize web API server
        self.web_api = AreTomo3WebAPI(self.realtime_processor)
        self.web_api.set_main_window(self)
        
        # Start web server in background thread
        self.start_web_server()
    
    def start_web_server(self):
        """Start the web server in a background thread."""
        import threading
        
        def run_server():
            self.web_api.run(host="0.0.0.0", port=8080, debug=False)
        
        self.web_thread = threading.Thread(target=run_server, daemon=True)
        self.web_thread.start()
        
        self.log_message("Web dashboard started at http://localhost:8080", "INFO")
```

### 2. **Real-Time Analysis Integration**

Connect the real-time analysis tab:

```python
def setup_realtime_analysis_integration(self):
    """Set up integration with real-time analysis."""
    if hasattr(self, 'realtime_analysis_tab'):
        # Connect the analysis tab to the web API
        self.web_api.realtime_analysis_tab = self.realtime_analysis_tab
        
        # Set up quality metrics sharing
        if hasattr(self.realtime_analysis_tab, 'quality_metrics_updated'):
            self.realtime_analysis_tab.quality_metrics_updated.connect(
                self.on_quality_metrics_updated
            )

def on_quality_metrics_updated(self, series_name, metrics):
    """Handle quality metrics updates for web dashboard."""
    # Broadcast to web clients
    asyncio.create_task(
        self.web_api.websocket_manager.broadcast(json.dumps({
            "type": "quality_update",
            "series_name": series_name,
            "metrics": metrics
        }))
    )
```

### 3. **Processing Job Integration**

Connect processing jobs to the web dashboard:

```python
def setup_processing_integration(self):
    """Set up processing job integration."""
    # Connect processor signals
    if hasattr(self.realtime_processor, 'job_started'):
        self.realtime_processor.job_started.connect(self.on_job_started)
    
    if hasattr(self.realtime_processor, 'job_completed'):
        self.realtime_processor.job_completed.connect(self.on_job_completed)
    
    if hasattr(self.realtime_processor, 'job_progress_updated'):
        self.realtime_processor.job_progress_updated.connect(self.on_job_progress)

def on_job_started(self, job):
    """Handle job start for web dashboard."""
    asyncio.create_task(
        self.web_api.broadcast_job_update(job, "started")
    )

def on_job_completed(self, job):
    """Handle job completion for web dashboard."""
    asyncio.create_task(
        self.web_api.broadcast_job_update(job, "completed")
    )

def on_job_progress(self, job):
    """Handle job progress updates for web dashboard."""
    asyncio.create_task(
        self.web_api.broadcast_job_update(job, "progress")
    )
```

### 4. **Session Management Integration**

Connect session management:

```python
def setup_session_integration(self):
    """Set up session management integration."""
    # Connect session manager to web API
    if hasattr(self, 'session_manager'):
        self.web_api.session_manager = self.session_manager
        
        # Connect session signals
        if hasattr(self.session_manager, 'session_created'):
            self.session_manager.session_created.connect(self.on_session_created)
        
        if hasattr(self.session_manager, 'session_updated'):
            self.session_manager.session_updated.connect(self.on_session_updated)

def on_session_created(self, session):
    """Handle session creation for web dashboard."""
    asyncio.create_task(
        self.web_api.websocket_manager.broadcast(json.dumps({
            "type": "session_created",
            "session": session.to_dict()
        }))
    )

def on_session_updated(self, session):
    """Handle session updates for web dashboard."""
    asyncio.create_task(
        self.web_api.websocket_manager.broadcast(json.dumps({
            "type": "session_updated",
            "session": session.to_dict()
        }))
    )
```

### 5. **Viewer Integration**

Connect the Napari viewer:

```python
def setup_viewer_integration(self):
    """Set up viewer integration for web dashboard."""
    # Add method to open viewer from web interface
    def open_viewer_for_job(job_id):
        """Open viewer for a specific job from web interface."""
        job = self.realtime_processor.get_job_status(job_id)
        if job and job.result_path and job.result_path.exists():
            # Open in existing viewer tab
            if hasattr(self, 'viewer_tab'):
                self.viewer_tab.load_tomogram(str(job.result_path))
                # Switch to viewer tab
                self.tab_widget.setCurrentWidget(self.viewer_tab)
            else:
                self.log_message(f"Viewer not available for job {job_id}", "WARNING")
        else:
            self.log_message(f"No results available for job {job_id}", "ERROR")
    
    # Make this method available to web API
    self.web_api.open_viewer_for_job = open_viewer_for_job
```

### 6. **Menu Integration**

Add web dashboard to the main menu:

```python
def setup_web_dashboard_menu(self):
    """Add web dashboard options to menu."""
    # Add to View menu
    web_menu = self.menuBar().addMenu("Web Dashboard")
    
    # Open dashboard action
    open_dashboard_action = QAction("Open Dashboard", self)
    open_dashboard_action.triggered.connect(self.open_web_dashboard)
    web_menu.addAction(open_dashboard_action)
    
    # Dashboard settings action
    dashboard_settings_action = QAction("Dashboard Settings", self)
    dashboard_settings_action.triggered.connect(self.show_dashboard_settings)
    web_menu.addAction(dashboard_settings_action)

def open_web_dashboard(self):
    """Open web dashboard in default browser."""
    import webbrowser
    webbrowser.open("http://localhost:8080")
    self.log_message("Web dashboard opened in browser", "INFO")

def show_dashboard_settings(self):
    """Show dashboard settings dialog."""
    # Create settings dialog for web dashboard configuration
    dialog = DashboardSettingsDialog(self)
    dialog.exec_()
```

### 7. **Status Bar Integration**

Add web server status to status bar:

```python
def setup_status_bar_integration(self):
    """Add web server status to status bar."""
    # Create web server status widget
    self.web_status_label = QLabel("Web Server: Starting...")
    self.statusBar().addPermanentWidget(self.web_status_label)
    
    # Update status when server starts
    QTimer.singleShot(2000, self.update_web_server_status)

def update_web_server_status(self):
    """Update web server status in status bar."""
    try:
        import requests
        response = requests.get("http://localhost:8080/api/status", timeout=1)
        if response.status_code == 200:
            self.web_status_label.setText("Web Server: ✅ Running (Port 8080)")
            self.web_status_label.setStyleSheet("color: green;")
        else:
            self.web_status_label.setText("Web Server: ⚠️ Error")
            self.web_status_label.setStyleSheet("color: orange;")
    except:
        self.web_status_label.setText("Web Server: ❌ Offline")
        self.web_status_label.setStyleSheet("color: red;")
```

## 🔧 Configuration Options

### **Web Server Configuration**

```python
class WebDashboardConfig:
    """Configuration for web dashboard."""
    
    def __init__(self):
        self.host = "0.0.0.0"
        self.port = 8080
        self.debug = False
        self.auto_start = True
        self.enable_cors = True
        self.log_level = "INFO"
        
    def to_dict(self):
        return {
            "host": self.host,
            "port": self.port,
            "debug": self.debug,
            "auto_start": self.auto_start,
            "enable_cors": self.enable_cors,
            "log_level": self.log_level
        }
```

### **Settings Dialog**

```python
class DashboardSettingsDialog(QDialog):
    """Settings dialog for web dashboard configuration."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Web Dashboard Settings")
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Port setting
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("Port:"))
        self.port_spinbox = QSpinBox()
        self.port_spinbox.setRange(1024, 65535)
        self.port_spinbox.setValue(8080)
        port_layout.addWidget(self.port_spinbox)
        layout.addLayout(port_layout)
        
        # Auto-start setting
        self.auto_start_checkbox = QCheckBox("Auto-start web server")
        self.auto_start_checkbox.setChecked(True)
        layout.addWidget(self.auto_start_checkbox)
        
        # Debug mode setting
        self.debug_checkbox = QCheckBox("Enable debug mode")
        layout.addWidget(self.debug_checkbox)
        
        # Buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
```

## 🚀 Complete Integration Example

```python
class AreTomo3MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.setup_ui()
        self.setup_processors()
        self.setup_web_dashboard()
        self.setup_integrations()
    
    def setup_web_dashboard(self):
        """Set up the web dashboard."""
        # Create web API
        self.web_api = AreTomo3WebAPI(self.realtime_processor)
        self.web_api.set_main_window(self)
        
        # Start web server
        self.start_web_server()
        
        # Set up menu and status
        self.setup_web_dashboard_menu()
        self.setup_status_bar_integration()
    
    def setup_integrations(self):
        """Set up all integrations."""
        self.setup_realtime_analysis_integration()
        self.setup_processing_integration()
        self.setup_session_integration()
        self.setup_viewer_integration()
    
    def closeEvent(self, event):
        """Handle application close."""
        # Stop web server gracefully
        if hasattr(self, 'web_api'):
            # The web server will stop when the main thread exits
            self.log_message("Stopping web dashboard...", "INFO")
        
        super().closeEvent(event)
```

## 📝 Notes

- **Thread Safety**: The web server runs in a separate thread, so use appropriate thread-safe mechanisms for communication
- **Port Configuration**: Default port is 8080, but should be configurable
- **Error Handling**: Implement proper error handling for web server startup failures
- **Security**: Consider adding authentication for production deployments
- **Performance**: Monitor memory usage with large datasets and many concurrent connections

## 🔍 Testing

Use the provided test script to verify integration:

```bash
python test_web_dashboard.py
```

This will start the dashboard with sample data for testing all features.
