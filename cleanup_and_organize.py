#!/usr/bin/env python3
"""
Comprehensive cleanup and organization script for AreTomo3 GUI.
Creates proper directory structure with minimal package, installed package, and compressed packages.
"""

import os
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path
import datetime


class DirectoryOrganizer:
    """Organize the project into proper structure."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.parent_dir = self.project_root.parent
        
    def clean_current_directory(self):
        """Clean up the current messy directory."""
        print("🧹 Cleaning up current directory...")
        
        # Remove build artifacts and duplicates
        artifacts_to_remove = [
            "build",
            "dist", 
            "*.egg-info",
            "__pycache__",
            "aretomo3_gui-1.0.0",  # Duplicate directory
            "bin",
            "aretomo3_gui_distribution_*",
            "*.pyc",
        ]
        
        for pattern in artifacts_to_remove:
            if "*" in pattern:
                # Use shell expansion for patterns
                os.system(f"rm -rf {pattern} 2>/dev/null")
            else:
                path = Path(pattern)
                if path.exists():
                    if path.is_dir():
                        shutil.rmtree(path)
                    else:
                        path.unlink()
                    print(f"  ✅ Removed: {pattern}")
        
        # Clean Python cache files
        os.system("find . -name '*.pyc' -delete 2>/dev/null")
        os.system("find . -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null")
        
        print("✅ Current directory cleaned")
    
    def create_minimal_package(self):
        """Create minimal package with only essential source code."""
        print("📦 Creating minimal package...")
        
        minimal_dir = Path("minimal_package")
        if minimal_dir.exists():
            shutil.rmtree(minimal_dir)
        minimal_dir.mkdir()
        
        # Essential files for minimal package
        essential_files = [
            "aretomo3_gui/",
            "setup.py",
            "pyproject.toml", 
            "requirements.txt",
            "README.md",
            "MANIFEST.in",
        ]
        
        # Essential docs
        essential_docs = [
            "INSTALLATION_GUIDE.md",
            "QUICK_START.md",
        ]
        
        # Copy essential files
        for item in essential_files:
            src = Path(item)
            if src.exists():
                dst = minimal_dir / src.name
                if src.is_dir():
                    shutil.copytree(src, dst, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                else:
                    shutil.copy2(src, dst)
                print(f"  ✅ Copied: {item}")
        
        # Copy essential documentation
        docs_dir = minimal_dir / "docs"
        docs_dir.mkdir()
        for doc in essential_docs:
            if Path(doc).exists():
                shutil.copy2(doc, docs_dir / doc)
                print(f"  ✅ Copied doc: {doc}")
        
        # Create minimal package README
        self.create_minimal_readme(minimal_dir)
        
        print(f"✅ Minimal package created: {minimal_dir}")
        return minimal_dir
    
    def create_minimal_readme(self, minimal_dir):
        """Create README for minimal package."""
        readme_content = '''# AreTomo3 GUI - Minimal Package

## Overview
This is the minimal source package for AreTomo3 GUI containing only essential files.

## Contents
- `aretomo3_gui/` - Source code
- `setup.py` - Package setup
- `pyproject.toml` - Project configuration  
- `requirements.txt` - Dependencies
- `docs/` - Essential documentation

## Installation
```bash
pip install .
```

## Development Installation
```bash
pip install -e .
```

## Building Distribution
```bash
python setup.py sdist bdist_wheel
```

## Usage
```bash
# Set Qt environment
export QT_API=pyqt6

# Launch application
aretomo3-gui
```

## Documentation
See `docs/` directory for installation guides and quick start.
'''
        
        with open(minimal_dir / "README.md", "w") as f:
            f.write(readme_content)
    
    def create_installed_package(self):
        """Create installed package outside project directory."""
        print("🏗️  Creating installed package...")
        
        install_dir = self.parent_dir / "AT3GUI_installed"
        if install_dir.exists():
            shutil.rmtree(install_dir)
        install_dir.mkdir()
        
        # Create virtual environment
        venv_dir = install_dir / "venv"
        print(f"  📦 Creating virtual environment: {venv_dir}")
        subprocess.run([
            "python", "-m", "venv", str(venv_dir)
        ], check=True)
        
        # Install the package in the virtual environment
        pip_path = venv_dir / "bin" / "pip"
        python_path = venv_dir / "bin" / "python"
        
        print("  📥 Installing package in virtual environment...")
        subprocess.run([
            str(pip_path), "install", "."
        ], check=True, cwd=self.project_root)
        
        # Create launcher scripts
        self.create_installed_launchers(install_dir, python_path)
        
        # Create installed package documentation
        self.create_installed_readme(install_dir)
        
        print(f"✅ Installed package created: {install_dir}")
        return install_dir
    
    def create_installed_launchers(self, install_dir, python_path):
        """Create launcher scripts for installed package."""
        
        # Main launcher
        launcher_script = install_dir / "launch_aretomo3_gui.py"
        launcher_content = f'''#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher for Installed Package
"""

import os
import sys
import subprocess

# Set Qt environment
os.environ['QT_API'] = 'pyqt6'
os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'

# Use the virtual environment Python
python_path = "{python_path}"

try:
    # Launch the GUI
    subprocess.run([python_path, "-m", "aretomo3_gui.main"] + sys.argv[1:])
except KeyboardInterrupt:
    print("\\nApplication closed by user.")
except Exception as e:
    print(f"Error launching AreTomo3 GUI: {{e}}")
    sys.exit(1)
'''
        
        with open(launcher_script, "w") as f:
            f.write(launcher_content)
        os.chmod(launcher_script, 0o755)
        
        # Shell launcher
        shell_launcher = install_dir / "launch_aretomo3_gui.sh"
        shell_content = f'''#!/bin/bash
# AreTomo3 GUI Shell Launcher

export QT_API=pyqt6
export NAPARI_QT_BACKEND=pyqt6

{python_path} -m aretomo3_gui.main "$@"
'''
        
        with open(shell_launcher, "w") as f:
            f.write(shell_content)
        os.chmod(shell_launcher, 0o755)
        
        print("  ✅ Created launcher scripts")
    
    def create_installed_readme(self, install_dir):
        """Create README for installed package."""
        readme_content = '''# AreTomo3 GUI - Installed Package

## Overview
This is a complete installed package of AreTomo3 GUI with its own virtual environment.

## Contents
- `venv/` - Virtual environment with AreTomo3 GUI installed
- `launch_aretomo3_gui.py` - Python launcher script
- `launch_aretomo3_gui.sh` - Shell launcher script

## Usage

### Method 1: Python Launcher
```bash
python launch_aretomo3_gui.py
```

### Method 2: Shell Launcher  
```bash
./launch_aretomo3_gui.sh
```

### Method 3: Direct Virtual Environment
```bash
source venv/bin/activate
aretomo3-gui
```

## Features
- Self-contained installation
- No system-wide dependencies
- Isolated virtual environment
- Multiple launch methods
- Qt environment pre-configured

## System Requirements
- Python 3.8+
- Graphics card with OpenGL support (recommended)
- 4GB RAM minimum, 8GB recommended

## Troubleshooting
If you encounter issues:
1. Ensure Python 3.8+ is installed
2. Check that the virtual environment is intact
3. Try the different launch methods
4. Check system graphics drivers for GUI issues

## Verification
Test the installation:
```bash
source venv/bin/activate
python -c "import aretomo3_gui; print('Installation OK')"
```
'''
        
        with open(install_dir / "README.md", "w") as f:
            f.write(readme_content)
    
    def create_compressed_packages(self, minimal_dir, install_dir):
        """Create compressed packages for distribution."""
        print("📦 Creating compressed packages...")
        
        compressed_dir = Path("compressed_packages")
        if compressed_dir.exists():
            shutil.rmtree(compressed_dir)
        compressed_dir.mkdir()
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create minimal package archive
        minimal_archive = compressed_dir / f"aretomo3_gui_minimal_{timestamp}.zip"
        self.create_zip_archive(minimal_dir, minimal_archive, "Minimal Package")
        
        # Create installed package archive
        installed_archive = compressed_dir / f"aretomo3_gui_installed_{timestamp}.zip"
        self.create_zip_archive(install_dir, installed_archive, "Installed Package")
        
        # Create distribution package (wheel + docs)
        self.create_distribution_package(compressed_dir, timestamp)
        
        # Create package documentation
        self.create_compressed_readme(compressed_dir)
        
        print(f"✅ Compressed packages created: {compressed_dir}")
        return compressed_dir
    
    def create_zip_archive(self, source_dir, archive_path, package_type):
        """Create a zip archive from source directory."""
        print(f"  📦 Creating {package_type} archive: {archive_path.name}")
        
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    # Skip certain files
                    if any(skip in str(file_path) for skip in ['__pycache__', '.pyc', '.git']):
                        continue
                    
                    arcname = file_path.relative_to(source_dir.parent)
                    zipf.write(file_path, arcname)
        
        print(f"    ✅ Archive created: {archive_path}")
    
    def create_distribution_package(self, compressed_dir, timestamp):
        """Create wheel-based distribution package."""
        print("  📦 Creating distribution package...")
        
        # Build wheel
        subprocess.run(["python", "setup.py", "bdist_wheel"], 
                      capture_output=True, check=True)
        
        # Create distribution directory
        dist_dir = compressed_dir / f"aretomo3_gui_distribution_{timestamp}"
        dist_dir.mkdir()
        
        # Copy wheel
        wheel_files = list(Path("dist").glob("*.whl"))
        if wheel_files:
            shutil.copy2(wheel_files[0], dist_dir)
        
        # Copy essential files
        essential_files = [
            "aretomo3_gui_launcher.py",
            "quick_test.py", 
            "INSTALLATION_GUIDE.md",
            "requirements.txt"
        ]
        
        for file_name in essential_files:
            if Path(file_name).exists():
                shutil.copy2(file_name, dist_dir)
        
        # Create distribution archive
        dist_archive = compressed_dir / f"aretomo3_gui_distribution_{timestamp}.zip"
        self.create_zip_archive(dist_dir, dist_archive, "Distribution Package")
        
        # Clean up temporary dist directory
        shutil.rmtree(dist_dir)
    
    def create_compressed_readme(self, compressed_dir):
        """Create README for compressed packages."""
        readme_content = '''# AreTomo3 GUI - Compressed Packages

## Overview
This directory contains different package formats for AreTomo3 GUI distribution.

## Package Types

### 1. Minimal Package (`*_minimal_*.zip`)
- **Purpose**: Source code only for developers
- **Contents**: Essential source files, setup scripts, documentation
- **Use case**: Development, custom installations, building from source
- **Installation**: Extract and run `pip install .`

### 2. Installed Package (`*_installed_*.zip`)  
- **Purpose**: Complete ready-to-run installation
- **Contents**: Virtual environment with AreTomo3 GUI pre-installed
- **Use case**: End users who want immediate usage
- **Installation**: Extract and run launcher scripts

### 3. Distribution Package (`*_distribution_*.zip`)
- **Purpose**: Professional distribution with wheel package
- **Contents**: Wheel file, installation scripts, documentation
- **Use case**: System administrators, package managers
- **Installation**: Extract and run installation scripts

## Quick Start Guide

### For Developers (Minimal Package)
```bash
unzip aretomo3_gui_minimal_*.zip
cd minimal_package/
pip install .
export QT_API=pyqt6
aretomo3-gui
```

### For End Users (Installed Package)
```bash
unzip aretomo3_gui_installed_*.zip
cd AT3GUI_installed/
python launch_aretomo3_gui.py
```

### For System Installation (Distribution Package)
```bash
unzip aretomo3_gui_distribution_*.zip
cd aretomo3_gui_distribution_*/
pip install *.whl
python aretomo3_gui_launcher.py
```

## System Requirements
- Python 3.8 or higher
- 4GB RAM (8GB recommended)
- Graphics card with OpenGL support
- 2GB free disk space

## Package Verification
Each package includes verification tools to test the installation.

## Support
See individual package README files for detailed instructions and troubleshooting.
'''
        
        with open(compressed_dir / "README.md", "w") as f:
            f.write(readme_content)
    
    def verify_packages(self, minimal_dir, install_dir, compressed_dir):
        """Verify all created packages."""
        print("🧪 Verifying packages...")
        
        verification_results = {
            "minimal": self.verify_minimal_package(minimal_dir),
            "installed": self.verify_installed_package(install_dir),
            "compressed": self.verify_compressed_packages(compressed_dir)
        }
        
        # Create verification report
        self.create_verification_report(verification_results)
        
        return verification_results
    
    def verify_minimal_package(self, minimal_dir):
        """Verify minimal package."""
        print("  🔍 Verifying minimal package...")
        
        required_files = [
            "aretomo3_gui/__init__.py",
            "setup.py",
            "pyproject.toml",
            "requirements.txt",
            "README.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (minimal_dir / file_path).exists():
                missing_files.append(file_path)
        
        result = {
            "status": "PASS" if not missing_files else "FAIL",
            "missing_files": missing_files,
            "total_files": len(required_files),
            "found_files": len(required_files) - len(missing_files)
        }
        
        print(f"    ✅ Minimal package: {result['status']}")
        return result
    
    def verify_installed_package(self, install_dir):
        """Verify installed package."""
        print("  🔍 Verifying installed package...")
        
        required_items = [
            "venv/bin/python",
            "launch_aretomo3_gui.py",
            "launch_aretomo3_gui.sh",
            "README.md"
        ]
        
        missing_items = []
        for item_path in required_items:
            if not (install_dir / item_path).exists():
                missing_items.append(item_path)
        
        # Test import in virtual environment
        python_path = install_dir / "venv/bin/python"
        import_test = False
        if python_path.exists():
            try:
                result = subprocess.run([
                    str(python_path), "-c", "import aretomo3_gui; print('OK')"
                ], capture_output=True, text=True, timeout=10)
                import_test = result.returncode == 0
            except:
                pass
        
        result = {
            "status": "PASS" if not missing_items and import_test else "FAIL",
            "missing_items": missing_items,
            "import_test": import_test,
            "total_items": len(required_items),
            "found_items": len(required_items) - len(missing_items)
        }
        
        print(f"    ✅ Installed package: {result['status']}")
        return result
    
    def verify_compressed_packages(self, compressed_dir):
        """Verify compressed packages."""
        print("  🔍 Verifying compressed packages...")
        
        expected_archives = ["minimal", "installed", "distribution"]
        found_archives = []
        
        for archive_type in expected_archives:
            archives = list(compressed_dir.glob(f"*{archive_type}*.zip"))
            if archives:
                found_archives.append(archive_type)
        
        result = {
            "status": "PASS" if len(found_archives) == len(expected_archives) else "FAIL",
            "expected_archives": expected_archives,
            "found_archives": found_archives,
            "total_expected": len(expected_archives),
            "total_found": len(found_archives)
        }
        
        print(f"    ✅ Compressed packages: {result['status']}")
        return result
    
    def create_verification_report(self, results):
        """Create comprehensive verification report."""
        report_content = f'''# AreTomo3 GUI - Package Verification Report

Generated: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Verification Summary

### Minimal Package
- **Status**: {results["minimal"]["status"]}
- **Files Found**: {results["minimal"]["found_files"]}/{results["minimal"]["total_files"]}
- **Missing Files**: {results["minimal"]["missing_files"]}

### Installed Package  
- **Status**: {results["installed"]["status"]}
- **Items Found**: {results["installed"]["found_items"]}/{results["installed"]["total_items"]}
- **Import Test**: {"PASS" if results["installed"]["import_test"] else "FAIL"}
- **Missing Items**: {results["installed"]["missing_items"]}

### Compressed Packages
- **Status**: {results["compressed"]["status"]}
- **Archives Found**: {results["compressed"]["total_found"]}/{results["compressed"]["total_expected"]}
- **Expected**: {results["compressed"]["expected_archives"]}
- **Found**: {results["compressed"]["found_archives"]}

## Overall Status
{"✅ ALL PACKAGES VERIFIED SUCCESSFULLY" if all(r["status"] == "PASS" for r in results.values()) else "❌ SOME PACKAGES FAILED VERIFICATION"}

## Next Steps
1. Test minimal package installation
2. Test installed package launchers
3. Verify compressed package extraction
4. Run application tests

## Package Locations
- **Minimal Package**: `minimal_package/`
- **Installed Package**: `../AT3GUI_installed/`
- **Compressed Packages**: `compressed_packages/`
'''
        
        with open("PACKAGE_VERIFICATION_REPORT.md", "w") as f:
            f.write(report_content)
        
        print("✅ Verification report created: PACKAGE_VERIFICATION_REPORT.md")
    
    def run_complete_organization(self):
        """Run the complete organization process."""
        print("🚀 Starting Complete Directory Organization")
        print("=" * 60)
        
        try:
            # Step 1: Clean current directory
            self.clean_current_directory()
            
            # Step 2: Create minimal package
            minimal_dir = self.create_minimal_package()
            
            # Step 3: Create installed package
            install_dir = self.create_installed_package()
            
            # Step 4: Create compressed packages
            compressed_dir = self.create_compressed_packages(minimal_dir, install_dir)
            
            # Step 5: Verify all packages
            verification_results = self.verify_packages(minimal_dir, install_dir, compressed_dir)
            
            # Final summary
            print("\n" + "=" * 60)
            print("📊 Organization Complete!")
            print(f"✅ Minimal Package: {minimal_dir}")
            print(f"✅ Installed Package: {install_dir}")
            print(f"✅ Compressed Packages: {compressed_dir}")
            
            all_passed = all(r["status"] == "PASS" for r in verification_results.values())
            if all_passed:
                print("\n🎉 ALL PACKAGES VERIFIED SUCCESSFULLY!")
            else:
                print("\n⚠️  Some packages need attention - check verification report")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Organization failed: {e}")
            return False


def main():
    """Main execution function."""
    organizer = DirectoryOrganizer()
    success = organizer.run_complete_organization()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
