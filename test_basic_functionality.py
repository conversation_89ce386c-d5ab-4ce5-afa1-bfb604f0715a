#!/usr/bin/env python3
"""
Basic functionality test for AreTomo3 GUI package.
Tests core functionality without GUI components.
"""

import sys
import importlib


def test_core_imports():
    """Test core package imports without GUI."""
    print("Testing core imports...")
    
    # Test basic package import
    try:
        import aretomo3_gui
        print("✅ aretomo3_gui package imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import aretomo3_gui: {e}")
        return False
    
    # Test core modules
    core_modules = [
        "aretomo3_gui.core.config",
        "aretomo3_gui.utils.file_utils",
        "aretomo3_gui.core.processing",
    ]
    
    for module in core_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module} imported successfully")
        except ImportError as e:
            print(f"⚠️  {module} import failed: {e}")
    
    return True


def test_dependencies():
    """Test required dependencies."""
    print("\nTesting dependencies...")
    
    dependencies = [
        "numpy",
        "matplotlib", 
        "mrcfile",
        "psutil",
        "PIL",
        "scipy",
        "h5py",
        "tifffile",
        "imageio",
        "yaml",
    ]
    
    all_passed = True
    for dep in dependencies:
        try:
            importlib.import_module(dep)
            print(f"✅ {dep} available")
        except ImportError as e:
            print(f"❌ {dep} missing: {e}")
            all_passed = False
    
    return all_passed


def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from aretomo3_gui.core.config import config
        print("✅ Configuration loaded successfully")
        return True
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False


def test_file_operations():
    """Test file operation utilities."""
    print("\nTesting file operations...")
    
    try:
        from aretomo3_gui.utils import file_utils
        print("✅ File utilities available")
        return True
    except Exception as e:
        print(f"❌ File utilities failed: {e}")
        return False


def test_processing_core():
    """Test processing core functionality."""
    print("\nTesting processing core...")

    try:
        from aretomo3_gui.core import realtime_processor
        print("✅ Realtime processor available")

        from aretomo3_gui.core import tilt_series
        print("✅ Tilt series module available")

        return True
    except Exception as e:
        print(f"❌ Processing core failed: {e}")
        return False


def main():
    """Run all basic functionality tests."""
    print("🧪 AreTomo3 GUI Basic Functionality Test")
    print("=" * 50)
    
    tests = [
        test_core_imports,
        test_dependencies,
        test_configuration,
        test_file_operations,
        test_processing_core,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All basic functionality tests PASSED!")
        print("The package core functionality is working correctly.")
        print("\nNote: GUI functionality may require additional setup.")
        return 0
    else:
        print(f"\n💥 {failed} test(s) FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
