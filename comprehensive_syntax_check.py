#!/usr/bin/env python3
"""
Comprehensive syntax checker to find ALL remaining syntax errors.
"""

import ast
import os
import sys
from pathlib import Path

def check_all_python_files():
    """Check all Python files for syntax errors."""
    print("🔍 COMPREHENSIVE SYNTAX ERROR HUNT")
    print("=" * 50)
    
    project_root = Path.cwd()
    python_files = list(project_root.rglob("aretomo3_gui/**/*.py"))
    
    print(f"📁 Checking {len(python_files)} Python files...")
    print()
    
    syntax_errors = []
    checked_files = 0
    
    for py_file in sorted(python_files):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to parse the file
            ast.parse(content, filename=str(py_file))
            checked_files += 1
            print(f"✅ {py_file.relative_to(project_root)}")
            
        except SyntaxError as e:
            syntax_errors.append((py_file, e))
            print(f"❌ {py_file.relative_to(project_root)}: Line {e.lineno}: {e.msg}")
            
        except UnicodeDecodeError as e:
            syntax_errors.append((py_file, f"Encoding error: {e}"))
            print(f"❌ {py_file.relative_to(project_root)}: Encoding error")
            
        except Exception as e:
            syntax_errors.append((py_file, f"Unexpected error: {e}"))
            print(f"❌ {py_file.relative_to(project_root)}: Unexpected error: {e}")
    
    print()
    print("=" * 50)
    print("📊 COMPREHENSIVE SYNTAX CHECK RESULTS")
    print("=" * 50)
    print(f"✅ Files checked: {checked_files}")
    print(f"❌ Files with errors: {len(syntax_errors)}")
    
    if syntax_errors:
        print()
        print("🔧 FILES THAT NEED FIXING:")
        print("-" * 30)
        for file_path, error in syntax_errors:
            print(f"📄 {file_path.relative_to(project_root)}")
            if isinstance(error, SyntaxError):
                print(f"   Line {error.lineno}: {error.msg}")
                if error.text:
                    print(f"   Code: {error.text.strip()}")
            else:
                print(f"   Error: {error}")
            print()
        
        print("🔄 CONTINUE ITERATION TO FIX THESE ERRORS!")
        return False
    else:
        print()
        print("🎉 ALL FILES HAVE VALID SYNTAX!")
        print("✅ Ready for production use!")
        return True

if __name__ == "__main__":
    success = check_all_python_files()
    sys.exit(0 if success else 1)
