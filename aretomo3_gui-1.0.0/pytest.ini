[pytest]
addopts = -ra --tb=short -q --maxfail=1 -x
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
markers =
    unit: marks tests as unit tests (fast, isolated)
    integration: marks tests as integration tests (slower, system-wide)
    gui: marks tests as GUI tests (require display)
    slow: marks tests as slow running
    eer: marks tests as EER-specific tests
    utils: marks tests for utility modules
    asyncio: marks tests as async tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::pytest.PytestDeprecationWarning
    ignore::RuntimeWarning
    ignore::UserWarning
qt_api = pyqt6
qt_default_raising = true
