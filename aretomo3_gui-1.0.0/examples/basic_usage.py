#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic Usage Example for AreTomo3 GUI Professional Edition

This example demonstrates the basic usage of the AreTomo3 GUI
for tomographic reconstruction with professional best practices.

Features demonstrated:
- Application initialization
- Main window creation
- Professional error handling
- Graceful shutdown

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License
"""

import sys
import logging
from pathlib import Path
from typing import Optional

# Add the parent directory to the path to import aretomo3_gui
sys.path.insert(0, str(Path(__file__).parent.parent))

from aretomo3_gui.gui.main_window import AreTomoGUI
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_application() -> QApplication:
    """Create and configure the QApplication instance.

    Returns:
        QApplication: Configured application instance
    """
    app = QApplication(sys.argv)
    app.setApplicationName("AreTomo3 GUI Professional")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("AreTomo3 GUI Development Team")

    # Enable high DPI support (Qt6 handles this automatically)
    try:
        # Only set these attributes if they exist (Qt5 compatibility)
        if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
            app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # Qt6 handles high DPI automatically, no action needed
        pass

    return app


def create_main_window() -> Optional[AreTomoGUI]:
    """Create and configure the main window.

    Returns:
        Optional[AreTomoGUI]: Main window instance or None if creation failed
    """
    try:
        window = AreTomoGUI()
        window.setWindowTitle("AreTomo3 GUI Professional Edition")
        return window
    except Exception as e:
        logger.error(f"Failed to create main window: {e}")
        return None


def main() -> int:
    """Run the AreTomo3 GUI application.

    Returns:
        int: Exit code (0 for success, 1 for error)
    """
    logger.info("Starting AreTomo3 GUI Professional Edition")

    try:
        # Create QApplication
        app = create_application()
        logger.info("Application created successfully")

        # Create main window
        window = create_main_window()
        if window is None:
            logger.error("Failed to create main window")
            return 1

        # Show window
        window.show()
        logger.info("Main window displayed")

        # Run application event loop
        logger.info("Starting application event loop")
        exit_code = app.exec()

        logger.info(f"Application exited with code: {exit_code}")
        return exit_code

    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        logger.info("Application cleanup completed")


if __name__ == "__main__":
    sys.exit(main())
