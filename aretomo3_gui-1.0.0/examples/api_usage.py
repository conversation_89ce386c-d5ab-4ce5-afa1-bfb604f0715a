#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API Usage Example for AreTomo3 GUI Professional Edition

This example demonstrates how to use the AreTomo3 GUI API
for programmatic access to reconstruction functions with
professional error handling and logging.

Features demonstrated:
- Async API usage
- Error handling
- Status monitoring
- Project management
- Professional logging

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from aretomo3_gui.web.api_server import AreTomo3WebAPI

# Configure professional logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('api_usage_example.log')
    ]
)
logger = logging.getLogger(__name__)


class AreTomo3APIClient:
    """Professional API client for AreTomo3 GUI."""

    def __init__(self):
        """Initialize the API client."""
        self.api: Optional[AreTomo3WebAPI] = None
        logger.info("AreTomo3 API Client initialized")

    async def initialize(self) -> bool:
        """Initialize the API connection.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            self.api = AreTomo3WebAPI()
            logger.info("API connection established")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize API: {e}")
            return False

    async def get_system_status(self) -> Optional[Dict[str, Any]]:
        """Get system status information.

        Returns:
            Optional[Dict[str, Any]]: System status or None if failed
        """
        if not self.api:
            logger.error("API not initialized")
            return None

        try:
            status = await self.api.get_system_status()
            logger.info(f"System status retrieved: {status}")
            return status
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return None


async def main() -> int:
    """Run the API usage examples.

    Returns:
        int: Exit code (0 for success, 1 for error)
    """
    logger.info("Starting AreTomo3 GUI API Usage Examples")

    try:
        client = AreTomo3APIClient()

        # Initialize API
        if not await client.initialize():
            logger.error("Failed to initialize API client")
            return 1

        # Get system status
        status = await client.get_system_status()
        if status:
            logger.info(f"System is {'online' if status.get('online') else 'offline'}")

        logger.info("API examples completed successfully!")
        return 0

    except KeyboardInterrupt:
        logger.info("Examples interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error in examples: {e}")
        return 1
    finally:
        logger.info("API examples cleanup completed")


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
