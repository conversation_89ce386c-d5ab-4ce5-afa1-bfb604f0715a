#!/bin/bash
# AT3GUI Update Script
# Professional updater with backup and rollback capabilities

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
DEFAULT_INSTALL_DIR="$(pwd)"
INSTALL_DIR=""
BACKUP_DIR=""
FORCE_UPDATE=false
SKIP_BACKUP=false
SKIP_TEST=false
DRY_RUN=false
VERBOSE=false

# Show help
show_help() {
    echo "AT3GUI Update Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --install-dir DIR    Custom installation directory"
    echo "  --backup-dir DIR     Custom backup directory (default: install_dir/backup)"
    echo "  --force              Force update even if up to date"
    echo "  --skip-backup        Skip creating backup before update"
    echo "  --skip-test          Skip testing after update"
    echo "  --dry-run            Show what would be updated without making changes"
    echo "  --verbose            Enable verbose output"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Standard update"
    echo "  $0 --dry-run                        # Check what would be updated"
    echo "  $0 --force --skip-backup           # Force update without backup"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        --force)
            FORCE_UPDATE=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-test)
            SKIP_TEST=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Auto-detect installation directory
detect_install_dir() {
    if [[ -n "$INSTALL_DIR" ]]; then
        return 0
    fi
    
    # Check common locations
    local candidates=(
        "$DEFAULT_INSTALL_DIR"
        "/opt/AT3GUI"
        "$HOME/.local/share/AT3GUI"
        "$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
    )
    
    for dir in "${candidates[@]}"; do
        if [[ -d "$dir" && -f "$dir/venv/bin/activate" ]]; then
            INSTALL_DIR="$dir"
            if [[ "$VERBOSE" == true ]]; then
                print_info "Auto-detected installation: $INSTALL_DIR"
            fi
            return 0
        fi
    done
    
    return 1
}

# Check current installation
check_installation() {
    if ! detect_install_dir; then
        print_error "AT3GUI installation not found!"
        echo ""
        print_info "Please install AT3GUI first using: ./scripts/install.sh"
        exit 1
    fi
    
    # Set backup directory if not specified
    if [[ -z "$BACKUP_DIR" ]]; then
        BACKUP_DIR="$INSTALL_DIR/backup"
    fi
    
    print_success "Installation found at: $INSTALL_DIR"
    
    # Check if it's a git repository
    if [[ ! -d "$INSTALL_DIR/.git" ]]; then
        print_warning "Installation is not a git repository"
        print_info "Manual updates required - please reinstall from source"
        exit 1
    fi
}

# Check for updates
check_updates() {
    print_header "🔍 Checking for Updates"
    
    cd "$INSTALL_DIR"
    
    # Fetch latest changes
    print_info "Fetching latest changes..."
    git fetch origin
    
    # Check current branch
    local current_branch=$(git branch --show-current)
    print_info "Current branch: $current_branch"
    
    # Check current commit
    local current_commit=$(git rev-parse HEAD)
    local current_short=$(git rev-parse --short HEAD)
    print_info "Current commit: $current_short"
    
    # Check remote commit
    local remote_commit=$(git rev-parse origin/$current_branch)
    local remote_short=$(git rev-parse --short origin/$current_branch)
    print_info "Remote commit: $remote_short"
    
    # Compare commits
    if [[ "$current_commit" == "$remote_commit" ]]; then
        if [[ "$FORCE_UPDATE" == false ]]; then
            print_success "AT3GUI is already up to date!"
            if [[ "$DRY_RUN" == false ]]; then
                exit 0
            fi
        else
            print_warning "Forcing update even though already up to date"
        fi
    else
        local behind=$(git rev-list HEAD..origin/$current_branch --count)
        print_info "Updates available: $behind commits behind"
        
        # Show what's new
        if [[ "$VERBOSE" == true ]]; then
            print_info "Recent changes:"
            git log --oneline HEAD..origin/$current_branch | head -5
        fi
    fi
    
    echo ""
}

# Create backup
create_backup() {
    if [[ "$SKIP_BACKUP" == true ]]; then
        print_warning "Skipping backup creation"
        return 0
    fi
    
    print_header "💾 Creating Backup"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_info "Would create backup at: $BACKUP_DIR"
        return 0
    fi
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Create timestamped backup
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="at3gui_backup_$timestamp"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    print_info "Creating backup: $backup_name"
    
    # Backup current installation
    cp -r "$INSTALL_DIR" "$backup_path"
    
    # Remove backup's backup directory to avoid recursion
    rm -rf "$backup_path/backup"
    
    # Create a restore script
    cat > "$backup_path/restore.sh" << EOF
#!/bin/bash
# AT3GUI Backup Restore Script
# Created: $(date)

echo "Restoring AT3GUI from backup..."
echo "Target: $INSTALL_DIR"
echo "Backup: $backup_path"

read -p "Are you sure you want to restore? (y/N): " confirm
if [[ \$confirm == [yY] ]]; then
    rm -rf "$INSTALL_DIR"
    cp -r "$backup_path" "$INSTALL_DIR"
    rm -rf "$INSTALL_DIR/backup"
    echo "Restore completed!"
else
    echo "Restore cancelled."
fi
EOF
    
    chmod +x "$backup_path/restore.sh"
    
    print_success "Backup created: $backup_path"
    
    # Clean old backups (keep last 5)
    local backup_count=$(ls -1 "$BACKUP_DIR" | grep "at3gui_backup_" | wc -l)
    if [[ $backup_count -gt 5 ]]; then
        print_info "Cleaning old backups (keeping last 5)..."
        cd "$BACKUP_DIR"
        ls -1t | grep "at3gui_backup_" | tail -n +6 | xargs rm -rf
    fi
    
    echo ""
}

# Update AT3GUI
update_at3gui() {
    print_header "⬆️  Updating AT3GUI"
    
    cd "$INSTALL_DIR"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_info "Would pull latest changes from git"
        print_info "Would reinstall Python packages"
        return 0
    fi
    
    # Pull latest changes
    print_info "Pulling latest changes..."
    git pull origin $(git branch --show-current)
    
    # Activate virtual environment
    source "$INSTALL_DIR/venv/bin/activate"
    
    # Update dependencies
    print_info "Updating dependencies..."
    pip install --upgrade pip
    
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt --upgrade
    fi
    
    # Install/update AT3GUI package
    print_info "Updating AT3GUI package..."
    pip install -e . --upgrade
    
    print_success "Update completed!"
    echo ""
}

# Test updated installation
test_installation() {
    if [[ "$SKIP_TEST" == true ]]; then
        print_warning "Skipping installation testing"
        return 0
    fi
    
    print_header "🧪 Testing Updated Installation"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_info "Would test updated installation"
        return 0
    fi
    
    cd "$INSTALL_DIR"
    source "$INSTALL_DIR/venv/bin/activate"
    
    # Test import
    print_info "Testing AT3GUI import..."
    if python -c "import aretomo3_gui; print('✅ Import successful')"; then
        print_success "AT3GUI import test passed"
    else
        print_error "AT3GUI import test failed"
        return 1
    fi
    
    # Run basic tests if available
    if [[ -d "tests" ]]; then
        print_info "Running basic tests..."
        if python -m pytest tests/ -x --tb=short -q; then
            print_success "Basic tests passed"
        else
            print_warning "Some tests failed - see output above"
        fi
    fi
    
    print_success "Installation testing completed"
    echo ""
}

# Show update summary
show_summary() {
    print_header "📋 Update Summary"
    
    cd "$INSTALL_DIR"
    
    # Show version info
    local version=$(python -c "import aretomo3_gui; print(getattr(aretomo3_gui, '__version__', 'unknown'))" 2>/dev/null || echo "unknown")
    print_info "AT3GUI version: $version"
    
    local commit=$(git rev-parse --short HEAD)
    print_info "Git commit: $commit"
    
    local commit_date=$(git log -1 --format=%cd --date=short)
    print_info "Last commit: $commit_date"
    
    if [[ "$SKIP_BACKUP" == false && "$DRY_RUN" == false ]]; then
        print_info "Backup location: $BACKUP_DIR"
    fi
    
    echo ""
    print_success "Update completed successfully!"
    print_info "You can now launch AT3GUI with: ./scripts/launch.sh"
}

# Error handler
error_handler() {
    local exit_code=$?
    print_error "Update failed with exit code $exit_code"
    
    echo ""
    print_info "Troubleshooting tips:"
    print_info "1. Check if you have uncommitted changes: git status"
    print_info "2. Try updating manually: git pull"
    print_info "3. Restore from backup if available"
    print_info "4. Reinstall if necessary: ./scripts/install.sh"
    
    exit $exit_code
}

# Main update function
main() {
    # Set up error handling
    trap error_handler ERR
    
    print_header "⬆️  AT3GUI Updater"
    print_header "========================================"
    echo ""
    
    if [[ "$DRY_RUN" == true ]]; then
        print_warning "DRY RUN MODE - No changes will be made"
        echo ""
    fi
    
    # Check current installation
    check_installation
    
    # Check for updates
    check_updates
    
    # Create backup
    create_backup
    
    # Update AT3GUI
    update_at3gui
    
    # Test installation
    test_installation
    
    # Show summary
    if [[ "$DRY_RUN" == false ]]; then
        show_summary
    else
        print_info "Run without --dry-run to perform the actual update"
    fi
    
    echo ""
    print_info "Update completed at $(date)"
}

# Run main function
main "$@"
