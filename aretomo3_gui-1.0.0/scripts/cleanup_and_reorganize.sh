#!/bin/bash
# AreTomo3 GUI Directory Cleanup and Reorganization Script
# This script will clean up duplicate files, organize test files, and create a clean project structure

echo "🧹 AreTomo3 GUI Directory Cleanup and Reorganization"
echo "======================================================"

# Create backup before cleanup
BACKUP_DIR="cleanup_backup_$(date +%Y%m%d_%H%M%S)"
echo "📦 Creating backup: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Files to clean up (duplicates, temporary, and irrelevant files)
CLEANUP_FILES=(
    "bypass_test.py"
    "demo_features.py" 
    "eer_reader.py"
    "minimal_import_test.py"
    "simple_gui_test.py"
    "simple_recursive_test.py"
    "widget_import_test.py"
    "test_analysis_tab.py"
    "test_auto_features.py"
    "test_new_features.py"
    "test_recursive_completion.py"
    "test_recursive_demo.py"
    "test_recursive_functionality.py"
    "test_recursive_search.py"
    "SIMPLE_TEST.py"
)

# Directories to clean up or consolidate
CLEANUP_DIRS=(
    "backup_20250530_002620"
    "build"
    ".benchmarks"
    ".pytest_cache"
    "src/logs"
    "src/gui"  # Duplicate of src/aretomo3_gui/gui
    "src/EerReaderLib-master"  # Should be in external dependencies
    "src/aretomo3_gui.egg-info"  # Build artifact
)

# Documentation files to organize
DOC_FILES=(
    "BACKUP_INFO.txt"
    "COMPLETION_REPORT.md"
    "INTEGRATION_STATUS_REPORT.md"
    "ORGANIZATION_COMPLETE.md"
    "RECURSIVE_SEARCH_IMPLEMENTATION_COMPLETE.md"
)

echo "🗂️  Moving files to backup..."
for file in "${CLEANUP_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  Moving: $file"
        mv "$file" "$BACKUP_DIR/"
    fi
done

for dir in "${CLEANUP_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "  Moving directory: $dir"
        mv "$dir" "$BACKUP_DIR/"
    fi
done

echo "📚 Organizing documentation..."
mkdir -p "docs/reports"
for doc in "${DOC_FILES[@]}"; do
    if [ -f "$doc" ]; then
        echo "  Moving: $doc -> docs/reports/"
        mv "$doc" "docs/reports/"
    fi
done

echo "🧪 Organizing test files..."
# The tests/ directory structure looks good, keep it as is

echo "🔧 Creating organized project structure..."

# Create a clean project layout
mkdir -p {scripts/dev,scripts/deploy,config/templates,docs/{api,user,dev}}

echo "📝 Creating new project structure documentation..."

cat > "PROJECT_STRUCTURE.md" << 'EOF'
# AreTomo3 GUI Project Structure

## Directory Organization

```
AT3GUI_working/
├── src/                          # Source code
│   └── aretomo3_gui/            # Main application package
│       ├── core/                # Core functionality
│       ├── gui/                 # GUI components
│       ├── utils/               # Utilities
│       └── __init__.py
├── tests/                       # Test suite
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   ├── gui/                     # GUI tests
│   └── conftest.py
├── docs/                        # Documentation
│   ├── api/                     # API documentation
│   ├── user/                    # User guides
│   ├── dev/                     # Developer documentation
│   └── reports/                 # Status reports and completion docs
├── scripts/                     # Utility scripts
│   ├── dev/                     # Development scripts
│   └── deploy/                  # Deployment scripts
├── config/                      # Configuration files
│   └── templates/               # Configuration templates
├── tools/                       # Development tools
├── archive/                     # Archived files
├── requirements.txt             # Python dependencies
├── pyproject.toml              # Project configuration
├── pytest.ini                 # Test configuration
└── README.md                   # Main project documentation
```

## Key Files

- **src/aretomo3_gui/**: Main application source code
- **tests/**: Comprehensive test suite
- **docs/**: All documentation and reports
- **scripts/**: Development and deployment utilities
- **config/**: Configuration files and templates

## Removed During Cleanup

- Duplicate test files in root directory
- Temporary debugging scripts
- Build artifacts and cache directories
- Duplicate backup directories
- Legacy EER reader library (should be external dependency)

## Next Steps

1. Run tests to ensure cleanup didn't break anything: `pytest tests/`
2. Update imports if any files were moved
3. Review and update documentation
4. Test GUI functionality with clean environment
EOF

echo "✅ Cleanup completed!"
echo ""
echo "📊 Summary:"
echo "  - Moved $(ls -1 "$BACKUP_DIR" | wc -l) files/directories to backup"
echo "  - Organized documentation into docs/reports/"
echo "  - Created clean project structure"
echo "  - Generated PROJECT_STRUCTURE.md"
echo ""
echo "🚀 Next steps:"
echo "  1. Review the changes"
echo "  2. Test the application: cd src && python -m aretomo3_gui"
echo "  3. Run tests: pytest tests/"
echo "  4. Check PROJECT_STRUCTURE.md for the new organization"
echo ""
echo "⚠️  Backup created at: $BACKUP_DIR"
