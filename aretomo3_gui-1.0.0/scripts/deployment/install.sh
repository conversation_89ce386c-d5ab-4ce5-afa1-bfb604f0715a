#!/bin/bash
# AT3GUI Installation Script
# Professional installation with comprehensive error handling

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
DEFAULT_INSTALL_DIR="$(pwd)/build"
INSTALL_DIR="$DEFAULT_INSTALL_DIR"
SKIP_DEPS=false
SKIP_TEST=false
CREATE_SHORTCUT=true
VERBOSE=false

# Note: Backup scripts use /mnt/HDD/ak_devel/AT3GUI_backups/ as default
# This ensures backups are in parent directory of the project

# Show help
show_help() {
    echo "AT3GUI Installation Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --install-dir DIR    Custom installation directory (default: current directory/build)"
    echo "  --skip-deps          Skip system dependency installation"
    echo "  --skip-test          Skip installation testing"
    echo "  --no-shortcut        Skip desktop shortcut creation"
    echo "  --verbose            Enable verbose output"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Default installation"
    echo "  $0 --install-dir /opt/AT3GUI        # Install to /opt/AT3GUI"
    echo "  $0 --skip-deps --skip-test          # Skip deps and testing"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --skip-test)
            SKIP_TEST=true
            shift
            ;;
        --no-shortcut)
            CREATE_SHORTCUT=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Main installation function
main() {
    print_header "🚀 AT3GUI Installation"
    print_header "========================================"
    echo ""
    
    print_info "Installation directory: $INSTALL_DIR"
    print_info "Project directory: $PROJECT_DIR"
    print_info "Script directory: $SCRIPT_DIR"
    echo ""
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_DIR/pyproject.toml" ]]; then
        print_error "pyproject.toml not found. Please run this script from the AT3GUI directory."
        exit 1
    fi
    
    # Check Python version
    check_python
    
    # Install system dependencies
    if [[ "$SKIP_DEPS" == false ]]; then
        install_system_deps
    else
        print_warning "Skipping system dependency installation"
    fi
    
    # Create installation directory
    create_install_dir
    
    # Setup virtual environment
    setup_venv
    
    # Install AT3GUI
    install_at3gui
    
    # Test installation
    if [[ "$SKIP_TEST" == false ]]; then
        test_installation
    else
        print_warning "Skipping installation testing"
    fi
    
    # Create launcher scripts
    create_launchers
    
    # Create desktop shortcut
    if [[ "$CREATE_SHORTCUT" == true ]]; then
        create_desktop_shortcut
    fi
    
    # Display success message
    display_success
}

# Check Python version
check_python() {
    print_info "Checking Python version..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    if ! python3 -c "import sys; assert sys.version_info >= (3, 8)" 2>/dev/null; then
        print_error "Python 3.8 or higher is required"
        PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
        print_error "Found Python $PYTHON_VERSION"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')")
    print_success "Python $PYTHON_VERSION detected"
}

# Install system dependencies
install_system_deps() {
    print_info "Checking system dependencies..."
    
    missing_deps=()
    
    # Check for git
    if ! command -v git &> /dev/null; then
        missing_deps+=("git")
    fi
    
    # Check for pip
    if ! python3 -m pip --version &> /dev/null; then
        missing_deps+=("python3-pip")
    fi
    
    # Check for build tools
    if ! command -v gcc &> /dev/null && ! command -v clang &> /dev/null; then
        missing_deps+=("build-essential")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_warning "Missing dependencies: ${missing_deps[*]}"
        print_info "Attempting to install missing dependencies..."
        
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y "${missing_deps[@]}"
        elif command -v yum &> /dev/null; then
            sudo yum install -y "${missing_deps[@]}"
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y "${missing_deps[@]}"
        elif command -v brew &> /dev/null; then
            brew install "${missing_deps[@]}"
        else
            print_warning "Could not automatically install dependencies"
            print_warning "Please install: ${missing_deps[*]}"
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    else
        print_success "All system dependencies are available"
    fi
}

# Create installation directory
create_install_dir() {
    print_info "Creating installation directory..."
    
    # Check if we're trying to install in the current working directory
    CURRENT_DIR="$(pwd)"
    
    if [[ "$INSTALL_DIR" -ef "$CURRENT_DIR" ]]; then
        print_info "Installing in current directory: $INSTALL_DIR"
        
        # If installing in current directory, just clean up specific directories instead of removing everything
        if [[ -d "$INSTALL_DIR/venv" ]]; then
            print_warning "Virtual environment already exists. Removing and recreating..."
            rm -rf "$INSTALL_DIR/venv"
        fi
        
        if [[ -d "$INSTALL_DIR/.pytest_cache" ]]; then
            rm -rf "$INSTALL_DIR/.pytest_cache"
        fi
        
        if [[ -d "$INSTALL_DIR/__pycache__" ]]; then
            rm -rf "$INSTALL_DIR/__pycache__"
        fi
        
        print_success "Current directory prepared for installation"
    else
        # Installing in a different directory
        if [[ -d "$INSTALL_DIR" ]]; then
            print_warning "Installation directory already exists: $INSTALL_DIR"
            read -p "Remove existing installation? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rm -rf "$INSTALL_DIR"
                print_success "Removed existing installation"
            else
                print_info "Continuing with existing directory"
            fi
        fi
        
        mkdir -p "$INSTALL_DIR"
        print_success "Installation directory created: $INSTALL_DIR"
    fi
}

# Setup virtual environment
setup_venv() {
    print_info "Setting up virtual environment..."
    
    VENV_DIR="$INSTALL_DIR/venv"
    
    if [[ -d "$VENV_DIR" ]]; then
        print_warning "Virtual environment already exists. Removing and recreating..."
        rm -rf "$VENV_DIR"
    fi
    
    # Ensure we can create the virtual environment even if the install dir was just created
    cd "$INSTALL_DIR"
    
    python3 -m venv "$VENV_DIR"
    source "$VENV_DIR/bin/activate"
    
    # Upgrade pip
    print_info "Upgrading pip..."
    python -m pip install --upgrade pip
    
    print_success "Virtual environment created and activated"
}

# Install AT3GUI
install_at3gui() {
    print_info "Installing AT3GUI and dependencies..."
    
    # Copy project files (exclude build directory to avoid recursion)
    print_info "Copying project files..."
    
    # Use rsync if available, otherwise cp with exclusions
    if command -v rsync &> /dev/null; then
        rsync -av --exclude='build/' --exclude='venv/' --exclude='.git/' \
              --exclude='__pycache__/' --exclude='*.pyc' \
              --exclude='*backups*/' --exclude='backup_*/' \
              --exclude='.pytest_cache/' --exclude='*.log' \
              "$PROJECT_DIR"/ "$INSTALL_DIR"/
    else
        # Create temporary exclusion list for cp
        find "$PROJECT_DIR" -maxdepth 1 -type f -exec cp {} "$INSTALL_DIR"/ \;
        find "$PROJECT_DIR" -maxdepth 1 -type d ! -name "build" ! -name "venv" ! -name ".git" ! -name "__pycache__" ! -name "*backup*" ! -path "$PROJECT_DIR" -exec cp -r {} "$INSTALL_DIR"/ \;
    fi
    
    print_success "Project files copied successfully"
    
    # Change to installation directory
    cd "$INSTALL_DIR"
    
    # Install in editable mode
    print_info "Installing Python package..."
    if [[ "$VERBOSE" == true ]]; then
        python -m pip install -e . -v
    else
        python -m pip install -e .
    fi
    
    print_success "AT3GUI installed successfully"
}

# Test installation
test_installation() {
    print_info "Testing installation..."
    
    # Test import
    if python -c "import aretomo3_gui; print('✅ AT3GUI imports successfully')" 2>/dev/null; then
        print_success "Import test passed"
    else
        print_warning "Import test failed"
        return 1
    fi
    
    # Test basic functionality
    if python -c "
from aretomo3_gui.core.tilt_series import TiltSeries
ts = TiltSeries('test')
print('✅ Core functionality test passed')
" 2>/dev/null; then
        print_success "Core functionality test passed"
    else
        print_warning "Core functionality test failed"
    fi
    
    # Run quick tests if available
    if [[ -d "tests" ]]; then
        print_info "Running quick test suite..."
        if python -m pytest tests/unit/ --tb=no -q > /dev/null 2>&1; then
            print_success "Quick test suite passed"
        else
            print_warning "Some tests failed, but installation may still work"
        fi
    fi
}

# Create launcher scripts
create_launchers() {
    print_info "Creating launcher scripts..."
    
    # Create bash launcher
    LAUNCHER_SCRIPT="$INSTALL_DIR/launch.sh"
    cat > "$LAUNCHER_SCRIPT" << 'EOF'
#!/bin/bash
# AT3GUI Launcher Script

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$SCRIPT_DIR/venv"

# Activate virtual environment
if [[ -d "$VENV_DIR" ]]; then
    source "$VENV_DIR/bin/activate"
else
    echo "❌ Virtual environment not found: $VENV_DIR"
    exit 1
fi

# Launch AT3GUI
echo "🚀 Launching AT3GUI..."
cd "$SCRIPT_DIR"
python -m aretomo3_gui "$@"
EOF
    
    chmod +x "$LAUNCHER_SCRIPT"
    
    # Create Python launcher
    PYTHON_LAUNCHER="$INSTALL_DIR/launch.py"
    cat > "$PYTHON_LAUNCHER" << 'EOF'
#!/usr/bin/env python3
"""AT3GUI Python Launcher"""

import sys
import os
from pathlib import Path

# Add src to path
script_dir = Path(__file__).parent
src_dir = script_dir / "src"
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

# Launch AT3GUI
try:
    from aretomo3_gui.main import main
    main()
except ImportError as e:
    print(f"❌ Failed to import AT3GUI: {e}")
    print("💡 Try running the installation script first")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error launching AT3GUI: {e}")
    sys.exit(1)
EOF
    
    chmod +x "$PYTHON_LAUNCHER"
    
    print_success "Launcher scripts created"
}

# Create desktop shortcut (Linux only for now)
create_desktop_shortcut() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_info "Creating desktop shortcut..."
        
        DESKTOP_FILE="$HOME/.local/share/applications/at3gui.desktop"
        mkdir -p "$(dirname "$DESKTOP_FILE")"
        
        cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=AT3GUI
Comment=AreTomo3 Graphical User Interface
Exec=$INSTALL_DIR/launch.sh
Icon=$INSTALL_DIR/src/aretomo3_gui/gui/icons/at3gui.png
Terminal=false
Categories=Science;Education;
StartupNotify=true
EOF
        
        chmod +x "$DESKTOP_FILE"
        print_success "Desktop shortcut created"
    else
        print_info "Desktop shortcut creation not supported on this platform"
    fi
}

# Display success message
display_success() {
    echo ""
    print_header "🎉 Installation Complete!"
    print_header "========================================"
    echo ""
    print_success "AT3GUI has been successfully installed!"
    echo ""
    print_info "Installation directory: $INSTALL_DIR"
    print_info "Launch script: $INSTALL_DIR/launch.sh"
    print_info "Python launcher: $INSTALL_DIR/launch.py"
    echo ""
    print_header "🚀 Quick Start:"
    echo ""
    echo "  # Launch AT3GUI"
    echo "  $INSTALL_DIR/launch.sh"
    echo ""
    echo "  # Or use Python launcher"
    echo "  python $INSTALL_DIR/launch.py"
    echo ""
    echo "  # Run tests"
    echo "  cd $INSTALL_DIR && python -m pytest tests/ -v"
    echo ""
    print_header "📚 Documentation:"
    echo ""
    echo "  # User guide"
    echo "  cat $INSTALL_DIR/docs/USER_GUIDE.md"
    echo ""
    echo "  # Troubleshooting"
    echo "  cat $INSTALL_DIR/docs/TROUBLESHOOTING.md"
    echo ""
    print_success "Enjoy using AT3GUI!"
}

# Error handling
trap 'print_error "Installation failed on line $LINENO"; exit 1' ERR

# Run main installation
main "$@"
