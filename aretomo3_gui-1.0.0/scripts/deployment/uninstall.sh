#!/bin/bash
# AT3GUI Uninstall Script
# Professional uninstaller with confirmation and backup options

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
DEFAULT_INSTALL_DIR="$(pwd)"
INSTALL_DIR=""
FORCE_UNINSTALL=false
KEEP_DATA=false
KEEP_CONFIG=false
CREATE_BACKUP=true
DRY_RUN=false
VERBOSE=false

# Show help
show_help() {
    echo "AT3GUI Uninstall Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --install-dir DIR    Custom installation directory"
    echo "  --force              Force uninstall without confirmation"
    echo "  --keep-data          Keep user data and projects"
    echo "  --keep-config        Keep configuration files"
    echo "  --no-backup          Don't create backup before uninstall"
    echo "  --dry-run            Show what would be removed without deleting"
    echo "  --verbose            Enable verbose output"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Interactive uninstall"
    echo "  $0 --dry-run                        # Preview what will be removed"
    echo "  $0 --force --keep-data             # Uninstall but keep user data"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --force)
            FORCE_UNINSTALL=true
            shift
            ;;
        --keep-data)
            KEEP_DATA=true
            shift
            ;;
        --keep-config)
            KEEP_CONFIG=true
            shift
            ;;
        --no-backup)
            CREATE_BACKUP=false
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Auto-detect installation directory
detect_install_dir() {
    if [[ -n "$INSTALL_DIR" ]]; then
        return 0
    fi
    
    # Check common locations
    local candidates=(
        "$DEFAULT_INSTALL_DIR"
        "/opt/AT3GUI"
        "$HOME/.local/share/AT3GUI"
        "$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
    )
    
    for dir in "${candidates[@]}"; do
        if [[ -d "$dir" ]]; then
            INSTALL_DIR="$dir"
            if [[ "$VERBOSE" == true ]]; then
                print_info "Auto-detected installation: $INSTALL_DIR"
            fi
            return 0
        fi
    done
    
    return 1
}

# Check installation
check_installation() {
    if ! detect_install_dir; then
        print_warning "AT3GUI installation not found!"
        echo ""
        print_info "Checked locations:"
        print_info "• $DEFAULT_INSTALL_DIR"
        print_info "• /opt/AT3GUI"
        print_info "• $HOME/.local/share/AT3GUI"
        echo ""
        print_info "If AT3GUI is installed elsewhere, use --install-dir to specify the location"
        exit 1
    fi
    
    print_success "Installation found at: $INSTALL_DIR"
    
    # Show what will be removed
    print_header "📂 Installation Analysis"
    
    if [[ -d "$INSTALL_DIR" ]]; then
        local size=$(du -sh "$INSTALL_DIR" 2>/dev/null | cut -f1 || echo "unknown")
        print_info "Installation size: $size"
        
        if [[ "$VERBOSE" == true ]]; then
            print_info "Directory contents:"
            ls -la "$INSTALL_DIR" 2>/dev/null | head -10
            
            local file_count=$(find "$INSTALL_DIR" -type f 2>/dev/null | wc -l || echo "unknown")
            print_info "Total files: $file_count"
        fi
    fi
    
    echo ""
}

# Show uninstall plan
show_uninstall_plan() {
    print_header "🗑️  Uninstall Plan"
    
    echo "The following will be removed:"
    echo ""
    
    if [[ -d "$INSTALL_DIR" ]]; then
        print_info "• Main installation: $INSTALL_DIR"
        
        if [[ -d "$INSTALL_DIR/venv" ]]; then
            print_info "• Virtual environment: $INSTALL_DIR/venv"
        fi
        
        if [[ -d "$INSTALL_DIR/src" ]]; then
            print_info "• Source code: $INSTALL_DIR/src"
        fi
        
        if [[ -d "$INSTALL_DIR/logs" ]]; then
            if [[ "$KEEP_DATA" == true ]]; then
                print_warning "• Logs: $INSTALL_DIR/logs (WILL BE KEPT)"
            else
                print_info "• Logs: $INSTALL_DIR/logs"
            fi
        fi
        
        if [[ -d "$INSTALL_DIR/config" ]]; then
            if [[ "$KEEP_CONFIG" == true ]]; then
                print_warning "• Configuration: $INSTALL_DIR/config (WILL BE KEPT)"
            else
                print_info "• Configuration: $INSTALL_DIR/config"
            fi
        fi
        
        if [[ -d "$INSTALL_DIR/data" || -d "$INSTALL_DIR/projects" ]]; then
            if [[ "$KEEP_DATA" == true ]]; then
                print_warning "• User data (WILL BE KEPT)"
            else
                print_info "• User data and projects"
            fi
        fi
    fi
    
    # Check for launchers and shortcuts
    local launchers=()
    
    if [[ -f "$HOME/Desktop/AT3GUI.desktop" ]]; then
        launchers+=("$HOME/Desktop/AT3GUI.desktop")
    fi
    
    if [[ -f "$HOME/.local/share/applications/AT3GUI.desktop" ]]; then
        launchers+=("$HOME/.local/share/applications/AT3GUI.desktop")
    fi
    
    if [[ -f "/usr/local/bin/at3gui" ]]; then
        launchers+=("/usr/local/bin/at3gui")
    fi
    
    if [[ ${#launchers[@]} -gt 0 ]]; then
        print_info "• Launchers and shortcuts:"
        for launcher in "${launchers[@]}"; do
            print_info "  - $launcher"
        done
    fi
    
    echo ""
}

# Confirm uninstall
confirm_uninstall() {
    if [[ "$FORCE_UNINSTALL" == true ]]; then
        print_warning "Forcing uninstall without confirmation"
        return 0
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        return 0
    fi
    
    print_header "⚠️  Confirmation Required"
    echo ""
    print_warning "This will permanently remove AT3GUI from your system!"
    echo ""
    
    if [[ "$CREATE_BACKUP" == true ]]; then
        print_info "A backup will be created before removal"
    else
        print_warning "No backup will be created"
    fi
    
    echo ""
    read -p "Are you sure you want to uninstall AT3GUI? (type 'yes' to confirm): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        print_info "Uninstall cancelled"
        exit 0
    fi
    
    echo ""
}

# Create backup
create_backup() {
    if [[ "$CREATE_BACKUP" == false ]]; then
        print_warning "Skipping backup creation"
        return 0
    fi
    
    print_header "💾 Creating Backup"
    
    local backup_dir="$HOME/AT3GUI_uninstall_backup_$(date +%Y%m%d_%H%M%S)"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_info "Would create backup at: $backup_dir"
        return 0
    fi
    
    print_info "Creating backup: $backup_dir"
    
    # Copy installation
    cp -r "$INSTALL_DIR" "$backup_dir"
    
    # Create restore script
    cat > "$backup_dir/restore.sh" << EOF
#!/bin/bash
# AT3GUI Restore Script
# Created: $(date)

echo "Restoring AT3GUI from uninstall backup..."
echo "Source: $backup_dir"
echo "Target: $INSTALL_DIR"

read -p "Are you sure you want to restore AT3GUI? (y/N): " confirm
if [[ \$confirm == [yY] ]]; then
    mkdir -p "$(dirname "$INSTALL_DIR")"
    cp -r "$backup_dir" "$INSTALL_DIR"
    rm -f "$INSTALL_DIR/restore.sh"
    echo "✅ AT3GUI restored successfully!"
    echo "You may need to reinstall system dependencies"
else
    echo "Restore cancelled."
fi
EOF
    
    chmod +x "$backup_dir/restore.sh"
    
    print_success "Backup created: $backup_dir"
    print_info "To restore: $backup_dir/restore.sh"
    echo ""
}

# Remove installation
remove_installation() {
    print_header "🗑️  Removing Installation"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_info "Would remove installation directory: $INSTALL_DIR"
        return 0
    fi
    
    cd "$(dirname "$INSTALL_DIR")"
    
    # Remove main installation
    if [[ -d "$INSTALL_DIR" ]]; then
        # Handle special cases for kept data
        if [[ "$KEEP_DATA" == true || "$KEEP_CONFIG" == true ]]; then
            print_info "Selectively removing files..."
            
            # Remove specific directories
            for dir in venv src build dist __pycache__ .git; do
                if [[ -d "$INSTALL_DIR/$dir" ]]; then
                    print_info "Removing $dir..."
                    rm -rf "$INSTALL_DIR/$dir"
                fi
            done
            
            # Remove specific files
            for file in pyproject.toml setup.py requirements.txt .gitignore; do
                if [[ -f "$INSTALL_DIR/$file" ]]; then
                    print_info "Removing $file..."
                    rm -f "$INSTALL_DIR/$file"
                fi
            done
            
            if [[ "$KEEP_DATA" == false ]]; then
                for dir in logs data projects; do
                    if [[ -d "$INSTALL_DIR/$dir" ]]; then
                        print_info "Removing $dir..."
                        rm -rf "$INSTALL_DIR/$dir"
                    fi
                done
            fi
            
            if [[ "$KEEP_CONFIG" == false ]]; then
                if [[ -d "$INSTALL_DIR/config" ]]; then
                    print_info "Removing config..."
                    rm -rf "$INSTALL_DIR/config"
                fi
            fi
            
            # Check if directory is empty (except for kept items)
            local remaining=$(find "$INSTALL_DIR" -mindepth 1 -maxdepth 1 2>/dev/null | wc -l)
            if [[ $remaining -eq 0 ]]; then
                print_info "Removing empty installation directory..."
                rmdir "$INSTALL_DIR"
            else
                print_warning "Installation directory not empty (kept items remain)"
            fi
        else
            print_info "Removing entire installation directory..."
            rm -rf "$INSTALL_DIR"
        fi
        
        print_success "Installation removed"
    fi
    
    echo ""
}

# Remove launchers and shortcuts
remove_launchers() {
    print_header "🔗 Removing Launchers and Shortcuts"
    
    local launchers=(
        "$HOME/Desktop/AT3GUI.desktop"
        "$HOME/.local/share/applications/AT3GUI.desktop"
        "/usr/local/bin/at3gui"
    )
    
    for launcher in "${launchers[@]}"; do
        if [[ -f "$launcher" ]]; then
            if [[ "$DRY_RUN" == true ]]; then
                print_info "Would remove: $launcher"
            else
                print_info "Removing: $launcher"
                rm -f "$launcher" 2>/dev/null || sudo rm -f "$launcher" 2>/dev/null || true
                print_success "Removed: $launcher"
            fi
        elif [[ "$VERBOSE" == true ]]; then
            print_info "Not found: $launcher"
        fi
    done
    
    echo ""
}

# Show uninstall summary
show_summary() {
    print_header "📋 Uninstall Summary"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_info "This was a dry run - no files were actually removed"
        print_info "Run without --dry-run to perform the actual uninstall"
    else
        print_success "AT3GUI has been successfully uninstalled!"
        
        if [[ "$KEEP_DATA" == true || "$KEEP_CONFIG" == true ]]; then
            print_info "Some files were preserved as requested"
        fi
        
        if [[ "$CREATE_BACKUP" == true ]]; then
            print_info "Backup was created for recovery if needed"
        fi
    fi
    
    echo ""
    print_info "Thank you for using AT3GUI!"
}

# Main uninstall function
main() {
    print_header "🗑️  AT3GUI Uninstaller"
    print_header "========================================"
    echo ""
    
    if [[ "$DRY_RUN" == true ]]; then
        print_warning "DRY RUN MODE - No files will be removed"
        echo ""
    fi
    
    # Check current installation
    check_installation
    
    # Show what will be removed
    show_uninstall_plan
    
    # Confirm uninstall
    confirm_uninstall
    
    # Create backup
    create_backup
    
    # Remove installation
    remove_installation
    
    # Remove launchers
    remove_launchers
    
    # Show summary
    show_summary
    
    echo ""
    print_info "Uninstall completed at $(date)"
}

# Run main function
main "$@"
