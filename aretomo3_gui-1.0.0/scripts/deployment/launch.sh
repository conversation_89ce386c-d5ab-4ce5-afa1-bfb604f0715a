#!/bin/bash
# AT3GUI Launch Script
# Professional launcher with environment detection and error handling

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
DEFAULT_INSTALL_DIR="$(pwd)"
INSTALL_DIR=""
DEBUG_MODE=false
VERBOSE=false
CHECK_UPDATES=true
PROFILE=""

# Show help
show_help() {
    echo "AT3GUI Launch Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --install-dir DIR    Custom installation directory"
    echo "  --debug              Enable debug mode"
    echo "  --verbose            Enable verbose output"
    echo "  --no-update-check    Skip checking for updates"
    echo "  --profile NAME       Use specific configuration profile"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Launch with default settings"
    echo "  $0 --debug                          # Launch in debug mode"
    echo "  $0 --install-dir /opt/AT3GUI       # Launch from custom directory"
    echo "  $0 --profile production             # Use production profile"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --debug)
            DEBUG_MODE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --no-update-check)
            CHECK_UPDATES=false
            shift
            ;;
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Auto-detect installation directory
detect_install_dir() {
    if [[ -n "$INSTALL_DIR" ]]; then
        return 0
    fi
    
    # Check common locations
    local candidates=(
        "$DEFAULT_INSTALL_DIR"
        "/opt/AT3GUI"
        "$HOME/.local/share/AT3GUI"
        "$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
    )
    
    for dir in "${candidates[@]}"; do
        # Check for both venv and AT3GUI/at3gui_env patterns
        if [[ -f "$dir/venv/bin/activate" ]] || [[ -f "$dir/AT3GUI/at3gui_env/bin/activate" ]]; then
            INSTALL_DIR="$dir"
            if [[ "$VERBOSE" == true ]]; then
                print_info "Auto-detected installation: $INSTALL_DIR"
            fi
            return 0
        fi
    done
    
    return 1
}

# Check if AT3GUI is installed
check_installation() {
    if ! detect_install_dir; then
        print_error "AT3GUI installation not found!"
        echo ""
        print_info "Please check if AT3GUI is installed or specify the installation directory with --install-dir"
        print_info "You can install AT3GUI by running: ./scripts/install.sh"
        exit 1
    fi
    
    # Determine virtual environment path
    local venv_path=""
    if [[ -f "$INSTALL_DIR/venv/bin/activate" ]]; then
        venv_path="$INSTALL_DIR/venv"
    elif [[ -f "$INSTALL_DIR/AT3GUI/at3gui_env/bin/activate" ]]; then
        venv_path="$INSTALL_DIR/AT3GUI/at3gui_env"
    else
        print_error "Virtual environment not found"
        print_info "Expected at $INSTALL_DIR/venv or $INSTALL_DIR/AT3GUI/at3gui_env"
        print_info "Please reinstall AT3GUI using: ./scripts/install.sh"
        exit 1
    fi
    
    # Store the venv path for later use
    VENV_PATH="$venv_path"
    
    # Verify AT3GUI package
    if ! "$venv_path/bin/python" -c "import aretomo3_gui" 2>/dev/null; then
        print_error "AT3GUI package not found in virtual environment"
        print_info "Please reinstall AT3GUI using: ./scripts/install.sh"
        exit 1
    fi
}

# Check for updates
check_for_updates() {
    if [[ "$CHECK_UPDATES" == false ]]; then
        return 0
    fi
    
    if [[ "$VERBOSE" == true ]]; then
        print_info "Checking for updates..."
    fi
    
    # Check if we're in a git repository
    if [[ -d "$INSTALL_DIR/.git" ]]; then
        cd "$INSTALL_DIR"
        if git status --porcelain 2>/dev/null | grep -q '^'; then
            print_warning "Local changes detected - skipping update check"
        elif git fetch --dry-run 2>/dev/null; then
            local behind=$(git rev-list HEAD..origin/main --count 2>/dev/null || echo "0")
            if [[ "$behind" -gt 0 ]]; then
                print_warning "Updates available! Run 'git pull' to update."
            elif [[ "$VERBOSE" == true ]]; then
                print_success "Installation is up to date"
            fi
        fi
    fi
}

# Setup environment
setup_environment() {
    # Activate virtual environment
    source "$VENV_PATH/bin/activate"
    
    # Set environment variables
    export PYTHONPATH="$INSTALL_DIR/src:$PYTHONPATH"
    
    if [[ "$DEBUG_MODE" == true ]]; then
        export AT3GUI_DEBUG=1
        export PYTHONDONTWRITEBYTECODE=1
        print_info "Debug mode enabled"
    fi
    
    if [[ -n "$PROFILE" ]]; then
        export AT3GUI_PROFILE="$PROFILE"
        print_info "Using profile: $PROFILE"
    fi
    
    # Set up logging
    export AT3GUI_LOG_DIR="$INSTALL_DIR/logs"
    mkdir -p "$AT3GUI_LOG_DIR"
}

# Pre-flight checks
preflight_checks() {
    print_header "🔍 Pre-flight Checks"
    
    # Check Python version
    local python_version=$("$VENV_PATH/bin/python" --version 2>&1 | cut -d' ' -f2)
    print_info "Python version: $python_version"
    
    # Check AT3GUI version
    local at3gui_version=$("$VENV_PATH/bin/python" -c "import aretomo3_gui; print(getattr(aretomo3_gui, '__version__', 'unknown'))" 2>/dev/null || echo "unknown")
    print_info "AT3GUI version: $at3gui_version"
    
    # Check available memory
    if command -v free >/dev/null 2>&1; then
        local available_mem=$(free -h | awk '/^Mem:/ {print $7}' || echo "unknown")
        print_info "Available memory: $available_mem"
    fi
    
    # Check disk space
    local disk_space=$(df -h "$INSTALL_DIR" | awk 'NR==2 {print $4}' || echo "unknown")
    print_info "Available disk space: $disk_space"
    
    print_success "Pre-flight checks completed"
    echo ""
}

# Launch AT3GUI
launch_at3gui() {
    print_header "🚀 Launching AT3GUI"
    
    cd "$INSTALL_DIR"
    
    # Set launch parameters
    local launch_cmd=("$VENV_PATH/bin/python" "-m" "aretomo3_gui")
    
    if [[ "$DEBUG_MODE" == true ]]; then
        launch_cmd+=("--debug")
    fi
    
    if [[ "$VERBOSE" == true ]]; then
        launch_cmd+=("--verbose")
    fi
    
    print_info "Command: ${launch_cmd[*]}"
    print_info "Working directory: $INSTALL_DIR"
    echo ""
    
    # Launch the application
    exec "${launch_cmd[@]}"
}

# Error handler
error_handler() {
    local exit_code=$?
    print_error "Launch failed with exit code $exit_code"
    
    echo ""
    print_info "Troubleshooting tips:"
    print_info "1. Check if AT3GUI is properly installed: ./scripts/install.sh"
    print_info "2. Try launching in debug mode: $0 --debug"
    print_info "3. Check the installation: $0 --verbose"
    print_info "4. See the troubleshooting guide: docs/TROUBLESHOOTING.md"
    
    exit $exit_code
}

# Main launch function
main() {
    # Set up error handling
    trap error_handler ERR
    
    print_header "🎯 AT3GUI Launcher"
    print_header "========================================"
    echo ""
    
    # Check installation
    check_installation
    
    print_success "Installation found at: $INSTALL_DIR"
    
    # Check for updates
    check_for_updates
    
    # Setup environment
    setup_environment
    
    # Run pre-flight checks
    if [[ "$VERBOSE" == true ]]; then
        preflight_checks
    fi
    
    # Launch the application
    launch_at3gui
}

# Run main function
main "$@"
