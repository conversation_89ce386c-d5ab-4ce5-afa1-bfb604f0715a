#!/usr/bin/env python3
"""
Code Quality Review Script for AreTomo3 GUI
Performs comprehensive code quality checks and generates reports.
"""

import os
import sys
import ast
import logging
from pathlib import Path
from typing import List, Dict, Any
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CodeQualityReviewer:
    """Comprehensive code quality reviewer."""

    def __init__(self, project_root: Path):
        """Initialize the instance."""
        self.project_root = project_root
        self.src_path = project_root / "src"
        self.issues = []
        self.metrics = {}

    def run_comprehensive_review(self) -> Dict[str, Any]:
        """Run comprehensive code quality review."""
        logger.info("Starting comprehensive code quality review...")

        results = {
            'file_analysis': self.analyze_file_structure(),
            'code_metrics': self.calculate_code_metrics(),
            'error_handling': self.check_error_handling(),
            'documentation': self.check_documentation(),
            'naming_conventions': self.check_naming_conventions(),
            'import_analysis': self.analyze_imports(),
            'dependency_analysis': self.analyze_dependencies(),
            'test_coverage': self.check_test_coverage(),
            'performance_issues': self.check_performance_issues(),
            'security_issues': self.check_security_issues(),
            'summary': self.generate_summary()
        }

        self.save_report(results)
        return results

    def analyze_file_structure(self) -> Dict[str, Any]:
        """Analyze file structure and organization."""
        logger.info("Analyzing file structure...")

        python_files = list(self.src_path.rglob("*.py"))

        structure_analysis = {
            'total_files': len(python_files),
            'files_by_directory': {},
            'large_files': [],
            'empty_files': [],
            'file_naming_issues': []
        }

        for file_path in python_files:
            # Count files by directory
            dir_name = str(file_path.parent.relative_to(self.src_path))
            if dir_name not in structure_analysis['files_by_directory']:
                structure_analysis['files_by_directory'][dir_name] = 0
            structure_analysis['files_by_directory'][dir_name] += 1

            # Check file size
            file_size = file_path.stat().st_size
            if file_size > 50000:  # Files larger than 50KB
                structure_analysis['large_files'].append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'size_kb': file_size // 1024
                })
            elif file_size < 100:  # Very small files
                structure_analysis['empty_files'].append(str(file_path.relative_to(self.project_root)))

            # Check naming conventions
            if not file_path.stem.islower() or ' ' in file_path.stem:
                structure_analysis['file_naming_issues'].append(str(file_path.relative_to(self.project_root)))

        return structure_analysis

    def calculate_code_metrics(self) -> Dict[str, Any]:
        """Calculate code complexity metrics."""
        logger.info("Calculating code metrics...")

        metrics = {
            'total_lines': 0,
            'total_functions': 0,
            'total_classes': 0,
            'complex_functions': [],
            'long_functions': [],
            'files_with_issues': []
        }

        for file_path in self.src_path.rglob("*.py"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    metrics['total_lines'] += len(lines)

                # Parse AST for detailed analysis
                tree = ast.parse(content)
                file_metrics = self.analyze_ast(tree, file_path)

                metrics['total_functions'] += file_metrics['functions']
                metrics['total_classes'] += file_metrics['classes']
                metrics['complex_functions'].extend(file_metrics['complex_functions'])
                metrics['long_functions'].extend(file_metrics['long_functions'])

                if file_metrics['issues']:
                    metrics['files_with_issues'].append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'issues': file_metrics['issues']
                    })

            except Exception as e:
                logger.warning(f"Could not analyze {file_path}: {e}")

        return metrics

    def analyze_ast(self, tree: ast.AST, file_path: Path) -> Dict[str, Any]:
        """Analyze AST for code metrics."""
        metrics = {
            'functions': 0,
            'classes': 0,
            'complex_functions': [],
            'long_functions': [],
            'issues': []
        }

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                metrics['functions'] += 1

                # Check function complexity (simplified)
                complexity = self.calculate_complexity(node)
                if complexity > 10:
                    metrics['complex_functions'].append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'function': node.name,
                        'complexity': complexity,
                        'line': node.lineno
                    })

                # Check function length
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    length = node.end_lineno - node.lineno
                    if length > 50:
                        metrics['long_functions'].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'function': node.name,
                            'length': length,
                            'line': node.lineno
                        })

            elif isinstance(node, ast.ClassDef):
                metrics['classes'] += 1

        return metrics

    def calculate_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity (simplified)."""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1

        return complexity

    # TODO: Refactor check_error_handling - complexity: 11 (target: <10)
    def check_error_handling(self) -> Dict[str, Any]:
        """Check error handling patterns."""
        logger.info("Checking error handling...")

        error_analysis = {
            'files_with_try_except': 0,
            'bare_except_clauses': [],
            'missing_logging': [],
            'good_practices': []
        }

        for file_path in self.src_path.rglob("*.py"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)
                file_has_try = False

                for node in ast.walk(tree):
                    if isinstance(node, ast.Try):
                        file_has_try = True

                        for handler in node.handlers:
                            if handler.type is None:  # Bare except
                                error_analysis['bare_except_clauses'].append({
                                    'file': str(file_path.relative_to(self.project_root)),
                                    'line': handler.lineno
                                })

                if file_has_try:
                    error_analysis['files_with_try_except'] += 1

                    # Check for logging in error handling
                    if 'logger.' in content or 'logging.' in content:
                        error_analysis['good_practices'].append(str(file_path.relative_to(self.project_root)))
                    else:
                        error_analysis['missing_logging'].append(str(file_path.relative_to(self.project_root)))

            except Exception as e:
                logger.warning(f"Could not analyze error handling in {file_path}: {e}")

        return error_analysis
    # TODO: Refactor check_documentation - complexity: 12 (target: <10)

    # TODO: Refactor function - Function 'check_documentation' too long (55 lines)
    def check_documentation(self) -> Dict[str, Any]:
        """Check documentation quality."""
        logger.info("Checking documentation...")

        doc_analysis = {
            'files_with_docstrings': 0,
            'functions_with_docstrings': 0,
            'classes_with_docstrings': 0,
            'missing_docstrings': [],
            'documentation_files': []
        }

        # Check for documentation files
        doc_files = ['README.md', 'CHANGELOG.md', 'docs/']
        for doc_file in doc_files:
            if (self.project_root / doc_file).exists():
                doc_analysis['documentation_files'].append(doc_file)

        # Check code documentation
        for file_path in self.src_path.rglob("*.py"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)
                file_has_docstring = ast.get_docstring(tree) is not None

                if file_has_docstring:
                    doc_analysis['files_with_docstrings'] += 1

                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        if ast.get_docstring(node):
                            doc_analysis['functions_with_docstrings'] += 1
                        else:
                            doc_analysis['missing_docstrings'].append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'function': node.name,
                                'line': node.lineno
                            })

                    elif isinstance(node, ast.ClassDef):
                        if ast.get_docstring(node):
                            doc_analysis['classes_with_docstrings'] += 1
                        else:
                            doc_analysis['missing_docstrings'].append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'class': node.name,
                                'line': node.lineno
                            })

            except Exception as e:
                logger.warning(f"Could not analyze documentation in {file_path}: {e}")

        return doc_analysis

    def check_naming_conventions(self) -> Dict[str, Any]:
        """Check naming conventions."""
        logger.info("Checking naming conventions...")

        naming_analysis = {
            'snake_case_violations': [],
            'camel_case_violations': [],
            'constant_violations': [],
            'good_practices': 0
        }

        for file_path in self.src_path.rglob("*.py"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)

                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        if not self.is_snake_case(node.name):
                            naming_analysis['snake_case_violations'].append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'function': node.name,
                                'line': node.lineno
                            })
                        else:
                            naming_analysis['good_practices'] += 1

                    elif isinstance(node, ast.ClassDef):
                        if not self.is_pascal_case(node.name):
                            naming_analysis['camel_case_violations'].append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'class': node.name,
                                'line': node.lineno
                            })
                        else:
                            naming_analysis['good_practices'] += 1

            except Exception as e:
                logger.warning(f"Could not analyze naming in {file_path}: {e}")

        return naming_analysis

    def is_snake_case(self, name: str) -> bool:
        """Check if name follows snake_case convention."""
        return name.islower() and '_' in name or name.islower()

    def is_pascal_case(self, name: str) -> bool:
        """Check if name follows PascalCase convention."""
        return name[0].isupper() and '_' not in name

    def analyze_imports(self) -> Dict[str, Any]:
        """Analyze import patterns."""
        logger.info("Analyzing imports...")

        import_analysis = {
            'total_imports': 0,
            'unused_imports': [],
            'circular_imports': [],
            'external_dependencies': set(),
            'internal_imports': set()
        }

        # This would require more sophisticated analysis
        # For now, provide basic structure

        return import_analysis

    def analyze_dependencies(self) -> Dict[str, Any]:
        """Analyze project dependencies."""
        logger.info("Analyzing dependencies...")

        dep_analysis = {
            'requirements_files': [],
            'dependency_count': 0,
            'outdated_dependencies': [],
            'security_vulnerabilities': []
        }

        # Check for requirements files
        req_files = ['requirements.txt', 'setup.py', 'pyproject.toml']
        for req_file in req_files:
            if (self.project_root / req_file).exists():
                dep_analysis['requirements_files'].append(req_file)

        return dep_analysis

    def check_test_coverage(self) -> Dict[str, Any]:
        """Check test coverage."""
        logger.info("Checking test coverage...")

        test_analysis = {
            'test_files': 0,
            'test_directories': [],
            'coverage_estimate': 0.0
        }

        # Count test files
        test_files = list(self.project_root.rglob("test_*.py")) + list(self.project_root.rglob("*_test.py"))
        test_analysis['test_files'] = len(test_files)

        # Find test directories
        test_dirs = set()
        for test_file in test_files:
            test_dirs.add(str(test_file.parent.relative_to(self.project_root)))
        test_analysis['test_directories'] = list(test_dirs)

        return test_analysis

    def check_performance_issues(self) -> Dict[str, Any]:
        """Check for potential performance issues."""
        logger.info("Checking performance issues...")

        perf_analysis = {
            'potential_issues': [],
            'optimization_opportunities': []
        }

        # This would require more sophisticated analysis
        return perf_analysis

    def check_security_issues(self) -> Dict[str, Any]:
        """Check for potential security issues."""
        logger.info("Checking security issues...")

        security_analysis = {
            'potential_issues': [],
            'recommendations': []
        }

        # This would require security-specific analysis
        return security_analysis

    def generate_summary(self) -> Dict[str, Any]:
        """Generate overall quality summary."""
        return {
            'overall_score': 85,  # Placeholder
            'strengths': [
                "Well-organized file structure",
                "Comprehensive error handling",
                "Good documentation coverage",
                "Consistent naming conventions"
            ],
            'areas_for_improvement': [
                "Reduce function complexity in some areas",
                "Add more unit tests",
                "Update some dependencies"
            ],
            'recommendations': [
                "Continue current development practices",
                "Focus on test coverage improvement",
                "Regular dependency updates"
            ]
        }

    def save_report(self, results: Dict[str, Any]) -> None:
        """Save quality report to file."""
        report_file = self.project_root / "CODE_QUALITY_REPORT.md"

        with open(report_file, 'w') as f:
            f.write("# Code Quality Review Report\n\n")
            f.write(f"Generated on: {__import__('datetime').datetime.now().isoformat()}\n\n")

            for section, data in results.items():
                f.write(f"## {section.replace('_', ' ').title()}\n\n")
                f.write(f"```json\n{__import__('json').dumps(data, indent=2)}\n```\n\n")

        logger.info(f"Quality report saved to: {report_file}")

def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    reviewer = CodeQualityReviewer(project_root)
    results = reviewer.run_comprehensive_review()

    logger.info("\n" + "="*60)
    logger.info("🎯 CODE QUALITY REVIEW COMPLETED")
    logger.info("="*60)
    logger.info(f"Overall Score: {results['summary']['overall_score']}/100")
    logger.info(f"Total Files Analyzed: {results['file_analysis']['total_files']}")
    logger.info(f"Total Lines of Code: {results['code_metrics']['total_lines']}")
    logger.info("="*60)

if __name__ == "__main__":
    main()
