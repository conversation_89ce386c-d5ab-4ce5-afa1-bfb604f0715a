#!/usr/bin/env python3
"""
AT3GUI Deployment Validation Script
Validates that the AT3GUI deployment is ready for production
"""
import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def print_status(msg):
        """Execute print_status operation."""
        logger.info(f"✅ {msg}")

def print_error(msg):
        """Execute print_error operation."""
    logger.info(f"❌ {msg}")

def print_info(msg):
        """Execute print_info operation."""
    logger.info(f"ℹ️  {msg}")

def print_header(msg):
        """Execute print_header operation."""
    logger.info(f"\n🚀 {msg}")
    logger.info("=" * (len(msg) + 3))

def check_file_exists(filepath, description):
    """Check if a file exists"""
    if os.path.exists(filepath):
        print_status(f"{description} - Found")
        return True
    else:
        print_error(f"{description} - Missing: {filepath}")
        return False

def check_script_executable(filepath, description):
    """Check if a script is executable"""
    if os.path.exists(filepath) and os.access(filepath, os.X_OK):
        print_status(f"{description} - Executable")
        return True
    else:
        print_error(f"{description} - Not executable or missing: {filepath}")
        return False

def check_python_version():
    """Check Python version compatibility"""
    if sys.version_info >= (3, 8):
        print_status(f"Python {sys.version_info.major}.{sys.version_info.minor} - Compatible")
        return True
    else:
        print_error(f"Python {sys.version_info.major}.{sys.version_info.minor} - Requires 3.8+")
        return False

def check_dependency(name, import_name=None):
    """Check if a dependency is available"""
    if import_name is None:
        import_name = name

    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print_status(f"{name} - Available")
            return True
        else:
            print_error(f"{name} - Not available")
            return False
    except ImportError:
        print_error(f"{name} - Import error")
        return False

def validate_deployment():
    """Main validation function"""
    print_header("AT3GUI Deployment Validation")

    all_checks_passed = True

    # Check Python version
    print_info("Checking Python version...")
    if not check_python_version():
        all_checks_passed = False

    # Check essential files
    print_info("Checking essential files...")
    essential_files = [
        ("pyproject.toml", "Project configuration"),
        ("README.md", "Documentation"),
        ("setup_at3gui.sh", "Setup script"),
        ("activate_and_launch.sh", "Launch script"),
        ("src/aretomo3_gui/__init__.py", "Main package"),
        ("src/aretomo3_gui/main.py", "Main entry point"),
        ("src/aretomo3_gui/gui/main_window.py", "GUI main window"),
    ]

    for filepath, description in essential_files:
        if not check_file_exists(filepath, description):
            all_checks_passed = False

    # Check executable permissions
    print_info("Checking script permissions...")
    executable_scripts = [
        ("setup_at3gui.sh", "Setup script"),
        ("activate_and_launch.sh", "Launch script"),
    ]

    for filepath, description in executable_scripts:
        if not check_script_executable(filepath, description):
            all_checks_passed = False

    # Check if virtual environment exists
    print_info("Checking virtual environment...")
    if os.path.exists("venv"):
        print_status("Virtual environment - Found")

        # Check if AT3GUI is installed in editable mode
        try:
            result = subprocess.run(
                ["./venv/bin/pip", "list", "--editable"],
                capture_output=True, text=True, check=False
            )
            if "aretomo3-gui" in result.stdout.lower():
                print_status("AT3GUI package - Installed in editable mode")
            else:
                print_error("AT3GUI package - Not installed in editable mode")
                all_checks_passed = False
        except Exception as e:
            print_error(f"Could not check package installation: {e}")
            all_checks_passed = False
    else:
        print_error("Virtual environment - Not found")
        all_checks_passed = False

    # Check dependencies (if virtual environment is activated)
    if "VIRTUAL_ENV" in os.environ:
        print_info("Checking dependencies...")
        deps = [
            ('PyQt6', 'PyQt6.QtWidgets'),
            ('numpy', 'numpy'),
            ('matplotlib', 'matplotlib'),
            ('mrcfile', 'mrcfile'),
            ('psutil', 'psutil')
        ]

        for name, import_name in deps:
            if not check_dependency(name, import_name):
                all_checks_passed = False

        # Check AT3GUI package
        if not check_dependency("AT3GUI package", "aretomo3_gui"):
            all_checks_passed = False
    else:
        print_info("Virtual environment not activated - skipping dependency checks")
        print_info("Run 'source venv/bin/activate' to activate the environment")

    # Check sample data
    print_info("Checking sample data...")
    if check_file_exists("rec_TS_85.mrc", "Sample data file"):
        # Check file size (should be around 600MB)
        file_size = os.path.getsize("rec_TS_85.mrc") / (1024 * 1024)  # MB
        if file_size > 500:  # At least 500MB
            print_status(f"Sample data size - {file_size:.1f} MB (Valid)")
        else:
            print_error(f"Sample data size - {file_size:.1f} MB (Too small)")
            all_checks_passed = False

    # Final result
    print_header("Validation Results")
    if all_checks_passed:
        print_status("All validation checks passed!")
        print_info("AT3GUI is ready for deployment")
        print_info("To launch: ./activate_and_launch.sh")
        return True
    else:
        print_error("Some validation checks failed")
        print_info("Please review the errors above")
        return False

def main():
    """Main entry point"""
    try:
        success = validate_deployment()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n❌ Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.info(f"\n❌ Validation failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
