#!/bin/bash
# AT3GUI Installation Launcher
# Interactive installation script for AreTomo3 GUI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Function to show main menu
show_main_menu() {
    clear
    print_header "═══════════════════════════════════════════════════════════════════"
    print_header "🎯 AT3GUI Installation Launcher"
    print_header "═══════════════════════════════════════════════════════════════════"
    echo ""
    echo "Welcome to the AreTomo3 GUI installation launcher!"
    echo ""
    echo "Please select an installation option:"
    echo ""
    echo "1) 🚀 Full Installation (Recommended)"
    echo "   Complete setup with virtual environment, dependencies, and shortcuts"
    echo ""
    echo "2) 🔧 Custom Installation"
    echo "   Choose specific installation options"
    echo ""
    echo "3) 🐍 Python-only Installation"
    echo "   Install using Python script (for advanced users)"
    echo ""
    echo "4) ℹ️  System Information"
    echo "   Check system requirements and current setup"
    echo ""
    echo "5) 📖 Help & Documentation"
    echo "   View installation help and troubleshooting"
    echo ""
    echo "6) ❌ Exit"
    echo ""
}

# Function to check system requirements
check_system_info() {
    clear
    print_header "🔍 System Information"
    print_header "═══════════════════════════════════════════════════════════════════"
    echo ""
    
    # Check Python
    print_status "Checking Python installation..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')")
        if python3 -c "import sys; assert sys.version_info >= (3, 8)" 2>/dev/null; then
            print_success "Python $PYTHON_VERSION ✅"
        else
            print_warning "Python $PYTHON_VERSION (⚠️  Requires 3.8+)"
        fi
    else
        print_error "Python 3 not found ❌"
    fi
    
    # Check pip
    print_status "Checking pip..."
    if python3 -m pip --version &> /dev/null; then
        PIP_VERSION=$(python3 -m pip --version | cut -d' ' -f2)
        print_success "pip $PIP_VERSION ✅"
    else
        print_error "pip not found ❌"
    fi
    
    # Check git
    print_status "Checking git..."
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version | cut -d' ' -f3)
        print_success "git $GIT_VERSION ✅"
    else
        print_warning "git not found (optional) ⚠️"
    fi
    
    # Check system
    print_status "System information..."
    echo "  OS: $(uname -s)"
    echo "  Architecture: $(uname -m)"
    
    echo ""
    print_status "Press any key to return to main menu..."
    read -n 1 -s
}

# Function for full installation
full_installation() {
    clear
    print_header "🚀 Full Installation"
    print_header "═══════════════════════════════════════════════════════════════════"
    echo ""
    print_status "Starting full AT3GUI installation..."
    echo ""
    print_status "This will:"
    echo "  • Create a virtual environment in current directory"
    echo "  • Install all dependencies"
    echo "  • Create launcher scripts"
    echo "  • Create desktop shortcuts"
    echo "  • Test the installation"
    echo ""
    echo "Installation directory: $(pwd)"
    echo ""
    
    read -p "Continue with full installation? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        print_status "Running full installation script..."
        "$SCRIPT_DIR/setup_at3gui.sh"
        
        echo ""
        print_status "Press any key to return to main menu..."
        read -n 1 -s
    fi
}

# Function for custom installation
custom_installation() {
    clear
    print_header "🔧 Custom Installation"
    print_header "═══════════════════════════════════════════════════════════════════"
    echo ""
    
    # Get options
    INSTALL_DIR="$(pwd)"
    SKIP_DEPS=false
    SKIP_TEST=false
    NO_SHORTCUT=false
    
    echo "Customize your installation:"
    echo ""
    
    # Installation directory
    read -p "Installation directory [$INSTALL_DIR]: " custom_dir
    if [ -n "$custom_dir" ]; then
        INSTALL_DIR="$custom_dir"
    fi
    
    # Skip dependencies
    read -p "Skip system dependency check? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        SKIP_DEPS=true
    fi
    
    # Skip testing
    read -p "Skip installation testing? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        SKIP_TEST=true
    fi
    
    # Skip shortcuts
    read -p "Skip desktop shortcut creation? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        NO_SHORTCUT=true
    fi
    
    echo ""
    print_status "Installation summary:"
    echo "  Directory: $INSTALL_DIR"
    echo "  Skip deps: $SKIP_DEPS"
    echo "  Skip test: $SKIP_TEST"
    echo "  No shortcut: $NO_SHORTCUT"
    echo ""
    
    read -p "Proceed with custom installation? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        print_status "Running custom installation..."
        
        # Build command
        CMD="$SCRIPT_DIR/setup_at3gui.sh --install-dir '$INSTALL_DIR'"
        if [ "$SKIP_DEPS" = true ]; then
            CMD="$CMD --skip-deps"
        fi
        if [ "$SKIP_TEST" = true ]; then
            CMD="$CMD --skip-test"
        fi
        if [ "$NO_SHORTCUT" = true ]; then
            CMD="$CMD --no-shortcut"
        fi
        
        eval "$CMD"
        
        echo ""
        print_status "Press any key to return to main menu..."
        read -n 1 -s
    fi
}

# Function for Python installation
python_installation() {
    clear
    print_header "🐍 Python Installation"
    print_header "═══════════════════════════════════════════════════════════════════"
    echo ""
    print_status "Using Python installation script..."
    echo ""
    print_warning "Note: This method requires manual virtual environment management"
    echo ""
    
    read -p "Continue with Python installation? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        print_status "Running Python installation script..."
        python3 "$SCRIPT_DIR/install.py"
        
        echo ""
        print_status "Press any key to return to main menu..."
        read -n 1 -s
    fi
}

# Function to show help
show_help() {
    clear
    print_header "📖 Help & Documentation"
    print_header "═══════════════════════════════════════════════════════════════════"
    echo ""
    echo "AT3GUI Installation Help"
    echo ""
    echo "🚀 FULL INSTALLATION (Recommended)"
    echo "   Creates a complete, isolated installation with all dependencies."
    echo "   Best for most users."
    echo ""
    echo "🔧 CUSTOM INSTALLATION"
    echo "   Allows you to customize installation options."
    echo "   Good for users with specific requirements."
    echo ""
    echo "🐍 PYTHON INSTALLATION"
    echo "   Uses the Python script directly."
    echo "   For advanced users who want more control."
    echo ""
    echo "📋 SYSTEM REQUIREMENTS:"
    echo "   • Python 3.8 or higher"
    echo "   • pip (Python package manager)"
    echo "   • git (optional, for development)"
    echo ""
    echo "🔧 TROUBLESHOOTING:"
    echo "   • If installation fails, try custom installation with --skip-deps"
    echo "   • Make sure you have Python 3.8+ installed"
    echo "   • Check that you have write permissions to the installation directory"
    echo ""
    echo "📄 NOTE: EER file support has been removed from AT3GUI."
    echo "   See docs/EER_SUPPORT.md for more information."
    echo ""
    print_status "Press any key to return to main menu..."
    read -n 1 -s
}

# Main loop
while true; do
    show_main_menu
    read -p "Please select an option (1-6): " -n 1 -r
    echo
    
    case $REPLY in
        1)
            full_installation
            ;;
        2)
            custom_installation
            ;;
        3)
            python_installation
            ;;
        4)
            check_system_info
            ;;
        5)
            show_help
            ;;
        6)
            echo ""
            print_status "Thank you for using AT3GUI!"
            exit 0
            ;;
        *)
            echo ""
            print_error "Invalid option. Please select 1-6."
            sleep 2
            ;;
    esac
done