# AT3GUI Installation Scripts

This directory contains all installation and setup scripts for AreTomo3 GUI with integrated EER support.

## 📁 Scripts Overview

### 🎯 Main Installation Scripts

#### `install_launcher.sh` (Recommended for new users)
Interactive launcher that guides users through installation options.
```bash
./scripts/install_launcher.sh
```

**Features:**
- Interactive menu system
- Supports all installation methods
- Checks prerequisites automatically
- Provides helpful guidance

#### `setup_at3gui.sh` (Full Setup)
Complete installation with virtual environment and all dependencies.
```bash
./scripts/setup_at3gui.sh [OPTIONS]
```

**Options:**
- `--skip-deps` - Skip system dependency installation
- `--skip-test` - Skip installation testing
- `--install-dir DIR` - Custom installation directory
- `--help` - Show help message

**Features:**
- Creates isolated virtual environment
- Builds EerReaderLib from local source
- Installs all dependencies
- Creates desktop shortcuts
- Comprehensive testing

#### `install.py` (Python Installer)
Python-based installation script for more control.
```bash
python scripts/install.py [OPTIONS]
```

**Options:**
- `--no-eer` - Skip EER support installation
- `--no-test` - Skip installation testing
- `--no-shortcut` - Skip desktop shortcut creation
- `--no-venv` - Install in current environment

#### `quick_install.sh` (Developer Tool)
Fast installation for development and testing.
```bash
./scripts/quick_install.sh
```

**Features:**
- Multiple installation modes
- Development environment setup
- Editable installation option

### 🔧 Platform-Specific Scripts

#### `setup_at3gui.bat` (Windows)
Windows batch script for installation on Windows systems.
```cmd
scripts\setup_at3gui.bat
```

**Features:**
- Windows-specific dependency checking
- Visual Studio build tools support
- Creates Windows shortcuts
- Comprehensive error handling

### 🧪 Testing and Utilities

#### `test_eer_build.sh`
Tests if EerReaderLib can be built from local source.
```bash
./scripts/test_eer_build.sh
```

**Features:**
- Pre-installation testing
- Dependency verification
- Build capability check
- Detailed diagnostics

## 🚀 Quick Start Guide

### For New Users (Recommended)
```bash
cd /mnt/HDD/ak_devel/AT3Gui
./scripts/install_launcher.sh
```

### For Developers
```bash
cd /mnt/HDD/ak_devel/AT3Gui
./scripts/quick_install.sh
```

### For System Administrators
```bash
cd /mnt/HDD/ak_devel/AT3Gui
./scripts/setup_at3gui.sh --install-dir /opt/AT3GUI
```

### For Windows Users
```cmd
cd \mnt\HDD\ak_devel\AT3Gui
scripts\setup_at3gui.bat
```

## 📋 Prerequisites

### Required Software
- **Python 3.8+** - Core runtime
- **Git** - Version control and source management
- **CMake 3.12+** - For building EerReaderLib
- **C++ Compiler** - Platform-specific build tools

### Platform-Specific Requirements

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get install -y \
    python3 python3-pip python3-venv \
    git cmake build-essential \
    libhdf5-dev libtbb-dev pkg-config
```

#### Linux (CentOS/RHEL/Fedora)
```bash
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y \
    python3 python3-pip \
    git cmake \
    hdf5-devel tbb-devel pkgconf-pkg-config
```

#### macOS
```bash
# Install Xcode command line tools
xcode-select --install

# Install Homebrew packages
brew install git cmake python@3.9 hdf5 tbb pkg-config
```

#### Windows
- Git for Windows
- Visual Studio Build Tools or Visual Studio
- CMake
- Python 3.8+

## 🎯 Installation Methods Comparison

| Method | Virtual Env | EER Support | Dependencies | Best For |
|--------|-------------|-------------|--------------|----------|
| `install_launcher.sh` | ✅ User Choice | ✅ Auto | ✅ Auto | New users |
| `setup_at3gui.sh` | ✅ Yes | ✅ Auto | ✅ Auto | Production |
| `install.py` | ⚙️ Optional | ✅ Auto | ⚙️ Manual | Custom setups |
| `quick_install.sh` | ⚙️ Choice | ✅ Auto | ❌ Manual | Developers |

## 🔍 Troubleshooting

### Common Issues

#### Script Not Found
```bash
# Ensure you're in the AT3GUI root directory
cd /mnt/HDD/ak_devel/AT3Gui
ls scripts/  # Should show all scripts
```

#### Permission Denied
```bash
# Make scripts executable
chmod +x scripts/*.sh
```

#### Missing Dependencies
```bash
# Test EerReaderLib build capability
./scripts/test_eer_build.sh

# Check Python version
python3 --version  # Should be 3.8+

# Check for required tools
cmake --version
git --version
```

#### EER Support Not Available
```bash
# Check if local EerReaderLib source exists
ls src/EerReaderLib-master/

# Test building manually
cd src/EerReaderLib-master
mkdir build && cd build
cmake ..
make
```

### Getting Help

#### Check Prerequisites
```bash
./scripts/test_eer_build.sh
```

#### Verbose Installation
```bash
./scripts/setup_at3gui.sh --help
python scripts/install.py --help
```

#### View Logs
Installation logs are typically saved to:
- Linux/macOS: `~/AT3GUI/installation.log`
- Windows: `%USERPROFILE%\AT3GUI\installation.log`

## 📚 Advanced Usage

### Custom Installation Directory
```bash
./scripts/setup_at3gui.sh --install-dir /custom/path
```

### Skip System Dependencies
```bash
./scripts/setup_at3gui.sh --skip-deps
```

### Development Installation
```bash
cd /mnt/HDD/ak_devel/AT3Gui
pip install -e .[eer,dev]
```

### Test-Only Run
```bash
./scripts/test_eer_build.sh
python scripts/install.py --no-eer --no-venv
```

## 🔄 Updates and Maintenance

### Update Existing Installation
```bash
cd ~/AT3GUI/AT3Gui
git pull
pip install -e .[eer]
```

### Reinstall from Scratch
```bash
rm -rf ~/AT3GUI
cd /mnt/HDD/ak_devel/AT3Gui
./scripts/setup_at3gui.sh
```

### Clean Installation
```bash
# Remove virtual environment
rm -rf ~/AT3GUI

# Remove system installation
pip uninstall aretomo3-gui

# Remove desktop shortcuts (Linux)
rm -f ~/.local/share/applications/aretomo3-gui.desktop
```

### Testing Your Installation

After installation, verify everything works:

```bash
# Test installation completeness
../tests/scripts/test_installation.sh

# Test EER library building capability
../tests/scripts/test_eer_build.sh

# Performance benchmarks (optional)
../tests/scripts/benchmark_eer_processing.sh

# Run unit tests
python -m pytest ../tests/unit/ -v

# Run integration tests
python -m pytest ../tests/integration/ -v
```