# 🚀 AreTomo3 GUI Deployment Configuration

Production-ready deployment configurations for AreTomo3 GUI v2.0.0

---

## 📁 Directory Structure

```
deployment/
├── docker/                    # Docker containers and compose files
│   ├── Dockerfile            # Production Docker image
│   ├── docker-compose.yml    # Production compose
│   └── docker-compose.dev.yml # Development compose
├── kubernetes/               # Kubernetes deployment manifests
│   ├── namespace.yaml        # K8s namespace
│   ├── deployment.yaml       # Application deployment
│   ├── service.yaml          # Service configuration
│   └── ingress.yaml          # Ingress configuration
├── systemd/                  # SystemD service files
│   ├── aretomo3-gui.service  # Main application service
│   └── aretomo3-web.service  # Web service
├── nginx/                    # Nginx configuration files
│   ├── aretomo3-gui.conf     # Main site configuration
│   └── ssl.conf              # SSL configuration
├── ssl/                      # SSL certificate templates
│   ├── generate-cert.sh      # Certificate generation script
│   └── cert-template.conf    # Certificate template
└── scripts/                  # Deployment automation scripts
    ├── deploy.sh             # Main deployment script
    ├── backup.sh             # Backup script
    └── health-check.sh       # Health check script
```

---

## 🐳 Docker Deployment

### Quick Start (Development)
```bash
# Clone repository
git clone https://github.com/aretomo3-gui/aretomo3-gui.git
cd aretomo3-gui

# Start development environment
docker-compose -f deployment/docker/docker-compose.dev.yml up
```

### Production Deployment
```bash
# Build production image
docker build -f deployment/docker/Dockerfile -t aretomo3-gui:2.0.0 .

# Start production environment
docker-compose -f deployment/docker/docker-compose.yml up -d
```

### Environment Variables
```bash
# Required environment variables
export ARETOMO3_GUI_SECRET_KEY="your-secret-key"
export ARETOMO3_GUI_DB_URL="postgresql://user:pass@localhost/aretomo3"
export ARETOMO3_GUI_REDIS_URL="redis://localhost:6379"

# Optional configuration
export ARETOMO3_GUI_DEBUG="false"
export ARETOMO3_GUI_LOG_LEVEL="INFO"
export ARETOMO3_GUI_WORKERS="4"
```

---

## ☸️ Kubernetes Deployment

### Prerequisites
- Kubernetes cluster (v1.20+)
- kubectl configured
- Ingress controller (nginx recommended)

### Deploy to Kubernetes
```bash
# Create namespace
kubectl apply -f deployment/kubernetes/namespace.yaml

# Deploy application
kubectl apply -f deployment/kubernetes/

# Check deployment status
kubectl get pods -n aretomo3-gui
```

### Access Application
```bash
# Port forward for testing
kubectl port-forward -n aretomo3-gui svc/aretomo3-gui 8080:80

# Access at http://localhost:8080
```

---

## 🔧 SystemD Service

### Installation
```bash
# Copy service files
sudo cp deployment/systemd/*.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable and start services
sudo systemctl enable aretomo3-gui.service
sudo systemctl start aretomo3-gui.service

# Check status
sudo systemctl status aretomo3-gui.service
```

### Service Management
```bash
# Start service
sudo systemctl start aretomo3-gui

# Stop service
sudo systemctl stop aretomo3-gui

# Restart service
sudo systemctl restart aretomo3-gui

# View logs
sudo journalctl -u aretomo3-gui -f
```

---

## 🌐 Nginx Configuration

### Setup Nginx Reverse Proxy
```bash
# Copy configuration
sudo cp deployment/nginx/aretomo3-gui.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/aretomo3-gui.conf /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

### SSL Configuration
```bash
# Generate SSL certificate
cd deployment/ssl/
./generate-cert.sh yourdomain.com

# Update nginx configuration with SSL
sudo cp deployment/nginx/ssl.conf /etc/nginx/snippets/
```

---

## 🔒 SSL/TLS Setup

### Generate Self-Signed Certificate (Development)
```bash
cd deployment/ssl/
./generate-cert.sh localhost
```

### Production SSL with Let's Encrypt
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 📊 Monitoring & Health Checks

### Health Check Endpoint
```bash
# Check application health
curl http://localhost:8080/api/health

# Expected response:
{
  "status": "healthy",
  "version": "2.0.0",
  "database": "connected",
  "timestamp": "2025-05-31T20:00:00Z"
}
```

### Monitoring Setup
```bash
# Run health check script
./deployment/scripts/health-check.sh

# Setup monitoring cron job
crontab -e
# Add: */5 * * * * /path/to/deployment/scripts/health-check.sh
```

---

## 🔄 Backup & Recovery

### Automated Backup
```bash
# Run backup script
./deployment/scripts/backup.sh

# Setup automated backups
crontab -e
# Add: 0 2 * * * /path/to/deployment/scripts/backup.sh
```

### Restore from Backup
```bash
# List available backups
ls /var/backups/aretomo3-gui/

# Restore specific backup
./deployment/scripts/restore.sh backup-20250531-020000.tar.gz
```

---

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Server requirements met (CPU, RAM, Storage)
- [ ] Dependencies installed (Python 3.8+, Node.js, etc.)
- [ ] Database configured and accessible
- [ ] SSL certificates generated/obtained
- [ ] Environment variables configured
- [ ] Firewall rules configured

### Deployment
- [ ] Application deployed successfully
- [ ] Database migrations completed
- [ ] Static files served correctly
- [ ] SSL/TLS working properly
- [ ] Health checks passing
- [ ] Monitoring configured

### Post-Deployment
- [ ] Application accessible via web browser
- [ ] All features working correctly
- [ ] Performance monitoring active
- [ ] Backup system operational
- [ ] Log rotation configured
- [ ] Security scan completed

---

## 🆘 Troubleshooting

### Common Issues

**Application won't start**
```bash
# Check logs
docker logs aretomo3-gui
# or
sudo journalctl -u aretomo3-gui -f
```

**Database connection issues**
```bash
# Test database connection
python -c "from aretomo3_gui.database import test_connection; test_connection()"
```

**SSL certificate issues**
```bash
# Check certificate validity
openssl x509 -in /path/to/cert.pem -text -noout
```

### Support
- Documentation: [docs/INDEX.md](../docs/INDEX.md)
- Issues: [GitHub Issues](https://github.com/aretomo3-gui/issues)
- Email: <EMAIL>

---

*Deployment configurations maintained by the AreTomo3 GUI DevOps Team*
