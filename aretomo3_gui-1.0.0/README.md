# AreTomo3 GUI

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/aretomo3/gui)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![PyQt6](https://img.shields.io/badge/GUI-PyQt6-orange.svg)](https://www.riverbankcomputing.com/software/pyqt/)

A professional, feature-rich graphical user interface for AreTomo3 tomographic reconstruction software, designed for cryo-electron tomography workflows.

## 🚀 Features

### Core Functionality
- **Modern Interface**: Professional PyQt6-based GUI with dark/light themes
- **Real-time Processing**: Live monitoring and analysis of reconstruction jobs
- **Advanced Parameters**: Comprehensive AreTomo3 parameter configuration
- **Batch Processing**: Efficient handling of multiple tilt series
- **File Management**: Intelligent parsing of SerialEM and EER formats

### Analysis & Visualization
- **Interactive Plots**: Plotly-based visualizations with zoom and pan
- **Quality Assessment**: Automated analysis of reconstruction quality
- **CTF Visualization**: Real-time CTF estimation and correction monitoring
- **Motion Correction**: Advanced motion correction parameter tuning
- **3D Visualization**: Integrated tomogram viewing and analysis

### Professional Features
- **Web Dashboard**: Browser-based monitoring and control interface
- **Session Management**: Save and restore complete workflow sessions
- **Project Organization**: Hierarchical project and dataset management
- **Export Capabilities**: Comprehensive data export and reporting
- **Plugin System**: Extensible architecture for custom tools

## 📋 Requirements

### System Requirements
- **Operating System**: Linux, macOS, Windows
- **Python**: 3.8 or higher
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 1GB for installation, additional space for data

### Dependencies
- **PyQt6**: Modern GUI framework
- **NumPy**: Numerical computing
- **Matplotlib**: Plotting and visualization
- **MRCFile**: MRC file format support
- **Plotly**: Interactive plotting
- **Pandas**: Data analysis and manipulation

## 🔧 Installation

### Quick Installation
```bash
# Clone the repository
git clone https://github.com/aretomo3/gui.git
cd gui

# Install dependencies
pip install -r requirements.txt

# Install AreTomo3 GUI
pip install -e .
```

### Development Installation
```bash
# Clone with development dependencies
git clone https://github.com/aretomo3/gui.git
cd gui

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode
pip install -e ".[dev]"
```

### Verification
```bash
# Validate installation
python tools/validate.py --full --verbose

# Run tests
pytest tests/
```

## 🚀 Usage

### Command Line Interface
```bash
# Basic usage
aretomo3-gui

# With specific options
aretomo3-gui --theme dark --debug

# Open specific files
aretomo3-gui data/tilt_series.mrc

# Show help
aretomo3-gui --help
```

### Python API
```python
from aretomo3_gui import AreTomoGUI
from PyQt6.QtWidgets import QApplication

app = QApplication([])
gui = AreTomoGUI()
gui.show()
app.exec()
```

### Web Interface
```bash
# Start web dashboard
aretomo3-gui --web-mode --port 8080

# Access at http://localhost:8080
```

## 📚 Documentation

### User Documentation
- [**Quick Start Guide**](docs/user/quick_start.md) - Get started in 5 minutes
- [**User Manual**](docs/user/manual.md) - Comprehensive usage guide
- [**Tutorials**](docs/user/tutorials/) - Step-by-step workflows
- [**FAQ**](docs/user/faq.md) - Frequently asked questions

### Developer Documentation
- [**API Reference**](docs/api/) - Complete API documentation
- [**Development Guide**](docs/developer/guide.md) - Contributing guidelines
- [**Architecture**](docs/developer/architecture.md) - System design overview
- [**Plugin Development**](docs/developer/plugins.md) - Creating custom plugins

### Deployment
- [**Installation Guide**](docs/deployment/installation.md) - Detailed setup instructions
- [**Configuration**](docs/deployment/configuration.md) - System configuration
- [**Docker Deployment**](docs/deployment/docker.md) - Containerized deployment
- [**Production Setup**](docs/deployment/production.md) - Production environment setup

## 🛠️ Development

### Project Structure
```
aretomo3_gui/
├── bin/                    # Executable scripts
├── aretomo3_gui/          # Main package
│   ├── gui/               # GUI components
│   ├── core/              # Core functionality
│   ├── analysis/          # Analysis modules
│   ├── web/               # Web interface
│   └── utils/             # Utility functions
├── docs/                  # Documentation
├── tests/                 # Test suite
├── tools/                 # Development tools
├── config/                # Configuration files
├── deployment/            # Deployment configurations
└── examples/              # Example scripts
```

### Quality Assurance
```bash
# Run validation suite
python tools/validate.py --full

# Create backup
python tools/backup.py --verbose

# Run tests
pytest tests/ --cov=aretomo3_gui

# Code formatting
black aretomo3_gui/
isort aretomo3_gui/
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the validation suite
6. Submit a pull request

### Code Standards
- **PEP 8**: Python code style
- **Type Hints**: Use type annotations
- **Documentation**: Comprehensive docstrings
- **Testing**: Unit tests for all features
- **Quality**: 100% validation suite pass rate

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **AreTomo3 Team**: For the excellent reconstruction software
- **PyQt6 Community**: For the robust GUI framework
- **Scientific Python Community**: For the foundational libraries
- **Contributors**: All developers who have contributed to this project

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/aretomo3/gui/issues)
- **Discussions**: [GitHub Discussions](https://github.com/aretomo3/gui/discussions)
- **Email**: <EMAIL>

## 🔄 Version History

- **v2.0.0** (2025-01-01): Major release with web interface and advanced analysis
- **v1.1.0** (2024-12-01): Enhanced visualization and batch processing
- **v1.0.0** (2024-11-01): Initial stable release

---

**AreTomo3 GUI** - Professional tomographic reconstruction interface for the modern era.
