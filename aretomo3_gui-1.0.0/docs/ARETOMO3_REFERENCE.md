# AreTomo3 Complete Reference Documentation

## 📋 **Overview**
AreTomo3 is a multi-GPU accelerated software package for real-time automated cryoET tomogram reconstruction that integrates MotionCor3, AreTomo2, and GCtfFind in a single autonomous preprocessing pipeline.

**Performance:** Handles 9-target PACE Tomo data collection with 4 RTX A6000 GPUs, enabling concurrent tomogram denoising and particle picking.

## 🔧 **Core Capabilities**
- **Real-time processing:** Faster than data collection, allows GPU resource sharing
- **Autonomous pipeline:** Motion correction → tilt series assembly → CTF estimation/correction → tomographic alignment → 3D reconstruction
- **No human intervention:** Activates automatically when new tilt series collected
- **Comprehensive output:** Tomograms + rich alignment parameters for subtomogram averaging

## 📁 **File Format Support**

### **Input Files (v1.0.23+)**
| Format | Mode | Requirement | Notes |
|--------|------|-------------|-------|
| MDOC | Live processing | Required | Contains multiple ZValue sections |
| MRC/MRCS/ST | Batch processing | No MDOC needed | Requires .rawtlt or _TLT.txt angle files |
| EER movies | Both | Gain reference | Use -FmInt 15 (default) |
| TIFF movies | Both | Gain reference | Use -FmInt 1-2 |

### **Required MDOC Fields**
- **ZValue:** Section identifier
- **TiltAngle:** Tilt angle for each image
- **ExposureDose:** Dose per tilt
- **SubFramePath:** Movie file path (directory ignored)

### **Tilt Angle Files (.rawtlt or _TLT.txt)**
- **Column 1:** Tilt angles (mandatory, same order as tilt series)
- **Column 2:** Acquisition order (optional, 1-based indices)
- **Column 3:** Image dose (optional)

## 🎮 **Command Entry Points**

| -Cmd | Operation | Description |
|------|-----------|-------------|
| 0 | Full processing | Movies → tomograms (default) |
| 1 | Alignment only | Start from tomographic alignment |
| 2 | Reconstruction only | Repeat only tomographic reconstruction |
| 3 | CTF estimation only | Repeat only CTF estimation |
| 4 | Axis rotation | Rotate tilt axis 180° and reconstruct |

## ⚙️ **Essential Parameters**

### **Input/Output**
```bash
-InPrefix <path>        # Directory path (batch) or full filename (single)
-Insuffix <ext>         # File extension (.mdoc, .mrc, .mrcs, .st)
-InSkips <pattern>      # Skip files containing pattern (e.g., _override)
-OutDir <path>          # Output directory
-Serial <seconds>       # Wait time for new tilt series (1=batch, 0=single)
```

### **Microscope Parameters**
```bash
-PixSize <float>        # Movie pixel size (Å)
-Kv <int>              # Acceleration voltage (default: 300 kV)
-Cs <float>            # Spherical aberration (default: 2.7 mm)
-AmpContrast <float>   # Amplitude contrast (default: 0.07)
```

### **Motion Correction**
```bash
-Gain <file>           # Gain reference (.gain or .mrc)
-FmInt <int>           # Raw frames per rendered frame (15=EER, 1-2=TIFF)
-Group <int> <int>     # Frame grouping (global, local) - default: 1 4
-McBin <float>         # Motion correction binning (default: 1)
-McPatch <int> <int>   # Local motion correction patches (0 0=global only)
-EerSampling <int>     # EER super-resolution (1=normal, 2=super-res)
-FlipGain <0|1>        # Flip gain reference
```

### **Tomographic Alignment**
```bash
-AlignZ <int>          # Sample thickness (pixels, 0=auto-estimate)
-VolZ <int>            # Volume Z-dimension (-1=auto with -ExtZ padding)
-ExtZ <int>            # Extra Z-space above/below sample (default: 300)
-AtBin <float> [<float> <float>]  # Binning factors (1st required, 2nd/3rd optional)
-AtPatch <int> <int>   # Local alignment patches (0 0=global only)
-TiltAxis <float>      # Tilt axis angle (degrees, relative to y-axis)
```

### **Reconstruction Options**
```bash
-Wbp <0|1>            # Weighted back projection (1=WBP, 0=SART)
-Sart <int> <int>     # SART iterations and subset size
-FlipVol <0|1>        # Rotate volume around x-axis (changes slice orientation)
-SplitSum <0|1>       # Generate odd/even tilt series and tomograms
```

### **CTF Processing**
```bash
-CorrCTF <0|1> [<float>]  # Local CTF correction and lowpass filter
-ExtPhase <float> <float> # Phase plate: phase shift and search range (degrees)
```

### **Output Control**
```bash
-OutXF <0|1>          # Generate transformation files
-OutImod <0|1>        # Generate IMOD-compatible files for Relion
-DarkTol <float>      # Dark image detection tolerance (default: 0.7)
-TiltCor <-1|0|1>     # Tilt angle correction (-1=disable, 0=measure, 1=apply)
```

### **GPU & Performance**
```bash
-Gpu <ids>            # GPU IDs (space-separated: "0 1 2 3")
-TmpDir <path>        # Temporary directory
```

## 🧭 **Coordinate System**
- **Right-hand coordinate system** with positive defocus handedness
- **Z-axis:** Points to electron source
- **Tilt axis:** Measured relative to y-axis
- **Defocus:** Moving sample closer to source = less underfocused

## 📊 **Output Files**

### **Tilt Series**
- `Position_5_10.mrc` - Full tilt series
- `Position_5_10_ODD.mrc` - Odd frames
- `Position_5_10_EVN.mrc` - Even frames

### **Tomograms**
- `Position_5_10_Vol.mrc` - Full tomogram
- `Position_5_10_ODD_Vol.mrc` - Odd tomogram (for DenoisET training)
- `Position_5_10_EVN_Vol.mrc` - Even tomogram (for DenoisET training)
- `Position_5_10_2ND_Vol.mrc` - Second binning (if specified)
- `Position_5_10_3RD_Vol.mrc` - Third binning (if specified)

### **Metadata Files**
- `Position_5_10_TLT.txt` - Tilt angles and acquisition order
- `Position_5_10_CTF.txt` - CTF parameters (CTFFind4 format)
- `Position_5_10_CTF_Imod.txt` - CTF parameters (IMOD format)
- `Position_5_10.aln` - Alignment parameters (global and local)

## 🔍 **Alignment File (.aln) Structure**

### **Header Section (lines start with #)**
```
# RawSize: Image width, height, number of images
# NumPatch: Number of patches for local alignment
# DarkFrame: Excluded dark images (index, reserved, tilt angle)
# AlphaOffset: Detected tilt angle offset
# BetaOffset: Reserved for future use
```

### **Global Alignment Table (5 columns)**
| SEC | ROT | TX | TY | TILT |
|-----|-----|----|----|------|
| Image index | Tilt axis angle | X translation | Y translation | Corrected tilt angle |

### **Local Alignment Table (7 columns)**
| Col 1 | Col 2 | Col 3 | Col 4 | Col 5 | Col 6 | Col 7 |
|-------|-------|-------|-------|-------|-------|-------|
| Image index | Patch index | X coord | Y coord | X translation | Y translation | Reliability (1=good) |

## 🛠️ **Advanced Tools**

### **remap3D - Particle Coordinate Mapping**
Maps 3D particle coordinates between tomogram sets from same tilt series after re-alignment.

**Dependencies:** pandas, numpy, starfile

**Usage:**
```bash
python remap3D.py \
    -ovs 4096 4096 1200 \      # Old volume XYZ sizes
    -nvs 4096 4096 1200 \      # New volume XYZ sizes  
    -ops 1.54 \                # Old pixel size (Å)
    -nps 1.54 \                # New pixel size (Å)
    -os old.star \             # Input star file
    -ns new.star \             # Output star file
    -oa old_alns/ \            # Old .aln files directory
    -na new_alns/ \            # New .aln files directory
    -oap Position_             # .aln file prefix
```

## 📝 **Processing Examples**

### **Live Processing**
```bash
AreTomo3 -InPrefix ./Position -Insuffix .mdoc -InSkips _override \
    -OutDir ./run001/ -Gain ./gainref.gain -FmInt 15 -Group 2 4 \
    -PixSize 1.54 -McPatch 5 5 -EerSampling 2 -McBin 2 \
    -AtPatch 4 4 -AtBin 4 4 6 -Wbp 1 -FlipVol 1 -TiltCor 1 \
    -AlignZ 600 -VolZ 1200 -Gpu 0 1 -Cmd 0 -Serial 1000
```

### **Batch Processing (MRC files)**
```bash
AreTomo3 -InPrefix ./Position -Insuffix .mrc -InSkips override \
    -OutDir ./output/ -PixSize 1.54 -AtPatch 4 4 -AtBin 4 4 6 \
    -Wbp 1 -FlipVol 1 -TiltCor 1 -AlignZ 600 -VolZ 1200 \
    -Gpu 0 1 -Serial 1
```

### **Single Processing**
```bash
AreTomo3 -InPrefix ./Position.mrc -OutDir ./run001/ \
    -PixSize 1.54 -AtPatch 4 4 -AtBin 4 4 6 -Wbp 1 \
    -FlipVol 1 -TiltCor 1 -AlignZ 600 -VolZ 1200 -Gpu 0
```

## ⚠️ **Important Notes**

### **Parameter Interactions**
- **Pixel size calculation:** Output = Input × McBin × AtBin
- **EER super-resolution:** Use -EerSampling 2 + -McBin 2 together
- **Group sizes:** Frames ÷ group size ≥ 3
- **TiltCor warning:** -TiltCor 1 changes particle Z-coordinates (avoid for subtomogram averaging)

### **File Organization**
- **MDOC + movies:** Must be in same flat directory
- **Tilt angle files:** Must have same basename as tilt series
- **Gain reference:** Can be .gain or .mrc format
- **Output naming:** Follows consistent pattern with suffixes

### **Performance Optimization**
- **Multi-GPU:** Space-separated GPU IDs
- **Memory management:** Use -TmpDir for large datasets
- **Real-time capability:** Runs faster than data collection
- **Concurrent processing:** Supports denoising and particle picking
