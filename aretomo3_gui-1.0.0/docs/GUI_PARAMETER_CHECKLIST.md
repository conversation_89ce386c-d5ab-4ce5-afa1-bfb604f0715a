# AreTomo3 GUI Parameter Implementation Checklist

## 📋 **Parameter Coverage Analysis**

### ✅ **Currently Implemented Parameters**

#### **General Parameters Tab**
- [x] `-PixSize` → `pix_size` (SpinBox)
- [x] `-Kv` → `kv` (SpinBox) 
- [x] `-Cs` → `cs` (SpinBox)
- [x] `-AmpContrast` → `amp_contrast` (SpinBox)
- [x] `-TiltAxis` → `tilt_axis` (SpinBox)
- [x] `-FmDose` → `fm_dose` (SpinBox)
- [x] `-VolZ` → `vol_z` (SpinBox)
- [x] `-AtBin` → `at_bin` (SpinBox)
- [x] `-SplitSum` → `split_sum` (CheckBox)
- [x] `-FlipVol` → `flip_vol` (CheckBox)
- [x] `-OutImod` → `out_imod` (ComboBox)
- [x] `-OutXF` → `out_xf` (CheckBox)

#### **Advanced Parameters Tab**
- [x] `-McBin` → `mc_bin` (SpinBox)
- [x] `-FmInt` → `fm_int` (SpinBox)
- [x] `-DarkTol` → `dark_tol` (SpinBox)
- [x] `-AlignZ` → `align_z` (SpinBox)
- [x] `-TiltCor` → `tilt_cor` (ComboBox)
- [x] `-Wbp` → `wbp` (CheckBox)
- [x] `-CorrCTF` → `corr_ctf` (CheckBox)
- [x] `-Gpu` → `gpu` (LineEdit)

### ❌ **Missing Critical Parameters**

#### **🚨 High Priority - Essential for Functionality**
- [ ] `-Insuffix` - File extension specification
- [ ] `-InSkips` - Skip files with pattern
- [ ] `-Serial` - Processing mode control (batch vs single)
- [ ] `-Gain` - Gain reference file path
- [ ] `-Group` - Frame grouping for motion measurement
- [ ] `-McPatch` - Local motion correction patches
- [ ] `-AtPatch` - Local alignment patches
- [ ] `-EerSampling` - EER super-resolution sampling
- [ ] `-FlipGain` - Gain reference flipping
- [ ] `-ExtZ` - Extra Z-space padding
- [ ] `-Cmd` - Entry point selection

#### **⚠️ Medium Priority - Important for Advanced Use**
- [ ] `-Sart` - SART reconstruction parameters
- [ ] `-ExtPhase` - Phase plate parameters
- [ ] `-DefectFile` - Defect file specification
- [ ] `-TmpDir` - Temporary directory
- [ ] `-AtBin` second/third values - Multi-resolution tomograms
- [ ] `-CorrCTF` lowpass parameter - CTF correction filter

#### **💡 Low Priority - Nice to Have**
- [ ] `-InvGain` - Invert gain reference
- [ ] `-RotGain` - Rotate gain reference
- [ ] `-Mag` - Magnification correction
- [ ] `-InFmMotion` - Input frame motion
- [ ] `-TiffOrder` - TIFF byte order
- [ ] `-McIter` - Motion correction iterations
- [ ] `-McTol` - Motion correction tolerance
- [ ] `-FmRef` - Reference frame
- [ ] `-TotalDose` - Total dose specification
- [ ] `-ReconRange` - Reconstruction tilt range
- [ ] `-IntpCor` - Interpolation correction
- [ ] `-CropVol` - Volume cropping
- [ ] `-Resume` - Resume processing

## 🔧 **Implementation Plan**

### **Phase 1: Critical Missing Parameters**
1. **File Handling Parameters**
   ```python
   # Add to General Parameters Tab
   self.insuffix = QLineEdit(".mdoc")  # File extension
   self.inskips = QLineEdit("_override")  # Skip pattern
   self.gain_file = QLineEdit()  # Gain reference path
   self.gain_browse = QPushButton("Browse")
   ```

2. **Processing Mode Control**
   ```python
   # Add to General Parameters Tab
   self.serial = QSpinBox()  # 0=single, 1=batch, >1=live wait time
   self.cmd = QComboBox()  # Entry point selection
   # Items: "0 - Full processing", "1 - Alignment only", etc.
   ```

3. **Motion Correction Enhancement**
   ```python
   # Add to Advanced Parameters Tab
   self.group_global = QSpinBox()  # Global frame grouping
   self.group_local = QSpinBox()   # Local frame grouping
   self.mc_patch_x = QSpinBox()    # Motion correction patches X
   self.mc_patch_y = QSpinBox()    # Motion correction patches Y
   self.eer_sampling = QSpinBox()  # EER sampling factor
   self.flip_gain = QCheckBox()    # Flip gain reference
   ```

4. **Alignment Enhancement**
   ```python
   # Add to Advanced Parameters Tab
   self.at_patch_x = QSpinBox()    # Alignment patches X
   self.at_patch_y = QSpinBox()    # Alignment patches Y
   self.ext_z = QSpinBox()         # Extra Z padding
   ```

### **Phase 2: Advanced Parameters**
1. **SART Reconstruction**
   ```python
   self.sart_iterations = QSpinBox()
   self.sart_subset = QSpinBox()
   ```

2. **Phase Plate Support**
   ```python
   self.ext_phase_shift = QDoubleSpinBox()
   self.ext_phase_range = QDoubleSpinBox()
   ```

3. **Multi-Resolution Tomograms**
   ```python
   self.at_bin_2nd = QDoubleSpinBox()  # Optional 2nd binning
   self.at_bin_3rd = QDoubleSpinBox()  # Optional 3rd binning
   ```

### **Phase 3: File Management**
1. **Advanced File Handling**
   ```python
   self.defect_file = QLineEdit()
   self.tmp_dir = QLineEdit()
   ```

2. **Gain Reference Options**
   ```python
   self.inv_gain = QCheckBox()
   self.rot_gain = QSpinBox()  # Rotation angle
   ```

## 📊 **Parameter Organization Strategy**

### **General Parameters Tab Layout**
```
┌─ Microscope Settings ─────────────────┐
│ Pixel Size, Voltage, Cs, Amp Contrast │
│ Tilt Axis, Frame Dose                 │
└────────────────────────────────────────┘
┌─ File Handling ───────────────────────┐
│ Input Suffix, Skip Pattern            │
│ Gain Reference (with Browse button)   │
└────────────────────────────────────────┘
┌─ Processing Mode ─────────────────────┐
│ Command Entry Point, Serial Mode      │
│ Volume Z, Binning                     │
└────────────────────────────────────────┘
┌─ Basic Options ───────────────────────┐
│ Split Sum, Flip Volume, Output Files  │
└────────────────────────────────────────┘
```

### **Advanced Parameters Tab Layout**
```
┌─ Motion Correction ───────────────────┐
│ McBin, FmInt, Group Settings          │
│ McPatch, EER Sampling, Flip Gain      │
└────────────────────────────────────────┘
┌─ Alignment & Reconstruction ──────────┐
│ AlignZ, AtPatch, ExtZ                 │
│ TiltCor, Wbp, SART Settings           │
└────────────────────────────────────────┘
┌─ CTF & Advanced ──────────────────────┐
│ CTF Correction, Phase Plate           │
│ Dark Tolerance, GPU Settings          │
└────────────────────────────────────────┘
┌─ File Management ─────────────────────┐
│ Defect File, Temporary Directory      │
│ Multi-resolution Settings             │
└────────────────────────────────────────┘
```

## 🎯 **Implementation Priority**

### **Immediate (Week 1)**
1. Add missing file handling parameters (`-Insuffix`, `-InSkips`, `-Gain`)
2. Implement processing mode control (`-Serial`, `-Cmd`)
3. Add motion correction patches (`-McPatch`, `-Group`)

### **Short-term (Week 2)**
1. Add alignment patches (`-AtPatch`)
2. Implement EER super-resolution (`-EerSampling`)
3. Add volume padding (`-ExtZ`)

### **Medium-term (Week 3-4)**
1. SART reconstruction parameters
2. Phase plate support
3. Multi-resolution tomogram generation

### **Long-term (Month 2)**
1. Advanced file management options
2. Gain reference manipulation
3. Performance optimization parameters

## 🔍 **Validation Requirements**

### **Parameter Validation Rules**
- **Group sizes:** Ensure frames ÷ group size ≥ 3
- **Patch numbers:** Must be positive integers
- **EER + McBin:** Recommend EerSampling=2 with McBin=2
- **File paths:** Validate existence for gain/defect files
- **GPU IDs:** Space-separated format validation

### **Command Generation Updates**
- Update `_build_command()` to include all new parameters
- Implement conditional parameter inclusion based on settings
- Add parameter validation before command execution
- Ensure proper parameter formatting (spaces vs commas)

## 📝 **Documentation Updates Needed**
1. Update parameter help tooltips with AreTomo3 documentation
2. Add parameter interaction warnings (e.g., TiltCor + subtomogram averaging)
3. Create parameter preset configurations for common use cases
4. Add validation error messages with helpful suggestions
