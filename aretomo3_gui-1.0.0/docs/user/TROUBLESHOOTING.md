# 🔧 AT3GUI Troubleshooting Guide

This comprehensive troubleshooting guide helps you diagnose and resolve common issues with AT3GUI installation, configuration, and operation.

## 🎯 Table of Contents

- [Quick Diagnostics](#-quick-diagnostics)
- [Installation Issues](#-installation-issues)
- [Launch Problems](#-launch-problems)
- [Processing Errors](#-processing-errors)
- [Performance Issues](#-performance-issues)
- [GUI Problems](#-gui-problems)
- [File and Data Issues](#-file-and-data-issues)
- [Platform-Specific Issues](#-platform-specific-issues)
- [Advanced Diagnostics](#-advanced-diagnostics)
- [Getting Help](#-getting-help)

## 🚀 Quick Diagnostics

### System Check Script

Run the built-in diagnostics to quickly identify common issues:

```bash
# Navigate to AT3GUI directory
cd /mnt/HDD/ak_devel/AT3Gui

# Run comprehensive diagnostics
./scripts/launch.sh --verbose

# Check installation status
./scripts/cleanup.sh --dry-run

# Verify dependencies
python -c "import aretomo3_gui; print('✅ AT3GUI import successful')"
```

### Common Quick Fixes

1. **Clear temporary files**: `./scripts/cleanup.sh`
2. **Reinstall dependencies**: `./scripts/update.sh`
3. **Reset configuration**: Remove `~/.config/at3gui/`
4. **Check permissions**: `chmod +x scripts/*.sh`

## 🛠️ Installation Issues

### Installation Script Fails

#### Error: "pyproject.toml not found"
```bash
# Cause: Running script from wrong directory
# Solution: Navigate to correct directory
cd /mnt/HDD/ak_devel/AT3Gui
./scripts/install.sh
```

#### Error: "Python version not supported"
```bash
# Check Python version
python --version

# Install Python 3.8+ if needed (Ubuntu/Debian)
sudo apt update
sudo apt install python3.10 python3.10-venv python3.10-dev

# Use specific Python version
python3.10 -m venv venv
source venv/bin/activate
```

#### Error: "Permission denied"
```bash
# Make scripts executable
chmod +x scripts/*.sh

# If still failing, check ownership
ls -la scripts/
sudo chown $USER:$USER scripts/*.sh
```

#### Error: "Package installation failed"
```bash
# Update pip and try again
pip install --upgrade pip setuptools wheel

# Install with verbose output to see specific error
./scripts/install.sh --verbose

# Try without optional dependencies
pip install -e . --no-deps
pip install -r requirements.txt
```

### Virtual Environment Issues

#### Error: "Virtual environment creation failed"
```bash
# Install venv module
sudo apt install python3-venv  # Ubuntu/Debian
sudo yum install python3-venv  # RHEL/CentOS

# Create manually
python -m venv AT3GUI_venv
source AT3GUI_venv/bin/activate
pip install -e .
```

#### Error: "Activation script not found"
```bash
# Check if virtual environment exists
ls -la venv/

# Recreate if corrupted
rm -rf venv/
./scripts/install.sh
```

### System Dependencies

#### Error: "Qt platform plugin could not be initialized"
```bash
# Install Qt dependencies (Ubuntu/Debian)
sudo apt install qt5-default libqt5gui5 libqt5widgets5

# Set display environment
export DISPLAY=:0

# For headless systems, install virtual display
sudo apt install xvfb
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 &
```

#### Error: "Missing development headers"
```bash
# Install development packages (Ubuntu/Debian)
sudo apt install python3-dev build-essential

# RHEL/CentOS
sudo yum install python3-devel gcc gcc-c++

# Install specific library headers
sudo apt install libhdf5-dev  # For HDF5 support
```

## 🚀 Launch Problems

### Application Won't Start

#### Error: "AT3GUI installation not found"
```bash
# Check installation locations
ls -la ~/AT3GUI/
ls -la /opt/AT3GUI/

# Specify installation directory
./scripts/launch.sh --install-dir /path/to/installation

# Reinstall if necessary
./scripts/install.sh
```

#### Error: "Import Error: No module named 'aretomo3_gui'"
```bash
# Activate virtual environment
source ~/AT3GUI/venv/bin/activate

# Check Python path
python -c "import sys; print('\n'.join(sys.path))"

# Reinstall package
pip install -e .
```

#### Error: "Qt application: cannot connect to X server"
```bash
# Check X11 forwarding (SSH)
ssh -X username@hostname

# Set DISPLAY variable
export DISPLAY=:0

# Use Wayland compatibility
export QT_QPA_PLATFORM=wayland

# For remote connections
export QT_QPA_PLATFORM=xcb
```

### GUI Startup Issues

#### Error: "Application exits immediately"
```bash
# Launch with debug output
./scripts/launch.sh --debug --verbose

# Check for core dumps
dmesg | tail
journalctl -u display-manager

# Run in terminal to see errors
cd ~/AT3GUI
source venv/bin/activate
python -m aretomo3_gui --debug
```

#### Error: "Blank or corrupted window"
```bash
# Reset UI configuration
rm -rf ~/.config/at3gui/ui_state.json

# Try different Qt backend
export QT_QPA_PLATFORM=xcb
export QT_QPA_PLATFORM=wayland

# Update graphics drivers
sudo apt update && sudo apt upgrade
```

## ⚙️ Processing Errors

### AreTomo3 Integration Issues

#### Error: "AreTomo3 not found"
```bash
# Check AreTomo3 installation
which AreTomo3
AreTomo3 --version

# Add to PATH
export PATH=$PATH:/path/to/aretomo3/bin

# Configure custom path in AT3GUI
# File → Preferences → AreTomo3 Path
```

#### Error: "AreTomo3 execution failed"
```bash
# Test AreTomo3 directly
AreTomo3 -InMrc test.mrc -OutMrc output.mrc

# Check permissions
ls -la $(which AreTomo3)

# Check dependencies
ldd $(which AreTomo3)

# Run with verbose logging
./scripts/launch.sh --debug
```

### File Processing Errors

#### Error: "Input file not readable"
```bash
# Check file permissions
ls -la input_file.mrc

# Check file format
file input_file.mrc

# Test with another file
cp /path/to/known/good/file.mrc test.mrc
```

#### Error: "Output directory not writable"
```bash
# Check output directory permissions
ls -ld /path/to/output/directory

# Create directory if needed
mkdir -p /path/to/output/directory

# Fix permissions
chmod 755 /path/to/output/directory
```

#### Error: "Insufficient disk space"
```bash
# Check available space
df -h /path/to/output

# Clean temporary files
./scripts/cleanup.sh

# Change temporary directory
export TMPDIR=/path/to/large/disk
```

### Parameter Validation Issues

#### Error: "Invalid parameter value"
```bash
# Check parameter ranges in GUI
# Most common issues:
# - Tilt angle: must be (-90, 90)
# - Pixel size: must be positive
# - Voltage: typically 200, 300 kV

# Reset to defaults
# File → Parameters → Reset to Defaults
```

#### Error: "Parameter file corrupted"
```bash
# Remove corrupted parameter file
rm ~/.config/at3gui/parameters.json

# Reset configuration
./scripts/launch.sh --reset-config
```

## 🐌 Performance Issues

### Slow Processing

#### Diagnosis
```bash
# Monitor system resources
htop
iotop
nvidia-smi  # If using CUDA

# Check CPU usage in AT3GUI
# View → System Monitor
```

#### Solutions
```bash
# Reduce parallel processes
# File → Preferences → Processing → Max CPU Cores

# Increase memory allocation
# File → Preferences → Processing → Memory Limit

# Use faster temporary storage
export TMPDIR=/path/to/fast/ssd

# Close other applications
```

### Memory Issues

#### Error: "Out of memory"
```bash
# Check available memory
free -h

# Reduce memory usage
# - Process smaller files first
# - Reduce number of parallel jobs
# - Close other applications

# Increase swap space
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### GPU Issues

#### Error: "CUDA not available"
```bash
# Check CUDA installation
nvidia-smi
nvcc --version

# Install CUDA toolkit
# Follow NVIDIA CUDA installation guide

# Check PyTorch CUDA support
python -c "import torch; print(torch.cuda.is_available())"
```

## 🖥️ GUI Problems

### Display Issues

#### Window appears corrupted or scaled incorrectly
```bash
# Reset UI scaling
export QT_SCALE_FACTOR=1.0
export QT_AUTO_SCREEN_SCALE_FACTOR=0

# Reset window state
rm ~/.config/at3gui/window_state.json

# Try different desktop environment
# KDE, GNOME, XFCE may handle scaling differently
```

#### Fonts appear too small/large
```bash
# Adjust font DPI
export QT_FONT_DPI=96

# Reset font settings
# File → Preferences → Appearance → Reset Fonts
```

### Theme and Appearance

#### Dark/light theme issues
```bash
# Reset theme settings
# File → Preferences → Appearance → Theme → Default

# Clear theme cache
rm -rf ~/.config/at3gui/themes/

# Force theme reload
./scripts/launch.sh --reset-theme
```

## 📁 File and Data Issues

### Input File Problems

#### Error: "Unsupported file format"
```bash
# Check supported formats
# AT3GUI supports: .mrc, .st, .rec, .ali

# Convert file format if needed
# Use IMOD or other tools to convert to MRC

# Check file headers
header input_file.mrc  # IMOD command
```

#### Error: "File appears corrupted"
```bash
# Test file with other tools
header test.mrc
clip info test.mrc

# Try different file
# Verify file download/transfer

# Check file size
ls -lh suspicious_file.mrc
```

### Project Management Issues

#### Error: "Project file corrupted"
```bash
# Backup project file
cp project.at3gui project.at3gui.backup

# Try manual JSON repair
# Project files are JSON format

# Create new project and re-add files
```

#### Error: "Cannot save project"
```bash
# Check directory permissions
ls -ld /path/to/project/directory

# Check disk space
df -h /path/to/project

# Try different location
# File → Save Project As → Different directory
```

## 🖥️ Platform-Specific Issues

### Linux-Specific

#### Wayland vs X11 Issues
```bash
# Force X11
export QT_QPA_PLATFORM=xcb

# Force Wayland
export QT_QPA_PLATFORM=wayland

# Check current session
echo $XDG_SESSION_TYPE
```

#### AppImage Issues
```bash
# Make AppImage executable
chmod +x AT3GUI.AppImage

# Run with debug
./AT3GUI.AppImage --appimage-extract-and-run

# Extract and run manually
./AT3GUI.AppImage --appimage-extract
./squashfs-root/AppRun
```

### Ubuntu/Debian Specific

```bash
# Install additional packages
sudo apt install python3-pyqt5 python3-pyqt5.qtwidgets

# Fix broken packages
sudo apt --fix-broken install

# Clear package cache
sudo apt clean && sudo apt update
```

### RHEL/CentOS Specific

```bash
# Enable EPEL repository
sudo yum install epel-release

# Install Python 3.8+
sudo yum install python38 python38-devel

# Use alternative Python
python38 -m venv venv
```

## 🔍 Advanced Diagnostics

### Debug Mode

Enable comprehensive logging:

```bash
# Launch with maximum verbosity
./scripts/launch.sh --debug --verbose

# Check log files
tail -f ~/AT3GUI/logs/at3gui_$(date +%Y%m%d).log

# Enable Python debugging
export PYTHONDEBUG=1
export QT_LOGGING_RULES="*.debug=true"
```

### Environment Information

Collect system information for bug reports:

```bash
# System info script
cat > debug_info.sh << 'EOF'
#!/bin/bash
echo "=== AT3GUI Debug Information ==="
echo "Date: $(date)"
echo "User: $USER"
echo "Hostname: $(hostname)"
echo ""

echo "=== System Information ==="
cat /etc/os-release
uname -a
echo ""

echo "=== Python Information ==="
python --version
which python
python -c "import sys; print('Python path:', sys.executable)"
echo ""

echo "=== AT3GUI Installation ==="
ls -la ~/AT3GUI/ 2>/dev/null || echo "Not found in ~/AT3GUI/"
ls -la ./scripts/ 2>/dev/null || echo "Scripts directory not found"
echo ""

echo "=== Environment Variables ==="
env | grep -E "(PATH|PYTHON|QT|DISPLAY)" | sort
echo ""

echo "=== Disk Space ==="
df -h
echo ""

echo "=== Memory ==="
free -h
echo ""

echo "=== Process List ==="
ps aux | grep -E "(python|qt|at3gui)" | head -10
EOF

chmod +x debug_info.sh
./debug_info.sh > debug_report.txt
```

### Network Diagnostics

For network-related issues:

```bash
# Check network connectivity
ping -c 4 google.com

# Test DNS resolution
nslookup github.com

# Check proxy settings
env | grep -i proxy

# Test file downloads
wget --spider https://github.com/
```

### Performance Profiling

For performance analysis:

```bash
# CPU profiling
python -m cProfile -o profile.stats -m aretomo3_gui

# Memory profiling
python -m memory_profiler at3gui_script.py

# System monitoring
iostat 1 10
vmstat 1 10
```

## 🆘 Getting Help

### Self-Help Resources

1. **Documentation**: Check `docs/` directory
2. **Log Files**: Review `~/AT3GUI/logs/`
3. **FAQ**: Common questions and answers
4. **Known Issues**: Check project issues page

### Community Support

1. **GitHub Issues**: Report bugs and request features
2. **Discussion Forums**: Community Q&A
3. **Video Tutorials**: Step-by-step guides
4. **User Groups**: Local user communities

### Professional Support

For critical issues or enterprise deployments:

1. **Email Support**: Direct developer contact
2. **Remote Assistance**: Screen sharing sessions
3. **Custom Training**: On-site or online training
4. **Consulting Services**: Implementation assistance

### Reporting Issues

When reporting problems, include:

```bash
# Generate comprehensive report
./debug_info.sh > issue_report.txt

# Include log files
cp ~/AT3GUI/logs/at3gui_*.log ./

# Package everything
tar -czf at3gui_issue_$(date +%Y%m%d).tar.gz \
    issue_report.txt \
    at3gui_*.log \
    ~/.config/at3gui/settings.json
```

**Report should include:**
- Detailed description of the problem
- Steps to reproduce the issue
- Expected vs actual behavior
- System information (from debug script)
- Log files and error messages
- Screenshots if GUI-related

### Emergency Recovery

If AT3GUI is completely broken:

```bash
# Complete clean installation
./scripts/uninstall.sh --force
rm -rf ~/.config/at3gui/
./scripts/cleanup.sh --deep
./scripts/install.sh

# Restore from backup (if available)
cp backup/settings.json ~/.config/at3gui/
cp backup/projects/* ~/AT3GUI/projects/
```

---

## 📋 Quick Reference

### Essential Commands
```bash
# Diagnostics
./scripts/launch.sh --verbose --debug

# Clean installation
./scripts/cleanup.sh && ./scripts/install.sh

# Reset configuration
rm -rf ~/.config/at3gui/

# View logs
tail -f ~/AT3GUI/logs/at3gui_$(date +%Y%m%d).log

# Test installation
python -c "import aretomo3_gui; print('OK')"
```

### Common Environment Variables
```bash
export QT_QPA_PLATFORM=xcb        # Force X11
export DISPLAY=:0                 # Set display
export TMPDIR=/path/to/temp       # Temporary directory
export PYTHONPATH=/path/to/src    # Python path
export AT3GUI_DEBUG=1             # Enable debug mode
```

---

*Last updated: May 28, 2025*  
*Version: Latest*
