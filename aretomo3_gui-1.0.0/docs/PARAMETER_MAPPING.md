# AreTomo3 Parameter Mapping Reference

## 📋 **Complete Parameter List with GUI Implementation Status**

| AreTomo3 Parameter | GUI Widget Name | Type | Default | Status | Priority | Notes |
|-------------------|-----------------|------|---------|--------|----------|-------|
| **Input/Output** |
| `-InPrefix` | `input_dir` | Path | - | ✅ | Critical | Handled by Batch/Live tabs |
| `-Insuffix` | `insuffix` | LineEdit | ".mdoc" | ❌ | Critical | **MISSING** |
| `-InSkips` | `inskips` | LineEdit | "" | ❌ | Critical | **MISSING** |
| `-OutDir` | `output_dir` | Path | - | ✅ | Critical | Auto-generated |
| `-Serial` | `serial` | SpinBox | 0 | ❌ | Critical | **MISSING** |
| `-Gain` | `gain_file` | Path | - | ❌ | Critical | **MISSING** |
| `-TmpDir` | `tmp_dir` | Path | - | ❌ | Medium | Optional |
| **Microscope** |
| `-PixSize` | `pix_size` | DoubleSpinBox | 1.0 | ✅ | Critical | ✅ Implemented |
| `-Kv` | `kv` | SpinBox | 300 | ✅ | Critical | ✅ Implemented |
| `-Cs` | `cs` | DoubleSpinBox | 2.7 | ✅ | Critical | ✅ Implemented |
| `-AmpContrast` | `amp_contrast` | DoubleSpinBox | 0.07 | ✅ | Critical | ✅ Implemented |
| `-TiltAxis` | `tilt_axis` | DoubleSpinBox | 0.0 | ✅ | Critical | ✅ Implemented |
| `-FmDose` | `fm_dose` | DoubleSpinBox | 0.0 | ✅ | Critical | ✅ Implemented |
| **Motion Correction** |
| `-FmInt` | `fm_int` | SpinBox | 15 | ✅ | High | ✅ Implemented |
| `-Group` | `group_global`, `group_local` | SpinBox | 1, 4 | ❌ | High | **MISSING** |
| `-McBin` | `mc_bin` | DoubleSpinBox | 1.0 | ✅ | High | ✅ Implemented |
| `-McPatch` | `mc_patch_x`, `mc_patch_y` | SpinBox | 0, 0 | ❌ | High | **MISSING** |
| `-EerSampling` | `eer_sampling` | SpinBox | 1 | ❌ | High | **MISSING** |
| `-FlipGain` | `flip_gain` | CheckBox | 0 | ❌ | High | **MISSING** |
| `-InvGain` | `inv_gain` | CheckBox | 0 | ❌ | Low | Optional |
| `-RotGain` | `rot_gain` | SpinBox | 0 | ❌ | Low | Optional |
| `-DefectFile` | `defect_file` | Path | - | ❌ | Medium | Optional |
| `-McIter` | `mc_iter` | SpinBox | 15 | ❌ | Low | Optional |
| `-McTol` | `mc_tol` | DoubleSpinBox | 0.1 | ❌ | Low | Optional |
| `-FmRef` | `fm_ref` | SpinBox | -1 | ❌ | Low | Optional |
| `-Mag` | `mag_x`, `mag_y`, `mag_angle` | DoubleSpinBox | 1,1,0 | ❌ | Low | Optional |
| `-InFmMotion` | `in_fm_motion` | CheckBox | 0 | ❌ | Low | Optional |
| `-TiffOrder` | `tiff_order` | SpinBox | 1 | ❌ | Low | Optional |
| **Tomographic Alignment** |
| `-AlignZ` | `align_z` | SpinBox | 0 | ✅ | High | ✅ Implemented |
| `-VolZ` | `vol_z` | SpinBox | -1 | ✅ | High | ✅ Implemented |
| `-ExtZ` | `ext_z` | SpinBox | 300 | ❌ | High | **MISSING** |
| `-AtBin` | `at_bin` | DoubleSpinBox | 1.0 | ✅ | High | ✅ Implemented |
| `-AtBin` (2nd) | `at_bin_2nd` | DoubleSpinBox | 0.0 | ❌ | Medium | **MISSING** |
| `-AtBin` (3rd) | `at_bin_3rd` | DoubleSpinBox | 0.0 | ❌ | Medium | **MISSING** |
| `-AtPatch` | `at_patch_x`, `at_patch_y` | SpinBox | 0, 0 | ❌ | High | **MISSING** |
| `-TiltCor` | `tilt_cor` | ComboBox | 0 | ✅ | High | ✅ Implemented |
| `-ReconRange` | `recon_min`, `recon_max` | DoubleSpinBox | -90,90 | ❌ | Medium | Optional |
| `-TotalDose` | `total_dose` | DoubleSpinBox | 0.0 | ❌ | Medium | Optional |
| **Reconstruction** |
| `-Wbp` | `wbp` | CheckBox | 1 | ✅ | High | ✅ Implemented |
| `-Sart` | `sart_iter`, `sart_subset` | SpinBox | 20, 5 | ❌ | Medium | **MISSING** |
| `-FlipVol` | `flip_vol` | CheckBox | 0 | ✅ | High | ✅ Implemented |
| `-FlipInt` | `flip_int` | CheckBox | 0 | ❌ | Low | Optional |
| `-SplitSum` | `split_sum` | CheckBox | 1 | ✅ | High | ✅ Implemented |
| `-CropVol` | `crop_vol_x`, `crop_vol_y` | SpinBox | 0, 0 | ❌ | Low | Optional |
| **CTF Processing** |
| `-CorrCTF` | `corr_ctf` | CheckBox | 1 | ✅ | High | ✅ Implemented |
| `-CorrCTF` (lowpass) | `ctf_lowpass` | DoubleSpinBox | 15.0 | ❌ | High | **MISSING** |
| `-ExtPhase` | `ext_phase_shift`, `ext_phase_range` | DoubleSpinBox | 0,0 | ❌ | Medium | **MISSING** |
| **Output Control** |
| `-OutXF` | `out_xf` | CheckBox | 0 | ✅ | High | ✅ Implemented |
| `-OutImod` | `out_imod` | ComboBox | 0 | ✅ | High | ✅ Implemented |
| `-DarkTol` | `dark_tol` | DoubleSpinBox | 0.7 | ✅ | High | ✅ Implemented |
| `-IntpCor` | `intp_cor` | CheckBox | 0 | ❌ | Low | Optional |
| **Processing Control** |
| `-Cmd` | `cmd` | ComboBox | 0 | ❌ | High | **MISSING** |
| `-Resume` | `resume` | CheckBox | 0 | ❌ | Medium | Optional |
| `-Gpu` | `gpu` | LineEdit | "0" | ✅ | High | ✅ Implemented |

## 🚨 **Critical Missing Parameters (Must Implement)**

### **File Handling (Essential)**
```python
# Add to General Parameters Tab
self.insuffix = QLineEdit(".mdoc")
self.inskips = QLineEdit("_override") 
self.gain_file = QLineEdit()
self.gain_browse = QPushButton("Browse")
```

### **Processing Mode Control (Essential)**
```python
# Add to General Parameters Tab
self.serial = QSpinBox()  # 0=single, 1=batch, >1=live
self.cmd = QComboBox()    # 0=full, 1=align, 2=recon, 3=ctf, 4=rotate
```

### **Motion Correction Enhancement (High Priority)**
```python
# Add to Advanced Parameters Tab
self.group_global = QSpinBox()     # Default: 1
self.group_local = QSpinBox()      # Default: 4
self.mc_patch_x = QSpinBox()       # Default: 0 (global only)
self.mc_patch_y = QSpinBox()       # Default: 0 (global only)
self.eer_sampling = QSpinBox()     # Default: 1
self.flip_gain = QCheckBox()       # Default: False
```

### **Alignment Enhancement (High Priority)**
```python
# Add to Advanced Parameters Tab
self.at_patch_x = QSpinBox()       # Default: 0 (global only)
self.at_patch_y = QSpinBox()       # Default: 0 (global only)
self.ext_z = QSpinBox()            # Default: 300
self.ctf_lowpass = QDoubleSpinBox() # Default: 15.0
```

## 📊 **Implementation Statistics**

- **Total Parameters:** 47
- **Currently Implemented:** 18 (38%)
- **Critical Missing:** 11 (23%)
- **High Priority Missing:** 8 (17%)
- **Medium Priority Missing:** 6 (13%)
- **Low Priority Missing:** 4 (9%)

## 🎯 **Implementation Phases**

### **Phase 1: Critical (Week 1)**
Focus on the 11 critical missing parameters that are essential for basic functionality.

### **Phase 2: High Priority (Week 2)**
Add the 8 high-priority parameters for advanced motion correction and alignment.

### **Phase 3: Medium Priority (Week 3-4)**
Implement medium-priority parameters for specialized use cases.

### **Phase 4: Low Priority (Month 2)**
Add remaining optional parameters for completeness.

## 🔧 **Command Generation Updates Required**

The `_build_command()` method needs updates to handle:
1. **Conditional parameter inclusion** based on checkbox states
2. **Multi-value parameters** (Group, McPatch, AtPatch, AtBin multiple values)
3. **File path validation** for Gain and DefectFile
4. **Parameter interdependencies** (EerSampling + McBin)
5. **Format validation** (GPU space-separated, not comma-separated)

## 📝 **GUI Layout Recommendations**

### **General Tab Additions**
- File Handling section with Gain reference browser
- Processing Mode section with Cmd and Serial controls

### **Advanced Tab Reorganization**
- Motion Correction section (Group, McPatch, EerSampling, FlipGain)
- Alignment section (AtPatch, ExtZ)
- CTF section (CorrCTF lowpass, ExtPhase)
- Multi-resolution section (AtBin 2nd/3rd values)

This comprehensive mapping ensures all AreTomo3 parameters can be properly implemented in the GUI!
