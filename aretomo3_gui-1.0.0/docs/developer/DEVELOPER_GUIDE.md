# 👨‍💻 AT3GUI Developer Guide

Welcome to the AT3GUI development guide. This document provides comprehensive information for developers who want to contribute to, extend, or understand the AT3GUI codebase.

## 🎯 Table of Contents

- [Development Setup](#-development-setup)
- [Project Architecture](#-project-architecture)
- [Code Organization](#-code-organization)
- [Development Workflow](#-development-workflow)
- [Testing Framework](#-testing-framework)
- [Plugin Development](#-plugin-development)
- [API Reference](#-api-reference)
- [Contributing Guidelines](#-contributing-guidelines)
- [Release Process](#-release-process)

## 🛠️ Development Setup

### Prerequisites

- **Python**: 3.8+ (3.10+ recommended)
- **Git**: For version control
- **PyQt5/PySide2**: GUI framework
- **AreTomo3**: Core processing engine
- **Development Tools**: pytest, black, flake8, mypy

### Quick Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd AT3Gui

# Install in development mode
./scripts/install.sh --install-dir ./dev_env

# Activate development environment
source dev_env/venv/bin/activate

# Install development dependencies
pip install -e ".[dev]"

# Run tests to verify setup
python -m pytest tests/ -v
```

### Development Environment

```bash
# Create isolated development environment
python -m venv venv_dev
source venv_dev/bin/activate

# Install in editable mode
pip install -e .

# Install development tools
pip install pytest pytest-cov black flake8 mypy sphinx
```

### IDE Configuration

#### Visual Studio Code
```json
{
    "python.defaultInterpreterPath": "./venv_dev/bin/python",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black"
}
```

#### PyCharm
- Set project interpreter to `./venv_dev/bin/python`
- Configure pytest as default test runner
- Enable code style inspections
- Set up run configurations for GUI and CLI

## 🏗️ Project Architecture

### High-Level Architecture

```
AT3GUI Architecture
├── 🎨 GUI Layer (PyQt5/PySide2)
│   ├── Main Window
│   ├── Parameter Panels
│   ├── Progress Monitoring
│   └── Result Display
├── 🧠 Core Logic Layer
│   ├── Processing Pipeline
│   ├── Parameter Management
│   ├── File I/O Operations
│   └── Error Handling
├── 🔧 Integration Layer
│   ├── AreTomo3 Interface
│   ├── External Tools
│   ├── Plugin System
│   └── Configuration Management
└── 📊 Data Layer
    ├── Project Management
    ├── Metadata Handling
    ├── Result Storage
    └── Logging System
```

### Design Patterns

#### Model-View-Controller (MVC)
- **Model**: Data structures and business logic (`core/`)
- **View**: GUI components and presentation (`gui/`)
- **Controller**: User interaction handling and coordination

#### Observer Pattern
- Event-driven communication between components
- Progress updates and status notifications
- Plugin system integration

#### Factory Pattern
- Parameter creation and validation
- Plugin instantiation
- File format handlers

## 📁 Code Organization

### Directory Structure

```
src/aretomo3_gui/
├── __init__.py              # Package initialization and version
├── main.py                  # Application entry point
├── cli.py                   # Command-line interface
├── core/                    # Core business logic
│   ├── __init__.py
│   ├── processing.py        # Processing pipeline
│   ├── parameters.py        # Parameter management
│   ├── project.py          # Project handling
│   ├── config.py           # Configuration management
│   └── exceptions.py       # Custom exceptions
├── gui/                     # User interface components
│   ├── __init__.py
│   ├── main_window.py      # Main application window
│   ├── widgets/            # Custom widgets
│   │   ├── parameter_panel.py
│   │   ├── progress_widget.py
│   │   └── result_viewer.py
│   ├── dialogs/            # Dialog windows
│   │   ├── preferences.py
│   │   ├── batch_setup.py
│   │   └── about.py
│   └── themes/             # UI themes and styles
├── tools/                   # External tool integration
│   ├── __init__.py
│   ├── aretomo3.py         # AreTomo3 interface
│   ├── plugins.py          # Plugin management
│   └── validators.py       # Input validation
└── utils/                   # Utility functions
    ├── __init__.py
    ├── file_utils.py       # File operations
    ├── logging_utils.py    # Logging configuration
    ├── system_utils.py     # System information
    └── decorators.py       # Common decorators
```

### Key Components

#### Core Processing (`core/processing.py`)
```python
class ProcessingPipeline:
    """Main processing pipeline for AreTomo3 operations."""
    
    def __init__(self, parameters: ProcessingParameters):
        self.parameters = parameters
        self.status_callback = None
        
    def process(self, input_file: Path) -> ProcessingResult:
        """Execute processing pipeline."""
        # Implementation details...
        
    def set_status_callback(self, callback: Callable):
        """Set callback for progress updates."""
        self.status_callback = callback
```

#### GUI Main Window (`gui/main_window.py`)
```python
class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """Initialize user interface."""
        # UI setup code...
        
    def connect_signals(self):
        """Connect signals and slots."""
        # Signal connections...
```

#### Parameter Management (`core/parameters.py`)
```python
class ProcessingParameters:
    """Container for all processing parameters."""
    
    def __init__(self):
        self._parameters = {}
        self._validators = {}
        
    def set_parameter(self, name: str, value: Any):
        """Set parameter with validation."""
        # Validation and setting logic...
        
    def validate(self) -> List[str]:
        """Validate all parameters."""
        # Validation logic...
```

## 🔄 Development Workflow

### Git Workflow

1. **Feature Development**
   ```bash
   # Create feature branch
   git checkout -b feature/new-functionality
   
   # Make changes and commit
   git add .
   git commit -m "Add new functionality"
   
   # Push and create pull request
   git push origin feature/new-functionality
   ```

2. **Code Review Process**
   - Create pull request with detailed description
   - Ensure all tests pass
   - Request review from maintainers
   - Address feedback and update

3. **Merge and Deploy**
   - Squash commits if necessary
   - Merge to main branch
   - Update version numbers
   - Create release tags

### Code Quality Standards

#### Code Formatting
```bash
# Format code with black
black src/ tests/

# Check formatting
black --check src/ tests/
```

#### Linting
```bash
# Run flake8 linting
flake8 src/ tests/

# Run mypy type checking
mypy src/
```

#### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.950
    hooks:
      - id: mypy
```

## 🧪 Testing Framework

### Test Organization

```
tests/
├── unit/                    # Unit tests
│   ├── test_core/
│   ├── test_gui/
│   └── test_utils/
├── integration/             # Integration tests
│   ├── test_processing_pipeline.py
│   └── test_gui_integration.py
├── performance/             # Performance benchmarks
│   └── test_processing_speed.py
├── fixtures/               # Test data and fixtures
│   ├── sample_data/
│   └── mock_objects.py
└── conftest.py             # Pytest configuration
```

### Testing Best Practices

#### Unit Tests
```python
import pytest
from aretomo3_gui.core.parameters import ProcessingParameters

class TestProcessingParameters:
    """Test cases for ProcessingParameters class."""
    
    def test_parameter_setting(self):
        """Test basic parameter setting."""
        params = ProcessingParameters()
        params.set_parameter("tilt_angle", (-60, 60))
        assert params.get_parameter("tilt_angle") == (-60, 60)
        
    def test_parameter_validation(self):
        """Test parameter validation."""
        params = ProcessingParameters()
        with pytest.raises(ValueError):
            params.set_parameter("tilt_angle", "invalid")
```

#### GUI Tests
```python
import pytest
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest
from aretomo3_gui.gui.main_window import MainWindow

class TestMainWindow:
    """Test cases for MainWindow."""
    
    @pytest.fixture
    def main_window(self, qtbot):
        """Create MainWindow instance for testing."""
        window = MainWindow()
        qtbot.addWidget(window)
        return window
        
    def test_window_creation(self, main_window):
        """Test window creation."""
        assert main_window.isVisible()
        assert main_window.windowTitle() == "AT3GUI"
```

#### Mock Objects
```python
from unittest.mock import Mock, patch
from aretomo3_gui.tools.aretomo3 import AreTomo3Interface

class TestAreTomo3Interface:
    """Test AreTomo3 integration."""
    
    @patch('subprocess.run')
    def test_processing_call(self, mock_run):
        """Test AreTomo3 processing call."""
        mock_run.return_value.returncode = 0
        
        interface = AreTomo3Interface()
        result = interface.process("input.mrc", parameters={})
        
        assert result.success
        mock_run.assert_called_once()
```

### Running Tests

```bash
# Run all tests
python -m pytest

# Run with coverage
python -m pytest --cov=aretomo3_gui

# Run specific test categories
python -m pytest tests/unit/
python -m pytest tests/integration/

# Run with verbose output
python -m pytest -v

# Run tests in parallel
python -m pytest -n auto
```

## 🔌 Plugin Development

### Plugin Architecture

AT3GUI supports plugins for extending functionality:

```python
from aretomo3_gui.core.plugin_base import PluginBase

class MyPlugin(PluginBase):
    """Example plugin implementation."""
    
    name = "My Custom Plugin"
    version = "1.0.0"
    description = "Adds custom functionality"
    
    def __init__(self):
        super().__init__()
        
    def initialize(self, app_context):
        """Initialize plugin with application context."""
        self.app_context = app_context
        
    def get_menu_items(self):
        """Return menu items to add to application."""
        return [
            ("Tools", "My Plugin Action", self.execute_action)
        ]
        
    def execute_action(self):
        """Execute plugin's main action."""
        # Plugin functionality here...
```

### Plugin Registration

```python
# plugin_manifest.json
{
    "name": "my_plugin",
    "version": "1.0.0",
    "entry_point": "my_plugin.main:MyPlugin",
    "dependencies": ["numpy>=1.20.0"],
    "compatible_versions": [">=1.0.0"],
    "author": "Plugin Author",
    "license": "MIT"
}
```

## 📚 API Reference

### Core API

#### Processing API
```python
from aretomo3_gui.core import ProcessingPipeline, ProcessingParameters

# Create parameters
params = ProcessingParameters()
params.set_parameter("tilt_angle", (-60, 60))
params.set_parameter("pixel_size", 2.7)

# Create pipeline
pipeline = ProcessingPipeline(params)

# Process file
result = pipeline.process("input.mrc")
```

#### Project API
```python
from aretomo3_gui.core import Project

# Create new project
project = Project.create("my_project", "/path/to/project")

# Add files
project.add_file("dataset1.mrc")
project.add_file("dataset2.mrc")

# Process all files
for result in project.process_all():
    print(f"Processed: {result.input_file}")
```

### GUI API

#### Custom Widgets
```python
from PyQt5.QtWidgets import QWidget
from aretomo3_gui.gui.widgets import ParameterPanel

class CustomParameterWidget(QWidget):
    """Custom parameter input widget."""
    
    def __init__(self):
        super().__init__()
        self.parameter_panel = ParameterPanel()
        # Custom implementation...
```

### Events and Signals

```python
from PyQt5.QtCore import pyqtSignal, QObject

class ProcessingEventEmitter(QObject):
    """Event emitter for processing events."""
    
    processing_started = pyqtSignal(str)  # filename
    processing_finished = pyqtSignal(str, bool)  # filename, success
    progress_updated = pyqtSignal(int)  # percentage
```

## 🤝 Contributing Guidelines

### Getting Started

1. **Fork the Repository**
   - Create a fork on GitHub
   - Clone your fork locally
   - Set up development environment

2. **Find an Issue**
   - Browse open issues
   - Look for "good first issue" labels
   - Discuss approach in issue comments

3. **Development Process**
   - Create feature branch
   - Write tests for new functionality
   - Implement changes
   - Ensure all tests pass
   - Update documentation

### Code Style Guidelines

#### Python Style
- Follow PEP 8 conventions
- Use type hints for function signatures
- Write docstrings for all public functions
- Maximum line length: 88 characters (Black default)

#### Documentation Style
- Use Google-style docstrings
- Include examples in docstrings
- Update relevant documentation files
- Add inline comments for complex logic

#### Commit Message Format
```
feat: add new parameter validation system

- Implement comprehensive parameter validation
- Add unit tests for validation logic
- Update documentation with validation examples

Closes #123
```

### Pull Request Process

1. **Pre-submission Checklist**
   - [ ] All tests pass
   - [ ] Code is properly formatted
   - [ ] Documentation is updated
   - [ ] Changelog is updated
   - [ ] No merge conflicts

2. **Review Process**
   - Automated CI checks must pass
   - At least one maintainer review required
   - Address all feedback
   - Squash commits if requested

## 🚀 Release Process

### Version Management

AT3GUI uses semantic versioning (SemVer):
- **Major**: Breaking changes (1.0.0 → 2.0.0)
- **Minor**: New features (1.0.0 → 1.1.0)
- **Patch**: Bug fixes (1.0.0 → 1.0.1)

### Release Steps

1. **Prepare Release**
   ```bash
   # Update version numbers
   python scripts/bump_version.py 1.2.0
   
   # Update changelog
   python scripts/update_changelog.py
   
   # Create release commit
   git commit -am "Release v1.2.0"
   git tag v1.2.0
   ```

2. **Build and Test**
   ```bash
   # Build distribution packages
   python setup.py sdist bdist_wheel
   
   # Test installation
   python -m pip install dist/aretomo3_gui-1.2.0.tar.gz
   
   # Run full test suite
   python -m pytest tests/
   ```

3. **Deploy Release**
   ```bash
   # Push to repository
   git push origin main --tags
   
   # Upload to PyPI (if applicable)
   python -m twine upload dist/*
   
   # Create GitHub release
   gh release create v1.2.0 --notes-file RELEASE_NOTES.md
   ```

### Release Checklist

- [ ] Version numbers updated
- [ ] Changelog updated
- [ ] All tests passing
- [ ] Documentation current
- [ ] Security scan clean
- [ ] Performance benchmarks met
- [ ] Cross-platform testing completed
- [ ] Release notes prepared

---

## 🎉 Conclusion

This developer guide provides the foundation for contributing to AT3GUI. The project welcomes contributions from developers of all experience levels.

For questions or support:
- **Issues**: Use GitHub issue tracker
- **Discussions**: GitHub discussions for general questions
- **Direct Contact**: Maintainer email for urgent matters

**Happy Coding!** 🚀

---

*Last updated: May 28, 2025*  
*Version: Latest*
