# 📚 AreTomo3 GUI Documentation Index

**Version:** 2.0.0 Production Release  
**Last Updated:** 2025-05-31

---

## 🚀 Quick Start

- [**README.md**](../README.md) - Main project overview and installation
- [**QUICK_START.md**](../QUICK_START.md) - Get started in 5 minutes
- [**Installation Guide**](user/installation.md) - Detailed installation instructions

---

## 👥 User Documentation

### Getting Started
- [User Guide](user/) - Complete user documentation
- [GUI Parameter Checklist](GUI_PARAMETER_CHECKLIST.md) - Parameter reference
- [Parameter Mapping](PARAMETER_MAPPING.md) - AreTomo3 parameter mapping
- [EER Support](EER_SUPPORT.md) - EER file format support

### Features & Workflows
- [Comprehensive Feature Guide](COMPREHENSIVE_FEATURE_GUIDE.md) - All features explained
- [Comprehensive System Guide](COMPREHENSIVE_SYSTEM_GUIDE.md) - System architecture
- [AreTomo3 Reference](ARETOMO3_REFERENCE.md) - AreTomo3 integration

---

## 🔧 Developer Documentation

### Development
- [Developer Guide](developer/) - Development setup and guidelines
- [API Documentation](api/) - Complete API reference
- [Enhancement Roadmap](ENHANCEMENT_ROADMAP.md) - Future development plans

### Architecture
- [System Architecture](COMPREHENSIVE_SYSTEM_GUIDE.md) - Technical architecture
- [Code Organization](dev/) - Code structure and patterns

---

## 🚀 Deployment & Operations

### Production Deployment
- [**Production Readiness Report**](PRODUCTION_READINESS_REPORT.md) - Production status ✅
- [Deployment Summary](DEPLOYMENT_SUMMARY.md) - Deployment guide
- [Web Dashboard Setup](WEB_DASHBOARD_README.md) - Web interface deployment

### Integration
- [Dashboard Integration Guide](DASHBOARD_INTEGRATION_GUIDE.md) - Dashboard integration
- [Implementation Roadmap](IMPLEMENTATION_ROADMAP.md) - Implementation timeline

---

## 📊 Project Reports & Status

### Development Reports
- [Task Completion Summary](TASK_COMPLETION_SUMMARY.md) - Completed tasks
- [Task Execution Log](TASK_EXECUTION_LOG.md) - Development timeline
- [Stability Enhancement Plan](STABILITY_ENHANCEMENT_PLAN.md) - Stability improvements

### Real-time Feedback
- [Real-time Feedback](REALTIME_FEEDBACK.md) - Live development feedback

---

## 📁 Documentation Structure

```
docs/
├── INDEX.md                           # This file
├── user/                             # User documentation
│   ├── installation.md              # Installation guide
│   ├── getting-started.md           # Getting started
│   ├── tutorials/                   # Step-by-step tutorials
│   └── troubleshooting.md          # Common issues
├── developer/                        # Developer documentation
│   ├── setup.md                    # Development setup
│   ├── contributing.md             # Contribution guidelines
│   ├── architecture.md             # System architecture
│   └── testing.md                  # Testing guidelines
├── api/                             # API documentation
│   ├── rest-api.md                 # REST API reference
│   ├── websocket-api.md            # WebSocket API
│   └── python-api.md               # Python API
└── reports/                         # Project reports
    ├── security-audit.md           # Security audit results
    ├── performance-analysis.md     # Performance analysis
    └── user-feedback.md            # User feedback summary
```

---

## 🔍 Quick Navigation

### By Role
- **End Users** → [User Guide](user/) | [Quick Start](../QUICK_START.md)
- **Administrators** → [Deployment](DEPLOYMENT_SUMMARY.md) | [Production Report](PRODUCTION_READINESS_REPORT.md)
- **Developers** → [Developer Guide](developer/) | [API Docs](api/)
- **Integrators** → [Dashboard Integration](DASHBOARD_INTEGRATION_GUIDE.md) | [Web Setup](WEB_DASHBOARD_README.md)

### By Topic
- **Installation** → [Quick Start](../QUICK_START.md) | [User Guide](user/)
- **Configuration** → [Parameters](GUI_PARAMETER_CHECKLIST.md) | [System Guide](COMPREHENSIVE_SYSTEM_GUIDE.md)
- **Features** → [Feature Guide](COMPREHENSIVE_FEATURE_GUIDE.md) | [Tutorials](user/tutorials/)
- **Deployment** → [Production Report](PRODUCTION_READINESS_REPORT.md) | [Deployment Guide](DEPLOYMENT_SUMMARY.md)
- **Development** → [Developer Guide](developer/) | [Architecture](COMPREHENSIVE_SYSTEM_GUIDE.md)

---

## 📞 Support & Community

- **Issues & Bugs** → [GitHub Issues](https://github.com/aretomo3-gui/issues)
- **Discussions** → [GitHub Discussions](https://github.com/aretomo3-gui/discussions)
- **Documentation** → [Online Docs](https://aretomo3-gui.readthedocs.io/)
- **Email Support** → <EMAIL>

---

## 📝 Contributing to Documentation

Documentation contributions are welcome! Please see our [Contributing Guide](developer/contributing.md) for guidelines on:

- Writing clear, concise documentation
- Following our documentation standards
- Submitting documentation updates
- Reviewing documentation changes

---

*Documentation maintained by the AreTomo3 GUI Development Team*  
*For the latest updates, visit our [GitHub repository](https://github.com/aretomo3-gui/aretomo3-gui)*
