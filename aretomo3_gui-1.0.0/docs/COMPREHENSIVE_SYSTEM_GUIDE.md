# AreTomo3 GUI - Comprehensive System Guide

## 🎯 **OVERVIEW**

The AreTomo3 GUI is a comprehensive, production-ready system for tomographic reconstruction with real-time analysis, session management, continue mode support, and web interface integration.

## 🏗️ **SYSTEM ARCHITECTURE**

### **Core Components**

1. **Real-Time Analysis Engine** - Multi-series comparative analysis with live plotting
2. **Session Management System** - Automatic session saving and loading
3. **Continue Mode Manager** - Pause/resume/continue interrupted processing
4. **Web Interface** - Remote monitoring and control
5. **Results Parser** - Comprehensive AreTomo3 output parsing
6. **Quality Assessment** - Real-time quality metrics and scoring

### **Data Flow Architecture**

```
Input Data → AreTomo3 Processing → Results Parser → Real-Time Analysis
     ↓                                    ↓              ↓
Session Manager ← Quality Assessment ← Web Interface ← Continue Mode
     ↓                                    ↓              ↓
Auto-Save → Load Session → Status Updates → Remote Control
```

## 📊 **REAL-TIME ANALYSIS SYSTEM**

### **Features**
- **Multi-series comparative analysis** with overlaid plots
- **MDOC-based tilt angle parsing** for accurate CTF and resolution plots
- **Quality assessment** with automatic scoring
- **Live plot updates** during processing
- **Export functionality** for plots and reports

### **Plot Types**
1. **Motion Analysis**
   - Motion vs tilt angle (from MDOC)
   - Motion quality distribution
   - Motion trajectories
   - Motion statistics comparison

2. **CTF Analysis**
   - Defocus vs tilt angle (from MDOC)
   - Resolution vs tilt angle (Angstrom units)
   - CTF fit quality distribution
   - Resolution summary statistics

3. **Alignment Analysis**
   - Alignment scores vs tilt angle
   - Tilt axis stability
   - Alignment shifts analysis
   - Quality summary dashboard

4. **Comparative View**
   - Multi-series overlays
   - Quality metrics heatmap
   - Processing statistics
   - Performance comparison

### **Usage**
```python
# Load completed results
analysis_tab.load_completed_results()

# Select directory with AreTomo3 output
# → Automatically parses all result files
# → Creates comparative plots
# → Generates quality metrics
```

## 💾 **SESSION MANAGEMENT**

### **Features**
- **Automatic session creation** for each processing run
- **Real-time session updates** during processing
- **Session persistence** with JSON storage
- **Web interface integration** for remote access
- **Session history** with cleanup management

### **Session Data Structure**
```python
ProcessingSession:
    session_id: str
    session_name: str
    processing_mode: str  # 'batch', 'live', 'analysis'
    input_directory: str
    output_directory: str
    parameters: Dict[str, Any]
    series_data: Dict[str, Any]
    quality_metrics: Dict[str, Any]
    processing_status: str
    progress_percentage: float
    completed_series: List[str]
    failed_series: List[str]
```

### **API Usage**
```python
# Create new session
session = session_manager.create_new_session(
    "Batch Processing Run 1", "batch", "/input", "/output"
)

# Update session during processing
session_manager.update_session(
    processing_status="running",
    progress_percentage=45.0,
    completed_series=["series1", "series2"]
)

# Get session data for web interface
web_data = session_manager.get_session_for_web()
```

## ⏯️ **CONTINUE MODE SYSTEM**

### **AreTomo3 Continue Mode Support**
- **Pause processing** with SIGSTOP signal
- **Resume processing** with SIGCONT signal
- **Continue interrupted** processing from checkpoint
- **Process monitoring** with status tracking
- **Graceful termination** with cleanup

### **Continue Mode States**
```python
ProcessingState:
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    INTERRUPTED = "interrupted"
```

### **Usage Examples**
```python
# Start with continue mode support
success, session_id = continue_manager.start_processing(
    "tomo25", "aretomo3 -InPrefix tomo25", "/input", "/output"
)

# Pause processing
continue_manager.pause_processing(session_id)

# Resume processing
continue_manager.resume_processing(session_id)

# Continue after interruption
continue_manager.continue_processing(session_id)
```

## 🌐 **WEB INTERFACE INTEGRATION**

### **API Endpoints**

#### **Session Management**
- `GET /api/session/current` - Get current session
- `GET /api/session/list` - List all sessions
- `POST /api/session/create` - Create new session
- `POST /api/session/load/{session_id}` - Load session
- `POST /api/session/update` - Update session data

#### **Continue Mode**
- `GET /api/continue/sessions` - List continue sessions
- `POST /api/continue/start` - Start with continue mode
- `POST /api/continue/pause/{session_id}` - Pause processing
- `POST /api/continue/resume/{session_id}` - Resume processing
- `POST /api/continue/continue/{session_id}` - Continue interrupted
- `GET /api/continue/status/{session_id}` - Get session status

#### **Real-Time Analysis**
- `GET /api/analysis/quality-metrics` - Get quality metrics
- `GET /api/analysis/live-table` - Get analysis table data
- `GET /api/analysis/plots/{plot_type}` - Get plot data

### **WebSocket Updates**
Real-time updates for:
- Processing status changes
- Quality metric updates
- Session state changes
- Log messages
- Plot data updates

## 🔧 **CONFIGURATION & SETUP**

### **Installation Requirements**
```bash
# Core dependencies
pip install PyQt6 matplotlib numpy fastapi uvicorn

# Optional dependencies
pip install psutil mrcfile napari

# Development dependencies
pip install pytest pytest-qt pytest-asyncio
```

### **Directory Structure**
```
~/.aretomo3_gui/
├── sessions/           # Session files
├── continue_states/    # Continue mode states
├── logs/              # Application logs
├── plots/             # Generated plots
└── config.json        # Configuration
```

### **Configuration Options**
```json
{
    "auto_save_interval": 30,
    "max_sessions": 50,
    "web_port": 8080,
    "log_level": "INFO",
    "plot_export_format": "png",
    "quality_thresholds": {
        "motion_good": 1.0,
        "motion_fair": 2.0,
        "ctf_good": 0.8,
        "ctf_fair": 0.6
    }
}
```

## 🧪 **TESTING FRAMEWORK**

### **Test Categories**
1. **Unit Tests** - Individual component testing
2. **Integration Tests** - Component interaction testing
3. **System Tests** - End-to-end workflow testing
4. **Performance Tests** - Load and stress testing
5. **UI Tests** - GUI component testing

### **Running Tests**
```bash
# Run all tests
pytest tests/ -v

# Run specific test categories
pytest tests/test_session_manager.py -v
pytest tests/test_continue_mode.py -v
pytest tests/test_realtime_analysis.py -v

# Run with coverage
pytest tests/ --cov=src/aretomo3_gui --cov-report=html
```

### **Test Data Generation**
```python
# Create mock AreTomo3 data for testing
def create_test_data():
    # Motion correction data
    motion_data = generate_motion_csv()
    
    # CTF estimation data
    ctf_data = generate_ctf_txt()
    
    # Alignment data
    alignment_data = generate_alignment_csv()
    
    return motion_data, ctf_data, alignment_data
```

## 🚀 **DEPLOYMENT GUIDE**

### **Production Deployment**
1. **Environment Setup**
   ```bash
   # Create virtual environment
   python -m venv aretomo3_env
   source aretomo3_env/bin/activate
   
   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Configuration**
   ```bash
   # Set environment variables
   export ARETOMO3_CONFIG_DIR=/opt/aretomo3_gui
   export ARETOMO3_LOG_LEVEL=INFO
   export ARETOMO3_WEB_PORT=8080
   ```

3. **Service Setup**
   ```bash
   # Create systemd service
   sudo cp aretomo3-gui.service /etc/systemd/system/
   sudo systemctl enable aretomo3-gui
   sudo systemctl start aretomo3-gui
   ```

### **Docker Deployment**
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY docs/ ./docs/

EXPOSE 8080
CMD ["python", "-m", "src.aretomo3_gui.main", "--web-only"]
```

### **Security Considerations**
- **Authentication** for web interface
- **HTTPS** for remote access
- **File permissions** for data directories
- **Network security** for multi-user environments

## 📈 **PERFORMANCE OPTIMIZATION**

### **Memory Management**
- **Lazy loading** of large datasets
- **Data streaming** for real-time updates
- **Memory cleanup** for completed sessions
- **Efficient plot rendering** with matplotlib

### **Processing Optimization**
- **Parallel processing** for multiple series
- **Asynchronous updates** for web interface
- **Caching** for frequently accessed data
- **Background tasks** for non-critical operations

### **Monitoring & Metrics**
- **System resource monitoring**
- **Processing performance metrics**
- **Error rate tracking**
- **User activity logging**

## 🔍 **TROUBLESHOOTING**

### **Common Issues**
1. **Plot Generation Errors**
   - Check matplotlib backend
   - Verify data format
   - Check memory usage

2. **Session Loading Failures**
   - Verify file permissions
   - Check JSON format
   - Clear corrupted sessions

3. **Continue Mode Issues**
   - Check process permissions
   - Verify signal handling
   - Monitor system resources

4. **Web Interface Problems**
   - Check port availability
   - Verify network configuration
   - Review CORS settings

### **Debug Mode**
```bash
# Enable debug logging
export ARETOMO3_LOG_LEVEL=DEBUG

# Run with verbose output
python -m src.aretomo3_gui.main --debug --verbose
```

### **Log Analysis**
```bash
# View real-time logs
tail -f ~/.aretomo3_gui/logs/aretomo3_gui.log

# Search for errors
grep ERROR ~/.aretomo3_gui/logs/aretomo3_gui.log

# Analyze performance
grep "Processing time" ~/.aretomo3_gui/logs/aretomo3_gui.log
```

## 📚 **API REFERENCE**

### **Session Manager API**
```python
class SessionManager:
    def create_new_session(session_name, mode, input_dir, output_dir)
    def load_session(session_id)
    def save_session(session)
    def update_session(**kwargs)
    def get_session_for_web()
    def get_session_list()
```

### **Continue Mode API**
```python
class ContinueModeManager:
    def start_processing(series_name, command, input_dir, output_dir)
    def pause_processing(session_id)
    def resume_processing(session_id)
    def stop_processing(session_id)
    def continue_processing(session_id)
    def get_session_status(session_id)
```

### **Real-Time Analysis API**
```python
class RealTimeAnalysisTab:
    def load_completed_results()
    def parse_mdoc_tilt_angles(results_dir, series_name)
    def update_motion_plots(selected_series)
    def update_ctf_plots(selected_series)
    def update_alignment_plots(selected_series)
```

## 🎯 **BEST PRACTICES**

### **Development**
- **Follow PEP 8** coding standards
- **Write comprehensive tests** for all features
- **Document all public APIs**
- **Use type hints** for better code clarity
- **Handle errors gracefully**

### **Usage**
- **Regular session cleanup** to manage disk space
- **Monitor system resources** during processing
- **Use quality filters** for large datasets
- **Export important results** regularly
- **Keep backups** of critical sessions

### **Maintenance**
- **Regular updates** of dependencies
- **Monitor log files** for issues
- **Performance profiling** for optimization
- **User feedback** collection and analysis
- **Security updates** and patches

---

**🎉 The AreTomo3 GUI system is now production-ready with comprehensive features, robust error handling, extensive testing, and complete documentation!**
