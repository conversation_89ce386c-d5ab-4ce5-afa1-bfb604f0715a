# 🚀 AreTomo3 GUI - Quick Start

**Get up and running with AreTomo3 GUI in 2 minutes!**

## ⚡ Super Quick Install

```bash
# 1. Navigate to project directory
cd /mnt/HDD/ak_devel/AT3GUI_working

# 2. Install in development mode
pip install -e .

# 3. Launch the application
python -m aretomo3_gui
```

## 🎯 What You Get

After installation, you'll have access to:

### **📊 Enhanced Analysis Tab**
- Multi-tab analysis interface
- Real-time plots and statistics
- Export functionality (PNG, CSV, JSON, HTML)
- Auto-detection of result files

### **⚙️ Enhanced Parameters Tab**
- Help buttons (ℹ️) for every parameter
- General/Advanced parameter organization
- Parameter validation and defaults
- Save/load configurations

### **🔴 Live Processing Tab**
- Real-time file monitoring
- Auto-processing queue
- Processing timeline and statistics
- Multiple file format support

### **🏠 Main Control Center**
- Project management integration
- Command preview and generation
- Configuration management
- Organized sub-tabs

## 🔧 System Requirements

- **Python 3.8+** (3.9+ recommended)
- **4GB RAM** (8GB+ for large datasets)
- **PyQt6** (installed automatically)

## 🧪 Quick Test

Verify your installation:

```bash
# Test 1: Basic import
python -c "import aretomo3_gui; print('✅ Installation successful!')"

# Test 2: GUI creation (headless)
python -c "
import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
from aretomo3_gui.gui.main_window import AreTomo3MainWindow
from PyQt6.QtWidgets import QApplication
app = QApplication([])
window = AreTomo3MainWindow()
print(f'✅ GUI created with {window.tab_widget.count()} tabs')
app.quit()
"

# Test 3: Launch full application
python -m aretomo3_gui
```

## 📚 Next Steps

1. **Read the full documentation**: `docs/user/INSTALLATION.md`
2. **Check the user guide**: `docs/user/USER_GUIDE.md`
3. **Explore sample data**: `sample_data/` directory
4. **Set up automated backups**: `backups_AT3GUI/create_backup.sh`

## 🛠️ Troubleshooting

### Common Issues

**Import Error?**
```bash
cd /mnt/HDD/ak_devel/AT3GUI_working
pip install -e .
```

**GUI Won't Launch?**
```bash
# For headless systems
export QT_QPA_PLATFORM=offscreen
python -m aretomo3_gui
```

**Permission Issues?**
```bash
# Use virtual environment
python3 -m venv at3gui_env
source at3gui_env/bin/activate
pip install -e .
```

## 🎉 Features Highlights

### **Professional Quality**
- 68 Python files (~39,264 lines)
- Comprehensive testing framework
- Modern PyQt6 interface
- Type hints throughout

### **Enhanced Functionality**
- Real-time file monitoring
- Comprehensive analysis tools
- Parameter help system
- Automated backup system

### **User Experience**
- Intuitive tab organization
- Context-sensitive help
- Professional documentation
- Clean, modern interface

---

## 🚀 Ready to Go!

Your AreTomo3 GUI is now ready for professional cryo-EM processing!

**Launch command**: `python -m aretomo3_gui`

For detailed documentation, see: `docs/user/INSTALLATION.md`
