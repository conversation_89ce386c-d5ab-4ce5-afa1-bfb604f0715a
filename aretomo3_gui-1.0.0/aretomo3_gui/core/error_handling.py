#!/usr/bin/env python3
"""
Error Handling Module for AreTomo3 GUI Application
===============================================

This module provides a centralized error handling system with:
- Custom exception types for specific error conditions
- Error recovery mechanisms
- User-friendly error reporting
- Structured logging integration
- Error tracking and analysis capabilities

Usage:
    from core.error_handling import try_operation, ApplicationError

    # Use decorator for automatic error handling
    @try_operation
    def some_function():
        ...

    # Or use context manager
    with try_operation():
        ...

Classes:
    ApplicationError: Base class for all custom exceptions
    GPUError: GPU-related errors
    FileSystemError: File system operation errors
    ProcessingError: Data processing errors

Functions:
    try_operation: Decorator/context manager for error handling
    show_error_dialog: Display error message to user
    log_error: Log error with appropriate context

Author: AreTomo3 GUI Team
Date: May 2025
"""

# =============================================================================
# Imports
# =============================================================================

import logging
import os

# Standard library imports
import sys
import traceback
from enum import Enum
from typing import Any, Callable, Dict, Optional, Type

# Qt imports
from PyQt6.QtWidgets import QMessageBox, QWidget

# =============================================================================
# Logger Configuration
# =============================================================================

logger = logging.getLogger("error_handler")
logger.setLevel(logging.DEBUG)

# Configure console logging if not already set up
if not logger.handlers:
    formatter = logging.Formatter(
        "%(levelname)s    %(name)s:%(filename)s:%(lineno)d %(message)s"
    )

    # Configure console handler for immediate feedback
    console_handler = logging.StreamHandler(sys.stderr)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)

    # Configure file handler for persistent logging if enabled
    if "LOGFILE" in os.environ:
        file_handler = logging.FileHandler(os.environ["LOGFILE"])
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.info(f"Logging to file: {os.environ['LOGFILE']}")

# =============================================================================
# Error Classification
# =============================================================================


class ErrorSeverity(Enum):
    """
    Classification of error severity levels.

    Levels:
        INFO: Informational message, not an error
        WARNING: Non-critical issue that may need attention
        ERROR: Serious issue that needs immediate attention
        CRITICAL: Fatal error that prevents further operation
    """

    INFO = 0
    WARNING = 1
    ERROR = 2
    CRITICAL = 3

    def __ge__(self, other):
        """Execute __ge__ operation."""
        if isinstance(other, ErrorSeverity):
            return self.value >= other.value
        return NotImplemented

    def __le__(self, other):
        """Execute __le__ operation."""
        if isinstance(other, ErrorSeverity):
            return self.value <= other.value
        return NotImplemented

    def __gt__(self, other):
        """Execute __gt__ operation."""
        if isinstance(other, ErrorSeverity):
            return self.value > other.value
        return NotImplemented

    def __lt__(self, other):
        """Execute __lt__ operation."""
        if isinstance(other, ErrorSeverity):
            return self.value < other.value
        return NotImplemented


# =============================================================================
# Custom Exceptions
# =============================================================================


class AreTomo3Error(Exception):
    """
    Base class for all AreTomo3 application errors.

    Provides consistent error handling with severity levels and
    optional context information for better error reporting.

    Attributes:
        message (str): Human-readable error description
        severity (ErrorSeverity): Error severity level
        context (Dict[str, Any]): Optional context information
    """

    def __init__(
        self,
        message: str,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Initialize error with message and severity.

        Args:
            message: Human-readable error description
            severity: Error severity level
            context: Optional dictionary of context information
        """
        super().__init__(message)
        self.severity = severity
        self.context = context or {}

        # Log error creation
        logger.log(
            logging.ERROR if severity >= ErrorSeverity.ERROR else logging.WARNING,
            f"{
                self.__class__.__name__}: {message}",
        )


class ProcessingError(AreTomo3Error):
    """
    Error raised during data processing operations.

    Used for errors that occur during:
    - Tilt series alignment
    - Tomogram reconstruction
    - Data analysis and measurement
    - Batch processing operations
    """

    pass


class FileSystemError(AreTomo3Error):
    """
    Error raised during file system operations.

    Used for errors that occur during:
    - File reading/writing
    - Directory operations
    - File permissions issues
    - Disk space issues
    """

    pass


class GPUError(AreTomo3Error):
    """
    Error raised during GPU-related operations.

    Used for errors that occur during:
    - GPU initialization
    - CUDA operations
    - Memory allocation
    - Device compatibility checks

    Attributes:
        device_id (Optional[int]): ID of the GPU device that caused the error
        cuda_error (Optional[str]): Original CUDA error message
    """

    def __init__(
        self,
        message: str,
        device_id: Optional[int] = None,
        cuda_error: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
    ) -> None:
        """
        Initialize GPU error with device context.

        Args:
            message: Human-readable error description
            device_id: Optional GPU device identifier
            cuda_error: Optional original CUDA error message
            severity: Error severity level
        """
        context = {"device_id": device_id, "cuda_error": cuda_error}
        super().__init__(message, severity=severity, context=context)


# =============================================================================
# Error Handling Functions
# =============================================================================


# TODO: Refactor function - Function 'handle_exception' too long (74 lines)
def handle_exception(
    error: Exception,
    parent: Optional[QWidget] = None,
    show_dialog: bool = True,
    log_level: Optional[int] = None,
) -> None:
    """
    Centralized exception handler with logging and UI feedback.

    This function provides consistent error handling by:
    1. Logging the error with full stack trace
    2. Optionally showing a user-friendly error dialog
    3. Providing context-appropriate error information

    Args:
        error: The exception to handle
        parent: Parent widget for dialog boxes (if GUI feedback needed)
        show_dialog: Whether to show a dialog box
        log_level: Optional override for log level

    Note:
        Dialog appearance and message are determined by error severity
        when available (for AreTomo3Error instances) or defaults to
        ERROR severity for standard exceptions.
    """
    # Capture full stack trace
    tb = traceback.format_exc()

    # Determine logging level
    if log_level is None:
        if isinstance(error, AreTomo3Error):
            log_level = (
                logging.ERROR
                if error.severity >= ErrorSeverity.ERROR
                else logging.WARNING
            )
        else:
            log_level = logging.ERROR

    # Log the error with context
    logger.log(log_level, f"Exception: {str(error)}")
    logger.log(log_level, f"Stack trace:\n{tb}")

    # Show dialog if requested and possible
    if show_dialog and parent is not None:
        # Determine severity and appropriate icon
        severity = (
            error.severity if isinstance(error, AreTomo3Error) else ErrorSeverity.ERROR
        )

        icon_map = {
            ErrorSeverity.INFO: QMessageBox.Icon.Information,
            ErrorSeverity.WARNING: QMessageBox.Icon.Warning,
            ErrorSeverity.ERROR: QMessageBox.Icon.Critical,
            ErrorSeverity.CRITICAL: QMessageBox.Icon.Critical,
        }
        icon = icon_map[severity]
        # Show context-aware error dialog
        title = {
            ErrorSeverity.INFO: "Information",
            ErrorSeverity.WARNING: "Warning",
            ErrorSeverity.ERROR: "Error",
            ErrorSeverity.CRITICAL: "Critical Error",
        }[severity]

        # Build detailed message including context if available
        message = str(error)
        if isinstance(error, AreTomo3Error) and error.context:
            context_str = "\n\nContext:\n" + "\n".join(
                f"- {k}: {v}" for k, v in error.context.items()
            )
            message += context_str

        QMessageBox.critical(parent, title, message, icon)

    # TODO: Refactor function - Function 'try_operation' too long (56 lines)


def try_operation(
    operation: Callable[..., Any],
    error_message: str,
    parent: Optional[QWidget] = None,
    show_dialog: bool = True,
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    context: Optional[Dict[str, Any]] = None,
) -> Any:
    """
    Execute an operation with error handling and user feedback.

    This function can be used as either a decorator or a context manager:

    As a decorator:
        @try_operation("Failed to process data")
        def process_data():
            ...

    As a context manager:
        with try_operation("Failed to save file"):
            ...

    Args:
        operation: Function to execute
        error_message: Base message for error reporting
        parent: Parent widget for error dialogs
        show_dialog: Whether to show error dialogs
        severity: Default severity for errors
        context: Additional context to include in error reports

    Returns:
        Result of the operation if successful

    Raises:
        AreTomo3Error: If the operation fails, wrapping the original error
    """
    try:
        return operation()
    except Exception as e:
        # Build error message with context
        full_message = error_message
        if str(e):
            full_message += f": {str(e)}"

        # Create error with context
        error_context = context or {}
        if isinstance(e, AreTomo3Error):
            error_context.update(e.context)

        custom_error = AreTomo3Error(
            message=full_message, severity=severity, context=error_context
        )

        # Handle the error
        handle_exception(custom_error, parent, show_dialog)
        raise custom_error from e


# =============================================================================
# Global Exception Handling
# =============================================================================


# TODO: Refactor function - Function 'install_global_exception_handler'
# too long (75 lines)
def install_global_exception_handler(parent: Optional[QWidget] = None) -> None:
    """
    Install a global exception handler for unhandled exceptions.

    This handler provides last-resort error handling for otherwise
    unhandled exceptions. It will:
    1. Log the error with full stack trace
    2. Release any grabbed mouse if needed
    3. Show a critical error dialog
    4. Gracefully terminate the application

    Args:
        parent: Optional parent widget for error dialogs. If None,
               will attempt to find the main application window.
    """

    # TODO: Refactor function - Function 'global_exception_handler' too long
    # (55 lines)
    def global_exception_handler(
        exctype: Type[Exception], value: Exception, tb: Any
    ) -> None:
        """
        Handle uncaught exceptions globally.

        Args:
            exctype: The type of the exception
            value: The exception instance
            tb: The traceback object

        Note:
            This handler will terminate the application after showing
            the error, as unhandled exceptions indicate serious issues.
        """
        try:
            # Format detailed error message
            error_message = f"Uncaught {exctype.__name__}: {value}"
            stack_trace = "".join(traceback.format_tb(tb))

            # Log the error
            logger.critical("Unhandled exception occurred:")
            logger.critical(error_message)
            logger.critical(f"Stack trace:\n{stack_trace}")

            # Release mouse grab if any
            if QWidget.mouseGrabber() is not None:
                logger.debug("Releasing mouse grab")
                QWidget.mouseGrabber().releaseMouse()

            # Find a suitable parent window
            dialog_parent = parent
            if dialog_parent is None:
                dialog_parent = QWidget.find(1)  # Try to find main window

            # Show error dialog if possible
            if dialog_parent is not None:
                detailed_message = (
                    f"A critical error has occurred:\n\n"
                    f"{error_message}\n\n"
                    f"The application must close for stability reasons.\n\n"
                    f"Please check the log files for more information."
                )

                QMessageBox.critical(
                    dialog_parent,
                    "Critical Error",
                    detailed_message,
                    QMessageBox.Icon.Critical,
                )

        finally:
            # Always terminate after unhandled exception
            logger.critical("Terminating application due to unhandled exception")
            sys.exit(1)

    # Install the global handler
    sys.excepthook = global_exception_handler
    logger.info("Global exception handler installed")
