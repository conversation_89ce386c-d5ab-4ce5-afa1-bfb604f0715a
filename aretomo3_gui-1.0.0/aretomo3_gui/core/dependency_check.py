"""Check and verify all required dependencies."""

import importlib.util
import os
import subprocess
import sys


def check_dependency(package_name, min_version=None):
    """Check if a Python package is installed and meets minimum version."""
    try:
        spec = importlib.util.find_spec(package_name)
        if spec is None:
            return False, f"{package_name} is not installed"

        if min_version:
            pkg = importlib.import_module(package_name)
            version = getattr(pkg, "__version__", None)
            if version and version < min_version:
                return (
                    False,
                    f"{package_name} version {version} is below required {min_version}",
                )

        return True, f"{package_name} is properly installed"
    except Exception as e:
        return False, f"Error checking {package_name}: {str(e)}"


def check_cuda():
    """Check if CUDA is available through nvidia-smi."""
    try:
        subprocess.check_output(["nvidia-smi"])
        return True, "CUDA is available"
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False, "CUDA is not available (nvidia-smi not found or failed)"


def check_aretomo(path):
    """Check if AreTomo3 is available at the specified path."""
    try:
        if not path:
            return False, "AreTomo3 path is not set"

        # Handle directory vs file paths
        if os.path.isdir(path):
            # If directory, look for AreTomo3 executable inside
            executable_path = os.path.join(path, "AreTomo3")
            if not os.path.exists(executable_path):
                return False, f"AreTomo3 executable not found in directory: {path}"
        else:
            # If file path, use it directly
            executable_path = path
            if not os.path.exists(executable_path):
                return False, f"AreTomo3 executable not found: {executable_path}"

        # Validate the executable
        if not os.path.isfile(executable_path):
            return False, f"AreTomo3 path is not a file: {executable_path}"

        if not os.access(executable_path, os.X_OK):
            return False, f"AreTomo3 binary is not executable: {executable_path}"

        # Try to execute AreTomo3 with --version or -h
        try:
            subprocess.check_output([executable_path], stderr=subprocess.STDOUT)
            return True, "AreTomo3 is properly installed"
        except subprocess.CalledProcessError as e:
            # AreTomo3 returns non-zero on no arguments, which is fine
            if "AreTomo" in e.output.decode():
                return True, "AreTomo3 is properly installed"
            return (
                False,
                f"AreTomo3 test execution failed: {
                e.output.decode()}",
            )

    except Exception as e:
        return False, f"Error checking AreTomo3: {str(e)}"


def check_environment():
    """Check all required dependencies and return status."""
    status = []

    # Check Python packages
    dependencies = {
        "PyQt6": "6.0.0",
        "psutil": "5.9.0",
        "numpy": "1.24.0",
        "mrcfile": "1.5.0",
        "matplotlib": "3.7.0",
    }

    for pkg, version in dependencies.items():
        ok, msg = check_dependency(pkg, version)
        status.append((ok, msg))

    # Check CUDA
    ok, msg = check_cuda()
    status.append((ok, msg))

    return status


def check_dependencies():
    """Check all dependencies and return a summary."""
    status = check_environment()

    # Count successful checks
    successful = sum(1 for ok, _ in status if ok)
    total = len(status)

    return {
        "success_rate": successful / total if total > 0 else 0,
        "successful": successful,
        "total": total,
        "details": status,
    }


def print_status(status):
    """Print dependency check status in a formatted way."""
    logger.info("\nDependency Check Results:")
    logger.info("-" * 50)

    all_ok = True
    for ok, msg in status:
        status_symbol = "✓" if ok else "✗"
        logger.info(f"{status_symbol} {msg}")
        if not ok:
            all_ok = False

    logger.info("-" * 50)
    if all_ok:
        logger.info("All dependencies are properly installed!")
    else:
        logger.info("Some dependencies are missing or incorrectly installed.")
        logger.info("Please install missing dependencies and try again.")
        sys.exit(1)


if __name__ == "__main__":
    status = check_environment()
    print_status(status)
