#!/usr/bin/env python3
"""
AreTomo3 GUI Performance Monitor
Monitors system performance and optimizes resource usage.
"""

import gc
import logging
import threading
import time
import weakref
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import psutil

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""

    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float = 0.0
    network_recv_mb: float = 0.0
    gui_response_time_ms: float = 0.0
    active_threads: int = 0
    open_files: int = 0


@dataclass
class OptimizationSettings:
    """Performance optimization settings."""

    max_memory_percent: float = 80.0
    max_cpu_percent: float = 90.0
    gc_threshold_mb: float = 500.0
    cache_cleanup_interval: int = 300  # seconds
    plot_cache_max_size: int = 50
    data_cache_max_size: int = 100
    enable_auto_optimization: bool = True
    enable_memory_monitoring: bool = True
    enable_cpu_monitoring: bool = True


class PerformanceMonitor:
    """
    Monitors system performance and provides optimization recommendations.
    """

    def __init__(self):
        """Initialize the performance monitor."""
        self.settings = OptimizationSettings()
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history_size = 1000
        self.monitoring_active = False
        self.monitoring_thread = None
        self.monitoring_interval = 5.0  # seconds

        # Performance caches
        self.plot_cache = {}
        self.data_cache = {}
        self.weak_refs = weakref.WeakSet()

        # Baseline metrics
        self.baseline_metrics = None
        self.last_optimization = datetime.now()

        logger.info("Performance Monitor initialized")

    def start_monitoring(self):
        """Start performance monitoring."""
        if self.monitoring_active:
            logger.warning("Performance monitoring already active")
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True, name="PerformanceMonitor"
        )
        self.monitoring_thread.start()
        logger.info("Performance monitoring started")

    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        logger.info("Performance monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                metrics = self._collect_metrics()
                self._store_metrics(metrics)
                self._check_optimization_triggers(metrics)

                time.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
                time.sleep(self.monitoring_interval)

    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()

            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = disk_io.read_bytes / (1024 * 1024) if disk_io else 0
            disk_write_mb = disk_io.write_bytes / (1024 * 1024) if disk_io else 0

            # Network I/O
            net_io = psutil.net_io_counters()
            net_sent_mb = net_io.bytes_sent / (1024 * 1024) if net_io else 0
            net_recv_mb = net_io.bytes_recv / (1024 * 1024) if net_io else 0

            # Process info
            process = psutil.Process()
            active_threads = process.num_threads()
            open_files = len(process.open_files())

            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=net_sent_mb,
                network_recv_mb=net_recv_mb,
                active_threads=active_threads,
                open_files=open_files,
            )

        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                disk_io_read_mb=0.0,
                disk_io_write_mb=0.0,
            )

    def _store_metrics(self, metrics: PerformanceMetrics):
        """Store metrics in history."""
        self.metrics_history.append(metrics)

        # Trim history if too large
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size :]

        # Set baseline if not set
        if self.baseline_metrics is None:
            self.baseline_metrics = metrics

    def _check_optimization_triggers(self, metrics: PerformanceMetrics):
        """Check if optimization should be triggered."""
        if not self.settings.enable_auto_optimization:
            return

        # Check memory threshold
        if (
            self.settings.enable_memory_monitoring
            and metrics.memory_percent > self.settings.max_memory_percent
        ):
            logger.warning(f"High memory usage: {metrics.memory_percent:.1f}%")
            self._optimize_memory()

        # Check CPU threshold
        if (
            self.settings.enable_cpu_monitoring
            and metrics.cpu_percent > self.settings.max_cpu_percent
        ):
            logger.warning(f"High CPU usage: {metrics.cpu_percent:.1f}%")
            self._optimize_cpu()

        # Periodic cache cleanup
        now = datetime.now()
        if (
            now - self.last_optimization
        ).seconds > self.settings.cache_cleanup_interval:
            self._cleanup_caches()
            self.last_optimization = now

    def _optimize_memory(self):
        """Optimize memory usage."""
        try:
            logger.info("Optimizing memory usage...")

            # Force garbage collection
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")

            # Clear plot cache if too large
            if len(self.plot_cache) > self.settings.plot_cache_max_size:
                self._cleanup_plot_cache()

            # Clear data cache if too large
            if len(self.data_cache) > self.settings.data_cache_max_size:
                self._cleanup_data_cache()

        except Exception as e:
            logger.error(f"Error optimizing memory: {e}")

    def _optimize_cpu(self):
        """Optimize CPU usage."""
        try:
            logger.info("Optimizing CPU usage...")

            # Reduce monitoring frequency temporarily
            original_interval = self.monitoring_interval
            self.monitoring_interval = min(original_interval * 2, 30.0)

            # Reset after 60 seconds
            def reset_interval():
                """Execute reset_interval operation."""
                time.sleep(60)
                self.monitoring_interval = original_interval

            threading.Thread(target=reset_interval, daemon=True).start()

        except Exception as e:
            logger.error(f"Error optimizing CPU: {e}")

    def _cleanup_caches(self):
        """Clean up various caches."""
        try:
            logger.info("Cleaning up caches...")

            self._cleanup_plot_cache()
            self._cleanup_data_cache()

            # Clean up weak references
            self.weak_refs.clear()

        except Exception as e:
            logger.error(f"Error cleaning up caches: {e}")

    def _cleanup_plot_cache(self):
        """Clean up plot cache."""
        if len(self.plot_cache) > self.settings.plot_cache_max_size:
            # Remove oldest entries
            items = list(self.plot_cache.items())
            items.sort(key=lambda x: x[1].get("timestamp", datetime.min))

            remove_count = len(items) - self.settings.plot_cache_max_size
            for i in range(remove_count):
                del self.plot_cache[items[i][0]]

            logger.info(f"Cleaned up {remove_count} plot cache entries")

    def _cleanup_data_cache(self):
        """Clean up data cache."""
        if len(self.data_cache) > self.settings.data_cache_max_size:
            # Remove oldest entries
            items = list(self.data_cache.items())
            items.sort(key=lambda x: x[1].get("timestamp", datetime.min))

            remove_count = len(items) - self.settings.data_cache_max_size
            for i in range(remove_count):
                del self.data_cache[items[i][0]]

            logger.info(f"Cleaned up {remove_count} data cache entries")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        if not self.metrics_history:
            return {"status": "no_data"}

        recent_metrics = self.metrics_history[-10:]  # Last 10 measurements

        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)

        return {
            "status": "active" if self.monitoring_active else "inactive",
            "current_cpu_percent": recent_metrics[-1].cpu_percent,
            "current_memory_percent": recent_metrics[-1].memory_percent,
            "average_cpu_percent": avg_cpu,
            "average_memory_percent": avg_memory,
            "active_threads": recent_metrics[-1].active_threads,
            "open_files": recent_metrics[-1].open_files,
            "plot_cache_size": len(self.plot_cache),
            "data_cache_size": len(self.data_cache),
            "metrics_history_size": len(self.metrics_history),
            "last_optimization": self.last_optimization.isoformat(),
            "optimization_settings": {
                "max_memory_percent": self.settings.max_memory_percent,
                "max_cpu_percent": self.settings.max_cpu_percent,
                "auto_optimization": self.settings.enable_auto_optimization,
            },
        }

    def cache_plot(self, key: str, plot_data: Any):
        """Cache plot data."""
        self.plot_cache[key] = {"data": plot_data, "timestamp": datetime.now()}

    def get_cached_plot(self, key: str) -> Optional[Any]:
        """Get cached plot data."""
        cache_entry = self.plot_cache.get(key)
        if cache_entry:
            return cache_entry["data"]
        return None

    def cache_data(self, key: str, data: Any):
        """Cache data."""
        self.data_cache[key] = {"data": data, "timestamp": datetime.now()}

    def get_cached_data(self, key: str) -> Optional[Any]:
        """Get cached data."""
        cache_entry = self.data_cache.get(key)
        if cache_entry:
            return cache_entry["data"]
        return None


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
