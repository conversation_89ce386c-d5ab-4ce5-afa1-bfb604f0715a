"""Settings and configuration management with profiles and presets."""

import json
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional

from PyQt6.QtCore import QObject, pyqtSignal


@dataclass
class MicroscopeProfile:
    """Profile for microscope settings."""

    name: str
    voltage: int
    cs: float
    pixel_size: float
    amp_contrast: float
    frame_dose: float
    custom_settings: Dict[str, Any]


@dataclass
class ProcessingPreset:
    """Preset for processing parameters."""

    name: str
    description: str
    settings: Dict[str, Any]
    sample_type: str


class ConfigManager(QObject):
    """Manages application configuration, profiles, and presets."""

    config_changed = pyqtSignal()
    profile_changed = pyqtSignal(str)  # profile name
    preset_changed = pyqtSignal(str)  # preset name

    def __init__(self):
        """Initialize the instance."""
        super().__init__()
        self.config_dir = Path.home() / ".aretomo3"
        self.config_dir.mkdir(parents=True, exist_ok=True)

        self.profiles: Dict[str, MicroscopeProfile] = {}
        self.presets: Dict[str, ProcessingPreset] = {}
        self.current_profile: Optional[str] = None
        self.current_preset: Optional[str] = None

        self._load_profiles()
        self._load_presets()
        self._load_config()

    def _load_config(self):
        """Load general configuration settings."""
        config_file = self.config_dir / "config.json"
        if not config_file.exists():
            # Create default config
            default_config = {
                "current_profile": "Default",
                "current_preset": "Standard",
                "last_directory": str(Path.home()),
                "theme": "dark",
                "auto_save": True,
            }
            with open(config_file, "w") as f:
                json.dump(default_config, f, indent=2)

            self.current_profile = "Default"
            self.current_preset = "Standard"
            return

        try:
            with open(config_file) as f:
                config = json.load(f)

            self.current_profile = config.get("current_profile", "Default")
            self.current_preset = config.get("current_preset", "Standard")

        except Exception as e:
            logger.info(f"Error loading config: {e}")
            self.current_profile = "Default"
            self.current_preset = "Standard"

    def _load_profiles(self):
        """Load microscope profiles from disk."""
        profile_file = self.config_dir / "profiles.json"
        if not profile_file.exists():
            self._create_default_profile()
            return

        try:
            with open(profile_file) as f:
                data = json.load(f)

            self.profiles = {
                name: MicroscopeProfile(
                    name=name,
                    voltage=p["voltage"],
                    cs=p["cs"],
                    pixel_size=p["pixel_size"],
                    amp_contrast=p["amp_contrast"],
                    frame_dose=p["frame_dose"],
                    custom_settings=p.get("custom_settings", {}),
                )
                for name, p in data.items()
            }
        except Exception as e:
            logger.info(f"Error loading profiles: {e}")
            self._create_default_profile()

    def _create_default_profile(self):
        """Create default microscope profile."""
        default = MicroscopeProfile(
            name="Default",
            voltage=300,
            cs=2.7,
            pixel_size=1.91,
            amp_contrast=0.1,
            frame_dose=0.14,
            custom_settings={},
        )
        self.profiles["Default"] = default
        self.current_profile = "Default"
        self._save_profiles()

    def _load_presets(self):
        """Load processing presets from disk."""
        preset_file = self.config_dir / "presets.json"
        if not preset_file.exists():
            self._create_default_presets()
            return

        try:
            with open(preset_file) as f:
                data = json.load(f)

            self.presets = {
                name: ProcessingPreset(
                    name=name,
                    description=p["description"],
                    settings=p["settings"],
                    sample_type=p["sample_type"],
                )
                for name, p in data.items()
            }
        except Exception as e:
            logger.info(f"Error loading presets: {e}")
            self._create_default_presets()

    def _create_default_presets(self):
        """Create default processing presets."""
        defaults = {
            "Standard": ProcessingPreset(
                name="Standard",
                description="Standard processing for typical samples",
                settings={
                    "motion_correction": {
                        "bin": 1,
                        "patch_size": [1, 1],
                        "frame_interval": 12,
                    },
                    "reconstruction": {"bin": 4, "wbp": True, "ctf": True},
                },
                sample_type="general",
            ),
            "High Resolution": ProcessingPreset(
                name="High Resolution",
                description="Settings optimized for high resolution",
                settings={
                    "motion_correction": {
                        "bin": 1,
                        "patch_size": [2, 2],
                        "frame_interval": 6,
                    },
                    "reconstruction": {"bin": 2, "wbp": True, "ctf": True},
                },
                sample_type="high_resolution",
            ),
        }

        self.presets = defaults
        self.current_preset = "Standard"
        self._save_presets()

    def save_profile(self, profile: MicroscopeProfile):
        """Save a microscope profile."""
        self.profiles[profile.name] = profile
        self._save_profiles()
        self.profile_changed.emit(profile.name)

    def save_preset(self, preset: ProcessingPreset):
        """Save a processing preset."""
        self.presets[preset.name] = preset
        self._save_presets()
        self.preset_changed.emit(preset.name)

    def _save_profiles(self):
        """Save profiles to disk."""
        profile_data = {
            name: {
                "voltage": p.voltage,
                "cs": p.cs,
                "pixel_size": p.pixel_size,
                "amp_contrast": p.amp_contrast,
                "frame_dose": p.frame_dose,
                "custom_settings": p.custom_settings,
            }
            for name, p in self.profiles.items()
        }

        with open(self.config_dir / "profiles.json", "w") as f:
            json.dump(profile_data, f, indent=2)

    def _save_presets(self):
        """Save presets to disk."""
        preset_data = {
            name: {
                "description": p.description,
                "settings": p.settings,
                "sample_type": p.sample_type,
            }
            for name, p in self.presets.items()
        }

        with open(self.config_dir / "presets.json", "w") as f:
            json.dump(preset_data, f, indent=2)

    def get_profile(self, name: str) -> Optional[MicroscopeProfile]:
        """Get a microscope profile by name."""
        return self.profiles.get(name)

    def get_preset(self, name: str) -> Optional[ProcessingPreset]:
        """Get a processing preset by name."""
        return self.presets.get(name)

    def set_current_profile(self, name: str):
        """Set the current microscope profile."""
        if name in self.profiles:
            self.current_profile = name
            self.profile_changed.emit(name)

    def set_current_preset(self, name: str):
        """Set the current processing preset."""
        if name in self.presets:
            self.current_preset = name
            self.preset_changed.emit(name)

    def detect_optimal_parameters(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:
        """Auto-detect optimal processing parameters based on input data."""
        # Implementation of parameter detection logic
        # This would analyze factors like:
        # - Image statistics (contrast, noise)
        # - Tilt angle distribution
        # - Sample thickness
        # - Desired resolution
        params = {}

        # Example logic:
        if sample_data.get("signal_to_noise", 0) < 0.5:
            params["motion_correction"] = {"bin": 2, "patch_size": [2, 2]}

        return params

    def backup_config(self, backup_path: Optional[str] = None):
        """Create a backup of all configuration files."""
        if not backup_path:
            backup_path = self.config_dir / "backup"

        backup_dir = Path(backup_path)
        backup_dir.mkdir(parents=True, exist_ok=True)

        # Backup profiles
        with open(backup_dir / "profiles.json", "w") as f:
            json.dump(self.profiles, f, indent=2)

        # Backup presets
        with open(backup_dir / "presets.json", "w") as f:
            json.dump(self.presets, f, indent=2)

    def restore_config(self, backup_path: str):
        """Restore configuration from backup."""
        backup_dir = Path(backup_path)

        if not backup_dir.exists():
            raise FileNotFoundError(f"Backup directory not found: {backup_path}")

        # Restore profiles
        if (backup_dir / "profiles.json").exists():
            with open(backup_dir / "profiles.json") as f:
                self.profiles = json.load(f)

        # Restore presets
        if (backup_dir / "presets.json").exists():
            with open(backup_dir / "presets.json") as f:
                self.presets = json.load(f)

        self.config_changed.emit()
