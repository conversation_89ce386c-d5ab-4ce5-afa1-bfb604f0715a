#!/usr/bin/env python3

import logging
import os
import platform
import re
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import psutil

logger = logging.getLogger(__name__)


class AreTomo3Config:
    """Configuration class for AreTomo3 parameters."""

    def __init__(
        self,
        pixel_size: float,
        voltage: int = 300,
        cs: float = 2.7,
        amp_contrast: float = 0.1,
        tilt_axis: float = -95.75,
        frame_dose: float = 0.14,
        **kwargs: Any,
    ) -> None:
        """
        Initialize AreTomo3 configuration.

        Args:
            pixel_size: Pixel size in Angstroms
            voltage: Acceleration voltage in kV
            cs: Spherical aberration in mm
            amp_contrast: Amplitude contrast
            tilt_axis: Tilt axis angle in degrees
            frame_dose: Frame dose in e⁻/Å²
            **kwargs: Additional parameters
        """
        self.pixel_size = pixel_size
        self.voltage = voltage
        self.cs = cs
        self.amp_contrast = amp_contrast
        self.tilt_axis = tilt_axis
        self.frame_dose = frame_dose
        self.additional_params = kwargs

    def validate(self) -> Tuple[bool, str]:
        """
        Validate configuration parameters.

        Returns:
            Tuple of (success, message)
        """
        try:
            if self.pixel_size <= 0:
                return False, "Pixel size must be positive"
            if not 60 <= self.voltage <= 300:
                return False, "Voltage must be between 60 and 300 kV"
            if not 0 <= self.cs <= 10:
                return False, "Cs must be between 0 and 10 mm"
            if not 0 <= self.amp_contrast <= 1:
                return False, "Amplitude contrast must be between 0 and 1"
            return True, "Configuration valid"
        except Exception as e:
            return False, f"Validation error: {str(e)}"


def validate_aretomo_installation(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate AreTomo3 installation path.

    Args:
        path: Path to AreTomo3 executable or directory

    Returns:
        Tuple of (success, message)
    """
    try:
        path = Path(path)

        # If it's a directory, look for AreTomo3 executable
        if path.is_dir():
            exe_name = "AreTomo3.exe" if platform.system() == "Windows" else "AreTomo3"
            exe_path = path / exe_name
            if not exe_path.exists():
                return False, f"AreTomo3 executable not found in {path}"
            path = exe_path

        # Check if file exists and is executable
        if not path.exists():
            return False, f"File not found: {path}"
        if not os.access(path, os.X_OK):
            return False, f"File is not executable: {path}"

        # Basic validation of file type/format
        if platform.system() == "Windows":
            if not path.name.lower().endswith(".exe"):
                return False, "Not a valid executable file"
        else:
            # On Unix-like systems, check if it's an executable
            if not os.access(path, os.X_OK):
                return False, "File is not executable"

        return True, "Valid AreTomo3 installation"

    except Exception as e:
        return False, f"Validation error: {str(e)}"


def check_gpu_compatibility() -> Tuple[bool, str, List[int]]:
    """
    Check GPU compatibility for AreTomo3.

    Returns:
        Tuple of (success, message, list of available GPU indices)
    """
    try:
        # This is a placeholder - actual GPU detection would use CUDA/GPU
        # libraries
        available_gpus = list(range(8))  # Assume 8 GPUs max
        return True, "GPU check successful", available_gpus
    except Exception as e:
        return False, f"GPU check failed: {str(e)}", []


def check_system_requirements() -> Tuple[bool, str]:
    """
    Check if system meets minimum requirements.

    Returns:
        Tuple of (success, message)
    """
    try:
        # Check CPU
        cpu_count = psutil.cpu_count(logical=False)
        if cpu_count < 4:
            return False, "Minimum 4 CPU cores required"

        # Check RAM
        memory = psutil.virtual_memory()
        if memory.total < (16 * 1024 * 1024 * 1024):  # 16 GB
            return False, "Minimum 16GB RAM required"

        # Check disk space
        disk = psutil.disk_usage("/")
        if disk.free < (50 * 1024 * 1024 * 1024):  # 50 GB
            return False, "Minimum 50GB free disk space required"

        return True, "System meets minimum requirements"

    except Exception as e:
        return False, f"System check failed: {str(e)}"


def validate_input_files(file_paths: List[Union[str, Path]]) -> Tuple[bool, str]:
    """
    Validate input files for AreTomo3 processing.

    Args:
        file_paths: List of paths to input files

    Returns:
        Tuple of (success, message)
    """
    try:
        if not file_paths:
            return False, "No input files provided"

        for path in file_paths:
            path = Path(path)
            if not path.exists():
                return False, f"File not found: {path}"
            if not path.is_file():
                return False, f"Not a file: {path}"

            # Check file extension
            valid_extensions = {".mrc", ".mrcs", ".st", ".tif", ".tiff", ".eer"}
            if path.suffix.lower() not in valid_extensions:
                return False, f"Unsupported file type for {path}"

        return True, "All input files valid"

    except Exception as e:
        return False, f"Validation error: {str(e)}"


def validate_output_directory(directory: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate output directory for AreTomo3 processing.

    Args:
        directory: Path to output directory

    Returns:
        Tuple of (success, message)
    """
    try:
        path = Path(directory)

        # Check if directory exists
        if path.exists():
            if not path.is_dir():
                return False, f"Path exists but is not a directory: {path}"

            # Check if directory is writable
            if not os.access(path, os.W_OK):
                return False, f"Directory is not writable: {path}"
        else:
            # Check if parent directory exists and is writable
            parent = path.parent
            if not parent.exists():
                return False, f"Parent directory does not exist: {parent}"
            if not parent.is_dir():
                return False, f"Parent path is not a directory: {parent}"
            if not os.access(parent, os.W_OK):
                return False, f"Cannot create directory in: {parent}"

        return True, "Output directory is valid"

    except Exception as e:
        return False, f"Validation error: {str(e)}"
