#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Configuration Management
Comprehensive configuration system with validation, versioning, and profiles.
"""

import copy
import hashlib
import json
import logging
from dataclasses import asdict, dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml

# Schema validation imports
try:
    import jsonschema

    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ConfigProfile:
    """Configuration profile."""

    name: str
    description: str
    config_data: Dict[str, Any]
    created_at: datetime
    modified_at: datetime
    version: str
    checksum: str
    is_default: bool = False


@dataclass
class ConfigHistory:
    """Configuration change history."""

    timestamp: datetime
    profile_name: str
    change_type: str  # created, modified, deleted, activated
    changes: Dict[str, Any]
    user: str
    description: str


class AdvancedConfigManager:
    """
    Advanced configuration manager with profiles, validation, and versioning.
    Provides comprehensive configuration management for AreTomo3 GUI.
    """

    def __init__(self, config_dir: Path = None):
        """Initialize the advanced configuration manager."""
        self.config_dir = config_dir or Path.home() / ".aretomo3_gui" / "config"
        self.config_dir.mkdir(parents=True, exist_ok=True)

        # Configuration profiles
        self.profiles: Dict[str, ConfigProfile] = {}
        self.active_profile: Optional[str] = None

        # Configuration history
        self.history: List[ConfigHistory] = []

        # Configuration schema for validation
        self.config_schema = self._create_config_schema()

        # File paths
        self.profiles_file = self.config_dir / "profiles.json"
        self.history_file = self.config_dir / "history.json"
        self.active_profile_file = self.config_dir / "active_profile.txt"

        # Load existing configuration
        self._load_profiles()
        self._load_history()
        self._load_active_profile()

        # Create default profile if none exist
        if not self.profiles:
            self._create_default_profile()

        logger.info(
            f"Advanced Config Manager initialized - {len(self.profiles)} profiles loaded"
        )

    def _create_config_schema(self) -> Dict[str, Any]:
        """Create JSON schema for configuration validation."""
        return {
            "type": "object",
            "properties": {
                "processing": {
                    "type": "object",
                    "properties": {
                        "aretomo3_path": {"type": "string"},
                        "gpu_ids": {"type": "array", "items": {"type": "integer"}},
                        "num_threads": {"type": "integer", "minimum": 1, "maximum": 64},
                        "memory_limit_gb": {
                            "type": "number",
                            "minimum": 1,
                            "maximum": 1024,
                        },
                        "temp_directory": {"type": "string"},
                        "output_format": {
                            "type": "string",
                            "enum": ["mrc", "tiff", "both"],
                        },
                        "compression": {"type": "boolean"},
                        "backup_enabled": {"type": "boolean"},
                    },
                    "required": ["aretomo3_path"],
                },
                "gui": {
                    "type": "object",
                    "properties": {
                        "theme": {"type": "string", "enum": ["light", "dark", "auto"]},
                        "font_size": {"type": "integer", "minimum": 8, "maximum": 24},
                        "auto_save_interval": {
                            "type": "integer",
                            "minimum": 30,
                            "maximum": 3600,
                        },
                        "show_advanced_options": {"type": "boolean"},
                        "enable_tooltips": {"type": "boolean"},
                        "window_geometry": {
                            "type": "object",
                            "properties": {
                                "width": {"type": "integer", "minimum": 800},
                                "height": {"type": "integer", "minimum": 600},
                                "x": {"type": "integer"},
                                "y": {"type": "integer"},
                            },
                        },
                    },
                },
                "analysis": {
                    "type": "object",
                    "properties": {
                        "auto_generate_plots": {"type": "boolean"},
                        "plot_format": {
                            "type": "string",
                            "enum": ["png", "svg", "pdf", "html"],
                        },
                        "interactive_plots": {"type": "boolean"},
                        "quality_thresholds": {
                            "type": "object",
                            "properties": {
                                "resolution_limit": {
                                    "type": "number",
                                    "minimum": 1,
                                    "maximum": 20,
                                },
                                "cross_correlation_min": {
                                    "type": "number",
                                    "minimum": 0,
                                    "maximum": 1,
                                },
                                "motion_threshold": {
                                    "type": "number",
                                    "minimum": 0,
                                    "maximum": 10,
                                },
                            },
                        },
                    },
                },
                "web_server": {
                    "type": "object",
                    "properties": {
                        "enabled": {"type": "boolean"},
                        "port": {"type": "integer", "minimum": 1024, "maximum": 65535},
                        "host": {"type": "string"},
                        "auto_start": {"type": "boolean"},
                        "cors_enabled": {"type": "boolean"},
                    },
                },
                "security": {
                    "type": "object",
                    "properties": {
                        "encryption_enabled": {"type": "boolean"},
                        "session_timeout": {
                            "type": "integer",
                            "minimum": 5,
                            "maximum": 1440,
                        },
                        "max_failed_attempts": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": 10,
                        },
                        "require_authentication": {"type": "boolean"},
                    },
                },
            },
            "required": ["processing", "gui"],
        }

    def create_profile(
        self,
        name: str,
        description: str,
        config_data: Dict[str, Any],
        set_as_default: bool = False,
    ) -> bool:
        """Create a new configuration profile."""
        try:
            # Validate configuration
            if not self._validate_config(config_data):
                logger.error(f"Invalid configuration data for profile: {name}")
                return False

            # Check if profile already exists
            if name in self.profiles:
                logger.error(f"Profile already exists: {name}")
                return False

            # Create profile
            profile = ConfigProfile(
                name=name,
                description=description,
                config_data=copy.deepcopy(config_data),
                created_at=datetime.now(),
                modified_at=datetime.now(),
                version="1.0.0",
                checksum=self._calculate_config_checksum(config_data),
                is_default=set_as_default,
            )

            # If setting as default, unset other defaults
            if set_as_default:
                for existing_profile in self.profiles.values():
                    existing_profile.is_default = False

            self.profiles[name] = profile

            # Save profiles
            self._save_profiles()

            # Add to history
            self._add_history_entry(
                profile_name=name,
                change_type="created",
                changes={"profile_created": True},
                description=f"Created profile: {name}",
            )

            logger.info(f"Created configuration profile: {name}")
            return True

        except Exception as e:
            logger.error(f"Error creating profile: {e}")
            return False

    def update_profile(
        self, name: str, config_data: Dict[str, Any], description: str = None
    ) -> bool:
        """Update an existing configuration profile."""
        try:
            if name not in self.profiles:
                logger.error(f"Profile not found: {name}")
                return False

            # Validate configuration
            if not self._validate_config(config_data):
                logger.error(f"Invalid configuration data for profile: {name}")
                return False

            profile = self.profiles[name]
            old_config = copy.deepcopy(profile.config_data)

            # Calculate changes
            changes = self._calculate_config_changes(old_config, config_data)

            # Update profile
            profile.config_data = copy.deepcopy(config_data)
            profile.modified_at = datetime.now()
            profile.checksum = self._calculate_config_checksum(config_data)

            if description:
                profile.description = description

            # Increment version
            version_parts = profile.version.split(".")
            version_parts[2] = str(int(version_parts[2]) + 1)
            profile.version = ".".join(version_parts)

            # Save profiles
            self._save_profiles()

            # Add to history
            self._add_history_entry(
                profile_name=name,
                change_type="modified",
                changes=changes,
                description=f"Updated profile: {name}",
            )

            logger.info(f"Updated configuration profile: {name}")
            return True

        except Exception as e:
            logger.error(f"Error updating profile: {e}")
            return False

    def delete_profile(self, name: str) -> bool:
        """Delete a configuration profile."""
        try:
            if name not in self.profiles:
                logger.error(f"Profile not found: {name}")
                return False

            # Don't delete if it's the active profile
            if name == self.active_profile:
                logger.error(f"Cannot delete active profile: {name}")
                return False

            # Don't delete if it's the only profile
            if len(self.profiles) == 1:
                logger.error("Cannot delete the only remaining profile")
                return False

            # Delete profile
            del self.profiles[name]

            # Save profiles
            self._save_profiles()

            # Add to history
            self._add_history_entry(
                profile_name=name,
                change_type="deleted",
                changes={"profile_deleted": True},
                description=f"Deleted profile: {name}",
            )

            logger.info(f"Deleted configuration profile: {name}")
            return True

        except Exception as e:
            logger.error(f"Error deleting profile: {e}")
            return False

    def activate_profile(self, name: str) -> bool:
        """Activate a configuration profile."""
        try:
            if name not in self.profiles:
                logger.error(f"Profile not found: {name}")
                return False

            old_profile = self.active_profile
            self.active_profile = name

            # Save active profile
            self._save_active_profile()

            # Add to history
            self._add_history_entry(
                profile_name=name,
                change_type="activated",
                changes={"old_profile": old_profile, "new_profile": name},
                description=f"Activated profile: {name}",
            )

            logger.info(f"Activated configuration profile: {name}")
            return True

        except Exception as e:
            logger.error(f"Error activating profile: {e}")
            return False

    def get_active_config(self) -> Optional[Dict[str, Any]]:
        """Get the active configuration."""
        if self.active_profile and self.active_profile in self.profiles:
            return copy.deepcopy(
                self.profiles[self.active_profile].config_data)
        return None

    def get_profile(self, name: str) -> Optional[ConfigProfile]:
        """Get a specific profile."""
        return self.profiles.get(name)

    def list_profiles(self) -> List[str]:
        """List all profile names."""
        return list(self.profiles.keys())

    def export_profile(
        self, name: str, export_path: Path, format: str = "json"
    ) -> bool:
        """Export a profile to file."""
        try:
            if name not in self.profiles:
                logger.error(f"Profile not found: {name}")
                return False

            profile = self.profiles[name]
            export_data = {
                "profile_info": {
                    "name": profile.name,
                    "description": profile.description,
                    "version": profile.version,
                    "created_at": profile.created_at.isoformat(),
                    "modified_at": profile.modified_at.isoformat(),
                },
                "configuration": profile.config_data,
            }

            if format.lower() == "yaml":
                with open(export_path, "w") as f:
                    yaml.dump(export_data, f, indent=2)
            else:  # JSON
                with open(export_path, "w") as f:
                    json.dump(export_data, f, indent=2)

            logger.info(f"Exported profile {name} to: {export_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting profile: {e}")
            return False

    def import_profile(self, import_path: Path, new_name: str = None) -> bool:
        """Import a profile from file."""
        try:
            # Load file
            if import_path.suffix.lower() in [".yaml", ".yml"]:
                with open(import_path, "r") as f:
                    import_data = yaml.safe_load(f)
            else:  # JSON
                with open(import_path, "r") as f:
                    import_data = json.load(f)

            # Extract profile info and config
            profile_info = import_data.get("profile_info", {})
            config_data = import_data.get("configuration", {})

            # Use provided name or original name
            profile_name = new_name or profile_info.get(
                "name", "imported_profile")

            # Ensure unique name
            original_name = profile_name
            counter = 1
            while profile_name in self.profiles:
                profile_name = f"{original_name}_{counter}"
                counter += 1

            # Create profile
            return self.create_profile(
                name=profile_name,
                description=profile_info.get(
    "description", "Imported profile"),
                config_data=config_data,
            )

        except Exception as e:
            logger.error(f"Error importing profile: {e}")
            return False

    def _validate_config(self, config_data: Dict[str, Any]) -> bool:
        """Validate configuration against schema."""
        if not JSONSCHEMA_AVAILABLE:
            logger.warning("jsonschema not available - skipping validation")
            return True

        try:
            jsonschema.validate(config_data, self.config_schema)
            return True
        except jsonschema.ValidationError as e:
            logger.error(f"Configuration validation error: {e.message}")
            return False
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return False

    def _calculate_config_checksum(self, config_data: Dict[str, Any]) -> str:
        """Calculate checksum for configuration data."""
        try:
            config_str = json.dumps(config_data, sort_keys=True)
            return hashlib.sha256(config_str.encode()).hexdigest()[:16]
        except Exception as e:
            logger.error(f"Error calculating checksum: {e}")
            return ""

    def _calculate_config_changes(
        self, old_config: Dict[str, Any], new_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate changes between configurations."""
        changes = {}

        def compare_dicts(old_dict, new_dict, path=""):
            """Execute compare_dicts operation."""
            for key in set(old_dict.keys()) | set(new_dict.keys()):
                current_path = f"{path}.{key}" if path else key

                if key not in old_dict:
                    changes[f"added.{current_path}"] = new_dict[key]
                elif key not in new_dict:
                    changes[f"removed.{current_path}"] = old_dict[key]
                elif isinstance(old_dict[key], dict) and isinstance(
                    new_dict[key], dict
                ):
                    compare_dicts(old_dict[key], new_dict[key], current_path)
                elif old_dict[key] != new_dict[key]:
                    changes[f"changed.{current_path}"] = {
                        "old": old_dict[key],
                        "new": new_dict[key],
                    }

        try:
            compare_dicts(old_config, new_config)
        except Exception as e:
            logger.error(f"Error calculating changes: {e}")
            changes["error"] = str(e)

        return changes

    def _create_default_profile(self):
        """Create default configuration profile."""
        default_config = {
            "processing": {
                "aretomo3_path": "/usr/local/bin/AreTomo3",
                "gpu_ids": [0],
                "num_threads": 4,
                "memory_limit_gb": 8.0,
                "temp_directory": "/tmp/aretomo3",
                "output_format": "mrc",
                "compression": False,
                "backup_enabled": True,
            },
            "gui": {
                "theme": "auto",
                "font_size": 12,
                "auto_save_interval": 300,
                "show_advanced_options": False,
                "enable_tooltips": True,
                "window_geometry": {"width": 1200, "height": 800, "x": 100, "y": 100},
            },
            "analysis": {
                "auto_generate_plots": True,
                "plot_format": "png",
                "interactive_plots": True,
                "quality_thresholds": {
                    "resolution_limit": 5.0,
                    "cross_correlation_min": 0.5,
                    "motion_threshold": 2.0,
                },
            },
            "web_server": {
                "enabled": True,
                "port": 8080,
                "host": "0.0.0.0",
                "auto_start": True,
                "cors_enabled": True,
            },
            "security": {
                "encryption_enabled": False,
                "session_timeout": 60,
                "max_failed_attempts": 3,
                "require_authentication": False,
            },
        }

        self.create_profile(
            name="default",
            description="Default AreTomo3 GUI configuration",
            config_data=default_config,
            set_as_default=True,
        )

        self.activate_profile("default")

    def _add_history_entry(
        self,
        profile_name: str,
        change_type: str,
        changes: Dict[str, Any],
        description: str,
    ):
        """Add entry to configuration history."""
        try:
            history_entry = ConfigHistory(
                timestamp=datetime.now(),
                profile_name=profile_name,
                change_type=change_type,
                changes=changes,
                user="system",  # Could be enhanced with actual user tracking
                description=description,
            )

            self.history.append(history_entry)

            # Limit history size
            if len(self.history) > 1000:
                self.history = self.history[-1000:]

            self._save_history()

        except Exception as e:
            logger.error(f"Error adding history entry: {e}")

    def _load_profiles(self):
        """Load profiles from file."""
        try:
            if self.profiles_file.exists():
                with open(self.profiles_file, "r") as f:
                    profiles_data = json.load(f)

                for profile_data in profiles_data:
                    profile = ConfigProfile(
                        name=profile_data["name"],
                        description=profile_data["description"],
                        config_data=profile_data["config_data"],
                        created_at=datetime.fromisoformat(
                            profile_data["created_at"]),
                        modified_at=datetime.fromisoformat(
                            profile_data["modified_at"]),
                        version=profile_data["version"],
                        checksum=profile_data["checksum"],
                        is_default=profile_data.get("is_default", False),
                    )
                    self.profiles[profile.name] = profile

                logger.info(
                    f"Loaded {len(self.profiles)} configuration profiles")

        except Exception as e:
            logger.error(f"Error loading profiles: {e}")

    def _save_profiles(self):
        """Save profiles to file."""
        try:
            profiles_data = []
            for profile in self.profiles.values():
                profile_dict = asdict(profile)
                profile_dict["created_at"] = profile.created_at.isoformat()
                profile_dict["modified_at"] = profile.modified_at.isoformat()
                profiles_data.append(profile_dict)

            with open(self.profiles_file, "w") as f:
                json.dump(profiles_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving profiles: {e}")

    def _load_history(self):
        """Load configuration history."""
        try:
            if self.history_file.exists():
                with open(self.history_file, "r") as f:
                    history_data = json.load(f)

                for entry_data in history_data:
                    entry = ConfigHistory(
                        timestamp=datetime.fromisoformat(
                            entry_data["timestamp"]),
                        profile_name=entry_data["profile_name"],
                        change_type=entry_data["change_type"],
                        changes=entry_data["changes"],
                        user=entry_data["user"],
                        description=entry_data["description"],
                    )
                    self.history.append(entry)

        except Exception as e:
            logger.error(f"Error loading history: {e}")

    def _save_history(self):
        """Save configuration history."""
        try:
            history_data = []
            for entry in self.history:
                entry_dict = asdict(entry)
                entry_dict["timestamp"] = entry.timestamp.isoformat()
                history_data.append(entry_dict)

            with open(self.history_file, "w") as f:
                json.dump(history_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving history: {e}")

    def _load_active_profile(self):
        """Load active profile name."""
        try:
            if self.active_profile_file.exists():
                with open(self.active_profile_file, "r") as f:
                    self.active_profile = f.read().strip()

        except Exception as e:
            logger.error(f"Error loading active profile: {e}")

    def _save_active_profile(self):
        """Save active profile name."""
        try:
            with open(self.active_profile_file, "w") as f:
                f.write(self.active_profile or "")

        except Exception as e:
            logger.error(f"Error saving active profile: {e}")


# Global configuration manager instance
config_manager = AdvancedConfigManager()


def get_active_config() -> Optional[Dict[str, Any]]:
    """Convenience function to get active configuration."""
    return config_manager.get_active_config()


def update_active_config(config_updates: Dict[str, Any]) -> bool:
    """Convenience function to update active configuration."""
    if config_manager.active_profile:
        current_config = config_manager.get_active_config()
        if current_config:
            # Merge updates recursively
            def deep_merge(base_dict, update_dict):
                """Execute deep_merge operation."""
                for key, value in update_dict.items():
                    if (
                        key in base_dict
                        and isinstance(base_dict[key], dict)
                        and isinstance(value, dict)
                    ):
                        deep_merge(base_dict[key], value)
                    else:
                        base_dict[key] = value

            deep_merge(current_config, config_updates)
            return config_manager.update_profile(
                config_manager.active_profile, current_config
            )
    return False

def create_profile_from_template(template_name: str, new_profile_name: str) -> bool:
    """Create a new profile from a predefined template."""
    templates = {
        "high_performance": {
            "processing": {
                "aretomo3_path": "/usr/local/bin/AreTomo3",
                "gpu_ids": [0, 1, 2, 3],
                "num_threads": 16,
                "memory_limit_gb": 32.0,
                "temp_directory": "/tmp/aretomo3_hp",
                "output_format": "mrc",
                "compression": True,
                "backup_enabled": True,
            },
            "gui": {
                "theme": "dark",
                "font_size": 11,
                "auto_save_interval": 120,
                "show_advanced_options": True,
                "enable_tooltips": True,
            },
            "analysis": {
                "auto_generate_plots": True,
                "plot_format": "html",
                "interactive_plots": True,
                "quality_thresholds": {
                    "resolution_limit": 3.0,
                    "cross_correlation_min": 0.7,
                    "motion_threshold": 1.0,
                },
            },
        },
        "basic": {
            "processing": {
                "aretomo3_path": "/usr/local/bin/AreTomo3",
                "gpu_ids": [0],
                "num_threads": 4,
                "memory_limit_gb": 8.0,
                "temp_directory": "/tmp/aretomo3",
                "output_format": "mrc",
                "compression": False,
                "backup_enabled": False,
            },
            "gui": {
                "theme": "light",
                "font_size": 12,
                "auto_save_interval": 600,
                "show_advanced_options": False,
                "enable_tooltips": True,
            },
            "analysis": {
                "auto_generate_plots": False,
                "plot_format": "png",
                "interactive_plots": False,
                "quality_thresholds": {
                    "resolution_limit": 8.0,
                    "cross_correlation_min": 0.3,
                    "motion_threshold": 5.0,
                },
            },
        },
        "tomography_focused": {
            "processing": {
                "aretomo3_path": "/usr/local/bin/AreTomo3",
                "gpu_ids": [0, 1],
                "num_threads": 8,
                "memory_limit_gb": 16.0,
                "temp_directory": "/tmp/aretomo3_tomo",
                "output_format": "both",
                "compression": True,
                "backup_enabled": True,
            },
            "gui": {
                "theme": "auto",
                "font_size": 12,
                "auto_save_interval": 300,
                "show_advanced_options": True,
                "enable_tooltips": True,
            },
            "analysis": {
                "auto_generate_plots": True,
                "plot_format": "html",
                "interactive_plots": True,
                "quality_thresholds": {
                    "resolution_limit": 5.0,
                    "cross_correlation_min": 0.5,
                    "motion_threshold": 2.0,
                },
            },
            "web_server": {
                "enabled": True,
                "port": 8080,
                "host": "0.0.0.0",
                "auto_start": True,
                "cors_enabled": True,
            },
        },
    }

    if template_name not in templates:
        logger.error(f"Template not found: {template_name}")
        return False

    template_config = templates[template_name]
    description = f"Profile created from {template_name} template"

    return config_manager.create_profile(new_profile_name, description, template_config)
