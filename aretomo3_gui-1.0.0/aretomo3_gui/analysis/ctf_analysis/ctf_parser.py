#!/usr/bin/env python3
"""
CTF Data Parser for AreTomo3 Output

This module parses CTF-related output files from AreTomo3:
- *_CTF.txt: CTF parameters in CTFFind4 format
- *_CTF.mrc: 2D FFT power spectra with CTF fits
- *_CTF_Imod.txt: IMOD-compatible CTF format with tilt angles
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

try:
    import mrcfile

    HAS_MRCFILE = True
except ImportError:
    HAS_MRCFILE = False
    logging.warning("mrcfile not available - MRC reading will be limited")

logger = logging.getLogger(__name__)


class CTFDataParser:
    """
    Parser for AreTomo3 CTF output files.

    Handles parsing of:
    - CTF parameters from text files
    - 2D FFT power spectra from MRC files
    - Tilt angle information
    """

    def __init__(self, base_path: Union[str, Path]):
        """
        Initialize CTF parser.

        Args:
            base_path: Path to AreTomo3 output directory or base filename
        """
        self.base_path = Path(base_path)
        self.ctf_data = {}
        self.power_spectra = None
        self.tilt_angles = []
        self.series_name = ""

        # Determine series name from path
        if self.base_path.is_dir():
            # Directory path - look for CTF files
            self._find_ctf_files_in_directory()
        else:
            # File path - extract base name
            self.series_name = self.base_path.stem.replace("_CTF", "")
            self.base_path = self.base_path.parent

    def _find_ctf_files_in_directory(self):
        """Find CTF files in the given directory."""
        ctf_files = list(self.base_path.glob("*_CTF.txt"))
        if ctf_files:
            # Use the first CTF file found
            ctf_file = ctf_files[0]
            self.series_name = ctf_file.stem.replace("_CTF", "")
        else:
            raise FileNotFoundError(f"No CTF files found in {self.base_path}")

    def parse_all(self) -> Dict:
        """
        Parse all CTF-related files.

        Returns:
            Dictionary containing all parsed CTF data
        """
        logger.info(f"Parsing CTF data for series: {self.series_name}")

        # Parse CTF parameters
        self.parse_ctf_parameters()

        # Parse power spectra
        self.parse_power_spectra()

        # Parse tilt angles
        self.parse_tilt_angles()

        # Combine all data
        return self.get_combined_data()

    def parse_ctf_parameters(self) -> pd.DataFrame:
        """
        Parse CTF parameters from *_CTF.txt file.

        Returns:
            DataFrame with CTF parameters
        """
        ctf_file = self.base_path / f"{self.series_name}_CTF.txt"

        if not ctf_file.exists():
            raise FileNotFoundError(f"CTF file not found: {ctf_file}")

        logger.info(f"Parsing CTF parameters from: {ctf_file}")

        # Read CTF file
        with open(ctf_file, "r") as f:
            lines = f.readlines()

        # Skip header and empty lines
        data_lines = [
            line.strip() for line in lines if line.strip() and not line.startswith("#")
        ]

        # Parse data
        ctf_records = []
        for line in data_lines:
            parts = line.split()
            if len(parts) >= 8:
                record = {
                    "micrograph_id": int(parts[0]),
                    "defocus1_A": float(parts[1]),
                    "defocus2_A": float(parts[2]),
                    "astigmatism_angle": float(parts[3]),
                    "phase_shift_rad": float(parts[4]),
                    "cross_correlation": float(parts[5]),
                    "resolution_limit_A": float(parts[6]),
                    "handedness": int(parts[7]),
                }
                ctf_records.append(record)

        self.ctf_data["parameters"] = pd.DataFrame(ctf_records)
        logger.info(f"Parsed {len(ctf_records)} CTF parameter records")

        return self.ctf_data["parameters"]

    def parse_power_spectra(self) -> Optional[np.ndarray]:
        """
        Parse 2D FFT power spectra from *_CTF.mrc file.

        Returns:
            3D numpy array (n_tilts, height, width) or None if file not found
        """
        if not HAS_MRCFILE:
            logger.warning("mrcfile not available - cannot read power spectra")
            return None

        ctf_mrc_file = self.base_path / f"{self.series_name}_CTF.mrc"

        if not ctf_mrc_file.exists():
            logger.warning(f"CTF MRC file not found: {ctf_mrc_file}")
            return None

        logger.info(f"Reading power spectra from: {ctf_mrc_file}")

        try:
            with mrcfile.open(ctf_mrc_file, mode="r") as mrc:
                self.power_spectra = np.array(mrc.data)

                # Handle different MRC formats
                if self.power_spectra.ndim == 2:
                    # Single image - add dimension
                    self.power_spectra = self.power_spectra[np.newaxis, ...]
                elif self.power_spectra.ndim == 3:
                    # Stack of images - ensure correct orientation
                    pass
                else:
                    raise ValueError(
                        f"Unexpected MRC data dimensions: {
                            self.power_spectra.ndim}"
                    )

                logger.info(
                    f"Loaded power spectra: {
                        self.power_spectra.shape}"
                )

        except Exception as e:
            logger.error(f"Error reading CTF MRC file: {e}")
            self.power_spectra = None

        return self.power_spectra

    def parse_tilt_angles(self) -> List[float]:
        """
        Parse stage tilt angles, prioritizing MDOC file for actual stage tilts.

        Returns:
            List of stage tilt angles
        """
        # First try to get actual stage tilt angles from MDOC file
        mdoc_angles = self._parse_mdoc_tilt_angles()
        if mdoc_angles:
            self.tilt_angles = mdoc_angles
            return self.tilt_angles

        # Fallback to IMOD CTF file
        imod_file = self.base_path / f"{self.series_name}_CTF_Imod.txt"

        if not imod_file.exists():
            logger.warning(f"IMOD CTF file not found: {imod_file}")
            # Try to get tilt angles from TLT file
            return self._parse_tlt_file()

        logger.info(f"Parsing tilt angles from: {imod_file}")

        tilt_angles = []
        with open(imod_file, "r") as f:
            lines = f.readlines()

        # Skip header lines and parse data
        for line in lines[1:]:  # Skip first line (header)
            parts = line.strip().split()
            if len(parts) >= 5:
                tilt_angle = float(parts[4])  # Tilt angle is in column 5
                tilt_angles.append(tilt_angle)

        self.tilt_angles = tilt_angles
        logger.info(f"Parsed {len(tilt_angles)} tilt angles")

        return self.tilt_angles

    def _parse_tlt_file(self) -> List[float]:
        """Parse tilt angles from *_TLT.txt file as fallback."""
        tlt_file = self.base_path / f"{self.series_name}_TLT.txt"

        if not tlt_file.exists():
            logger.warning(f"TLT file not found: {tlt_file}")
            # Try to parse from MDOC file as final fallback
            return self._parse_mdoc_tilt_angles()

        logger.info(f"Parsing tilt angles from TLT file: {tlt_file}")

        tilt_angles = []
        with open(tlt_file, "r") as f:
            for line in f:
                parts = line.strip().split()
                if parts:
                    tilt_angles.append(float(parts[0]))

        self.tilt_angles = tilt_angles
        return self.tilt_angles

    def _parse_mdoc_tilt_angles(self) -> List[float]:
        """Parse actual stage tilt angles from MDOC file."""
        # Look for MDOC file in the original input directory
        possible_mdoc_paths = [
            self.base_path / f"{self.series_name}.mdoc",
            self.base_path.parent / f"{self.series_name}" / f"{self.series_name}.mdoc",
            self.base_path.parent
            / "Test_Input_1"
            / f"{self.series_name.replace('tomo25', 'tomo24')}.mdoc",
            self.base_path.parent / f"{self.series_name}" / f"{self.series_name}.mdoc",
        ]

        mdoc_file = None
        for path in possible_mdoc_paths:
            if path.exists():
                mdoc_file = path
                break

        if not mdoc_file:
            logger.warning(f"MDOC file not found for {self.series_name}")
            return []

        logger.info(f"Parsing stage tilt angles from MDOC file: {mdoc_file}")

        tilt_angles = []
        try:
            with open(mdoc_file, "r") as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line.startswith("TiltAngle = "):
                    # Extract tilt angle value
                    tilt_value = float(line.split("=")[1].strip())
                    tilt_angles.append(tilt_value)

            logger.info(
                f"Parsed {
                    len(tilt_angles)} stage tilt angles from MDOC"
            )
            self.tilt_angles = tilt_angles
            return tilt_angles

        except Exception as e:
            logger.error(f"Error parsing MDOC file: {e}")
            return []

    def get_combined_data(self) -> Dict:
        """
        Get all parsed data in a combined dictionary.

        Returns:
            Dictionary with all CTF data
        """
        # Add tilt angles to parameters if available
        if hasattr(self, "ctf_data") and "parameters" in self.ctf_data:
            if self.tilt_angles and len(self.tilt_angles) == len(
                self.ctf_data["parameters"]
            ):
                self.ctf_data["parameters"]["tilt_angle"] = self.tilt_angles

        return {
            "series_name": self.series_name,
            "base_path": str(self.base_path),
            "parameters": self.ctf_data.get("parameters", pd.DataFrame()),
            "power_spectra": self.power_spectra,
            "tilt_angles": self.tilt_angles,
            "n_tilts": len(self.tilt_angles) if self.tilt_angles else 0,
            "has_power_spectra": self.power_spectra is not None,
        }

    def get_ctf_summary(self) -> Dict:
        """
        Get summary statistics of CTF data.

        Returns:
            Dictionary with summary statistics
        """
        if "parameters" not in self.ctf_data or self.ctf_data["parameters"].empty:
            return {}

        df = self.ctf_data["parameters"]

        summary = {
            "n_tilts": len(df),
            "defocus_range_um": (
                df["defocus1_A"].min() / 10000,
                df["defocus1_A"].max() / 10000,
            ),
            "mean_defocus_um": df["defocus1_A"].mean() / 10000,
            "resolution_range_A": (
                df["resolution_limit_A"].min(),
                df["resolution_limit_A"].max(),
            ),
            "mean_resolution_A": df["resolution_limit_A"].mean(),
            "mean_cross_correlation": df["cross_correlation"].mean(),
            "astigmatism_present": (df["defocus1_A"] - df["defocus2_A"]).abs().mean()
            > 100,  # >100Å difference
        }

        if self.tilt_angles:
            summary["tilt_range"] = (min(self.tilt_angles), max(self.tilt_angles))

        return summary


def test_ctf_parser():
    """Test function for CTF parser."""
    import sys

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        # Use default test path
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        parser = CTFDataParser(test_path)
        data = parser.parse_all()

        logger.info(f"Successfully parsed CTF data for: {data['series_name']}")
        logger.info(f"Number of tilts: {data['n_tilts']}")
        logger.info(f"Has power spectra: {data['has_power_spectra']}")

        if not data["parameters"].empty:
            logger.info("\nCTF Summary:")
            summary = parser.get_ctf_summary()
            for key, value in summary.items():
                logger.info(f"  {key}: {value}")

            logger.info(f"\nFirst few CTF parameters:")
            logger.info(data["parameters"].head())

        if data["power_spectra"] is not None:
            logger.info(
                f"\nPower spectra shape: {
                    data['power_spectra'].shape}"
            )
            logger.info(
                f"Power spectra data type: {
                    data['power_spectra'].dtype}"
            )
            logger.info(
                f"Power spectra range: {
                    data['power_spectra'].min():.3f} to {
                    data['power_spectra'].max():.3f}"
            )

        return True

    except Exception as e:
        logger.info(f"Error testing CTF parser: {e}")
        import traceback

        traceback.print_exc()
        return False

    # TODO: Refactor parse_ctf_data - complexity: 14 (target: <10)
    # TODO: Refactor function - Function 'parse_ctf_data' too long (128 lines)


def parse_ctf_data(results_dir):
    """
    Parse CTF data from AreTomo3 results directory.

    Args:
        results_dir: Path to AreTomo3 results directory

    Returns:
        Dictionary containing parsed CTF data including power spectra
    """
    try:
        results_path = Path(results_dir)

        # Initialize CTF data structure
        ctf_data = {
            "power_spectra": {},
            "ctf_parameters": {},
            "quality_metrics": {},
            "tilt_angles": [],
            "defocus_values": [],
            "resolution_estimates": [],
        }

        # Look for CTF-related files recursively
        ctf_files = []

        # Common CTF file patterns from AreTomo3
        patterns = [
            "*_CTF.txt",
            "*_ctf.txt",
            "*CTF*.txt",
            "*_CTF_Imod.txt",  # IMOD format CTF files
            "*_PS.mrc",  # Power spectra
            "*_ps.mrc",
            "*_CTF.mrc",  # CTF MRC files
        ]

        # Search recursively through all subdirectories
        for pattern in patterns:
            # Use rglob for recursive search
            ctf_files.extend(results_path.rglob(pattern))

        if not ctf_files:
            logger.warning(f"No CTF files found recursively in {results_dir}")
            # Also log what directories were searched
            subdirs = [d for d in results_path.rglob("*") if d.is_dir()]
            logger.info(
                f"Searched {
                    len(subdirs)} subdirectories in {results_dir}"
            )
            return ctf_data

        logger.info(
            f"Found {
                len(ctf_files)} CTF files recursively in {results_dir}"
        )

        # Parse each CTF file
        for ctf_file in ctf_files:
            if ctf_file.suffix.lower() == ".txt":
                # Parse text-based CTF parameters
                try:
                    parser = CTFDataParser(ctf_file)
                    parsed_data = parser.parse_all()
                    if parsed_data:
                        series_name = parsed_data["series_name"]
                        ctf_data["ctf_parameters"][series_name] = parsed_data

                        # Extract quality metrics
                        if not parsed_data["parameters"].empty:
                            df = parsed_data["parameters"]
                            ctf_data["quality_metrics"][series_name] = {
                                "cc": df["cross_correlation"].mean(),
                                "resolution": df["resolution_limit_A"].mean(),
                                "defocus_u": df["defocus1_A"].mean(),
                                "defocus_v": df["defocus2_A"].mean(),
                            }
                except Exception as e:
                    logger.error(f"Error parsing CTF file {ctf_file}: {e}")

            elif ctf_file.suffix.lower() == ".mrc":
                # Parse power spectrum files
                try:
                    if HAS_MRCFILE:
                        import mrcfile

                        with mrcfile.open(ctf_file, mode="r") as mrc:
                            power_spectrum = mrc.data
                            series_name = ctf_file.stem.replace("_PS", "").replace(
                                "_ps", ""
                            )
                            ctf_data["power_spectra"][series_name] = power_spectrum
                    else:
                        logger.warning(
                            "mrcfile not available for power spectrum parsing"
                        )

                except Exception as e:
                    logger.error(f"Error reading power spectrum {ctf_file}: {e}")

        # Extract summary statistics
        if ctf_data["quality_metrics"]:
            all_cc = [
                metrics["cc"]
                for metrics in ctf_data["quality_metrics"].values()
                if "cc" in metrics
            ]
            all_defocus = [
                metrics["defocus_u"]
                for metrics in ctf_data["quality_metrics"].values()
                if "defocus_u" in metrics
            ]

            ctf_data["summary"] = {
                "mean_cc": sum(all_cc) / len(all_cc) if all_cc else 0,
                "mean_defocus": (
                    sum(all_defocus) / len(all_defocus) if all_defocus else 0
                ),
                "series_count": len(ctf_data["quality_metrics"]),
            }

        logger.info(
            f"Parsed CTF data for {len(ctf_data['ctf_parameters'])} series from {results_dir}"
        )
        return ctf_data

    except Exception as e:
        logger.error(f"Error parsing CTF data from {results_dir}: {e}")
        return {
            "power_spectra": {},
            "ctf_parameters": {},
            "quality_metrics": {},
            "error": str(e),
        }


if __name__ == "__main__":
    test_ctf_parser()
