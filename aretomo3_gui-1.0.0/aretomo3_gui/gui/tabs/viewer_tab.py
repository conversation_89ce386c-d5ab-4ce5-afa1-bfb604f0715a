"""
Viewer tab manager for AreTomo3 GUI.
Handles the MRC file viewer tab.
"""

import logging
from typing import Optional

from PyQt6.QtWidgets import Q<PERSON>oxLayout, QWidget

from ..viewers.mrc_viewer import MR<PERSON>Viewer

logger = logging.getLogger(__name__)


class ViewerTabManager:
    """Manages the viewer tab setup and functionality."""

    def __init__(self, main_window):
        """Initialize the viewer tab manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        self.mrc_viewer: Optional[MRCViewer] = None

    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the viewer tab for MRC file viewing.

        Args:
            tab_widget: The parent widget for the viewer tab
            layout: The layout to add widgets to
        """
        try:
            self.mrc_viewer = MRCViewer(tab_widget)
            layout.addWidget(self.mrc_viewer)
            logger.info("MRC viewer tab setup completed successfully")

        except Exception as e:
            logger.error(
                f"Error setting up viewer tab: {
                    str(e)}",
                exc_info=True,
            )
            raise

    def get_mrc_viewer(self) -> Optional[MRCViewer]:
        """Get the MRC viewer instance."""
        return self.mrc_viewer
