"""
Batch tab manager for AreTomo3 GUI.
Handles the batch processing tab with tomogram selection and batch management.
"""

import logging
from typing import Optional

from PyQt6.QtWidgets import Q<PERSON>oxLayout, QWidget

from ..widgets.batch_processing import BatchProcessingWidget

logger = logging.getLogger(__name__)


class BatchTabManager:
    """Manages the batch processing tab setup and functionality."""

    def __init__(self, main_window):
        """Initialize the batch tab manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        self.batch_widget: Optional[BatchProcessingWidget] = None

    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the batch processing tab.

        Args:
            tab_widget: The parent widget for the batch tab
            layout: The layout to add widgets to
        """
        try:
            # Create batch processing widget
            self.batch_widget = BatchProcessingWidget(self.main_window)

            # Connect batch processing signal
            self.batch_widget.start_batch.connect(
                self.main_window.on_start_queue_processing
            )

            layout.addWidget(self.batch_widget)
            logger.info("Batch processing tab setup completed successfully")

        except Exception as e:
            logger.error(
                f"Error setting up batch tab: {
                    str(e)}",
                exc_info=True,
            )
            raise

    def get_batch_widget(self) -> Optional[BatchProcessingWidget]:
        """Get the batch processing widget instance."""
        return self.batch_widget
