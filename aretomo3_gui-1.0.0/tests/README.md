# 🧪 AT3GUI Test Suite

Comprehensive test suite for AreTomo3 GUI with properly organized test files.

## 📁 Directory Structure

```
tests/
├── unit/                       # Fast, isolated unit tests
│   ├── test_eer_reader.py      # EER functionality tests
│   ├── test_file_utils.py      # File utility tests  
│   ├── test_batch_processing.py # Batch processing tests
│   └── test_syntax_verification.py # Code syntax tests
├── integration/                # Slower, system-wide tests
│   ├── test_at3gui_workflow.py # End-to-end GUI tests
│   ├── test_implementation.py  # Implementation verification
│   └── test_progress_tracking.py # Progress tracking tests
├── scripts/                    # Test utility scripts
│   ├── test_eer_build.sh       # EerReaderLib build testing
│   ├── test_installation.sh    # Installation verification
│   └── benchmark_eer_processing.sh # Performance benchmarks
├── fixtures/                   # Test data (placeholder)
├── conftest.py                 # Pytest configuration & fixtures
└── README.md                   # This file
```

## 🚀 Quick Start

### Run All Tests
```bash
# Run the complete test suite
python -m pytest tests/ -v

# Run with coverage report
python -m pytest tests/ --cov=aretomo3_gui --cov-report=html
```

### Run Specific Test Categories
```bash
# Unit tests (fast)
python -m pytest tests/unit/ -v

# Integration tests (slower)
python -m pytest tests/integration/ -v

# EER-specific tests only
python -m pytest tests/ -m eer -v

# GUI tests (require display)
python -m pytest tests/ -m gui -v

# Non-GUI tests only
python -m pytest tests/ -m "not gui" -v
```

### Run Test Scripts
```bash
# Test EerReaderLib build capability
./tests/scripts/test_eer_build.sh

# Verify installation completeness
./tests/scripts/test_installation.sh

# Run performance benchmarks
./tests/scripts/benchmark_eer_processing.sh
```

## 🏷️ Test Markers

Tests are categorized using pytest markers:

| Marker | Description | Usage |
|--------|-------------|--------|
| `unit` | Fast, isolated tests | `pytest -m unit` |
| `integration` | System-wide tests | `pytest -m integration` |
| `eer` | EER-specific functionality | `pytest -m eer` |
| `gui` | Tests requiring display | `pytest -m gui` |
| `slow` | Long-running tests | `pytest -m "not slow"` |
| `utils` | Utility module tests | `pytest -m utils` |

### Combining Markers
```bash
# Run unit tests excluding slow ones
python -m pytest -m "unit and not slow" -v

# Run EER tests that don't require GUI
python -m pytest -m "eer and not gui" -v

# Run all tests except GUI and slow
python -m pytest -m "not gui and not slow" -v
```

## 📊 Available Tests

### Unit Tests (`tests/unit/`)
- **`test_eer_reader.py`** - EER file reading, metadata extraction
- **`test_file_utils.py`** - File detection, validation, analysis  
- **`test_batch_processing.py`** - Batch processing attribute fixes
- **`test_syntax_verification.py`** - Python syntax validation

### Integration Tests (`tests/integration/`)
- **`test_at3gui_workflow.py`** - End-to-end GUI workflows
- **`test_implementation.py`** - Implementation verification
- **`test_progress_tracking.py`** - Progress tracking systems

### Test Scripts (`tests/scripts/`)
- **`test_eer_build.sh`** - Test EerReaderLib building capability
- **`test_installation.sh`** - Verify installation completeness
- **`benchmark_eer_processing.sh`** - Performance benchmarking

## 🔧 Test Configuration

### Pytest Configuration (`../pytest.ini`)
- Custom test markers
- Coverage settings
- Test discovery patterns
- Timeout settings

### Shared Fixtures (`conftest.py`)
- `sample_eer_metadata` - Mock EER file metadata
- `mock_eer_file` - Temporary EER file for testing
- `sample_directory` - Sample dataset structure
- `qapp` - QApplication for GUI tests
- `performance_timer` - Benchmarking utilities

## 📈 Coverage Reports

```bash
# HTML coverage report
python -m pytest tests/ --cov=aretomo3_gui --cov-report=html

# Terminal coverage report
python -m pytest tests/ --cov=aretomo3_gui --cov-report=term-missing
```

## 📚 Test Migration

**ℹ️ Note:** Test files have been moved from the root directory to this organized structure for better maintainability and discoverability.

### Migration Benefits:
- ✅ **Professional organization** - Industry-standard test structure
- ✅ **Clear categorization** - Unit vs integration vs performance tests  
- ✅ **Easy discovery** - All tests in one location
- ✅ **Better tooling** - Proper pytest integration with markers and fixtures
- ✅ **Improved documentation** - Comprehensive test guides

### Cleanup Old Files:
If you still see old test files in the root directory, run:
```bash
./cleanup_old_tests.sh
```

## 🐛 Troubleshooting

### Common Issues

#### GUI Tests Failing
```bash
# Run without GUI tests
python -m pytest tests/ -m "not gui" -v

# Use virtual display (Linux)
xvfb-run python -m pytest tests/ -v
```

#### Import Errors
```bash
# Test basic imports
python -c "import aretomo3_gui; print('OK')"

# Run syntax verification
python -m pytest tests/unit/test_syntax_verification.py -v
```

#### EER Tests Failing
```bash
# Check EER support
python -c "from aretomo3_gui.utils.eer_reader import is_eer_supported; print(is_eer_supported())"

# Test EER build capability
./tests/scripts/test_eer_build.sh
```

---

**Run `python -m pytest tests/` to execute the complete test suite!** 🧪✨

For more information, see the main [README.md](../README.md) and [documentation](../docs/).
