"""
Pytest configuration and shared fixtures for AT3GUI tests.
"""

import pytest
import tempfile
import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent

# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers",
        "unit: marks tests as unit tests (fast, isolated)"
    )
    config.addinivalue_line(
        "markers",
        "integration: marks tests as integration tests (slower, system-wide)"
    )
    config.addinivalue_line(
        "markers",
        "eer: marks tests as EER-specific tests"
    )
    config.addinivalue_line(
        "markers",
        "gui: marks tests as GUI tests (require display)"
    )
    config.addinivalue_line(
        "markers",
        "slow: marks tests as slow running"
    )
    config.addinivalue_line(
        "markers",
        "utils: marks tests for utility modules"
    )
    config.addinivalue_line(
        "markers",
        "asyncio: marks tests as async tests"
    )

def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file location."""
    for item in items:
        # Add markers based on file path
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Add EER marker for EER-related tests
        if "eer" in str(item.fspath) or "eer" in item.name.lower():
            item.add_marker(pytest.mark.eer)

        # Add GUI marker for GUI tests
        if "gui" in str(item.fspath) or "gui" in item.name.lower():
            item.add_marker(pytest.mark.gui)

# Skip GUI tests if no display
def pytest_runtest_setup(item):
    """Setup hook to skip GUI tests when no display available."""
    if item.get_closest_marker("gui"):
        has_display = (
            os.environ.get('DISPLAY') is not None or
            sys.platform == 'darwin' or
            sys.platform == 'win32'
        )
        if not has_display:
            pytest.skip("GUI tests require display")

class MockSystemMonitorThread:
    """Mock system monitor thread that doesn't actually start threads."""
    def __init__(self, *args, **kwargs):
        """Initialize the instance."""
        self.is_running = False
        self._daemon = True

    @property
    def daemon(self):
        """Execute daemon operation."""
        return self._daemon

    @daemon.setter
    def daemon(self, value):
        """Execute daemon operation."""
        self._daemon = value

    def start(self):
        """Execute start operation."""
        self.is_running = True

    def stop(self):
        """Execute stop operation."""
        self.is_running = False

    def join(self, timeout=None):
        """Execute join operation."""
        pass

    def is_alive(self):
        """Execute is_alive operation."""
        return self.is_running

    def get_latest_data(self):
        """Mock method to return system monitoring data."""
        return {
            'cpu_percent': 50.0,
            'memory_percent': 60.0,
            'disk_percent': 30.0,
            'gpu_usage': 40.0,
            'timestamp': 1234567890
        }

class MockThreadManager:
    """Mock thread manager that doesn't actually manage threads."""
    def __init__(self):
        """Initialize the instance."""
        pass

    def start(self):
        """Execute start operation."""
        pass

    def stop(self):
        """Execute stop operation."""
        pass

class MockResourceMonitor:
    """Mock resource monitor that provides static values."""
    def __init__(self):
        """Initialize the instance."""
        self.cpu_percent = 50.0
        self.memory_percent = 50.0
        self.disk_percent = 30.0

    def check_memory(self):
        """Simulates checking memory and returns a warning if usage is high."""
        if self.memory_percent > 80:
            return "Memory usage is high!"
        return None

    def check_disk(self, path):
        """Simulates checking disk and returns a warning if usage is high."""
        if self.disk_percent > 80:
            return f"Disk usage for {path} is high!"
        return None

    def get_cpu_usage(self):
        """Execute get_cpu_usage operation."""
        return self.cpu_percent

    def get_memory_usage(self):
        """Execute get_memory_usage operation."""
        return self.memory_percent

    def get_disk_usage(self):
        """Execute get_disk_usage operation."""
        return self.disk_percent

@pytest.fixture(autouse=True)
def mock_system_monitoring():
    """Automatically mock system monitoring for all tests."""
    patches = [
        # Mock system monitor classes
        patch(
            'aretomo3_gui.core.system_monitor.SystemMonitor',
            MockSystemMonitorThread
        ),
        patch('aretomo3_gui.core.system_monitor.GPUMonitor', MockSystemMonitorThread),
        patch(
            'aretomo3_gui.gui.widgets.unified_processing_monitor.SystemMonitorThread',
            MockSystemMonitorThread
        ),
        patch(
            'aretomo3_gui.gui.widgets.processing_dashboard.SystemMonitorThread',
            MockSystemMonitorThread
        ),

        # Mock core managers
        patch('aretomo3_gui.core.thread_manager.ThreadManager', MockThreadManager),
        patch(
            'aretomo3_gui.core.resource_manager.ResourceMonitor',
            MockResourceMonitor
        ),
        patch(
            'aretomo3_gui.core.resource_manager.get_resource_monitor',
            return_value=MockResourceMonitor()
        ),
        patch(
            'aretomo3_gui.core.thread_manager.get_thread_manager',
            return_value=MockThreadManager()
        ),

        # Mock psutil functions
        patch('psutil.cpu_percent', return_value=50.0),
        patch('psutil.virtual_memory', return_value=Mock(percent=50.0)),
        patch('psutil.disk_usage', return_value=Mock(percent=30.0)),
        patch(
            'psutil.Process',
            return_value=Mock(memory_info=Mock(return_value=Mock(rss=1024*1024)))
        ),

        # Mock threading to prevent actual thread creation
        patch('threading.Thread', MockSystemMonitorThread),
    ]

    started_patches = []
    for p in patches:
        try:
            p.start()
            started_patches.append(p)
        except Exception:
            # Skip patches that can't be applied
            pass

    yield

    for p in started_patches:
        try:
            p.stop()
        except Exception:
            pass

# Global fixtures
@pytest.fixture(scope="session")
def test_data_dir():
    """Provide path to test data directory."""
    return Path(__file__).parent / "fixtures"

@pytest.fixture(scope="session")
def temp_dir():
    """Provide a temporary directory for test session."""
    with tempfile.TemporaryDirectory(prefix="at3gui_test_") as tmpdir:
        yield Path(tmpdir)

@pytest.fixture
def sample_eer_metadata():
    """Sample EER metadata for testing."""
    return {
        'filename': 'test_sample.eer',
        'file_size': 1024000,
        'frames': 40,
        'camera': 'Falcon 4i',
        'voltage': 300,
        'dose_rate': 1.5,
        'pixel_size': 0.8,
        'dimensions': (4096, 4096),
        'compression': 'lossless',
        'acquisition_time': '2024-01-15T10:30:00',
        'total_dose': 60.0,
    }

@pytest.fixture
def mock_eer_file(tmp_path):
    """Create a mock EER file for testing."""
    eer_file = tmp_path / "mock_sample.eer"

    # Create file with some realistic size
    content = b"mock eer file content " * 1000
    eer_file.write_bytes(content)

    return str(eer_file)

@pytest.fixture
def mock_mrc_file(tmp_path):
    """Create a mock MRC file for testing."""
    mrc_file = tmp_path / "mock_sample.mrc"

    # Create file with some content
    content = b"mock mrc file content " * 500
    mrc_file.write_bytes(content)

    return str(mrc_file)

@pytest.fixture
def mock_tiff_file(tmp_path):
    """Create a mock TIFF file for testing."""
    tiff_file = tmp_path / "mock_sample.tif"

    # Create file with some content
    content = b"mock tiff file content " * 300
    tiff_file.write_bytes(content)

    return str(tiff_file)

@pytest.fixture
def sample_file_set(tmp_path):
    """Create a set of sample files for testing."""
    files = {}

    # Create different file types
    files['eer'] = tmp_path / "sample.eer"
    files['eer'].write_bytes(b"eer content " * 1000)

    files['mrc'] = tmp_path / "sample.mrc"
    files['mrc'].write_bytes(b"mrc content " * 800)

    files['tiff'] = tmp_path / "sample.tif"
    files['tiff'].write_bytes(b"tiff content " * 600)

    files['unsupported'] = tmp_path / "sample.txt"
    files['unsupported'].write_text("text content")

    return {k: str(v) for k, v in files.items()}

@pytest.fixture
def sample_directory(tmp_path):
    """Create a sample directory structure for testing."""
    # Main directory
    main_dir = tmp_path / "sample_dataset"
    main_dir.mkdir()

    # Create files in main directory
    (main_dir / "tilt_001.eer").write_bytes(b"eer data 1 " * 1000)
    (main_dir / "tilt_002.eer").write_bytes(b"eer data 2 " * 1100)
    (main_dir / "tilt_003.eer").write_bytes(b"eer data 3 " * 900)
    (main_dir / "tilt_stack.mrc").write_bytes(b"mrc stack " * 2000)
    (main_dir / "metadata.txt").write_text("experiment metadata")

    # Create subdirectory
    sub_dir = main_dir / "processed"
    sub_dir.mkdir()
    (sub_dir / "aligned_stack.mrc").write_bytes(b"aligned data " * 1500)
    (sub_dir / "reconstruction.rec").write_bytes(b"reconstruction " * 3000)

    return str(main_dir)

@pytest.fixture
def large_file(tmp_path):
    """Create a large file for testing performance."""
    large_file = tmp_path / "large_sample.eer"

    # Create 10MB file
    with open(large_file, 'wb') as f:
        chunk = b"large file content chunk " * 1000  # ~25KB chunk
        for _ in range(400):  # 400 * 25KB = ~10MB
            f.write(chunk)

    return str(large_file)

@pytest.fixture
def mock_eer_reader():
    """Create a mock EER reader for testing."""
    reader = Mock()
    reader.is_eer_supported.return_value = True
    reader.can_read_file.return_value = True
    reader.read_metadata.return_value = {
        'filename': 'mock.eer',
        'file_size': 1000000,
        'frames': 40,
        'camera': 'Falcon 4i',
    }
    return reader

@pytest.fixture
def mock_eer_reader_no_support():
    """Create a mock EER reader without support."""
    reader = Mock()
    reader.is_eer_supported.return_value = False
    reader.can_read_file.return_value = False
    reader.read_metadata.return_value = None
    return reader

# GUI-specific fixtures
@pytest.fixture(scope="session")
def qapp():
    """Create QApplication for GUI tests."""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt, QTimer
        import os
        import atexit

        # Set up environment for headless testing
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        os.environ['QT_LOGGING_RULES'] = '*.debug=false'

        # Check if app already exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            app.setQuitOnLastWindowClosed(False)
            # Set application attributes if available
            try:
                app.setAttribute(
                    Qt.ApplicationAttribute.AA_DisableWindowContextHelpButton,
                    True
                )
            except AttributeError:
                pass  # Attribute not available in this Qt version

            # Register cleanup
            def cleanup_app():
                """Execute cleanup_app operation."""
                try:
                    if app and not app.closingDown():
                        app.quit()
                        app.deleteLater()
                except Exception:
                    pass
            atexit.register(cleanup_app)

        yield app

        # Process any pending events
        try:
            app.processEvents()
        except Exception:
            pass

    except ImportError:
        pytest.skip("PyQt6 not available")

@pytest.fixture
def widget_cleanup(qapp):
    """Fixture to ensure proper widget cleanup after each test."""
    widgets_to_cleanup = []

    def register_widget(widget):
        """Execute register_widget operation."""
        widgets_to_cleanup.append(widget)
        return widget

    yield register_widget

    # Cleanup all widgets
    for widget in widgets_to_cleanup:
        try:
            if hasattr(widget, 'close'):
                widget.close()
            if hasattr(widget, 'deleteLater'):
                widget.deleteLater()
        except Exception:
            pass

    # Process events to ensure cleanup
    try:
        qapp.processEvents()
    except Exception:
        pass

@pytest.fixture
def mock_main_window():
    """Create a mock main window for testing."""
    try:
        from PyQt6.QtWidgets import QMainWindow

        window = Mock(spec=QMainWindow)
        window.windowTitle.return_value = "Mock AT3GUI"
        window.isVisible.return_value = False
        window.width.return_value = 800
        window.height.return_value = 600

        return window
    except ImportError:
        pytest.skip("PyQt6 not available")

# Utility fixtures
@pytest.fixture
def captured_logs():
    """Capture log output during tests."""
    import logging
    from io import StringIO

    log_capture = StringIO()
    handler = logging.StreamHandler(log_capture)

    # Get AT3GUI logger
    logger = logging.getLogger('aretomo3_gui')
    logger.addHandler(handler)
    logger.setLevel(logging.DEBUG)

    yield log_capture

    # Cleanup
    logger.removeHandler(handler)

@pytest.fixture
def mock_file_system(tmp_path):
    """Create a mock file system structure."""
    fs = {
        'root': tmp_path,
        'data': tmp_path / "data",
        'output': tmp_path / "output",
        'temp': tmp_path / "temp",
    }

    # Create directories
    for dir_path in fs.values():
        if isinstance(dir_path, Path):
            dir_path.mkdir(exist_ok=True)

    return fs

@pytest.fixture(autouse=True)
def cleanup_temp_files():
    """Automatically cleanup temporary files after each test."""
    # Before test
    temp_files = []

    yield temp_files

    # After test - cleanup any files added to temp_files list
    for temp_file in temp_files:
        try:
            if os.path.exists(temp_file):
                if os.path.isfile(temp_file):
                    os.unlink(temp_file)
                elif os.path.isdir(temp_file):
                    import shutil
                    shutil.rmtree(temp_file)
        except Exception:
            pass  # Ignore cleanup errors

# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Provide a performance timer for benchmarking."""
    import time

    class Timer:
        """Class Timer implementation."""
        def __init__(self):
        """Initialize the instance."""
            self.start_time = None
            self.end_time = None

        def start(self):
        """Execute start operation."""
            self.start_time = time.perf_counter()

        def stop(self):
        """Execute stop operation."""
            self.end_time = time.perf_counter()

        @property
        def elapsed(self):
        """Execute elapsed operation."""
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None

    return Timer()

# Environment fixtures
@pytest.fixture
def clean_environment():
    """Provide a clean environment for testing."""
    # Save original environment
    orig_env = dict(os.environ)

    # Clean specific variables that might affect tests
    test_vars = [
        'EER_READER_LIB_PATH',
        'AT3GUI_DATA_DIR',
        'AT3GUI_CONFIG_DIR',
    ]

    for var in test_vars:
        if var in os.environ:
            del os.environ[var]

    yield

    # Restore original environment
    os.environ.clear()
    os.environ.update(orig_env)

@pytest.fixture
def thread_manager():
    """Provide a mock thread manager for tests."""
    return MockThreadManager()

@pytest.fixture
def resource_monitor():
    """Provide a mock resource monitor for tests."""
    return MockResourceMonitor()

@pytest.fixture
def qt_application(qapp):
    """Alias for qapp fixture for compatibility with tests."""
    return qapp

@pytest.fixture(scope="session")
def real_test_data():
    """Provide a directory with real test data for integration tests."""
    # Use the sample_data directory from project root
    return PROJECT_ROOT / "sample_data" / "Test_Input_1"

@pytest.fixture(scope="session")
def sample_data_dir():
    """Provide access to the project's sample data directory."""
    return PROJECT_ROOT / "sample_data"
