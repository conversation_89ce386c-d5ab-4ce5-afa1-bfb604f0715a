[build-system]
requires = [
    "setuptools>=45",
    "wheel",
]
build-backend = "setuptools.build_meta"

[project]
name = "aretomo3-gui"
version = "1.0.0"
description = "A comprehensive GUI for AreTomo3 tomographic reconstruction"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AreTomo3 GUI Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AreTomo3 GUI Development Team", email = "<EMAIL>"}
]
keywords = ["cryo-em", "tomography", "reconstruction", "gui", "microscopy"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Image Processing",
]
requires-python = ">=3.8"
dependencies = [
    "PyQt6>=6.4.0",
    "numpy>=1.21.0",
    "matplotlib>=3.5.0",
    "mrcfile>=1.4.0",
    "psutil>=5.8.0",
    "Pillow>=8.3.0",
    "scipy>=1.7.0",
    "h5py>=3.6.0",
    "tifffile>=2021.11.2",
    "imageio>=2.19.0",
    "PyYAML>=6.0",
    "typing-extensions>=4.0.0,<5.0.0",
    # Real-time processing dependencies
    "watchdog>=2.1.0",
    "asyncio-mqtt>=0.11.0",
    # Web interface dependencies
    "fastapi>=0.95.0",
    "uvicorn[standard]>=0.20.0",
    "websockets>=10.4",
    "pydantic>=1.10.0",
    # QR code generation
    "qrcode[pil]>=7.4.0",
    # Enhanced plotting
    "pyqtgraph>=0.13.0",
    # Napari viewer integration
    "napari[all]>=0.4.18",
    "easygui>=0.98.0",
]

[project.optional-dependencies]
# Development dependencies
dev = [
    "pytest>=6.0",
    "pytest-qt>=4.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.900",
    "pre-commit>=2.15.0",
]
# Documentation dependencies
docs = [
    "sphinx>=4.0",
    "sphinx-rtd-theme>=1.0",
    "myst-parser>=0.15",
    "sphinx-autodoc-typehints>=1.12",
]
# Testing dependencies
test = [
    "pytest>=6.0",
    "pytest-qt>=4.0",
    "pytest-cov>=3.0",
    "pytest-mock>=3.6",
]

[project.urls]
Homepage = "https://github.com/your-org/AT3Gui"
Documentation = "https://at3gui.readthedocs.io/"
Repository = "https://github.com/your-org/AT3Gui.git"
"Bug Tracker" = "https://github.com/your-org/AT3Gui/issues"
Changelog = "https://github.com/your-org/AT3Gui/blob/main/CHANGELOG.md"

[project.scripts]
aretomo3-gui = "aretomo3_gui.main:main"
at3gui = "aretomo3_gui.main:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
aretomo3_gui = [
    "resources/*",
    "docs/*",
    "*.md",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["aretomo3_gui"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
    "src/aretomo3_gui",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gui: marks tests that require GUI components",
    "asyncio: marks tests that use asyncio",
]

[tool.coverage.run]
source = ["src/aretomo3_gui"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
