#!/usr/bin/env python3
"""
Fresh Installation Test for AreTomo3 GUI
=========================================

This script performs a comprehensive test of the AreTomo3 GUI from scratch,
simulating a fresh installation and testing all major components.
"""

import sys
import os
import subprocess
import tempfile
import shutil
from pathlib import Path
import json
import time

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print a formatted step."""
    print(f"\n[STEP {step}] {description}")
    print("-" * 50)

def run_command(cmd, description="", timeout=30):
    """Run a command and return success status."""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd="/mnt/HDD/ak_devel/AT3GUI_working"
        )
        if result.returncode == 0:
            print(f"SUCCESS: {description}")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"FAILED: {description}")
            print(f"Error: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"TIMEOUT: {description}")
        return False
    except Exception as e:
        print(f"EXCEPTION: {description} - {e}")
        return False

def test_python_imports():
    """Test all critical Python imports."""
    print_step(1, "Testing Python Imports")

    imports_to_test = [
        ("import sys", "System module"),
        ("import os", "OS module"),
        ("from pathlib import Path", "Path module"),
        ("import json", "JSON module"),
        ("sys.path.insert(0, 'src')", "Adding src to path"),
        ("from aretomo3_gui.core.config import MICROSCOPE_SETTINGS", "Core config"),
        (
            "from aretomo3_gui.utils.file_utils import analyze_directory",
            "File utilities"
        ),
        ("from aretomo3_gui.utils.eer_reader import read_eer_metadata", "EER reader"),
        ("from aretomo3_gui.gui.main_window import AreTomoGUI", "Main window"),
        (
            "from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget",
            "Batch widget"
        ),
        (
            "from aretomo3_gui.gui.viewers.analysis_viewer import AnalysisViewer",
            "Analysis viewer"
        ),
    ]

    results = []
    for import_stmt, desc in imports_to_test:
        success = run_command(
            f'python3 -c "{import_stmt}; print(\'{desc} - OK\')"',
            desc
        )
        results.append((desc, success))

    passed = sum(1 for _, success in results if success)
    print(f"\n📊 Import Results: {passed}/{len(results)} passed")
    return passed == len(results)

    # TODO: Refactor function - Function 'test_gui_creation' too long (59 lines)
def test_gui_creation():
    """Test GUI creation without display."""
    print_step(2, "Testing GUI Creation (Headless)")

    gui_test_script = '''
import os
os.environ["QT_QPA_PLATFORM"] = "offscreen"
import sys
sys.path.insert(0, "src")

try:
    from PyQt6.QtWidgets import QApplication
    from aretomo3_gui.gui.main_window import AreTomoGUI

    app = QApplication([])
    gui = AreTomoGUI()

    # Test basic properties
    print(f"Window title: {gui.windowTitle()}")
    print(f"Window size: {gui.size().width()}x{gui.size().height()}")
    print(f"Tab count: {gui.tabs.count()}")

    # Test tab names
    for i in range(gui.tabs.count()):
        print(f"Tab {i}: {gui.tabs.tabText(i)}")

    # Test critical methods exist
    critical_methods = [
        "on_start_batch_processing", "on_pause_processing",
        "setup_file_browser_tab", "_preview_command",
        "collect_current_gui_state", "restore_gui_state_from_session"
    ]

    for method in critical_methods:
        if hasattr(gui, method):
            print(f"✅ Method {method} exists")
        else:
            print(f"❌ Method {method} MISSING")
            raise AttributeError(f"Missing method: {method}")

    app.quit()
    print("✅ GUI creation test completed successfully")

except Exception as e:
    print(f"❌ GUI creation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''

    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(gui_test_script)
        temp_script = f.name

    try:
        success = run_command(f'python3 {temp_script}', "GUI Creation Test", timeout=60)
        return success
    finally:
        os.unlink(temp_script)

    # TODO: Refactor function - Function 'test_file_operations' too long (52 lines)
def test_file_operations():
    """Test file operation capabilities."""
    print_step(3, "Testing File Operations")

    # Create test directory structure
    test_dir = tempfile.mkdtemp(prefix="aretomo_test_")
    print(f"Creating test directory: {test_dir}")

    try:
        # Create nested structure
        base_path = Path(test_dir)
        (base_path / "root.mrc").write_text("test mrc file")
        (base_path / "subdir1").mkdir()
        (base_path / "subdir1" / "nested.eer").write_text("test eer file")
        (base_path / "subdir1" / "analysis.xf").write_text("test xf file")
        (base_path / "subdir2").mkdir()
        (base_path / "subdir2" / "deep").mkdir()
        (base_path / "subdir2" / "deep" / "deeper.tif").write_text("test tif file")
        (base_path / "subdir2" / "deep" / "motion.log").write_text("test log file")

        # Test file analysis
        file_test_script = f'''
import sys
sys.path.insert(0, "src")
from aretomo3_gui.utils.file_utils import analyze_directory

# Test non-recursive
non_rec = analyze_directory("{test_dir}", recursive=False)
print(f"Non-recursive: {{non_rec['total_files']}} files")

# Test recursive
rec = analyze_directory("{test_dir}", recursive=True)
print(f"Recursive: {{rec['total_files']}} files")

if rec['total_files'] > non_rec['total_files']:
    print("✅ Recursive search working correctly")
else:
    print("❌ Recursive search not working")
    exit(1)

print(f"File types found: {{list(rec['file_types'].keys())}}")
print("✅ File operations test completed")
'''

        success = run_command(
            f'python3 -c "{file_test_script}"',
            "File Operations Test"
        )
        return success

    finally:
        shutil.rmtree(test_dir)

    # TODO: Refactor function - Function 'test_batch_processing' too long (67 lines)
def test_batch_processing():
    """Test batch processing functionality."""
    print_step(4, "Testing Batch Processing")

    batch_test_script = '''
import os
os.environ["QT_QPA_PLATFORM"] = "offscreen"
import sys
sys.path.insert(0, "src")

try:
    from PyQt6.QtWidgets import QApplication
    from aretomo3_gui.gui.main_window import AreTomoGUI

    app = QApplication([])
    gui = AreTomoGUI()

    # Test batch widget exists
    if hasattr(gui, "batch_widget"):
        print("✅ Batch widget found")

        # Test batch table
        if hasattr(gui.batch_widget, "batch_table"):
            cols = gui.batch_widget.batch_table.columnCount()
            print(f"✅ Batch table has {cols} columns")

        # Test batch processing methods
        if hasattr(gui, "on_start_batch_processing"):
            print("✅ Batch processing handler exists")

            # Test empty batch
            try:
                gui.on_start_batch_processing([])
                print("✅ Empty batch handling works")
            except Exception as e:
                print(f"? Empty batch issue: {e}")

    # Test analysis auto-loading
    if hasattr(gui, "_on_tab_changed"):
        print("✅ Tab change detection exists")

        # Test switching to analysis tab
        for i in range(gui.tabs.count()):
            if "Analysis" in gui.tabs.tabText(i):
                try:
                    gui._on_tab_changed(i)
                    print("✅ Analysis tab switch works")
                except Exception as e:
                    print(f"? Analysis tab switch issue: {e}")
                break

    app.quit()
    print("✅ Batch processing test completed")

except Exception as e:
    print(f"❌ Batch processing test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''

    success = run_command(
        f'python3 -c "{batch_test_script}"',
        "Batch Processing Test",
        timeout=60
    )
    return success

    # TODO: Refactor function - Function 'main' too long (51 lines)
def main():
    """Run the complete fresh installation test."""
    print_header("AreTomo3 GUI Fresh Installation Test")
    print("This script will test the entire codebase from scratch")
    print("Testing all components, dependencies, and functionality...")

    # Change to the project directory
    os.chdir("/mnt/HDD/ak_devel/AT3GUI_working")
    print(f"Working directory: {os.getcwd()}")

    test_results = []

    # Run all tests
    tests_to_run = [
        ("Python Imports", test_python_imports),
        ("GUI Creation", test_gui_creation),
        ("File Operations", test_file_operations),
        ("Batch Processing", test_batch_processing),
    ]

    for test_name, test_func in tests_to_run:
        try:
            success = test_func()
            test_results.append((test_name, success))
        except Exception as e:
            print(f"💥 Test {test_name} crashed: {e}")
            test_results.append((test_name, False))

    # Generate final report
    print_header("FRESH INSTALLATION TEST REPORT")

    total_tests = len(test_results)
    passed_tests = sum(1 for _, success in test_results if success)

    print(f"\n📊 OVERALL RESULTS:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {total_tests - passed_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")

    print(f"\n📋 DETAILED RESULTS:")
    for test_name, success in test_results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {test_name}")

    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   AreTomo3 GUI is ready for use!")
    else:
        print(f"\n⚠️  SOME TESTS FAILED")
        print(f"   Please review the failed tests above")

if __name__ == "__main__":
    main()
