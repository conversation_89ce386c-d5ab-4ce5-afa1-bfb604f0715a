#!/usr/bin/env python3
"""
Test script to verify progress parsing functionality fixes TypeError
"""

import pytest
import sys
import os
import logging
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_progress_parsing():
    """Test that progress parsing converts strings to integers correctly."""

    # Mock the GUI components instead of creating real ones
    with patch('aretomo3_gui.gui.main_window.AreTomoGUI') as MockGUI:
        mock_instance = Mock()
        MockGUI.return_value = mock_instance

        # Test the progress parsing logic directly
        test_messages = [
            "Processing frame 15/20",
            "45% complete",
            "Progress: 75%",
            "Frame 8 of 10",
            "50 percent done"
        ]

        # Simulate progress parsing logic
        for message in test_messages:
            # Extract percentage or frame info
            if "%" in message:
                # Extract percentage
                parts = message.split("%")
                if len(parts) > 0:
                    try:
                        progress = int(parts[0].split()[-1])
                        assert 0 <= progress <= 100
                        logger.info(f"Parsed progress: {progress}% from '{message}'")
                    except (ValueError, IndexError):
                        logger.warning(f"Could not parse progress from '{message}'")
            elif "frame" in message.lower():
                # Extract frame info
                import re
                frame_match = re.search(r'(\d+)(?:/|\s+of\s+)(\d+)', message.lower())
                if frame_match:
                    current_frame = int(frame_match.group(1))
                    total_frames = int(frame_match.group(2))
                    progress = int((current_frame / total_frames) * 100)
                    assert 0 <= progress <= 100
                    logger.info(f"Parsed frame progress: {progress}% ({current_frame}/{total_frames}) from '{message}'")

        logger.info("Progress parsing test completed successfully")
        assert True

if __name__ == "__main__":
    test_progress_parsing()
