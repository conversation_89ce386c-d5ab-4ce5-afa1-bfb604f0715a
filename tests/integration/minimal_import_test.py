#!/usr/bin/env python3
"""
Minimal test to identify hanging imports.
"""
import sys
import os

# Add src to path
sys.path.insert(0, 'src')

def test_import(module_name):
    """Test importing a specific module."""
    try:
        print(f"Testing: {module_name}...", end=" ", flush=True)
        __import__(module_name)
        print("OK")
        return True
    except Exception as e:
        print(f"FAILED: {e}")
        return False

if __name__ == "__main__":
    print("Minimal Import Test")
    print("=" * 30)

    # Test basic modules first
    modules = [
        "aretomo3_gui",
        "aretomo3_gui.core.error_handling",
        "aretomo3_gui.core.logging_config",
        "aretomo3_gui.core.resource_manager",
        "aretomo3_gui.core.thread_manager",
    ]

    for module in modules:
        if not test_import(module):
            print(f"STOPPED at: {module}")
            break
    else:
        print("All basic modules imported successfully!")
