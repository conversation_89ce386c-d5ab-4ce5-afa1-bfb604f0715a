#!/usr/bin/env python3
"""
Simple verification script for the batch processing directory fix.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def verify_batch_implementation():
    """Verify that the batch processing implementation is correct."""
    print("🔍 Verifying batch processing directory fix implementation...")

    try:
        # Import the main window class
        from aretomo3_gui.gui.main_window import AreTomoGUI

        # Check the _build_command method signature
        import inspect
        build_cmd_signature = inspect.signature(AreTomoGUI._build_command)
        params = list(build_cmd_signature.parameters.keys())

        print(f"✅ _build_command method signature: {params}")

        # Verify it has the batch_input_dir parameter
        if 'batch_input_dir' in params:
            print("✅ batch_input_dir parameter found in _build_command")
        else:
            print("❌ batch_input_dir parameter NOT found in _build_command")
            return False

        # Check the construct_aretomo_command method
        construct_signature = inspect.signature(AreTomoGUI.construct_aretomo_command)
        construct_params = list(construct_signature.parameters.keys())

        print(f"✅ construct_aretomo_command method signature: {construct_params}")

        # Read the source to verify logic
        import inspect
        source = inspect.getsource(AreTomoGUI._build_command)

        # Check for key implementation details
        checks = [
            ('batch_input_dir and hasattr(self, \'batch_widget\')', 'Override logic'),
            ('output_dir_override_chk.isChecked()', 'Override checkbox check'),
            (
                'os.path.join(batch_input_dir,'
                "aretomo_output"',
                'Relative directory creation'
            ),
            ('os.path.join(self.output_dir.text()', 'Global directory logic')
        ]

        for check_text, description in checks:
            if check_text in source:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: NOT found")
                return False

        # Check construct_aretomo_command for batch detection
        construct_source = inspect.getsource(AreTomoGUI.construct_aretomo_command)

        batch_checks = [
            ('if hasattr(self, \'batch_queue\')', 'Batch queue check'),
            ('batch_item.get(\'series\') == series', 'Series matching'),
            ('batch_item.get(\'input_dir\')', 'Input directory extraction')
        ]

        for check_text, description in batch_checks:
            if check_text in construct_source:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: NOT found")
                return False

        print("\n🎉 VERIFICATION SUCCESSFUL!")
        print("The batch processing directory fix has been correctly implemented:")
        print("  • _build_command accepts batch_input_dir parameter")
        print("  • Logic handles override checkbox correctly")
        print("  • Creates relative directories for batch processing")
        print("  • Uses global directory when override is enabled")
        print("  • construct_aretomo_command detects batch context")
        print("  • Individual processing still uses global directory")

        return True

    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = verify_batch_implementation()
    sys.exit(0 if success else 1)
