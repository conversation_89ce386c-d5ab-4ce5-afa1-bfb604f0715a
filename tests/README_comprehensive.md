# 🧪 AT3GUI Test Suite

Comprehensive test suite for AreTomo3 GUI with EER support integration.

## 📁 Directory Structure

```
tests/
├── unit/                       # Fast, isolated unit tests
│   ├── test_eer_reader.py      # EER functionality tests
│   ├── test_file_utils.py      # File utility tests  
│   ├── test_batch_processing.py # Batch processing tests
│   └── test_syntax_verification.py # Code syntax tests
├── integration/                # Slower, system-wide tests
│   ├── test_at3gui_workflow.py # End-to-end GUI tests
│   ├── test_implementation.py  # Implementation verification
│   └── test_progress_tracking.py # Progress tracking tests
├── scripts/                    # Test utility scripts
│   ├── test_eer_build.sh       # EerReaderLib build testing
│   ├── test_installation.sh    # Installation verification
│   └── benchmark_eer_processing.sh # Performance benchmarks
├── fixtures/                   # Test data (placeholder)
├── conftest.py                 # Pytest configuration & fixtures
└── README.md                   # This file
```

## 🚀 Quick Start

### Run All Tests
```bash
# Run the complete test suite
python -m pytest tests/ -v

# Run with coverage report
python -m pytest tests/ --cov=aretomo3_gui --cov-report=html

# Run in parallel (if pytest-xdist available)
python -m pytest tests/ -n auto
```

### Run Specific Test Categories
```bash
# Unit tests (fast)
python -m pytest tests/unit/ -v

# Integration tests (slower)
python -m pytest tests/integration/ -v

# EER-specific tests only
python -m pytest tests/ -m eer -v

# GUI tests (require display)
python -m pytest tests/ -m gui -v

# Non-GUI tests only
python -m pytest tests/ -m "not gui" -v
```

### Run Test Scripts
```bash
# Test EerReaderLib build capability
./tests/scripts/test_eer_build.sh

# Verify installation completeness
./tests/scripts/test_installation.sh

# Run performance benchmarks
./tests/scripts/benchmark_eer_processing.sh
```

## 🏷️ Test Markers

Tests are categorized using pytest markers:

| Marker | Description | Usage |
|--------|-------------|--------|
| `unit` | Fast, isolated tests | `pytest -m unit` |
| `integration` | System-wide tests | `pytest -m integration` |
| `eer` | EER-specific functionality | `pytest -m eer` |
| `gui` | Tests requiring display | `pytest -m gui` |
| `slow` | Long-running tests | `pytest -m "not slow"` |
| `utils` | Utility module tests | `pytest -m utils` |

### Combining Markers
```bash
# Run unit tests excluding slow ones
python -m pytest -m "unit and not slow" -v

# Run EER tests that don't require GUI
python -m pytest -m "eer and not gui" -v

# Run all tests except GUI and slow
python -m pytest -m "not gui and not slow" -v
```

## 📊 Test Types

### Unit Tests (`tests/unit/`)

**Purpose:** Test individual components in isolation
**Speed:** Fast (< 1s per test)
**Dependencies:** Minimal, uses mocks

#### Available Tests:
- **`test_eer_reader.py`** - EER file reading, metadata extraction
- **`test_file_utils.py`** - File detection, validation, analysis  
- **`test_batch_processing.py`** - Batch processing attribute fixes
- **`test_syntax_verification.py`** - Python syntax validation

```bash
# Run all unit tests
python -m pytest tests/unit/ -v

# Run specific unit test file
python -m pytest tests/unit/test_eer_reader.py -v

# Run unit tests with coverage
python -m pytest tests/unit/ --cov=aretomo3_gui.utils
```

### Integration Tests (`tests/integration/`)

**Purpose:** Test complete workflows and system integration
**Speed:** Medium to slow (1-10s per test)
**Dependencies:** May require GUI, files, or external tools

#### Available Tests:
- **`test_at3gui_workflow.py`** - End-to-end GUI workflows
- **`test_implementation.py`** - Implementation verification
- **`test_progress_tracking.py`** - Progress tracking systems

```bash
# Run all integration tests
python -m pytest tests/integration/ -v

# Run integration tests without GUI
python -m pytest tests/integration/ -m "not gui" -v

# Run specific integration test
python -m pytest tests/integration/test_at3gui_workflow.py -v
```

### Test Scripts (`tests/scripts/`)

**Purpose:** Utility scripts for testing installation and performance
**Speed:** Variable (5s - 5min)
**Dependencies:** System tools, build environment

#### Available Scripts:
- **`test_eer_build.sh`** - Test EerReaderLib building capability
- **`test_installation.sh`** - Verify installation completeness
- **`benchmark_eer_processing.sh`** - Performance benchmarking

```bash
# Test build environment
./tests/scripts/test_eer_build.sh

# Verify installation
./tests/scripts/test_installation.sh

# Run benchmarks (optional)
./tests/scripts/benchmark_eer_processing.sh --quick
```

## 🔧 Test Configuration

### Pytest Configuration (`pytest.ini`)

Key settings:
- **Test discovery:** Automatic Python file detection
- **Markers:** Custom test categorization
- **Coverage:** Code coverage tracking
- **Timeouts:** Prevent hanging tests
- **Logging:** Detailed test output

### Shared Fixtures (`conftest.py`)

Available fixtures:
- **`sample_eer_metadata`** - Mock EER file metadata
- **`mock_eer_file`** - Temporary EER file for testing
- **`sample_directory`** - Sample dataset structure
- **`qapp`** - QApplication for GUI tests
- **`performance_timer`** - Benchmarking utilities

```python
# Example test using fixtures
def test_eer_file_processing(mock_eer_file, sample_eer_metadata):
    from aretomo3_gui.utils.eer_reader import EerReader
    
    reader = EerReader()
    metadata = reader.read_metadata(mock_eer_file)
    
    assert metadata is not None
    assert metadata['filename'] == sample_eer_metadata['filename']
```

## 🐛 Troubleshooting

### Common Issues

#### GUI Tests Failing
```bash
# Check if display is available
echo $DISPLAY

# Run without GUI tests
python -m pytest tests/ -m "not gui" -v

# Use virtual display (Linux)
xvfb-run python -m pytest tests/ -v
```

#### Import Errors
```bash
# Check Python path
python -c "import sys; print(sys.path)"

# Test basic imports
python -c "import aretomo3_gui; print('OK')"

# Run syntax verification
python -m pytest tests/unit/test_syntax_verification.py -v
```

#### EER Tests Failing
```bash
# Check EER support
python -c "from aretomo3_gui.utils.eer_reader import is_eer_supported; print(is_eer_supported())"

# Test EER build capability
./tests/scripts/test_eer_build.sh

# Run EER tests only
python -m pytest tests/ -m eer -v
```

#### Slow Tests
```bash
# Skip slow tests
python -m pytest tests/ -m "not slow" -v

# Run in parallel
python -m pytest tests/ -n auto

# Show test durations
python -m pytest tests/ --durations=0
```

## 📈 Coverage Reports

### Generate Coverage
```bash
# HTML coverage report
python -m pytest tests/ --cov=aretomo3_gui --cov-report=html

# Terminal coverage report
python -m pytest tests/ --cov=aretomo3_gui --cov-report=term-missing

# XML coverage for CI
python -m pytest tests/ --cov=aretomo3_gui --cov-report=xml
```

### Coverage Targets
- **Overall coverage:** > 70%
- **Unit test coverage:** > 85%
- **Critical modules:** > 90%

## 📚 Migrated Tests

**⚠️ Note:** Several test files have been moved from the root directory to this organized structure:

### Moved Files:
- `test_batch_fix.py` → `tests/unit/test_batch_processing.py`
- `test_batch_fix_verification.py` → `tests/unit/test_batch_processing.py`
- `test_batch_processing.py` → `tests/unit/test_batch_processing.py`
- `test_implementation.py` → `tests/integration/test_implementation.py`
- `test_progress_direct.py` → `tests/integration/test_progress_tracking.py`
- `test_progress_parsing.py` → `tests/integration/test_progress_tracking.py`
- `test_syntax_verification.py` → `tests/unit/test_syntax_verification.py`

### Redirect Files:
Old test files in the root directory now contain redirect messages pointing to the new locations.

---

**Happy testing!** 🧪✨

For more information, see the main [README.md](../README.md) and [documentation](../docs/).