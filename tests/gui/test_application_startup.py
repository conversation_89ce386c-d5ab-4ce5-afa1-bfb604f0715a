#!/usr/bin/env python3
"""
Application startup test for AT3GUI.
Tests the main application entry point and basic functionality.
"""

import sys
import pytest
import os
import signal
import time
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

def test_main_import():
    """Test importing the main module."""
    print("🧪 Testing Main Module Import...")

    try:
        from aretomo3_gui.main import main
        print("   ✅ Main module imported successfully")

        if callable(main):
            print("   ✅ Main function is callable")
            # Test passed
        else:
            print("   ❌ Main function is not callable")
            pytest.fail("Test failed")
    except Exception as e:
        print(f"   ❌ Main module import failed: {e}")
        import traceback
        traceback.print_exc()
        pytest.fail("Test failed")
def test_gui_class_creation():
    """Test creating GUI class without showing window."""
    print("\n🧪 Testing GUI Class Creation...")

    try:
        # Import PyQt6 first
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt

        # Create QApplication (required for any Qt widgets)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        print("   ✅ QApplication created")

        # Import and create main window
        from aretomo3_gui.gui.main_window import AreTomoGUI

        # Create main window (but don't show it)
        main_window = AreTomoGUI()
        print("   ✅ Main window created successfully")

        # Test basic properties
        if hasattr(main_window, 'setWindowTitle'):
            print("   ✅ Window properties accessible")

        # Clean up
        main_window.close()
        app.quit()

        # Test passed
    except Exception as e:
        print(f"   ❌ GUI class creation failed: {e}")
        import traceback
        traceback.print_exc()
        pytest.fail("Test failed")
def test_theme_system():
    """Test theme system functionality."""
    print("\n🧪 Testing Theme System...")

    try:
        from aretomo3_gui.gui.theme_manager import ThemeManager

        # Create theme manager
        theme_manager = ThemeManager()
        print("   ✅ Theme manager created")

        # Test getting stylesheet
        stylesheet = theme_manager.get_theme_stylesheet()
        if stylesheet and len(stylesheet) > 100:  # Should be substantial
            print("   ✅ Theme stylesheet loaded")
        else:
            print("   ⚠️  Theme stylesheet seems small or empty")

        # Test passed
    except Exception as e:
        print(f"   ❌ Theme system failed: {e}")
        pytest.fail("Test failed")
def test_configuration_system():
    """Test configuration system."""
    print("\n🧪 Testing Configuration System...")

    try:
        from aretomo3_gui.core.config.config_validation import AreTomo3Config

        # Create configuration
        config = AreTomo3Config(pixel_size=1.91)
        print("   ✅ Configuration created")

        # Test configuration manager
        from aretomo3_gui.core.config.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("   ✅ Configuration manager created")

        # Test passed
    except Exception as e:
        print(f"   ❌ Configuration system failed: {e}")
        pytest.fail("Test failed")
def test_logging_system():
    """Test logging system."""
    print("\n🧪 Testing Logging System...")

    try:
        from aretomo3_gui.core.logging_config import setup_logging
        import logging

        # Setup logging
        setup_logging()
        print("   ✅ Logging system initialized")

        # Test logging
        logger = logging.getLogger("test")
        logger.info("Test log message")
        print("   ✅ Logging functionality working")

        # Test passed
    except Exception as e:
        print(f"   ❌ Logging system failed: {e}")
        pytest.fail("Test failed")
def test_file_operations():
    """Test file operation capabilities."""
    print("\n🧪 Testing File Operations...")

    try:
        # Test MDOC parser
        from aretomo3_gui.utils.mdoc_parser import parse_mdoc
        print("   ✅ MDOC parser available")

        # Test export functions
        from aretomo3_gui.utils.export_functions import export_to_csv
        print("   ✅ Export functions available")

        # Test utilities
        from aretomo3_gui.utils.utils import format_file_size
        size_str = format_file_size(1024 * 1024)  # 1 MB
        print(f"   ✅ Utilities working (1MB = {size_str})")

        # Test passed
    except Exception as e:
        print(f"   ❌ File operations failed: {e}")
        pytest.fail("Test failed")
def test_mrc_file_support():
    """Test MRC file support with the test tomogram."""
    print("\n🧪 Testing MRC File Support...")

    try:
        # Check if test MRC file exists
        test_mrc = Path("/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc")
        if test_mrc.exists():
            print(f"   ✅ Test MRC file found: {test_mrc}")
            print(f"   ✅ File size: {test_mrc.stat().st_size / (1024*1024):.1f} MB")

            # Test mrcfile import
            import mrcfile
            print("   ✅ mrcfile library available")

            # Test passed
        else:
            print("   ⚠️  Test MRC file not found (expected for testing)")
            # Not a failure - test passes even without the file

    except Exception as e:
        print(f"   ❌ MRC file support failed: {e}")
        pytest.fail("Test failed")
def main():
    """Run application startup tests."""
    print("🔬 AT3GUI Application Startup Testing")
    print("=" * 60)

    test_results = []

    # Execute all test phases
    test_results.append(("Main Module Import", test_main_import()))
    test_results.append(("GUI Class Creation", test_gui_class_creation()))
    test_results.append(("Theme System", test_theme_system()))
    test_results.append(("Configuration System", test_configuration_system()))
    test_results.append(("Logging System", test_logging_system()))
    test_results.append(("File Operations", test_file_operations()))
    test_results.append(("MRC File Support", test_mrc_file_support()))

    # Calculate results
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100

    print(f"\n{'='*60}")
    print("📊 APPLICATION STARTUP TEST RESULTS")
    print(f"{'='*60}")

    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")

    print(f"\n🎯 Overall Success Rate: {passed}/{total} ({success_rate:.1f}%)")

    if success_rate >= 95:
        print("🎉 EXCELLENT! Application startup is perfect!")
        # Test passed
    elif success_rate >= 85:
        print("✅ GOOD! Application startup working well.")
        # Test passed
    elif success_rate >= 75:
        print("⚠️  ACCEPTABLE! Some startup issues to address.")
        pytest.fail("Test failed")
    else:
        print("❌ CRITICAL! Major application startup failures.")
        pytest.fail("Test failed")
if __name__ == "__main__":
    sys.exit(main())
