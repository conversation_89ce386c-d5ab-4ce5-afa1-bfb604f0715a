#!/usr/bin/env python3
"""
Test suite for the new enhanced spinbox widgets.
"""

import pytest
import sys
from unittest.mock import Mock, patch

from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout
from PyQt6.QtCore import Qt
from PyQt6.QtTest import QTest

# Import the enhanced spinbox widgets
from src.aretomo3_gui.gui.widgets.enhanced_spinbox import (
    EnhancedSpinBox, EnhancedDoubleSpinBox, SpinBoxWithSlider,
    DoubleSpinBoxWithSlider, ParameterSpinBoxWidget, create_parameter_widget
)

class TestNewEnhancedSpinBox:
    """Test new enhanced spinbox functionality."""

    @pytest.fixture
    def app(self):
        """Create QApplication for testing."""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        app.quit()

    def test_enhanced_spinbox_creation(self, app):
        """Test enhanced spinbox creation."""
        spinbox = EnhancedSpinBox()
        assert spinbox is not None
        assert hasattr(spinbox, 'valueChangedDelayed')
        assert hasattr(spinbox, 'delay_timer')

    def test_enhanced_double_spinbox_creation(self, app):
        """Test enhanced double spinbox creation."""
        spinbox = EnhancedDoubleSpinBox()
        assert spinbox is not None
        assert hasattr(spinbox, 'valueChangedDelayed')
        assert hasattr(spinbox, 'delay_timer')

    def test_spinbox_with_slider_creation(self, app):
        """Test spinbox with slider creation."""
        widget = SpinBoxWithSlider()
        assert widget is not None
        assert hasattr(widget, 'spinbox')
        assert hasattr(widget, 'slider')
        assert hasattr(widget, 'valueChanged')
        assert hasattr(widget, 'valueChangedDelayed')

    def test_double_spinbox_with_slider_creation(self, app):
        """Test double spinbox with slider creation."""
        widget = DoubleSpinBoxWithSlider()
        assert widget is not None
        assert hasattr(widget, 'spinbox')
        assert hasattr(widget, 'slider')
        assert hasattr(widget, 'valueChanged')
        assert hasattr(widget, 'valueChangedDelayed')

    def test_spinbox_slider_synchronization(self, app):
        """Test that spinbox and slider stay synchronized."""
        widget = SpinBoxWithSlider()
        widget.setRange(0, 100)

        # Test spinbox -> slider sync
        widget.spinbox.setValue(50)
        assert widget.slider.value() == 50

        # Test slider -> spinbox sync
        widget.slider.setValue(75)
        assert widget.spinbox.value() == 75

    def test_double_spinbox_slider_synchronization(self, app):
        """Test that double spinbox and slider stay synchronized."""
        widget = DoubleSpinBoxWithSlider()
        widget.setRange(0.0, 10.0)
        widget.setDecimals(2)

        # Test spinbox -> slider sync
        widget.spinbox.setValue(5.5)
        assert widget.slider.value() == 550  # scaled by 100

        # Test slider -> spinbox sync
        widget.slider.setValue(750)  # 7.5 * 100
        assert abs(widget.spinbox.value() - 7.5) < 0.01

    def test_parameter_spinbox_widget_creation(self, app):
        """Test parameter spinbox widget creation."""
        widget = ParameterSpinBoxWidget(
            param_name="test_param",
            param_type="int",
            label="Test Parameter",
            help_text="This is a test parameter",
            default_value=10,
            minimum=0,
            maximum=100
        )

        assert widget is not None
        assert widget.param_name == "test_param"
        assert widget.param_type == "int"
        assert widget.help_text == "This is a test parameter"
        assert widget.value() == 10

    def test_parameter_widget_factory(self, app):
        """Test parameter widget factory function."""
        config = {
            'type': 'float',
            'label': 'Pixel Size',
            'help': 'Pixel size in Angstroms',
            'default': 1.5,
            'min': 0.1,
            'max': 10.0,
            'with_slider': True
        }

        widget = create_parameter_widget("PixSize", config)

        assert widget is not None
        assert widget.param_name == "PixSize"
        assert widget.param_type == "float"
        assert abs(widget.value() - 1.5) < 0.01

    def test_parameter_widget_validation(self, app):
        """Test parameter widget validation."""
        widget = ParameterSpinBoxWidget(
            param_name="PixSize",
            param_type="float",
            default_value=1.5,
            minimum=0.1,
            maximum=10.0
        )

        # Test valid value
        widget.setValue(2.0)
        assert "✓" in widget.status_label.text()

        # Test edge case (this should still be valid)
        widget.setValue(0.1)
        assert "✓" in widget.status_label.text()

    def test_enhanced_spinbox_styling(self, app):
        """Test that enhanced spinbox has custom styling."""
        spinbox = EnhancedSpinBox()
        style_sheet = spinbox.styleSheet()

        # Check that custom styling is applied
        assert "border:" in style_sheet
        assert "border-radius:" in style_sheet
        assert "background:" in style_sheet

    def test_spinbox_range_setting(self, app):
        """Test setting ranges on spinbox widgets."""
        # Test integer spinbox
        int_widget = SpinBoxWithSlider()
        int_widget.setRange(10, 50)
        assert int_widget.spinbox.minimum() == 10
        assert int_widget.spinbox.maximum() == 50
        assert int_widget.slider.minimum() == 10
        assert int_widget.slider.maximum() == 50

        # Test double spinbox
        double_widget = DoubleSpinBoxWithSlider()
        double_widget.setRange(1.0, 5.0)
        assert abs(double_widget.spinbox.minimum() - 1.0) < 0.01
        assert abs(double_widget.spinbox.maximum() - 5.0) < 0.01

    def test_spinbox_enable_disable(self, app):
        """Test enabling/disabling spinbox widgets."""
        widget = SpinBoxWithSlider()

        # Test enabling
        widget.setEnabled(True)
        assert widget.spinbox.isEnabled()
        assert widget.slider.isEnabled()

        # Test disabling
        widget.setEnabled(False)
        assert not widget.spinbox.isEnabled()
        assert not widget.slider.isEnabled()

    def test_parameter_widget_help_button(self, app):
        """Test parameter widget help button functionality."""
        widget = ParameterSpinBoxWidget(
            param_name="test_param",
            help_text="This is help text"
        )

        assert widget.help_button is not None
        assert widget.help_button.toolTip() == "This is help text"

    def test_delayed_value_change_signal(self, app):
        """Test delayed value change signal."""
        spinbox = EnhancedSpinBox()

        # Mock the delayed signal
        delayed_signal_emitted = []
        spinbox.valueChangedDelayed.connect(lambda v: delayed_signal_emitted.append(v))

        # Change value
        spinbox.setValue(42)

        # Signal should not be emitted immediately
        assert len(delayed_signal_emitted) == 0

        # Process events and wait for timer
        app.processEvents()

        # Note: In a real test, we'd need to wait for the timer or mock it
        # For now, just verify the timer exists and is configured
        assert spinbox.delay_timer.isSingleShot()

    def test_mouse_wheel_behavior(self, app):
        """Test mouse wheel behavior on enhanced spinboxes."""
        spinbox = EnhancedSpinBox()
        spinbox.setRange(0, 100)
        spinbox.setValue(50)

        # Test that spinbox has strong focus policy
        assert spinbox.focusPolicy() == Qt.FocusPolicy.StrongFocus

    def test_parameter_widget_comprehensive(self, app):
        """Test comprehensive parameter widget functionality."""
        # Create a comprehensive parameter widget
        widget = ParameterSpinBoxWidget(
            param_name="comprehensive_test",
            param_type="float",
            label="Comprehensive Test",
            help_text="This tests all functionality",
            default_value=3.14,
            minimum=0.0,
            maximum=10.0,
            with_slider=True
        )

        # Test all components exist
        assert widget.label is not None
        assert widget.spinbox_widget is not None
        assert widget.help_button is not None
        assert widget.status_label is not None

        # Test value operations
        assert abs(widget.value() - 3.14) < 0.01

        widget.setValue(7.5)
        assert abs(widget.value() - 7.5) < 0.01

        # Test range operations
        widget.setRange(1.0, 20.0)
        # Note: Actual range testing would require accessing the underlying spinbox

        # Test enable/disable
        widget.setEnabled(False)
        assert not widget.spinbox_widget.isEnabled()
        assert not widget.help_button.isEnabled()

        widget.setEnabled(True)
        assert widget.spinbox_widget.isEnabled()
        assert widget.help_button.isEnabled()

if __name__ == "__main__":
    pytest.main([__file__])
