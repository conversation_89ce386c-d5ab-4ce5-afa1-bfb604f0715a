#!/usr/bin/env python3
"""
Test script to verify batch processing functionality
"""

import sys
import pytest
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from aretomo3_gui.gui.main_window import AreTomoGUI, TiltSeries

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

    # TODO: Refactor function - Function 'test_batch_processing' too long (64 lines)
def test_batch_processing():
    """Test the batch processing signal connection and handler."""

    app = QApplication(sys.argv)

    # Create main window
    window = AreTomoGUI()
    window.show()

    # Create mock tilt series data
    mock_series = TiltSeries("Test_Position_001")
    # Add mock files to the series
    mock_series.files = ["/mock/file1.eer", "/mock/file2.eer"]
    mock_series.angles = [-60.0, 60.0]
    mock_series.mdoc_file = "/mock/test.mdoc"

    # Create mock batch items
    mock_batch_items = [
        {
            'position': 'Test_Position_001',
            'input_dir': '/mock/test/dir',
            'file_count': 2,
            'status': 'Pending',
            'series': mock_series
        }
    ]

    def test_signal_connection():
        """Test if the signal connection works."""
        logger.info("Testing batch processing signal connection...")

        # Check if the signal is connected
        if hasattr(
            window,
            'batch_widget') and hasattr(window.batch_widget,
            'start_batch'
        ):
            logger.info("✓ BatchProcessingWidget.start_batch signal found")

            # Check if the handler method exists
            if hasattr(window, 'on_start_batch_processing'):
                logger.info("✓ AreTomoGUI.on_start_batch_processing handler method found")

                try:
                    # Test the signal emission (this will call our new handler)
                    logger.info("Testing signal emission with mock data...")
                    window.batch_widget.start_batch.emit(mock_batch_items)
                    logger.info("✓ Signal emitted successfully")

                except Exception as e:
                    logger.error(f"✗ Error during signal emission: {e}")

            else:
                logger.error("✗ Handler method on_start_batch_processing not found")
        else:
            logger.error("✗ BatchProcessingWidget or start_batch signal not found")

    # Test after window is fully initialized
    QTimer.singleShot(1000, test_signal_connection)
    QTimer.singleShot(3000, app.quit)  # Exit after test

    # Execute the app and verify it ran without errors
    result = app.exec()
    assert result >= 0  # 0 = normal exit, negative = error

if __name__ == "__main__":
    logger.info("Starting batch processing test...")
    result = test_batch_processing()
    logger.info(f"Test completed with result: {result}")
