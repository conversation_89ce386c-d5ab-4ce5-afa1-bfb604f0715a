#!/bin/bash
# Installation verification test script

echo "🧪 AT3GUI Installation Verification"
echo "===================================="
echo ""

# Get script paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TESTS_ROOT="$(dirname "$SCRIPT_DIR")"
AT3GUI_ROOT="$(dirname "$TESTS_ROOT")"

echo "📍 Testing from: $SCRIPT_DIR"
echo "📁 AT3GUI root: $AT3GUI_ROOT"
echo ""

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo "🔍 Testing: $test_name"
    if eval "$test_command" >/dev/null 2>&1; then
        echo "  ✅ PASS"
        ((TESTS_PASSED++))
    else
        echo "  ❌ FAIL"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("$test_name")
    fi
}

# Test 1: Python import
echo "🐍 Python Package Tests"
echo "========================"
run_test "Basic import" "python -c 'import aretomo3_gui'"
run_test "Version check" "python -c 'import aretomo3_gui; print(aretomo3_gui.__version__)'"
run_test "GUI components" "python -c 'from aretomo3_gui.gui.main_window import AreTomoGUI'"
run_test "Utilities" "python -c 'from aretomo3_gui.utils import file_utils'"

echo ""

# Test 2: EER Support
echo "📁 EER Support Tests"
echo "===================="
run_test "EER reader import" "python -c 'from aretomo3_gui.utils.eer_reader import EerReader'"
run_test "EER support detection" "python -c 'from aretomo3_gui.utils.eer_reader import is_eer_supported; print(is_eer_supported())'"

# Check EER support status
EER_STATUS=$(python -c "from aretomo3_gui.utils.eer_reader import is_eer_supported; print(is_eer_supported())" 2>/dev/null || echo "False")
if [ "$EER_STATUS" = "True" ]; then
    echo "  ✅ EER support is ENABLED"
    run_test "EER metadata reading" "python -c 'from aretomo3_gui.utils.eer_reader import EerReader; r = EerReader()'"
else
    echo "  ⚠️  EER support is DISABLED (fallback mode)"
fi

echo ""

# Test 3: Entry Points
echo "🚀 Entry Points Tests"
echo "====================="
run_test "aretomo3-gui command" "command -v aretomo3-gui"
run_test "at3gui command" "command -v at3gui"
run_test "test-eer-support command" "command -v test-eer-support"

echo ""

# Test 4: File Structure
echo "📁 File Structure Tests"
echo "======================="
run_test "Source directory exists" "[ -d '$AT3GUI_ROOT/src/aretomo3_gui' ]"
run_test "Scripts directory exists" "[ -d '$AT3GUI_ROOT/scripts' ]"
run_test "Documentation exists" "[ -d '$AT3GUI_ROOT/docs' ]"
run_test "Tests directory exists" "[ -d '$AT3GUI_ROOT/tests' ]"

# Check for local EerReaderLib
if [ -d "$AT3GUI_ROOT/src/EerReaderLib-master" ]; then
    echo "  ✅ Local EerReaderLib source found"
    run_test "EerReaderLib CMakeLists.txt" "[ -f '$AT3GUI_ROOT/src/EerReaderLib-master/CMakeLists.txt' ]"
else
    echo "  ⚠️  Local EerReaderLib source not found"
fi

echo ""

# Test 5: Dependencies
echo "📦 Dependencies Tests"
echo "====================="
run_test "NumPy" "python -c 'import numpy'"
run_test "PyQt6" "python -c 'import PyQt6'"
run_test "Matplotlib" "python -c 'import matplotlib'"
run_test "MRCFile" "python -c 'import mrcfile'"
run_test "Pillow" "python -c 'import PIL'"

echo ""

# Test 6: Optional Dependencies
echo "🔧 Optional Dependencies Tests"
echo "=============================="
run_test "H5Py" "python -c 'import h5py'"
run_test "SciPy" "python -c 'import scipy'"
run_test "TiffFile" "python -c 'import tifffile'"
run_test "ImageIO" "python -c 'import imageio'"
run_test "PyYAML" "python -c 'import yaml'"

echo ""

# Test 7: Configuration Files
echo "⚙️  Configuration Tests"
echo "======================="
run_test "Setup.py exists" "[ -f '$AT3GUI_ROOT/setup.py' ]"
run_test "PyProject.toml exists" "[ -f '$AT3GUI_ROOT/pyproject.toml' ]"
run_test "Requirements file exists" "[ -f '$AT3GUI_ROOT/requirements.txt' ] || [ -f '$AT3GUI_ROOT/pyproject.toml' ]"

echo ""

# Test 8: Documentation
echo "📚 Documentation Tests"
echo "======================"
run_test "Main README" "[ -f '$AT3GUI_ROOT/README.md' ]"
run_test "Installation guide" "[ -f '$AT3GUI_ROOT/docs/INSTALLATION.md' ]"
run_test "EER support guide" "[ -f '$AT3GUI_ROOT/docs/EER_SUPPORT.md' ]"
run_test "Scripts README" "[ -f '$AT3GUI_ROOT/scripts/README.md' ]"
run_test "Tests README" "[ -f '$AT3GUI_ROOT/tests/README.md' ]"

echo ""

# Test 9: Executable Tests (if display available)
if [ -n "$DISPLAY" ] || [ "$(uname)" = "Darwin" ]; then
    echo "🖥️  GUI Tests (Display Available)"
    echo "================================="
    run_test "GUI version check" "timeout 10s aretomo3-gui --version"
    run_test "GUI help" "timeout 10s aretomo3-gui --help"
else
    echo "🖥️  GUI Tests (No Display)"
    echo "=========================="
    echo "  ⚠️  Display not available - skipping GUI tests"
fi

echo ""

# Summary
echo "📊 Test Results Summary"
echo "======================="
echo "✅ Tests passed: $TESTS_PASSED"
echo "❌ Tests failed: $TESTS_FAILED"
echo "📈 Success rate: $(( TESTS_PASSED * 100 / (TESTS_PASSED + TESTS_FAILED) ))%"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo "🎉 ALL TESTS PASSED!"
    echo "AT3GUI installation appears to be working correctly."
    echo ""
    echo "🚀 You can now:"
    echo "   • Run the GUI: aretomo3-gui"
    echo "   • Test EER support: test-eer-support"
    echo "   • View documentation: docs/"
    exit 0
else
    echo ""
    echo "⚠️  SOME TESTS FAILED"
    echo "Failed tests:"
    for test in "${FAILED_TESTS[@]}"; do
        echo "   • $test"
    done
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   • Check installation logs"
    echo "   • Verify all dependencies are installed"
    echo "   • Run: ./tests/scripts/test_eer_build.sh"
    echo "   • See: docs/INSTALLATION.md"
    exit 1
fi