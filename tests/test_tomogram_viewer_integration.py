#!/usr/bin/env python3
"""
Test suite for tomogram viewer integration in batch processing.
Tests the functionality of opening tomograms in the Napari viewer from batch results.
"""

import pytest
import os
import tempfile
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import Qt
from PyQt6.QtTest import QTest

# Import the modules to test
from src.aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
from src.aretomo3_gui.gui.viewers.napari_mrc_viewer import <PERSON><PERSON><PERSON>MRCViewer
from src.aretomo3_gui.gui.main_window import AreTomoGUI

class TestTomogramViewerIntegration:
    """Test tomogram viewer integration functionality."""

    @pytest.fixture
    def app(self):
        """Create QApplication for testing."""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        app.quit()

    @pytest.fixture
    def temp_tomogram(self):
        """Create a temporary MRC file for testing."""
        with tempfile.NamedTemporaryFile(suffix='.mrc', delete=False) as f:
            # Create a simple 3D array to simulate tomogram data
            data = np.random.randint(0, 255, (50, 50, 50), dtype=np.uint8)

            # Write minimal MRC header and data
            # This is a simplified MRC file for testing
            header = np.zeros(256, dtype=np.int32)
            header[0] = 50  # nx
            header[1] = 50  # ny
            header[2] = 50  # nz
            header[3] = 0   # mode (0 = uint8)

            f.write(header.tobytes())
            f.write(data.tobytes())

            temp_path = f.name

        yield temp_path

        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)

    @pytest.fixture
    def mock_main_window(self, app):
        """Create a mock main window with viewer tab."""
        main_window = Mock()

        # Create a real tab widget for testing
        from PyQt6.QtWidgets import QTabWidget, QVBoxLayout
        tab_widget = QTabWidget()

        # Create viewer tab
        viewer_tab = QWidget()
        viewer_layout = QVBoxLayout(viewer_tab)

        # Create mock Napari viewer
        napari_viewer = Mock(spec=NapariMRCViewer)
        napari_viewer.load_file = Mock(return_value=True)
        napari_viewer.load_mrc_file = Mock(return_value=True)

        viewer_layout.addWidget(napari_viewer)

        # Add tab to widget
        tab_widget.addTab(viewer_tab, "🔬 Napari Viewer")

        # Set up main window attributes
        main_window.tab_widget = tab_widget
        main_window.viewer_tab = viewer_tab

        return main_window, napari_viewer

    def test_view_tomogram_success(self, app, temp_tomogram, mock_main_window):
        """Test successful tomogram viewing."""
        main_window, napari_viewer = mock_main_window

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Test viewing tomogram
        with patch('PyQt6.QtWidgets.QMessageBox.information') as mock_info:
            batch_widget.view_tomogram(temp_tomogram)

            # Verify that the viewer was called
            napari_viewer.load_file.assert_called_once()

            # Verify success message was shown
            mock_info.assert_called_once()
            args = mock_info.call_args[0]
            assert "Tomogram Loaded" in args[1]

    def test_view_tomogram_file_not_found(self, app, mock_main_window):
        """Test viewing non-existent tomogram file."""
        main_window, napari_viewer = mock_main_window

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Test viewing non-existent file
        with patch('PyQt6.QtWidgets.QMessageBox.warning') as mock_warning:
            batch_widget.view_tomogram("/nonexistent/file.mrc")

            # Verify warning was shown
            mock_warning.assert_called_once()
            args = mock_warning.call_args[0]
            assert "File Not Found" in args[1]

    def test_view_tomogram_no_viewer_tab(self, app, temp_tomogram):
        """Test viewing tomogram when viewer tab is not available."""
        # Create main window without viewer tab
        main_window = Mock()
        main_window.tab_widget = Mock()
        main_window.tab_widget.count.return_value = 0

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Test viewing tomogram
        with patch('PyQt6.QtWidgets.QMessageBox.warning') as mock_warning:
            batch_widget.view_tomogram(temp_tomogram)

            # Verify warning was shown
            mock_warning.assert_called_once()
            args = mock_warning.call_args[0]
            assert "Could not find Napari Viewer tab" in args[2]

    def test_view_tomogram_viewer_error(self, app, temp_tomogram, mock_main_window):
        """Test viewing tomogram when viewer has an error."""
        main_window, napari_viewer = mock_main_window

        # Make the viewer raise an exception
        napari_viewer.load_file.side_effect = Exception("Viewer error")

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Test viewing tomogram
        with patch('PyQt6.QtWidgets.QMessageBox.critical') as mock_critical:
            batch_widget.view_tomogram(temp_tomogram)

            # Verify error message was shown
            mock_critical.assert_called_once()
            args = mock_critical.call_args[0]
            assert "Failed to view tomogram" in args[2]

    def test_add_view_icon_functionality(self, app, temp_tomogram, mock_main_window):
        """Test that view icons are properly added to batch table."""
        main_window, napari_viewer = mock_main_window

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Add a row to the table
        batch_widget.batch_table.insertRow(0)

        # Test adding view icon
        batch_widget.add_view_icon(0, temp_tomogram)

        # Verify that a widget was added to the view column
        view_widget = batch_widget.batch_table.cellWidget(0, 6)
        assert view_widget is not None

        # Test clicking the view button
        with patch.object(batch_widget, 'view_tomogram') as mock_view:
            # Simulate button click
            view_widget.click()

            # Verify view_tomogram was called
            mock_view.assert_called_once_with(temp_tomogram)

    def test_update_tomogram_after_processing(self, app, mock_main_window):
        """Test updating tomogram information after processing completion."""
        main_window, napari_viewer = mock_main_window

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Create temporary output directory with mock tomogram
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create mock tomogram file
            tomogram_path = os.path.join(temp_dir, "test_position_Vol.mrc")
            with open(tomogram_path, 'wb') as f:
                f.write(b'mock tomogram data')

            # Create batch item
            batch_item = {
                'position': 'test_position',
                'output_dir': temp_dir,
                'tomogram': '',
                'tomogram_path': ''
            }

            # Add row to table
            batch_widget.batch_table.insertRow(0)
            for col in range(8):
                from PyQt6.QtWidgets import QTableWidgetItem
                batch_widget.batch_table.setItem(0, col, QTableWidgetItem(''))

            # Test updating tomogram after processing
            batch_widget.update_tomogram_after_processing(0, batch_item)

            # Verify tomogram information was updated
            assert batch_item['tomogram'] == 'test_position_Vol.mrc'
            assert batch_item['tomogram_path'] == tomogram_path

            # Verify table was updated
            tomogram_item = batch_widget.batch_table.item(0, 5)
            assert tomogram_item.text() == 'test_position_Vol.mrc'

            # Verify view icon was added
            view_widget = batch_widget.batch_table.cellWidget(0, 6)
            assert view_widget is not None

    def test_napari_viewer_load_file_method(self, app, temp_tomogram):
        """Test that NapariMRCViewer can load MRC files."""
        # This test requires napari to be available
        try:
            import napari
            napari_available = True
        except ImportError:
            napari_available = False

        if not napari_available:
            pytest.skip("Napari not available for testing")

        # Create NapariMRCViewer
        viewer = NapariMRCViewer()

        # Test loading file
        result = viewer.load_file(Path(temp_tomogram))

        # The result depends on whether the file is a valid MRC file
        # For our simple test file, it might fail, but the method should exist
        assert hasattr(viewer, 'load_file')
        assert callable(viewer.load_file)

    def test_batch_processing_integration(self, app, mock_main_window):
        """Test full integration of batch processing with viewer."""
        main_window, napari_viewer = mock_main_window

        # Create batch processing widget
        batch_widget = BatchProcessingWidget(main_window)

        # Create temporary directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create input directory
            input_dir = os.path.join(temp_dir, "input")
            os.makedirs(input_dir)

            # Create output directory
            output_dir = os.path.join(temp_dir, "output", "test_series")
            os.makedirs(output_dir)

            # Create mock tomogram in output directory
            tomogram_path = os.path.join(output_dir, "test_series_Vol.mrc")
            with open(tomogram_path, 'wb') as f:
                f.write(b'mock tomogram data')

            # Create mock tilt series
            mock_series = Mock()
            mock_series.position_name = "test_series"
            mock_series.files = ["file1.eer", "file2.eer"]
            mock_series.angles = [-30, 0, 30]

            # Add series to batch
            batch_widget.add_series_to_batch(mock_series, input_dir)

            # Simulate processing completion
            batch_widget.update_item_status("test_series", "Completed")

            # Verify that tomogram was found and view icon added
            assert len(batch_widget.batch_items) == 1
            item = batch_widget.batch_items[0]

            # The update_tomogram_after_processing should have been called
            # and found the tomogram file
            if item.get('tomogram_path'):
                # Test viewing the tomogram
                with patch('PyQt6.QtWidgets.QMessageBox.information') as mock_info:
                    batch_widget.view_tomogram(item['tomogram_path'])

                    # Verify viewer was called
                    napari_viewer.load_file.assert_called()

if __name__ == "__main__":
    pytest.main([__file__])
