"""
Core component tests for AreTomo3 GUI system.
Tests core functionality without Qt dependencies.
"""

import pytest
import tempfile
import shutil
import os
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch

# Import the modules to test
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_session_manager():
    """Test session management functionality."""
    from aretomo3_gui.core.session_manager import SessionManager

    temp_dir = tempfile.mkdtemp()
    try:
        session_manager = SessionManager(temp_dir)

        # Test creating a new session
        session = session_manager.create_new_session(
            "Test Session", "batch", "/input", "/output"
        )

        assert session.session_name == "Test Session"
        assert session.processing_mode == "batch"
        assert session.input_directory == "/input"
        assert session.output_directory == "/output"
        assert session.processing_status == "initialized"
        assert session_manager.current_session == session

        # Test updating session
        session_manager.update_session(
            processing_status="running",
            progress_percentage=50.0,
            total_series=10
        )

        assert session_manager.current_session.processing_status == "running"
        assert session_manager.current_session.progress_percentage == 50.0
        assert session_manager.current_session.total_series == 10

        # Test web interface data
        web_data = session_manager.get_session_for_web()
        assert web_data["session_active"] is True
        assert web_data["session_name"] == "Test Session"
        assert web_data["processing_mode"] == "batch"
        assert web_data["progress_percentage"] == 50.0

        print("✅ Session manager test passed")

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_continue_mode_manager():
    """Test continue mode functionality."""
    from aretomo3_gui.core.continue_mode_manager import (
        ContinueModeManager,
        ProcessingState,
    )

    temp_dir = tempfile.mkdtemp()
    try:
        continue_manager = ContinueModeManager(temp_dir)

        # Test session status for non-existent session
        status = continue_manager.get_session_status("nonexistent_session")
        assert status is None

        # Test getting all sessions (should be empty)
        sessions = continue_manager.get_all_sessions()
        assert len(sessions) == 0

        print("✅ Continue mode manager test passed")

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_aretomo3_parser():
    """Test AreTomo3 results parser."""
    from aretomo3_gui.utils.aretomo3_parser import AreTomo3ResultsParser

    temp_dir = tempfile.mkdtemp()
    try:
        # Create mock AreTomo3 output files
        motion_data = """0   0  -10.02   1.91     0.00     0.00
1   0  -10.02   1.91    -2.23     3.00
2   0  -10.02   1.91    -4.47     5.95"""

        with open(os.path.join(temp_dir, "test_series_MC_GL.csv"), 'w') as f:
            f.write(motion_data)

        ctf_data = """# CTF data
   1   18596.46  17802.71     52.28     0.0000    0.0000  977.9200   -1
   2   31533.69  30187.75     52.16     0.0000    0.0900   13.2151   -1"""

        with open(os.path.join(temp_dir, "test_series_CTF.txt"), 'w') as f:
            f.write(ctf_data)

        # Test parser
        parser = AreTomo3ResultsParser(temp_dir)
        parsed_data = parser.parse_all_results()

        assert "motion_data" in parsed_data
        assert "test_series" in parsed_data["motion_data"]

        motion_series = parsed_data["motion_data"]["test_series"]
        assert len(motion_series["frames"]) == 3
        assert len(motion_series["x_shifts"]) == 3
        assert len(motion_series["y_shifts"]) == 3

        assert "ctf_data" in parsed_data
        assert "test_series" in parsed_data["ctf_data"]

        ctf_series = parsed_data["ctf_data"]["test_series"]
        assert len(ctf_series["micrograph_numbers"]) == 2
        assert len(ctf_series["defocus1"]) == 2

        print("✅ AreTomo3 parser test passed")

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_system_integration():
    """Test complete system integration."""
    from aretomo3_gui.core.session_manager import SessionManager
    from aretomo3_gui.core.continue_mode_manager import ContinueModeManager

    temp_dir = tempfile.mkdtemp()
    try:
        session_manager = SessionManager(os.path.join(temp_dir, "sessions"))
        continue_manager = ContinueModeManager(os.path.join(temp_dir, "continue"))

        # Test session creation
        session = session_manager.create_new_session(
            "Integration Test", "batch", "/input", "/output"
        )

        # Test session updates
        session_manager.update_session(
            processing_status="running",
            total_series=5,
            completed_series=["series1", "series2"],
            progress_percentage=40.0
        )

        # Test web interface data
        web_data = session_manager.get_session_for_web()
        assert web_data["session_active"] is True
        assert web_data["progress_percentage"] == 40.0
        assert web_data["completed_series"] == 2

        # Test continue mode integration
        sessions = continue_manager.get_all_sessions()
        assert isinstance(sessions, list)

        # Complete the workflow
        session_manager.update_session(
            processing_status="completed",
            progress_percentage=100.0,
            completed_series=["series1", "series2", "series3", "series4", "series5"]
        )

        final_web_data = session_manager.get_session_for_web()
        assert final_web_data["progress_percentage"] == 100.0
        assert final_web_data["completed_series"] == 5

        print("✅ System integration test passed")

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_error_handling():
    """Test error handling across the system."""
    from aretomo3_gui.core.continue_mode_manager import ContinueModeManager

    # Test continue manager with invalid session
    temp_dir = tempfile.mkdtemp()
    try:
        continue_manager = ContinueModeManager(temp_dir)
        status = continue_manager.get_session_status("nonexistent_session")
        assert status is None

        print("✅ Error handling test passed")

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_real_data_parsing():
    """Test parsing with real AreTomo3 data if available."""
    real_data_path = "/mnt/HDD/ak_devel/sample_data/test_batch/aretomo_output"

    if os.path.exists(real_data_path):
        from aretomo3_gui.utils.aretomo3_parser import AreTomo3ResultsParser

        parser = AreTomo3ResultsParser(real_data_path)
        parsed_data = parser.parse_all_results()

        # Check if we found any data
        motion_data = parsed_data.get('motion_data', {})
        ctf_data = parsed_data.get('ctf_data', {})
        alignment_data = parsed_data.get('alignment_data', {})

        total_series = len(set(list(motion_data.keys()) + list(ctf_data.keys()) + list(alignment_data.keys())))

        print(f"✅ Real data parsing test passed - found {total_series} series")
        print(f"   Motion data: {len(motion_data)} series")
        print(f"   CTF data: {len(ctf_data)} series")
        print(f"   Alignment data: {len(alignment_data)} series")
    else:
        print("⚠️  Real data not available, skipping real data test")

if __name__ == "__main__":
    print("🧪 Running AreTomo3 GUI Core Component Tests")
    print("=" * 50)

    try:
        test_session_manager()
        test_continue_mode_manager()
        test_aretomo3_parser()
        test_system_integration()
        test_error_handling()
        test_real_data_parsing()

        print("=" * 50)
        print("🎉 All core component tests passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
