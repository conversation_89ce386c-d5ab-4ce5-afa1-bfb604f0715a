#!/usr/bin/env python3
"""
Final comprehensive verification of AreTomo3 GUI package installation.
"""

import os
import sys
import subprocess
import tempfile
import importlib
from pathlib import Path


class FinalVerifier:
    """Comprehensive package verification."""
    
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.warnings = 0
        
        # Set Qt environment
        os.environ['QT_API'] = 'pyqt6'
        os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'
    
    def log_test(self, test_name, passed, message=""):
        """Log test result."""
        if passed:
            print(f"✅ {test_name}")
            self.passed_tests += 1
        else:
            print(f"❌ {test_name}: {message}")
            self.failed_tests += 1
    
    def log_warning(self, test_name, message):
        """Log warning."""
        print(f"⚠️  {test_name}: {message}")
        self.warnings += 1
    
    def test_python_version(self):
        """Test Python version compatibility."""
        version_ok = sys.version_info >= (3, 8)
        self.log_test(
            "Python Version Check",
            version_ok,
            f"Python 3.8+ required, found {sys.version}"
        )
        return version_ok
    
    def test_package_installation(self):
        """Test package installation."""
        try:
            import aretomo3_gui
            version = getattr(aretomo3_gui, '__version__', 'unknown')
            self.log_test("Package Installation", True)
            print(f"   Version: {version}")
            return True
        except ImportError as e:
            self.log_test("Package Installation", False, str(e))
            return False
    
    def test_core_dependencies(self):
        """Test core dependencies."""
        dependencies = [
            ("PyQt6", "PyQt6.QtWidgets"),
            ("numpy", "numpy"),
            ("matplotlib", "matplotlib"),
            ("mrcfile", "mrcfile"),
            ("psutil", "psutil"),
            ("Pillow", "PIL"),
            ("scipy", "scipy"),
            ("h5py", "h5py"),
            ("tifffile", "tifffile"),
            ("imageio", "imageio"),
            ("PyYAML", "yaml"),
        ]
        
        all_passed = True
        for dep_name, import_name in dependencies:
            try:
                importlib.import_module(import_name)
                self.log_test(f"Dependency {dep_name}", True)
            except ImportError as e:
                self.log_test(f"Dependency {dep_name}", False, str(e))
                all_passed = False
        
        return all_passed
    
    def test_entry_points(self):
        """Test console entry points."""
        entry_points = ["aretomo3-gui", "at3gui"]
        
        all_passed = True
        for entry_point in entry_points:
            try:
                env = os.environ.copy()
                env['QT_API'] = 'pyqt6'
                env['NAPARI_QT_BACKEND'] = 'pyqt6'
                
                result = subprocess.run(
                    [entry_point, "--version"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    env=env
                )
                if result.returncode == 0:
                    self.log_test(f"Entry point {entry_point}", True)
                else:
                    self.log_test(f"Entry point {entry_point}", False, "Non-zero exit code")
                    all_passed = False
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                self.log_test(f"Entry point {entry_point}", False, str(e))
                all_passed = False
        
        return all_passed
    
    def test_core_modules(self):
        """Test core module imports."""
        modules = [
            "aretomo3_gui.core.config",
            "aretomo3_gui.utils.file_utils",
            "aretomo3_gui.core.realtime_processor",
            "aretomo3_gui.core.tilt_series",
        ]
        
        all_passed = True
        for module in modules:
            try:
                importlib.import_module(module)
                self.log_test(f"Core module {module.split('.')[-1]}", True)
            except ImportError as e:
                self.log_test(f"Core module {module.split('.')[-1]}", False, str(e))
                all_passed = False
        
        return all_passed
    
    def test_gui_components(self):
        """Test GUI components."""
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            
            # Create QApplication if it doesn't exist
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            self.log_test("QApplication Creation", True)
            
            # Test basic GUI import (without full napari)
            try:
                from aretomo3_gui.gui import main_window
                self.log_test("GUI Module Import", True)
            except Exception as e:
                self.log_warning("GUI Module Import", f"May require display: {e}")
            
            return True
            
        except Exception as e:
            self.log_test("GUI Components", False, str(e))
            return False
    
    def test_launcher_script(self):
        """Test the launcher script."""
        launcher_path = Path("aretomo3_gui_launcher.py")
        if launcher_path.exists():
            try:
                result = subprocess.run(
                    [sys.executable, str(launcher_path), "--version"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    self.log_test("Launcher Script", True)
                    return True
                else:
                    self.log_test("Launcher Script", False, "Non-zero exit code")
                    return False
            except Exception as e:
                self.log_test("Launcher Script", False, str(e))
                return False
        else:
            self.log_warning("Launcher Script", "Not found")
            return True
    
    def test_package_integrity(self):
        """Test package file integrity."""
        try:
            # Test that we can access package files
            import aretomo3_gui
            package_path = Path(aretomo3_gui.__file__).parent
            
            required_dirs = ["core", "gui", "utils"]
            missing_dirs = []
            
            for dir_name in required_dirs:
                if not (package_path / dir_name).exists():
                    missing_dirs.append(dir_name)
            
            if missing_dirs:
                self.log_test("Package Integrity", False, f"Missing directories: {missing_dirs}")
                return False
            else:
                self.log_test("Package Integrity", True)
                return True
                
        except Exception as e:
            self.log_test("Package Integrity", False, str(e))
            return False
    
    def run_comprehensive_verification(self):
        """Run all verification tests."""
        print("🔍 AreTomo3 GUI Final Verification")
        print("=" * 60)
        
        # Core tests
        tests = [
            self.test_python_version,
            self.test_package_installation,
            self.test_package_integrity,
            self.test_core_dependencies,
            self.test_core_modules,
            self.test_gui_components,
            self.test_entry_points,
            self.test_launcher_script,
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                print(f"❌ Test {test.__name__} crashed: {e}")
                self.failed_tests += 1
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 Final Verification Summary")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"⚠️  Warnings: {self.warnings}")
        
        if self.failed_tests == 0:
            print("\n🎉 INSTALLATION VERIFICATION SUCCESSFUL!")
            print("AreTomo3 GUI is ready for use.")
            print("\n🚀 Usage Instructions:")
            print("  Method 1: python aretomo3_gui_launcher.py")
            print("  Method 2: QT_API=pyqt6 aretomo3-gui")
            print("  Method 3: export QT_API=pyqt6 && aretomo3-gui")
            return True
        else:
            print(f"\n💥 INSTALLATION VERIFICATION FAILED!")
            print(f"Please fix {self.failed_tests} failed test(s).")
            return False


def main():
    """Main verification process."""
    verifier = FinalVerifier()
    success = verifier.run_comprehensive_verification()
    
    if success:
        print("\n📦 Package Distribution Ready!")
        print("The wheel file in dist/ can be distributed and installed on other systems.")
        return 0
    else:
        print("\n🔧 Additional fixes needed before distribution.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
