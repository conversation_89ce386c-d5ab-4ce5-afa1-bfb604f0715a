#!/usr/bin/env python3
"""
Fix PyQt5/PyQt6 conflicts by ensuring only PyQt6 is used.
"""

import os
import sys
import subprocess


def uninstall_pyqt5():
    """Remove PyQt5 to avoid conflicts."""
    print("🔧 Removing PyQt5 to avoid conflicts...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "uninstall", "PyQt5", "PyQt5-Qt5", "PyQt5-sip", "-y"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PyQt5 removed successfully")
            return True
        else:
            print(f"⚠️  PyQt5 removal had issues: {result.stderr}")
            return True  # Continue anyway
    except Exception as e:
        print(f"❌ Failed to remove PyQt5: {e}")
        return False


def set_qt_api():
    """Set QT_API environment variable to force PyQt6."""
    print("🔧 Setting QT_API to PyQt6...")
    
    os.environ['QT_API'] = 'pyqt6'
    os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'
    
    print("✅ QT_API set to PyQt6")
    return True


def test_qt_import():
    """Test that PyQt6 imports correctly."""
    print("🧪 Testing PyQt6 import...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("✅ PyQt6 imports successfully")
        return True
    except ImportError as e:
        print(f"❌ PyQt6 import failed: {e}")
        return False


def test_napari_import():
    """Test napari import with PyQt6."""
    print("🧪 Testing napari import with PyQt6...")
    
    try:
        # Set environment before importing
        os.environ['QT_API'] = 'pyqt6'
        os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'
        
        import napari
        print("✅ Napari imports successfully with PyQt6")
        return True
    except Exception as e:
        print(f"❌ Napari import failed: {e}")
        return False


def create_qt_launcher():
    """Create a launcher script that sets the correct Qt environment."""
    print("📝 Creating Qt-aware launcher script...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher with Qt environment setup.
"""

import os
import sys

# Force PyQt6 usage
os.environ['QT_API'] = 'pyqt6'
os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'

# Import and run the main application
try:
    from aretomo3_gui.main import main
    sys.exit(main())
except Exception as e:
    print(f"Error launching AreTomo3 GUI: {e}")
    print("\\nTroubleshooting:")
    print("1. Ensure PyQt6 is installed: pip install PyQt6")
    print("2. Check system requirements")
    print("3. Try running in a clean environment")
    sys.exit(1)
'''
    
    try:
        with open('aretomo3_gui_launcher.py', 'w') as f:
            f.write(launcher_content)
        
        # Make it executable
        os.chmod('aretomo3_gui_launcher.py', 0o755)
        
        print("✅ Launcher script created: aretomo3_gui_launcher.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create launcher: {e}")
        return False


def test_gui_components():
    """Test GUI components without full napari."""
    print("🧪 Testing basic GUI components...")
    
    try:
        # Set environment
        os.environ['QT_API'] = 'pyqt6'
        
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import Qt
        
        # Create minimal QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test basic window creation
        window = QMainWindow()
        window.setWindowTitle("Test Window")
        
        print("✅ Basic GUI components work")
        
        # Clean up
        window.close()
        
        return True
    except Exception as e:
        print(f"❌ GUI components test failed: {e}")
        return False


def main():
    """Fix Qt conflicts and test functionality."""
    print("🔧 AreTomo3 GUI Qt Conflict Fixer")
    print("=" * 50)
    
    steps = [
        ("Remove PyQt5", uninstall_pyqt5),
        ("Set Qt API", set_qt_api),
        ("Test PyQt6", test_qt_import),
        ("Test GUI Components", test_gui_components),
        ("Create Launcher", create_qt_launcher),
        ("Test Napari", test_napari_import),
    ]
    
    passed = 0
    failed = 0
    
    for step_name, step_func in steps:
        print(f"\\n🔄 {step_name}...")
        try:
            if step_func():
                passed += 1
                print(f"✅ {step_name} completed")
            else:
                failed += 1
                print(f"❌ {step_name} failed")
        except Exception as e:
            failed += 1
            print(f"❌ {step_name} crashed: {e}")
    
    print("\\n" + "=" * 50)
    print("📊 Fix Summary")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\\n🎉 Qt conflicts fixed successfully!")
        print("\\nNext steps:")
        print("1. Use the launcher: python aretomo3_gui_launcher.py")
        print("2. Or set environment: export QT_API=pyqt6")
        print("3. Then run: aretomo3-gui")
        return 0
    else:
        print(f"\\n💥 {failed} step(s) failed!")
        print("\\nManual steps:")
        print("1. pip uninstall PyQt5 PyQt5-Qt5 PyQt5-sip -y")
        print("2. export QT_API=pyqt6")
        print("3. pip install --upgrade PyQt6")
        return 1


if __name__ == "__main__":
    sys.exit(main())
