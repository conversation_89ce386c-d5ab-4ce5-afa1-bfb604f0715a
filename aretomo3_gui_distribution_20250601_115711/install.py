#!/usr/bin/env python3
"""
AreTomo3 GUI Installation Script
"""

import subprocess
import sys
import os

def main():
    print("🚀 AreTomo3 GUI Installation")
    print("=" * 40)
    
    # Set Qt environment
    os.environ['QT_API'] = 'pyqt6'
    os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'
    
    try:
        # Install the wheel
        print("📦 Installing AreTomo3 GUI...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "aretomo3_gui-1.0.0-py3-none-any.whl"
        ], check=True)
        
        print("✅ Installation completed!")
        print("\n🧪 Running verification...")
        
        # Run quick test
        subprocess.run([sys.executable, "quick_test.py"], check=True)
        
        print("\n🎉 Installation successful!")
        print("\nUsage:")
        print("  python aretomo3_gui_launcher.py")
        print("  QT_API=pyqt6 aretomo3-gui")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        print("\nTroubleshooting:")
        print("  1. Check Python version (3.8+ required)")
        print("  2. Ensure pip is up to date")
        print("  3. Check system requirements")
        sys.exit(1)

if __name__ == "__main__":
    main()
