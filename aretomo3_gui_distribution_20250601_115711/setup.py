#!/usr/bin/env python3
"""
AreTomo3 GUI Setup Script
Production-ready installation script for AreTomo3 GUI v2.0.0
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding='utf-8') if readme_path.exists() else ""

# Read requirements
req_path = Path(__file__).parent / "requirements.txt"
requirements = []
if req_path.exists():
    with open(req_path, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="aretomo3-gui",
    version="2.0.0",
    description="Professional GUI for AreTomo3 tomographic reconstruction with enhanced security",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="AreTomo3 GUI Development Team",
    author_email="<EMAIL>",
    url="https://github.com/aretomo3-gui/aretomo3-gui",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.8",
    entry_points={
        'console_scripts': [
            'aretomo3-gui=aretomo3_gui.main:main',
            'aretomo3-gui-web=aretomo3_gui.web.server:main',
            'aretomo3-gui-cli=aretomo3_gui.cli:main',
        ],
    },
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Operating System :: OS Independent",
        "Environment :: X11 Applications :: Qt",
        "Environment :: Web Environment",
    ],
    keywords="tomography, cryo-em, reconstruction, aretomo3, gui, scientific-computing",
    project_urls={
        "Bug Reports": "https://github.com/aretomo3-gui/aretomo3-gui/issues",
        "Documentation": "https://aretomo3-gui.readthedocs.io/",
        "Source": "https://github.com/aretomo3-gui/aretomo3-gui",
    },
    package_data={
        'aretomo3_gui': [
            'config/*.yaml',
            'config/*.json',
            'web/static/*',
            'web/templates/*',
            'gui/icons/*',
            'gui/styles/*',
        ],
    },
    extras_require={
        'dev': [
            'pytest>=6.0',
            'pytest-qt>=4.0',
            'pytest-cov>=2.0',
            'black>=21.0',
            'flake8>=3.8',
            'mypy>=0.800',
        ],
        'web': [
            'fastapi>=0.68.0',
            'uvicorn>=0.15.0',
            'websockets>=9.0',
        ],
        'analysis': [
            'plotly>=5.0',
            'pandas>=1.3',
            'scipy>=1.7',
            'scikit-image>=0.18',
        ],
        'all': [
            'pytest>=6.0',
            'pytest-qt>=4.0',
            'pytest-cov>=2.0',
            'fastapi>=0.68.0',
            'uvicorn>=0.15.0',
            'plotly>=5.0',
            'pandas>=1.3',
            'scipy>=1.7',
        ],
    },
)
