# AreTomo3 GUI Distribution Package

## Quick Start

1. **Install the package:**
   ```bash
   python install.py
   ```

2. **Launch the application:**
   ```bash
   python aretomo3_gui_launcher.py
   ```

3. **Verify installation:**
   ```bash
   python quick_test.py
   ```

## Files Included

### Core Distribution
- `aretomo3_gui-1.0.0-py3-none-any.whl` - Main package wheel
- `install.py` - Automated installation script
- `aretomo3_gui_launcher.py` - Application launcher

### Verification Tools
- `quick_test.py` - Quick functionality test
- `test_basic_functionality.py` - Core functionality tests
- `final_verification.py` - Comprehensive verification
- `fix_qt_conflicts.py` - Qt conflict resolution

### Documentation
- `INSTALLATION_GUIDE.md` - Detailed installation guide
- `PACKAGE_SUMMARY.md` - Package overview and features
- `README_DISTRIBUTION.md` - This file

### Configuration
- `requirements.txt` - Python dependencies
- `setup.py` - Package setup script
- `pyproject.toml` - Project configuration

## System Requirements

- Python 3.8 or higher
- 4GB RAM (8GB recommended)
- 2GB free disk space
- Graphics card with OpenGL support (recommended)

## Installation Methods

### Method 1: Automated (Recommended)
```bash
python install.py
```

### Method 2: Manual
```bash
pip install aretomo3_gui-1.0.0-py3-none-any.whl
export QT_API=pyqt6
aretomo3-gui --version
```

### Method 3: Using Launcher
```bash
pip install aretomo3_gui-1.0.0-py3-none-any.whl
python aretomo3_gui_launcher.py
```

## Troubleshooting

If you encounter issues:

1. **Run the Qt conflict fixer:**
   ```bash
   python fix_qt_conflicts.py
   ```

2. **Check installation:**
   ```bash
   python final_verification.py
   ```

3. **Manual Qt setup:**
   ```bash
   export QT_API=pyqt6
   export NAPARI_QT_BACKEND=pyqt6
   ```

## Support

For detailed troubleshooting, see `INSTALLATION_GUIDE.md`.

---
Package created: 2025-06-01 11:57:11
Distribution ready for deployment on fresh systems.
