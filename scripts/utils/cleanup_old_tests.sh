#!/bin/bash

# Script to clean up old test files from root directory
# The content has been moved to the organized tests/ structure

echo "🧹 Cleaning up old test files from root directory"
echo "=" * 60

# List of old test files to remove
old_test_files=(
    "test_batch_fix.py"
    "test_batch_fix_verification.py"
    "test_batch_processing.py"
    "test_implementation.py"
    "test_progress_direct.py"
    "test_progress_parsing.py"
    "test_syntax_verification.py"
    "test_syntax_verification_moved.py"
    "verify_batch_fix.py"
    "verify_fix.py"
)

echo "Files to be removed:"
for file in "${old_test_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  📄 $file"
    fi
done

echo
read -p "Do you want to remove these old test files? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo "Removing old test files..."
    
    for file in "${old_test_files[@]}"; do
        if [ -f "$file" ]; then
            rm "$file"
            echo "  ✅ Removed $file"
        else
            echo "  ⚪ $file already removed"
        fi
    done
    
    echo
    echo "✨ Cleanup complete!"
    echo
    echo "🧪 Use the new test structure:"
    echo "   python -m pytest tests/ -v                 # All tests"
    echo "   python -m pytest tests/unit/ -v            # Unit tests"
    echo "   python -m pytest tests/integration/ -v     # Integration tests"
    echo "   ./tests/scripts/test_installation.sh       # Installation verification"
    
else
    echo
    echo "❌ Cleanup cancelled. Old test files remain."
    echo
    echo "💡 Note: The content has been moved to tests/ directory."
    echo "   You can safely remove these files manually when ready."
fi

echo