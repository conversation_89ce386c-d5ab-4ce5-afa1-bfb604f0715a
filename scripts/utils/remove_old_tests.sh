#!/bin/bash

# Remove old empty test files that have been moved to tests/ directory
# These files are now empty and can be safely removed

echo "🧹 Removing old empty test files from root directory"
echo "=" * 60

# Files to remove (now empty after migration)
files_to_remove=(
    "test_batch_fix.py"
    "test_batch_fix_verification.py"
    "test_batch_processing.py"
    "test_implementation.py"
    "test_progress_direct.py"
    "test_progress_parsing.py"
    "test_syntax_verification.py"
    "test_syntax_verification_moved.py"
    "verify_batch_fix.py"
    "verify_fix.py"
)

removed_count=0
for file in "${files_to_remove[@]}"; do
    if [ -f "$file" ]; then
        rm "$file"
        echo "  ✅ Removed $file"
        removed_count=$((removed_count + 1))
    fi
done

echo
if [ $removed_count -gt 0 ]; then
    echo "✨ Removed $removed_count old test files"
else
    echo "✅ No old test files found - directory already clean"
fi

echo
echo "🧪 Use the new organized test structure:"
echo "   python -m pytest tests/ -v                 # All tests"
echo "   python -m pytest tests/unit/ -v            # Unit tests"
echo "   python -m pytest tests/integration/ -v     # Integration tests"
echo "   ./tests/scripts/test_installation.sh       # Installation verification"
echo
echo "📁 All tests are now in the tests/ directory"