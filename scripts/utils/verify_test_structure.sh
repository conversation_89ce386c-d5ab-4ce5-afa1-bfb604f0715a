#!/bin/bash

# Quick test structure verification script
# This script verifies that the new test organization is working correctly

echo "🧪 AT3GUI Test Structure Verification"
echo "=" * 50

# Test 1: Check that test directories exist
echo
echo "1. Checking test directory structure..."

if [ -d "tests" ]; then
    echo "   ✅ tests/ directory exists"
else
    echo "   ❌ tests/ directory missing"
    exit 1
fi

if [ -d "tests/unit" ]; then
    echo "   ✅ tests/unit/ directory exists"
else
    echo "   ❌ tests/unit/ directory missing"
    exit 1
fi

if [ -d "tests/integration" ]; then
    echo "   ✅ tests/integration/ directory exists"
else
    echo "   ❌ tests/integration/ directory missing"
    exit 1
fi

if [ -d "tests/scripts" ]; then
    echo "   ✅ tests/scripts/ directory exists"
else
    echo "   ❌ tests/scripts/ directory missing"
    exit 1
fi

# Test 2: Check that key test files exist
echo
echo "2. Checking key test files..."

test_files=(
    "tests/unit/test_eer_reader.py"
    "tests/unit/test_file_utils.py"
    "tests/unit/test_batch_processing.py"
    "tests/unit/test_syntax_verification.py"
    "tests/integration/test_at3gui_workflow.py"
    "tests/integration/test_implementation.py"
    "tests/integration/test_progress_tracking.py"
    "tests/conftest.py"
)

for test_file in "${test_files[@]}"; do
    if [ -f "$test_file" ]; then
        echo "   ✅ $test_file exists"
    else
        echo "   ❌ $test_file missing"
    fi
done

# Test 3: Check that test scripts are executable
echo
echo "3. Checking test scripts..."

script_files=(
    "tests/scripts/test_eer_build.sh"
    "tests/scripts/test_installation.sh"
    "tests/scripts/benchmark_eer_processing.sh"
)

for script in "${script_files[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo "   ✅ $script exists and is executable"
        else
            echo "   ⚠️  $script exists but not executable"
            chmod +x "$script"
            echo "   🔧 Made $script executable"
        fi
    else
        echo "   ❌ $script missing"
    fi
done

# Test 4: Check for old test files in root directory
echo
echo "4. Checking for old test files in root directory..."

old_test_files=(
    "test_batch_fix.py"
    "test_batch_fix_verification.py" 
    "test_batch_processing.py"
    "test_implementation.py"
    "test_progress_direct.py"
    "test_progress_parsing.py"
    "test_syntax_verification.py"
    "test_syntax_verification_moved.py"
    "verify_batch_fix.py"
    "verify_fix.py"
)

old_files_found=0
for old_file in "${old_test_files[@]}"; do
    if [ -f "$old_file" ]; then
        echo "   ⚠️  $old_file still in root directory (should be moved)"
        old_files_found=$((old_files_found + 1))
    fi
done

if [ $old_files_found -eq 0 ]; then
    echo "   ✅ No old test files in root directory - clean structure!"
else
    echo "   💡 Run ./cleanup_old_tests.sh to remove old test files"
fi

# Test 5: Try basic imports
echo
echo "5. Testing basic Python imports..."

if python -c "import sys; sys.path.insert(0, 'src'); import aretomo3_gui" 2>/dev/null; then
    echo "   ✅ Basic aretomo3_gui import works"
else
    echo "   ⚠️  aretomo3_gui import failed (may be expected in some environments)"
fi

# Test 6: Check pytest configuration
echo
echo "6. Checking pytest configuration..."

if [ -f "pytest.ini" ]; then
    echo "   ✅ pytest.ini exists"
else
    echo "   ❌ pytest.ini missing"
fi

if [ -f "tests/conftest.py" ]; then
    echo "   ✅ tests/conftest.py exists"
else
    echo "   ❌ tests/conftest.py missing"
fi

# Test 7: Try running pytest discovery
echo
echo "7. Testing pytest test discovery..."

if command -v pytest >/dev/null 2>&1; then
    echo "   ✅ pytest is available"
    
    # Try test collection (without running tests)
    if pytest --collect-only tests/ >/dev/null 2>&1; then
        echo "   ✅ pytest can discover tests"
    else
        echo "   ⚠️  pytest test discovery had issues (may need dependencies)"
    fi
else
    echo "   ⚠️  pytest not available (install with: pip install pytest)"
fi

echo
echo "📊 Test Structure Verification Complete!"
echo
echo "🚀 To run tests:"
echo "   python -m pytest tests/ -v                 # All tests"
echo "   python -m pytest tests/unit/ -v            # Unit tests only"
echo "   python -m pytest tests/integration/ -v     # Integration tests only"
echo "   python -m pytest tests/ -m eer -v          # EER tests only"
echo
echo "🔧 To run test scripts:"
echo "   ./tests/scripts/test_installation.sh       # Installation verification"
echo "   ./tests/scripts/test_eer_build.sh          # EER build testing"
echo "   ./tests/scripts/benchmark_eer_processing.sh # Performance benchmarks"
echo
echo "✨ Test structure is ready for use!"