@echo off
REM AreTomo3 GUI Setup Script for Windows
REM Version: 2.0.0

setlocal enabledelayedexpansion

echo ===============================================================
echo AreTomo3 GUI Setup with Integrated EER Support (Windows)
echo ===============================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found! Please install Python 3.8 or higher.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Found Python %PYTHON_VERSION%

REM Check for required tools
echo [INFO] Checking for required tools...

git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Git not found! Please install Git for Windows.
    echo Download from: https://git-scm.com/download/win
    pause
    exit /b 1
)
echo [SUCCESS] Git found

cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] CMake not found. EER support may be limited.
    echo Download from: https://cmake.org/download/
    echo You can continue without CMake, but EER support will use fallback mode.
    set /p CONTINUE="Continue anyway? (y/N): "
    if /i not "!CONTINUE!"=="y" exit /b 1
)

REM Set installation directory
set INSTALL_DIR=%CD%
if not "%1"=="" set INSTALL_DIR=%1

REM Get script directory
set SCRIPT_DIR=%~dp0

echo [INFO] Installation directory: %INSTALL_DIR%
echo [INFO] Script directory: %SCRIPT_DIR%

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Setup repository
echo [INFO] Setting up AreTomo3 GUI repository...

REM Check if we're running from source directory
if exist "%SCRIPT_DIR%setup.py" (
    echo [INFO] Running from source directory, copying files...
    if exist "%INSTALL_DIR%\AT3Gui" (
        echo [WARNING] Installation directory already exists. Removing and recreating...
        rmdir /s /q "%INSTALL_DIR%\AT3Gui"
    )
    xcopy "%SCRIPT_DIR%" "%INSTALL_DIR%\AT3Gui\" /E /I /Y
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to copy source files
        pause
        exit /b 1
    )
    echo [SUCCESS] Source files copied to installation directory
) else (
    echo [ERROR] Please run this script from the AT3Gui source directory
    echo Current directory: %SCRIPT_DIR%
    echo Expected files: setup.py, pyproject.toml, src/, etc.
    pause
    exit /b 1
)

REM Create virtual environment
echo [INFO] Creating virtual environment...
if exist "%INSTALL_DIR%\at3gui_env" (
    echo [WARNING] Virtual environment already exists. Removing and recreating...
    rmdir /s /q "%INSTALL_DIR%\at3gui_env"
)

python -m venv "%INSTALL_DIR%\at3gui_env"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
call "%INSTALL_DIR%\at3gui_env\Scripts\activate.bat"

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip setuptools wheel

REM Install AT3GUI
echo [INFO] Installing AreTomo3 GUI with EER support...
cd /d "%INSTALL_DIR%\AT3Gui"

REM Check for local EerReaderLib source
if exist "%INSTALL_DIR%\AT3Gui\src\EerReaderLib-master" (
    echo [INFO] Found local EerReaderLib source
    if exist cmake.exe (
        echo [INFO] Building EerReaderLib from local source...
        cd /d "%INSTALL_DIR%\AT3Gui\src\EerReaderLib-master"
        if not exist build mkdir build
        cd build
        cmake .. -G "Visual Studio 16 2019" 2>nul || cmake .. 2>nul || (
            echo [WARNING] CMake configuration failed. EER support will use fallback mode.
            goto :install_python
        )
        cmake --build . --config Release 2>nul || (
            echo [WARNING] Build failed. EER support will use fallback mode.
            goto :install_python
        )
        echo [SUCCESS] EerReaderLib built from local source
    ) else (
        echo [WARNING] CMake not found. EER support will use fallback mode.
        echo Install CMake for full EER support: https://cmake.org/download/
    )
) else (
    echo [INFO] Local EerReaderLib source not found
    echo [WARNING] EER support will use fallback mode
)

:install_python
cd /d "%INSTALL_DIR%\AT3Gui"

if exist "install.py" (
    python install.py --no-shortcut
) else (
    python -m pip install -e .[eer]
)

if %errorlevel% neq 0 (
    echo [ERROR] Installation failed
    pause
    exit /b 1
)

REM Test installation
echo [INFO] Testing installation...
python -c "import aretomo3_gui" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Basic import test failed
) else (
    echo [SUCCESS] Basic import test passed
)

REM Test EER support
for /f %%i in ('python -c "from aretomo3_gui.utils.eer_reader import is_eer_supported; print(is_eer_supported())" 2^>nul') do set EER_SUPPORT=%%i
if "%EER_SUPPORT%"=="True" (
    echo [SUCCESS] EER support is available
) else (
    echo [WARNING] EER support not available (will use fallback mode)
)

REM Create launcher batch file
echo [INFO] Creating launcher scripts...
echo @echo off > "%INSTALL_DIR%\run_at3gui.bat"
echo call "%INSTALL_DIR%\at3gui_env\Scripts\activate.bat" >> "%INSTALL_DIR%\run_at3gui.bat"
echo cd /d "%INSTALL_DIR%\AT3Gui" >> "%INSTALL_DIR%\run_at3gui.bat"
echo aretomo3-gui %%* >> "%INSTALL_DIR%\run_at3gui.bat"

echo @echo off > "%INSTALL_DIR%\activate_at3gui.bat"
echo call "%INSTALL_DIR%\at3gui_env\Scripts\activate.bat" >> "%INSTALL_DIR%\activate_at3gui.bat"
echo echo AreTomo3 GUI environment activated! >> "%INSTALL_DIR%\activate_at3gui.bat"
echo echo Run 'aretomo3-gui' to start the application >> "%INSTALL_DIR%\activate_at3gui.bat"
echo cmd /k >> "%INSTALL_DIR%\activate_at3gui.bat"

REM Create desktop shortcut
echo [INFO] Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\AreTomo3 GUI.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\run_at3gui.bat" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%\AT3Gui" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "AreTomo3 GUI with EER Support" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript /nologo "%TEMP%\CreateShortcut.vbs"
del "%TEMP%\CreateShortcut.vbs"

REM Display final instructions
echo.
echo ===============================================================
echo [SUCCESS] AreTomo3 GUI Installation Complete!
echo ===============================================================
echo.
echo Installation Directory: %INSTALL_DIR%
echo Virtual Environment: %INSTALL_DIR%\at3gui_env
echo Repository: %INSTALL_DIR%\AT3Gui
echo.
echo To start AreTomo3 GUI:
echo   Option 1: Double-click "%DESKTOP%\AreTomo3 GUI.lnk"
echo   Option 2: Run "%INSTALL_DIR%\run_at3gui.bat"
echo   Option 3: Run "%INSTALL_DIR%\activate_at3gui.bat" then type "aretomo3-gui"
echo.
echo To test EER support:
echo   Run "%INSTALL_DIR%\activate_at3gui.bat" then type "test-eer-support"
echo.
echo Documentation: %INSTALL_DIR%\AT3Gui\docs\
echo.
echo Happy cryo-EM processing!
echo ===============================================================
echo.
pause