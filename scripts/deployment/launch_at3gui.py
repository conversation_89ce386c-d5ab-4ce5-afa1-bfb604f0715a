#!/usr/bin/env python3
"""
AT3GUI Launcher Script
======================

This script provides a convenient way to launch the AT3GUI application.
It handles environment setup and provides helpful error messages.

Usage:
    python launch_at3gui.py
    ./launch_at3gui.py
"""

import sys
import os
from pathlib import Path

def check_virtual_environment():
    """Check if we're running in a virtual environment."""
    return hasattr(sys, 'real_prefix') or (
        hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
    )

def check_qt_display():
    """Check if QT can access the display."""
    if 'DISPLAY' not in os.environ and sys.platform.startswith('linux'):
        logger.info("⚠️  Warning: No DISPLAY environment variable found.")
        logger.info("   If running over SSH, make sure to use 'ssh -X' for X11 forwarding.")
        logger.info("   Or set QT_QPA_PLATFORM=offscreen for headless mode.")
        logger.info()

    # TODO: Refactor function - Function 'main' too long (76 lines)
def main():
    """Main launcher function."""
    logger.info("🚀 AT3GUI Launcher")
    logger.info("==================")
    logger.info()

    # Check if we're in the correct directory
    current_dir = Path.cwd()
    expected_files = ['src/aretomo3_gui/main.py', 'pyproject.toml']

    for file in expected_files:
        if not (current_dir / file).exists():
            logger.info(f"❌ Error: Expected file '{file}' not found.")
            logger.info(f"   Make sure you're running this from the AT3GUI root directory.")
            logger.info(f"   Current directory: {current_dir}")
            sys.exit(1)

    # Check virtual environment
    if not check_virtual_environment():
        logger.info("⚠️  Warning: Not running in a virtual environment.")
        logger.info("   It's recommended to activate the virtual environment first:")
        logger.info("   source venv/bin/activate")
        logger.info()

        # Check if venv exists
        if (current_dir / 'venv').exists():
            # TODO: Add input validation

            response = input("Would you like to continue anyway? (y/N): ")
            if response.lower() != 'y':
                logger.info("Cancelled. Please activate the virtual environment and try again.")
                sys.exit(0)
        else:
            logger.info("❌ Virtual environment not found. Please run setup_at3gui.sh first.")
            sys.exit(1)
    else:
        logger.info("✅ Running in virtual environment")

    # Check display for GUI
    check_qt_display()

    # Try to import the main modules
    try:
        logger.info("📋 Checking AT3GUI installation...")
        import aretomo3_gui
        logger.info("✅ AT3GUI package found")

        from aretomo3_gui.main import AreTomoGUI
        logger.info("✅ Main GUI class available")

    except ImportError as e:
        logger.info(f"❌ Import error: {e}")
        logger.info("   Please make sure AT3GUI is properly installed:")
        logger.info("   pip install -e .")
        sys.exit(1)

    # Launch the application
    try:
        logger.info("🚀 Launching AT3GUI...")
        logger.info()

        # Import and run the main application
        from aretomo3_gui.main import main as run_app
        run_app()

    except KeyboardInterrupt:
        logger.info("\n👋 AT3GUI closed by user")
        sys.exit(0)
    except Exception as e:
        logger.info(f"\n❌ Error launching AT3GUI: {e}")
        logger.info("\n🔧 Troubleshooting tips:")
        logger.info("1. Make sure you're in a virtual environment with all dependencies")
        logger.info("2. For display issues, try: export QT_QPA_PLATFORM=offscreen")
        logger.info("3. For SSH connections, use: ssh -X username@hostname")
        logger.info("4. Check the logs in src/logs/ for detailed error information")
        sys.exit(1)

if __name__ == "__main__":
    main()
