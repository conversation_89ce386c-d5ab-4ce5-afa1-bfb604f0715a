#!/bin/bash
# AT3GUI File Organization Script
# Organizes project files according to the professional structure

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Options
DRY_RUN=false
VERBOSE=false
BACKUP=true

# Function to display colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    echo "AT3GUI File Organization Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "This script organizes project files according to the professional structure:"
    echo "- Moves utility scripts to scripts/utils/"
    echo "- Organizes temporary files to appropriate locations"
    echo "- Creates necessary directory structure"
    echo "- Archives old files that are no longer needed"
    echo ""
    echo "Options:"
    echo "  --dry-run          Show what would be done without making changes"
    echo "  --verbose          Show detailed output"
    echo "  --no-backup        Don't create backup before organizing"
    echo "  --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                 # Organize files with backup"
    echo "  $0 --dry-run       # Preview changes without applying"
    echo "  $0 --verbose       # Show detailed operations"
    echo ""
}

# Function to create backup
create_backup() {
    if [ "$BACKUP" = true ]; then
        local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
        log_info "Creating backup at: $backup_dir"
        
        if [ "$DRY_RUN" = false ]; then
            mkdir -p "$backup_dir"
            
            # Backup files that will be moved
            cp -r . "$backup_dir/" 2>/dev/null || true
            
            log_success "Backup created successfully"
        else
            log_info "DRY RUN: Would create backup at $backup_dir"
        fi
    fi
}

# Function to create directory structure
create_directories() {
    log_info "Creating directory structure..."
    
    local dirs=(
        "scripts/utils"
        "config"
        "sample_data"
        "tools"
        "archive/old_scripts"
    )
    
    for dir in "${dirs[@]}"; do
        if [ "$DRY_RUN" = false ]; then
            mkdir -p "$dir"
            [ "$VERBOSE" = true ] && log_info "Created directory: $dir"
        else
            log_info "DRY RUN: Would create directory: $dir"
        fi
    done
    
    log_success "Directory structure created"
}

# Function to organize root-level files
organize_root_files() {
    log_info "Organizing root-level files..."
    
    # Move temporary/utility Python scripts to tools/
    local temp_scripts=(
        "comprehensive_pytest_fix.py"
        "final_test_fix.py"
        "fix_failing_tests.py"
        "fix_pytest_warnings.py"
        "quick_fix_pytest.py"
    )
    
    for script in "${temp_scripts[@]}"; do
        if [ -f "$script" ]; then
            if [ "$DRY_RUN" = false ]; then
                mv "$script" "tools/"
                [ "$VERBOSE" = true ] && log_info "Moved $script to tools/"
            else
                log_info "DRY RUN: Would move $script to tools/"
            fi
        fi
    done
    
    # Move old shell scripts to archive
    local old_scripts=(
        "clean_project.sh"
        "reorganize_project.sh"
        "verify_project.sh"
        "activate_and_launch.sh"
        "setup_at3gui.sh"
        "targeted_cleanup.sh"
    )
    
    for script in "${old_scripts[@]}"; do
        if [ -f "$script" ]; then
            if [ "$DRY_RUN" = false ]; then
                mv "$script" "archive/old_scripts/"
                [ "$VERBOSE" = true ] && log_info "Archived $script"
            else
                log_info "DRY RUN: Would archive $script to archive/old_scripts/"
            fi
        fi
    done
    
    log_success "Root-level files organized"
}

# Function to organize scripts directory
organize_scripts() {
    log_info "Organizing scripts directory..."
    
    # Move utility scripts to scripts/utils/
    local util_scripts=(
        "cleanup_old_tests.sh"
        "organize_files_summary.py"
        "remove_old_tests.sh"
        "test_organization_summary.py"
        "validate_deployment.py"
        "verify_test_structure.sh"
    )
    
    for script in "${util_scripts[@]}"; do
        if [ -f "scripts/$script" ]; then
            if [ "$DRY_RUN" = false ]; then
                mv "scripts/$script" "scripts/utils/"
                [ "$VERBOSE" = true ] && log_info "Moved scripts/$script to scripts/utils/"
            else
                log_info "DRY RUN: Would move scripts/$script to scripts/utils/"
            fi
        fi
    done
    
    # Archive old/deprecated scripts
    local old_scripts=(
        "activate_and_launch.sh"
        "deploy.sh"
        "quick_install.sh"
        "setup_at3gui.sh"
        "targeted_cleanup.sh"
    )
    
    for script in "${old_scripts[@]}"; do
        if [ -f "scripts/$script" ]; then
            if [ "$DRY_RUN" = false ]; then
                mv "scripts/$script" "archive/old_scripts/"
                [ "$VERBOSE" = true ] && log_info "Archived scripts/$script"
            else
                log_info "DRY RUN: Would archive scripts/$script"
            fi
        fi
    done
    
    log_success "Scripts directory organized"
}

# Function to organize test files
organize_test_files() {
    log_info "Organizing test files..."
    
    # Move .mrc test files to sample_data if they exist in root
    local mrc_files=(*.mrc)
    if [ "${mrc_files[0]}" != "*.mrc" ]; then
        for file in "${mrc_files[@]}"; do
            if [ -f "$file" ]; then
                if [ "$DRY_RUN" = false ]; then
                    mv "$file" "sample_data/"
                    [ "$VERBOSE" = true ] && log_info "Moved $file to sample_data/"
                else
                    log_info "DRY RUN: Would move $file to sample_data/"
                fi
            fi
        done
    fi
    
    # Move large test data directories from tests/ to sample_data/
    local test_data_dirs=("Test_Input_1" "test_batch")
    for dir in "${test_data_dirs[@]}"; do
        if [ -d "tests/$dir" ]; then
            if [ "$DRY_RUN" = false ]; then
                mv "tests/$dir" "sample_data/"
                [ "$VERBOSE" = true ] && log_info "Moved tests/$dir to sample_data/"
            else
                log_info "DRY RUN: Would move tests/$dir to sample_data/"
            fi
        fi
    done
    
    log_success "Test files organized"
}

# Function to clean up empty files
cleanup_empty_files() {
    log_info "Cleaning up empty files..."
    
    # Find and remove empty files (except those that should be empty)
    local empty_files
    empty_files=$(find . -maxdepth 2 -type f -empty ! -name "__init__.py" ! -path "./.*" 2>/dev/null || true)
    
    if [ -n "$empty_files" ]; then
        while IFS= read -r file; do
            if [ "$DRY_RUN" = false ]; then
                rm "$file"
                [ "$VERBOSE" = true ] && log_info "Removed empty file: $file"
            else
                log_info "DRY RUN: Would remove empty file: $file"
            fi
        done <<< "$empty_files"
    fi
    
    log_success "Empty files cleaned up"
}

# Function to update documentation references
update_documentation() {
    log_info "Updating documentation references..."
    
    # This would update file paths in documentation
    # For now, just log what needs to be done
    log_info "Documentation may need manual updates for moved files"
    
    log_success "Documentation update noted"
}

# Main organization function
main_organize() {
    log_info "Starting AT3GUI project organization..."
    
    # Check if we're in the right directory
    if [ ! -f "README.md" ] || [ ! -d "scripts" ]; then
        log_error "This script must be run from the AT3GUI root directory"
        exit 1
    fi
    
    # Create backup
    create_backup
    
    # Perform organization
    create_directories
    organize_root_files
    organize_scripts
    organize_test_files
    cleanup_empty_files
    update_documentation
    
    log_success "AT3GUI project organization completed successfully!"
    echo ""
    if [ "$DRY_RUN" = true ]; then
        echo "This was a dry run. To apply changes, run without --dry-run"
    else
        echo "Project structure has been organized according to the professional layout."
        echo "Check the archive/ directory for moved files."
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --no-backup)
            BACKUP=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run the main organization
main_organize
