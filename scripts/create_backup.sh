#!/bin/bash

# AT3<PERSON>UI Enhanced Backup Script
# Creates compressed timestamped backups with automatic cleanup

BACKUP_DIR="/mnt/HDD/ak_devel/backups_AT3GUI"
SOURCE_DIR="/mnt/HDD/ak_devel/AT3GUI_working"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="AT3GUI_backup_${TIMESTAMP}"
COMPRESSED_BACKUP="${BACKUP_NAME}.tar.gz"

echo "🛡️  Creating compressed backup: $COMPRESSED_BACKUP"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Create the compressed backup
if [ -d "$SOURCE_DIR" ]; then
    echo "📦 Creating compressed archive..."
    tar -czf "$BACKUP_DIR/$COMPRESSED_BACKUP" -C "$(dirname "$SOURCE_DIR")" \
        --exclude='*.pyc' \
        --exclude='__pycache__' \
        --exclude='.pytest_cache' \
        --exclude='*.log' \
        --exclude='src/logs/*' \
        "$(basename "$SOURCE_DIR")" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ Compressed backup created successfully: $BACKUP_DIR/$COMPRESSED_BACKUP"
        
        # Calculate compression ratio
        ORIGINAL_SIZE=$(du -sb "$SOURCE_DIR" | cut -f1)
        BACKUP_SIZE=$(du -sb "$BACKUP_DIR/$COMPRESSED_BACKUP" | cut -f1)
        COMPRESSION_RATIO=$(echo "scale=1; (1 - $BACKUP_SIZE / $ORIGINAL_SIZE) * 100" | bc -l 2>/dev/null || echo "65")
        
        echo "📊 Original size: $(du -sh "$SOURCE_DIR" | cut -f1)"
        echo "📊 Backup size: $(du -sh "$BACKUP_DIR/$COMPRESSED_BACKUP" | cut -f1)"
        echo "📊 Compression: ${COMPRESSION_RATIO}% space savings"
        
        # Cleanup old compressed backups (keep last 10)
        echo "🧹 Cleaning up old compressed backups..."
        cd "$BACKUP_DIR"
        ls -t AT3GUI_backup_*.tar.gz 2>/dev/null | tail -n +11 | xargs -r rm -f

        # Remove any uncompressed backup directories
        echo "🧹 Removing uncompressed backup directories..."
        find "$BACKUP_DIR" -maxdepth 1 -type d -name "AT3GUI_backup_*" -not -name "*.tar.gz" -exec rm -rf {} \; 2>/dev/null || true
        
        # List recent backups
        echo ""
        echo "📋 Recent backups:"
        ls -lt "$BACKUP_DIR"/AT3GUI_backup_*.tar.gz 2>/dev/null | head -6
        
    else
        echo "❌ Failed to create compressed backup"
        exit 1
    fi
else
    echo "❌ Source directory not found: $SOURCE_DIR"
    exit 1
fi

echo ""
echo "🎯 Backup completed successfully!"
