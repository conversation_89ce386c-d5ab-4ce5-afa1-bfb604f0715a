#!/usr/bin/env python3
"""
Production deployment script for AreTomo3 GUI.
Ensures system is robust, error-free, and ready for deployment.
"""

import os
import sys
import subprocess
import shutil
import json
import logging
from pathlib import Path
from typing import List, Dict, Any
import tempfile

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionDeployment:
    """Handles production deployment of AreTomo3 GUI."""

    def __init__(self, target_dir: str = "/opt/aretomo3_gui"):
        """Initialize the instance."""
        self.target_dir = Path(target_dir)
        self.source_dir = Path(__file__).parent.parent
        self.errors = []
        self.warnings = []

    def run_deployment(self) -> bool:
        """Run complete deployment process."""
        logger.info("🚀 Starting AreTomo3 GUI Production Deployment")

        steps = [
            ("Validating source code", self.validate_source_code),
            ("Running comprehensive tests", self.run_tests),
            ("Checking dependencies", self.check_dependencies),
            ("Validating configuration", self.validate_configuration),
            ("Creating deployment structure", self.create_deployment_structure),
            ("Installing application", self.install_application),
            ("Setting up services", self.setup_services),
            ("Configuring security", self.configure_security),
            ("Running deployment tests", self.run_deployment_tests),
            ("Creating documentation", self.create_documentation)
        ]

        for step_name, step_func in steps:
            logger.info(f"📋 {step_name}...")
            try:
                if not step_func():
                    logger.error(f"❌ Failed: {step_name}")
                    return False
                logger.info(f"✅ Completed: {step_name}")
            except Exception as e:
                logger.error(f"❌ Error in {step_name}: {e}")
                self.errors.append(f"{step_name}: {e}")
                return False

        self.print_deployment_summary()
        return len(self.errors) == 0

    def validate_source_code(self) -> bool:
        """Validate source code quality and completeness."""
        logger.info("Validating Python syntax...")

        # Check Python syntax for all Python files
        python_files = list(self.source_dir.rglob("*.py"))
        for py_file in python_files:
            try:
                with open(py_file, 'r') as f:
                    compile(f.read(), py_file, 'exec')
            except SyntaxError as e:
                self.errors.append(f"Syntax error in {py_file}: {e}")
                return False

        logger.info(f"✅ Validated {len(python_files)} Python files")

        # Check for required core modules
        required_modules = [
            "src/aretomo3_gui/main.py",
            "src/aretomo3_gui/core/session_manager.py",
            "src/aretomo3_gui/core/continue_mode_manager.py",
            "src/aretomo3_gui/gui/tabs/realtime_analysis_tab.py",
            "src/aretomo3_gui/web/api_server.py",
            "src/aretomo3_gui/utils/aretomo3_parser.py"
        ]

        for module in required_modules:
            if not (self.source_dir / module).exists():
                self.errors.append(f"Missing required module: {module}")
                return False

        logger.info("✅ All required modules present")
        return True

    def run_tests(self) -> bool:
        """Run comprehensive test suite."""
        logger.info("Running test suite...")

        test_dir = self.source_dir / "tests"
        if not test_dir.exists():
            self.warnings.append("No tests directory found")
            return True

        try:
            # Run pytest with coverage
            result = subprocess.run([
                sys.executable, "-m", "pytest",
                str(test_dir), "-v", "--tb=short"
            ], capture_output=True, text=True, cwd=self.source_dir)

            if result.returncode != 0:
                self.errors.append(f"Tests failed:\n{result.stdout}\n{result.stderr}")
                return False

            logger.info("✅ All tests passed")
            return True

        except FileNotFoundError:
            self.warnings.append("pytest not available, skipping tests")
            return True

    def check_dependencies(self) -> bool:
        """Check and validate all dependencies."""
        logger.info("Checking dependencies...")

        # Core dependencies
        required_packages = [
            "PyQt6", "matplotlib", "numpy", "fastapi",
            "uvicorn", "pathlib", "json", "logging"
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.lower().replace("-", "_"))
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            self.errors.append(f"Missing required packages: {missing_packages}")
            return False

        logger.info("✅ All dependencies available")
        return True

    def validate_configuration(self) -> bool:
        """Validate configuration files and settings."""
        logger.info("Validating configuration...")

        # Create default configuration
        default_config = {
            "auto_save_interval": 30,
            "max_sessions": 50,
            "web_port": 8080,
            "log_level": "INFO",
            "plot_export_format": "png",
            "quality_thresholds": {
                "motion_good": 1.0,
                "motion_fair": 2.0,
                "ctf_good": 0.8,
                "ctf_fair": 0.6
            },
            "security": {
                "enable_auth": False,
                "session_timeout": 3600,
                "max_file_size": "100MB"
            }
        }

        # Validate configuration structure
        try:
            json.dumps(default_config)
            logger.info("✅ Configuration structure valid")
            return True
        except Exception as e:
            self.errors.append(f"Invalid configuration: {e}")
            return False

    def create_deployment_structure(self) -> bool:
        """Create deployment directory structure."""
        logger.info("Creating deployment structure...")

        try:
            # Create main directories
            directories = [
                self.target_dir,
                self.target_dir / "app",
                self.target_dir / "data",
                self.target_dir / "logs",
                self.target_dir / "config",
                self.target_dir / "sessions",
                self.target_dir / "continue_states",
                self.target_dir / "plots",
                self.target_dir / "backups"
            ]

            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created directory: {directory}")

            # Set appropriate permissions
            os.chmod(self.target_dir, 0o755)
            os.chmod(self.target_dir / "data", 0o755)
            os.chmod(self.target_dir / "logs", 0o755)

            logger.info("✅ Deployment structure created")
            return True

        except Exception as e:
            self.errors.append(f"Failed to create deployment structure: {e}")
            return False

    # TODO: Refactor function - Function 'install_application' too long (51 lines)
    def install_application(self) -> bool:
        """Install application files."""
        logger.info("Installing application...")

        try:
            # Copy source code
            src_target = self.target_dir / "app" / "src"
            if src_target.exists():
                shutil.rmtree(src_target)

            shutil.copytree(
                self.source_dir / "src",
                src_target,
                ignore=shutil.ignore_patterns("__pycache__", "*.pyc", ".pytest_cache")
            )

            # Copy documentation
            docs_target = self.target_dir / "app" / "docs"
            if docs_target.exists():
                shutil.rmtree(docs_target)

            if (self.source_dir / "docs").exists():
                shutil.copytree(self.source_dir / "docs", docs_target)

            # Copy scripts
            scripts_target = self.target_dir / "app" / "scripts"
            if scripts_target.exists():
                shutil.rmtree(scripts_target)

            if (self.source_dir / "scripts").exists():
                shutil.copytree(self.source_dir / "scripts", scripts_target)

            # Create startup script
            startup_script = self.target_dir / "start_aretomo3_gui.sh"
            with open(startup_script, 'w') as f:
                f.write(f"""#!/bin/bash
cd {self.target_dir}/app
export PYTHONPATH={self.target_dir}/app:$PYTHONPATH
export ARETOMO3_CONFIG_DIR={self.target_dir}/config
export ARETOMO3_DATA_DIR={self.target_dir}/data
python -m src.aretomo3_gui.main "$@"
""")

            os.chmod(startup_script, 0o755)

            logger.info("✅ Application installed")
            return True

        except Exception as e:
            self.errors.append(f"Failed to install application: {e}")
            return False

    def setup_services(self) -> bool:
        """Setup system services."""
        logger.info("Setting up services...")

        try:
            # Create systemd service file
            service_content = f"""[Unit]
Description=AreTomo3 GUI Service
After=network.target

[Service]
Type=simple
User=aretomo3
Group=aretomo3
WorkingDirectory={self.target_dir}/app
Environment=PYTHONPATH={self.target_dir}/app
Environment=ARETOMO3_CONFIG_DIR={self.target_dir}/config
Environment=ARETOMO3_DATA_DIR={self.target_dir}/data
ExecStart=/usr/bin/python3 -m src.aretomo3_gui.main --web-only
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""

            service_file = self.target_dir / "aretomo3-gui.service"
            with open(service_file, 'w') as f:
                f.write(service_content)

            logger.info("✅ Service configuration created")
            return True

        except Exception as e:
            self.errors.append(f"Failed to setup services: {e}")
            return False

    def configure_security(self) -> bool:
        """Configure security settings."""
        logger.info("Configuring security...")

        try:
            # Create security configuration
            security_config = {
                "web_interface": {
                    "cors_origins": ["http://localhost:8080"],
                    "max_request_size": "100MB",
                    "rate_limiting": {
                        "enabled": True,
                        "requests_per_minute": 60
                    }
                },
                "file_access": {
                    "allowed_extensions": [".mrc", ".mdoc", ".txt", ".csv", ".log"],
                    "max_file_size": "1GB",
                    "scan_uploads": True
                },
                "session_management": {
                    "session_timeout": 3600,
                    "max_sessions_per_user": 10,
                    "auto_cleanup_days": 30
                }
            }

            security_file = self.target_dir / "config" / "security.json"
            with open(security_file, 'w') as f:
                json.dump(security_config, f, indent=2)

            logger.info("✅ Security configuration created")
            return True

        except Exception as e:
            self.errors.append(f"Failed to configure security: {e}")
            return False

    def run_deployment_tests(self) -> bool:
        """Run deployment-specific tests."""
        logger.info("Running deployment tests...")

        try:
            # Test application startup
            test_script = f"""
import sys
sys.path.insert(0, '{self.target_dir}/app')
try:
    from src.aretomo3_gui.core.session_manager import SessionManager
    from src.aretomo3_gui.core.continue_mode_manager import ContinueModeManager
    from src.aretomo3_gui.utils.aretomo3_parser import AreTomo3ResultsParser
    logger.info("✅ All core modules import successfully")
except Exception as e:
    logger.info(f"❌ Import error: {{e}}")
    sys.exit(1)
"""

            result = subprocess.run([
                sys.executable, "-c", test_script
            ], capture_output=True, text=True)

            if result.returncode != 0:
                self.errors.append(f"Deployment test failed: {result.stderr}")
                return False

            logger.info("✅ Deployment tests passed")
            return True

        except Exception as e:
            self.errors.append(f"Failed to run deployment tests: {e}")
            return False

    # TODO: Refactor function - Function 'create_documentation' too long (62 lines)
    def create_documentation(self) -> bool:
        """Create deployment documentation."""
        logger.info("Creating documentation...")

        try:
            readme_content = f"""# AreTomo3 GUI - Production Deployment

## Installation Directory
{self.target_dir}

## Quick Start
```bash
# Start the application
{self.target_dir}/start_aretomo3_gui.sh

# Start web interface only
{self.target_dir}/start_aretomo3_gui.sh --web-only

# Start with debug mode
{self.target_dir}/start_aretomo3_gui.sh --debug
```

## Configuration
- Main config: {self.target_dir}/config/
- Data directory: {self.target_dir}/data/
- Logs: {self.target_dir}/logs/

## Service Management
```bash
# Install service (requires sudo)
sudo cp {self.target_dir}/aretomo3-gui.service /etc/systemd/system/
sudo systemctl enable aretomo3-gui
sudo systemctl start aretomo3-gui

# Check status
sudo systemctl status aretomo3-gui

# View logs
sudo journalctl -u aretomo3-gui -f
```

## Troubleshooting
- Check logs in {self.target_dir}/logs/
- Verify permissions on data directories
- Ensure all dependencies are installed
- Check network configuration for web interface

## Support
For issues and support, check the comprehensive documentation in:
{self.target_dir}/app/docs/COMPREHENSIVE_SYSTEM_GUIDE.md
"""

            readme_file = self.target_dir / "README.md"
            with open(readme_file, 'w') as f:
                f.write(readme_content)

            logger.info("✅ Documentation created")
            return True

        except Exception as e:
            self.errors.append(f"Failed to create documentation: {e}")
            return False

    def print_deployment_summary(self):
        """Print deployment summary."""
        logger.info("\n" + "="*60)
        logger.info("🎯 ARETOMO3 GUI DEPLOYMENT SUMMARY")
        logger.info("="*60)

        if len(self.errors) == 0:
            logger.info("✅ DEPLOYMENT SUCCESSFUL!")
            logger.info(f"📁 Installation directory: {self.target_dir}")
            logger.info(f"🚀 Start command: {self.target_dir}/start_aretomo3_gui.sh")
            logger.info("🌐 Web interface will be available at: http://localhost:8080")
        else:
            logger.info("❌ DEPLOYMENT FAILED!")
            logger.info("\nErrors:")
            for error in self.errors:
                logger.info(f"  - {error}")

        if self.warnings:
            logger.info("\nWarnings:")
            for warning in self.warnings:
                logger.info(f"  - {warning}")

        logger.info("\n📚 Documentation: {}/README.md".format(self.target_dir))
        logger.info("="*60)

def main():
    """Main deployment function."""
    import argparse

    parser = argparse.ArgumentParser(description="Deploy AreTomo3 GUI for production")
    parser.add_argument("--target", default="/opt/aretomo3_gui",
                       help="Target installation directory")
    parser.add_argument("--force", action="store_true",
                       help="Force deployment even if target exists")

    args = parser.parse_args()

    # Check if target exists
    if Path(args.target).exists() and not args.force:
        logger.info(f"❌ Target directory {args.target} already exists.")
        logger.info("Use --force to overwrite or choose a different target.")
        sys.exit(1)

    # Run deployment
    deployment = ProductionDeployment(args.target)
    success = deployment.run_deployment()

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
