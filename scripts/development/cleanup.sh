#!/bin/bash
# AT3GUI Cleanup Script
# Professional cleanup with comprehensive options

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
CLEAN_BUILD=true
CLEAN_CACHE=true
CLEAN_PYTEST=true
CLEAN_LOGS=true
CLEAN_TEMP=true
DEEP_CLEAN=false
DRY_RUN=false
VERBOSE=false

# Show help
show_help() {
    echo "AT3GUI Cleanup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --build-only         Only clean build artifacts"
    echo "  --cache-only         Only clean cache files"
    echo "  --logs-only          Only clean log files"
    echo "  --deep               Deep clean (includes .pyc, .pyo, __pycache__)"
    echo "  --dry-run            Show what would be cleaned without deleting"
    echo "  --verbose            Enable verbose output"
    echo "  --help               Show this help message"
    echo ""
    echo "Default: Cleans build/, dist/, cache, pytest cache, and logs"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --build-only)
            CLEAN_BUILD=true
            CLEAN_CACHE=false
            CLEAN_PYTEST=false
            CLEAN_LOGS=false
            CLEAN_TEMP=false
            shift
            ;;
        --cache-only)
            CLEAN_BUILD=false
            CLEAN_CACHE=true
            CLEAN_PYTEST=true
            CLEAN_LOGS=false
            CLEAN_TEMP=false
            shift
            ;;
        --logs-only)
            CLEAN_BUILD=false
            CLEAN_CACHE=false
            CLEAN_PYTEST=false
            CLEAN_LOGS=true
            CLEAN_TEMP=false
            shift
            ;;
        --deep)
            DEEP_CLEAN=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Cleanup function
cleanup_directory() {
    local dir_path="$1"
    local description="$2"
    
    if [[ -d "$dir_path" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            print_info "Would remove: $dir_path ($description)"
            if [[ "$VERBOSE" == true ]]; then
                find "$dir_path" -type f 2>/dev/null | head -5
                local count=$(find "$dir_path" -type f 2>/dev/null | wc -l)
                if [[ $count -gt 5 ]]; then
                    print_info "  ... and $((count - 5)) more files"
                fi
            fi
        else
            print_info "Removing $description..."
            rm -rf "$dir_path"
            print_success "Removed $description"
        fi
    else
        if [[ "$VERBOSE" == true ]]; then
            print_info "$description not found (already clean)"
        fi
    fi
}

# Cleanup files by pattern
cleanup_pattern() {
    local pattern="$1"
    local description="$2"
    
    local files=$(find "$PROJECT_DIR" -name "$pattern" -type f 2>/dev/null || true)
    if [[ -n "$files" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            print_info "Would remove: $description"
            if [[ "$VERBOSE" == true ]]; then
                echo "$files" | head -5
                local count=$(echo "$files" | wc -l)
                if [[ $count -gt 5 ]]; then
                    print_info "  ... and $((count - 5)) more files"
                fi
            fi
        else
            print_info "Removing $description..."
            find "$PROJECT_DIR" -name "$pattern" -type f -delete 2>/dev/null || true
            print_success "Removed $description"
        fi
    else
        if [[ "$VERBOSE" == true ]]; then
            print_info "$description not found (already clean)"
        fi
    fi
}

# Main cleanup function
main() {
    print_header "🧹 AT3GUI Cleanup"
    print_header "========================================"
    echo ""
    
    if [[ "$DRY_RUN" == true ]]; then
        print_warning "DRY RUN MODE - No files will be deleted"
        echo ""
    fi
    
    print_info "Project directory: $PROJECT_DIR"
    echo ""
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_DIR/pyproject.toml" ]]; then
        print_error "pyproject.toml not found. Please run this script from the AT3GUI directory."
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    
    # Clean build artifacts
    if [[ "$CLEAN_BUILD" == true ]]; then
        print_header "🏗️  Cleaning Build Artifacts"
        cleanup_directory "build" "build artifacts"
        cleanup_directory "dist" "distribution packages"
        cleanup_directory "*.egg-info" "egg info directories"
        cleanup_pattern "*.egg-info" "egg info files"
        echo ""
    fi
    
    # Clean cache files
    if [[ "$CLEAN_CACHE" == true ]]; then
        print_header "🗂️  Cleaning Cache Files"
        cleanup_directory "__pycache__" "Python cache directories"
        cleanup_pattern "*.pyc" "compiled Python files (.pyc)"
        cleanup_pattern "*.pyo" "optimized Python files (.pyo)"
        
        # Deep clean for more cache files
        if [[ "$DEEP_CLEAN" == true ]]; then
            find "$PROJECT_DIR" -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
            cleanup_pattern ".coverage" "coverage files"
            cleanup_directory ".mypy_cache" "MyPy cache"
            cleanup_directory ".ruff_cache" "Ruff cache"
        fi
        echo ""
    fi
    
    # Clean pytest cache
    if [[ "$CLEAN_PYTEST" == true ]]; then
        print_header "🧪 Cleaning Test Cache"
        cleanup_directory ".pytest_cache" "pytest cache"
        cleanup_directory "htmlcov" "coverage HTML reports"
        cleanup_pattern ".coverage.*" "coverage data files"
        echo ""
    fi
    
    # Clean log files
    if [[ "$CLEAN_LOGS" == true ]]; then
        print_header "📝 Cleaning Log Files"
        cleanup_pattern "*.log" "log files"
        cleanup_pattern "*.log.*" "rotated log files"
        cleanup_directory "logs" "log directory"
        echo ""
    fi
    
    # Clean temporary files
    if [[ "$CLEAN_TEMP" == true ]]; then
        print_header "🗑️  Cleaning Temporary Files"
        cleanup_pattern "*~" "backup files"
        cleanup_pattern "*.tmp" "temporary files"
        cleanup_pattern "*.temp" "temp files"
        cleanup_pattern ".DS_Store" "macOS metadata files"
        cleanup_pattern "Thumbs.db" "Windows thumbnail cache"
        echo ""
    fi
    
    # Show summary
    if [[ "$DRY_RUN" == true ]]; then
        print_header "📋 Cleanup Summary (Dry Run)"
        print_info "Run without --dry-run to actually clean these files"
    else
        print_header "✨ Cleanup Complete!"
        print_success "Project cleaned successfully"
        
        # Show disk space saved (estimate)
        if command -v du >/dev/null 2>&1; then
            local size=$(du -sh "$PROJECT_DIR" 2>/dev/null | cut -f1 || echo "unknown")
            print_info "Current project size: $size"
        fi
    fi
    
    echo ""
    print_info "Cleanup completed at $(date)"
}

# Run main function
main "$@"
