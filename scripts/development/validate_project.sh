#!/bin/bash
# AT3GUI Project Validation Script
# Comprehensive validation of the organized project
#
# VERIFIED: AT3GUI does NOT install pytom_tm conda environment
# - AT3G<PERSON> only creates standard Python virtual environments (venv)
# - No references to conda, PyTOM, or template matching in codebase
# - pytom_tm is a separate downstream analysis environment
# - AT3GUI focuses on tomographic reconstruction, not particle picking

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 AT3GUI Project Validation"
echo "============================="

# Check directory structure
log_info "Validating directory structure..."
required_dirs=("scripts" "docs" "src" "tests" "tools" "archive" "sample_data" "config")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        log_success "✓ $dir directory exists"
    else
        log_error "✗ $dir directory missing"
    fi
done

# Check essential scripts
log_info "Validating essential scripts..."
essential_scripts=("install.sh" "launch.sh" "cleanup.sh" "update.sh" "uninstall.sh")
for script in "${essential_scripts[@]}"; do
    if [ -f "scripts/$script" ] && [ -x "scripts/$script" ]; then
        log_success "✓ scripts/$script exists and is executable"
    else
        log_error "✗ scripts/$script missing or not executable"
    fi
done

# Check documentation
log_info "Validating documentation..."
essential_docs=("README.md" "docs/USER_GUIDE.md" "docs/DEVELOPER_GUIDE.md" "docs/API_REFERENCE.md" "docs/TROUBLESHOOTING.md" "docs/INSTALLATION.md")
for doc in "${essential_docs[@]}"; do
    if [ -f "$doc" ]; then
        log_success "✓ $doc exists"
    else
        log_error "✗ $doc missing"
    fi
done

# Test script functionality
log_info "Testing script functionality..."
# Scripts to skip (ones that don't have --help or might hang)
skip_scripts=("validate_project.sh" "organize_files.sh" "setup_at3gui.bat")
for script in scripts/*.sh; do
    script_name=$(basename "$script")
    if [[ " ${skip_scripts[@]} " =~ " ${script_name} " ]]; then
        log_info "⏸ Skipping $script_name (validation/utility script)"
        continue
    fi
    
    # Use timeout to prevent hanging
    if timeout 3s bash "$script" --help >/dev/null 2>&1; then
        log_success "✓ $script_name help function works"
    else
        log_warning "⚠ $script_name help function may have issues or timeout"
    fi
done

# Check Python package structure
log_info "Validating Python package structure..."
if [ -f "src/aretomo3_gui/__init__.py" ]; then
    log_success "✓ Python package structure is correct"
else
    log_error "✗ Python package structure needs attention"
fi

# Check configuration files
log_info "Validating configuration files..."
config_files=("pyproject.toml" "requirements.txt" "pytest.ini")
for config in "${config_files[@]}"; do
    if [ -f "$config" ]; then
        log_success "✓ $config exists"
    else
        log_error "✗ $config missing"
    fi
done

# Summary
echo ""
echo "🎯 Validation Summary"
echo "===================="
log_success "Project structure validation completed"
log_info "Review any errors or warnings above"
echo ""
echo "Next steps:"
echo "1. Run 'scripts/install.sh' to test installation"
echo "2. Run 'scripts/launch.sh' to test application launch"
echo "3. Run 'scripts/cleanup.sh --dry-run' to test cleanup"
echo ""
