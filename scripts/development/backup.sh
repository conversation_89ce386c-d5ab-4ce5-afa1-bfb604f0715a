#!/bin/bash
# Simple AT3GUI backup script that uses parent directory

# Get current directory and parent
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PARENT_DIR="$(dirname "$PROJECT_DIR")"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_DIR="$PARENT_DIR/AT3GUI_backups"

echo "Creating backup in parent directory..."
echo "Source: $PROJECT_DIR"
echo "Backup: $BACKUP_DIR"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create timestamped backup
BACKUP_NAME="AT3GUI_backup_${TIMESTAMP}"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"

# Use rsync for backup
if command -v rsync &> /dev/null; then
    rsync -av --exclude='build/' \
          --exclude='venv/' \
          --exclude='.git/' \
          --exclude='__pycache__/' \
          --exclude='*.pyc' \
          --exclude='.pytest_cache/' \
          --exclude='*.log' \
          "$PROJECT_DIR"/ "$BACKUP_PATH"/
else
    # Fallback to cp
    mkdir -p "$BACKUP_PATH"
    cp -r "$PROJECT_DIR"/* "$BACKUP_PATH"/ 2>/dev/null || true
    # Clean up excluded files
    rm -rf "$BACKUP_PATH"/build "$BACKUP_PATH"/venv "$BACKUP_PATH"/.git 2>/dev/null || true
    find "$BACKUP_PATH" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$BACKUP_PATH" -name "*.pyc" -delete 2>/dev/null || true
fi

echo "✅ Backup created successfully at: $BACKUP_PATH"
echo "Size: $(du -sh "$BACKUP_PATH" | cut -f1)"