#!/usr/bin/env python3
"""
Clean Environment Testing Script
Tests the AreTomo3 GUI in a completely clean environment.
"""

import os
import sys
import subprocess
import tempfile
import shutil
import logging
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CleanEnvironmentTester:
    """Clean environment testing manager."""
    
    def __init__(self, source_dir: Path):
        self.source_dir = Path(source_dir)
        self.test_dir = None
        self.venv_dir = None
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def create_clean_environment(self):
        """Create a completely clean testing environment."""
        logger.info("Creating clean testing environment...")
        
        # Create temporary directory for testing
        self.test_dir = Path(tempfile.mkdtemp(prefix=f"at3gui_test_{self.timestamp}_"))
        logger.info(f"Test directory: {self.test_dir}")
        
        # Copy only essential files
        essential_files = [
            "src/",
            "pyproject.toml",
            "requirements.txt",
            "README.md",
            "LICENSE",
            "CHANGELOG.md"
        ]
        
        for item in essential_files:
            src_path = self.source_dir / item
            dst_path = self.test_dir / item
            
            if src_path.is_dir():
                shutil.copytree(src_path, dst_path)
                logger.info(f"Copied directory: {item}")
            elif src_path.is_file():
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst_path)
                logger.info(f"Copied file: {item}")
            else:
                logger.warning(f"Source not found: {item}")
                
        return self.test_dir
        
    def create_virtual_environment(self):
        """Create a clean virtual environment."""
        logger.info("Creating virtual environment...")
        
        self.venv_dir = self.test_dir / "venv"
        
        # Create virtual environment
        result = subprocess.run([
            sys.executable, "-m", "venv", str(self.venv_dir)
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to create virtual environment: {result.stderr}")
            return False
            
        logger.info("Virtual environment created successfully")
        return True
        
    def install_dependencies(self):
        """Install dependencies in the clean environment."""
        logger.info("Installing dependencies...")
        
        # Get pip executable
        if os.name == 'nt':  # Windows
            pip_exe = self.venv_dir / "Scripts" / "pip.exe"
            python_exe = self.venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            pip_exe = self.venv_dir / "bin" / "pip"
            python_exe = self.venv_dir / "bin" / "python"
            
        # Upgrade pip
        result = subprocess.run([
            str(python_exe), "-m", "pip", "install", "--upgrade", "pip"
        ], capture_output=True, text=True, cwd=self.test_dir)
        
        if result.returncode != 0:
            logger.error(f"Failed to upgrade pip: {result.stderr}")
            return False
            
        # Install requirements
        requirements_file = self.test_dir / "requirements.txt"
        if requirements_file.exists():
            result = subprocess.run([
                str(pip_exe), "install", "-r", str(requirements_file)
            ], capture_output=True, text=True, cwd=self.test_dir)
            
            if result.returncode != 0:
                logger.error(f"Failed to install requirements: {result.stderr}")
                return False
                
        # Install package in development mode
        result = subprocess.run([
            str(pip_exe), "install", "-e", "."
        ], capture_output=True, text=True, cwd=self.test_dir)
        
        if result.returncode != 0:
            logger.error(f"Failed to install package: {result.stderr}")
            return False
            
        logger.info("Dependencies installed successfully")
        return True
        
    def test_imports(self):
        """Test all critical imports."""
        logger.info("Testing imports...")
        
        # Get python executable
        if os.name == 'nt':  # Windows
            python_exe = self.venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            python_exe = self.venv_dir / "bin" / "python"
            
        # Test script
        test_script = '''
import sys
import os

# Set offscreen platform for GUI testing
os.environ["QT_QPA_PLATFORM"] = "offscreen"

try:
    import aretomo3_gui
    print("✅ aretomo3_gui package imported")
except Exception as e:
    print(f"❌ aretomo3_gui import failed: {e}")
    sys.exit(1)

try:
    from aretomo3_gui.gui.main_window import AreTomo3MainWindow
    print("✅ Main window imported")
except Exception as e:
    print(f"❌ Main window import failed: {e}")
    sys.exit(1)

try:
    from aretomo3_gui.gui.tabs.enhanced_analysis_tab import EnhancedAnalysisTab
    print("✅ Enhanced analysis tab imported")
except Exception as e:
    print(f"❌ Enhanced analysis tab import failed: {e}")
    sys.exit(1)

try:
    from aretomo3_gui.gui.tabs.enhanced_parameters_tab import EnhancedParametersTab
    print("✅ Enhanced parameters tab imported")
except Exception as e:
    print(f"❌ Enhanced parameters tab import failed: {e}")
    sys.exit(1)

try:
    from aretomo3_gui.gui.tabs.live_processing_tab import LiveProcessingTab
    print("✅ Live processing tab imported")
except Exception as e:
    print(f"❌ Live processing tab import failed: {e}")
    sys.exit(1)

print("🎉 All imports successful!")
'''
        
        # Run test script
        result = subprocess.run([
            str(python_exe), "-c", test_script
        ], capture_output=True, text=True, cwd=self.test_dir)
        
        logger.info("Import test output:")
        logger.info(result.stdout)
        
        if result.stderr:
            logger.error("Import test errors:")
            logger.error(result.stderr)
            
        return result.returncode == 0
        
    def test_gui_creation(self):
        """Test GUI creation without display."""
        logger.info("Testing GUI creation...")
        
        # Get python executable
        if os.name == 'nt':  # Windows
            python_exe = self.venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            python_exe = self.venv_dir / "bin" / "python"
            
        # GUI test script
        gui_test_script = '''
import sys
import os

# Set offscreen platform for GUI testing
os.environ["QT_QPA_PLATFORM"] = "offscreen"

try:
    from PyQt6.QtWidgets import QApplication
    from aretomo3_gui.gui.main_window import AreTomo3MainWindow
    
    app = QApplication([])
    window = AreTomo3MainWindow()
    
    print(f"✅ GUI created successfully")
    print(f"✅ Main window has {window.tab_widget.count()} tabs:")
    
    for i in range(window.tab_widget.count()):
        tab_text = window.tab_widget.tabText(i)
        print(f"   - Tab {i}: {tab_text}")
    
    # Test if enhanced tabs are present
    tab_texts = [window.tab_widget.tabText(i) for i in range(window.tab_widget.count())]
    
    required_tabs = ["📊 Analysis", "⚙️ Parameters", "🔴 Live Processing"]
    missing_tabs = []
    
    for required_tab in required_tabs:
        if required_tab in tab_texts:
            print(f"✅ {required_tab} tab found")
        else:
            print(f"❌ {required_tab} tab missing")
            missing_tabs.append(required_tab)
    
    app.quit()
    
    if missing_tabs:
        print(f"❌ Missing tabs: {missing_tabs}")
        sys.exit(1)
    else:
        print("🎉 All required tabs present!")
        
except Exception as e:
    print(f"❌ GUI creation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''
        
        # Run GUI test script
        result = subprocess.run([
            str(python_exe), "-c", gui_test_script
        ], capture_output=True, text=True, cwd=self.test_dir, timeout=60)
        
        logger.info("GUI test output:")
        logger.info(result.stdout)
        
        if result.stderr:
            logger.error("GUI test errors:")
            logger.error(result.stderr)
            
        return result.returncode == 0
        
    def test_command_line_interface(self):
        """Test command line interface."""
        logger.info("Testing command line interface...")
        
        # Get python executable
        if os.name == 'nt':  # Windows
            python_exe = self.venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            python_exe = self.venv_dir / "bin" / "python"
            
        # Test CLI help
        result = subprocess.run([
            str(python_exe), "-m", "aretomo3_gui", "--help"
        ], capture_output=True, text=True, cwd=self.test_dir, timeout=30)
        
        logger.info("CLI test output:")
        logger.info(result.stdout)
        
        if result.stderr:
            logger.error("CLI test errors:")
            logger.error(result.stderr)
            
        # CLI should show help and exit with 0
        return result.returncode == 0 and "usage:" in result.stdout.lower()
        
    def cleanup(self):
        """Clean up test environment."""
        if self.test_dir and self.test_dir.exists():
            logger.info(f"Cleaning up test directory: {self.test_dir}")
            shutil.rmtree(self.test_dir)
            
    def run_complete_test(self):
        """Run complete clean environment test."""
        logger.info("🚀 Starting clean environment test...")
        
        try:
            # Create clean environment
            test_dir = self.create_clean_environment()
            logger.info(f"✅ Clean environment created: {test_dir}")
            
            # Create virtual environment
            if not self.create_virtual_environment():
                logger.error("❌ Virtual environment creation failed")
                return False
            logger.info("✅ Virtual environment created")
            
            # Install dependencies
            if not self.install_dependencies():
                logger.error("❌ Dependency installation failed")
                return False
            logger.info("✅ Dependencies installed")
            
            # Test imports
            if not self.test_imports():
                logger.error("❌ Import tests failed")
                return False
            logger.info("✅ Import tests passed")
            
            # Test GUI creation
            if not self.test_gui_creation():
                logger.error("❌ GUI creation tests failed")
                return False
            logger.info("✅ GUI creation tests passed")
            
            # Test CLI
            if not self.test_command_line_interface():
                logger.error("❌ CLI tests failed")
                return False
            logger.info("✅ CLI tests passed")
            
            logger.info("🎉 ALL CLEAN ENVIRONMENT TESTS PASSED!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Clean environment test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            # Always cleanup
            self.cleanup()

def main():
    """Main function."""
    # Get source directory (project root)
    source_dir = Path(__file__).parent.parent.parent
    
    # Run clean environment test
    tester = CleanEnvironmentTester(source_dir)
    success = tester.run_complete_test()
    
    if success:
        print("\n🎉 CLEAN ENVIRONMENT TEST SUCCESSFUL!")
        print("✅ Project is ready for professional deployment")
        sys.exit(0)
    else:
        print("\n❌ CLEAN ENVIRONMENT TEST FAILED!")
        print("⚠️  Please review errors above")
        sys.exit(1)

if __name__ == "__main__":
    main()
