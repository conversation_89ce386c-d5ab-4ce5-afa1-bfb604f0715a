#!/usr/bin/env python3
"""
Professional Project Validation Script
Validates the entire project structure, code quality, and documentation.
"""

import os
import sys
import ast
import logging
from pathlib import Path
from typing import List, Dict, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProjectValidator:
    """Professional project validation manager."""

    def __init__(self, project_root: Path):
        """Initialize the instance."""
        self.project_root = Path(project_root)
        self.errors = []
        self.warnings = []
        self.passed_checks = []

    def validate_project_structure(self) -> bool:
        """Validate professional project structure."""
        logger.info("Validating project structure...")

        # Required files
        required_files = [
            "README.md",
            "LICENSE",
            "CHANGELOG.md",
            "pyproject.toml",
            "requirements.txt",
            "src/aretomo3_gui/__init__.py",
            "src/aretomo3_gui/main.py"
        ]

        # Required directories
        required_dirs = [
            "src/aretomo3_gui",
            "src/aretomo3_gui/gui",
            "src/aretomo3_gui/core",
            "src/aretomo3_gui/utils",
            "tests",
            "docs",
            "scripts"
        ]

        structure_valid = True

        # Check required files
        for file_path in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.passed_checks.append(f"✅ Required file exists: {file_path}")
            else:
                self.errors.append(f"❌ Missing required file: {file_path}")
                structure_valid = False

        # Check required directories
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                self.passed_checks.append(f"✅ Required directory exists: {dir_path}")
            else:
                self.errors.append(f"❌ Missing required directory: {dir_path}")
                structure_valid = False

        return structure_valid

    def validate_python_syntax(self) -> bool:
        """Validate Python syntax in all source files."""
        logger.info("Validating Python syntax...")

        syntax_valid = True
        python_files = list(self.project_root.rglob("*.py"))

        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Parse the file to check syntax
                ast.parse(content, filename=str(py_file))
                self.passed_checks.append(f"✅ Syntax valid: {py_file.relative_to(self.project_root)}")

            except SyntaxError as e:
                self.errors.append(f"❌ Syntax error in {py_file.relative_to(self.project_root)}: {e}")
                syntax_valid = False
            except Exception as e:
                self.warnings.append(f"⚠️  Could not parse {py_file.relative_to(self.project_root)}: {e}")

        return syntax_valid

    def validate_imports(self) -> bool:
        """Validate import statements."""
        logger.info("Validating imports...")

        imports_valid = True
        src_dir = self.project_root / "src"

        if not src_dir.exists():
            self.errors.append("❌ Source directory not found")
            return False

        # Add src to path for import testing
        sys.path.insert(0, str(src_dir))

        try:
            # Test main package import
            import aretomo3_gui
            self.passed_checks.append("✅ Main package imports successfully")

            # Test main modules
            from aretomo3_gui.gui.main_window import AreTomo3MainWindow
            self.passed_checks.append("✅ Main window imports successfully")

            from aretomo3_gui.gui.tabs.enhanced_analysis_tab import EnhancedAnalysisTab
            self.passed_checks.append("✅ Enhanced analysis tab imports successfully")

            from aretomo3_gui.gui.tabs.enhanced_parameters_tab import EnhancedParametersTab
            self.passed_checks.append("✅ Enhanced parameters tab imports successfully")

            from aretomo3_gui.gui.tabs.live_processing_tab import LiveProcessingTab
            self.passed_checks.append("✅ Live processing tab imports successfully")

        except ImportError as e:
            self.errors.append(f"❌ Import error: {e}")
            imports_valid = False
        except Exception as e:
            self.errors.append(f"❌ Unexpected import error: {e}")
            imports_valid = False
        finally:
            # Remove from path
            if str(src_dir) in sys.path:
                sys.path.remove(str(src_dir))

        return imports_valid

    def validate_documentation(self) -> bool:
        """Validate documentation completeness."""
        logger.info("Validating documentation...")

        docs_valid = True

        # Check main documentation files
        doc_files = [
            "README.md",
            "CHANGELOG.md",
            "LICENSE",
            "docs/user/USER_GUIDE.md",
            "docs/user/INSTALLATION.md",
            "docs/developer/DEVELOPER_GUIDE.md",
            "docs/api/API_REFERENCE.md"
        ]

        for doc_file in doc_files:
            full_path = self.project_root / doc_file
            if full_path.exists():
                # Check if file has content
                if full_path.stat().st_size > 100:  # At least 100 bytes
                    self.passed_checks.append(f"✅ Documentation exists and has content: {doc_file}")
                else:
                    self.warnings.append(f"⚠️  Documentation file is too small: {doc_file}")
            else:
                self.errors.append(f"❌ Missing documentation: {doc_file}")
                docs_valid = False

        return docs_valid

    def validate_configuration(self) -> bool:
        """Validate configuration files."""
        logger.info("Validating configuration...")

        config_valid = True

        # Check pyproject.toml
        pyproject_file = self.project_root / "pyproject.toml"
        if pyproject_file.exists():
            try:
                import tomllib
                with open(pyproject_file, 'rb') as f:
                    config = tomllib.load(f)

                # Check required sections
                required_sections = ['project', 'build-system']
                for section in required_sections:
                    if section in config:
                        self.passed_checks.append(f"✅ pyproject.toml has {section} section")
                    else:
                        self.errors.append(f"❌ pyproject.toml missing {section} section")
                        config_valid = False

            except Exception as e:
                self.errors.append(f"❌ Invalid pyproject.toml: {e}")
                config_valid = False
        else:
            self.errors.append("❌ Missing pyproject.toml")
            config_valid = False

        # Check requirements.txt
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            try:
                with open(requirements_file, 'r') as f:
                    requirements = f.read().strip()
                    if requirements:
                        self.passed_checks.append("✅ requirements.txt has content")
                    else:
                        self.warnings.append("⚠️  requirements.txt is empty")
            except Exception as e:
                self.errors.append(f"❌ Could not read requirements.txt: {e}")
                config_valid = False
        else:
            self.errors.append("❌ Missing requirements.txt")
            config_valid = False

        return config_valid

    def validate_file_naming(self) -> bool:
        """Validate professional file naming conventions."""
        logger.info("Validating file naming conventions...")

        naming_valid = True

        # Check Python files follow snake_case
        python_files = list((self.project_root / "src").rglob("*.py"))

        for py_file in python_files:
            filename = py_file.stem

            # Skip special files
            if filename in ['__init__', '__main__']:
                continue

            # Check snake_case
            if self._is_snake_case(filename):
                self.passed_checks.append(f"✅ Good naming: {py_file.relative_to(self.project_root)}")
            else:
                self.warnings.append(f"⚠️  Non-standard naming: {py_file.relative_to(self.project_root)}")

        return naming_valid

    def _is_snake_case(self, name: str) -> bool:
        """Check if name follows snake_case convention."""
        return name.islower() and (name.replace('_', '').isalnum())

    def validate_backup_system(self) -> bool:
        """Validate backup system."""
        logger.info("Validating backup system...")

        backup_valid = True

        # Check backup script
        backup_script = self.project_root / "backups_AT3GUI" / "create_backup.sh"
        if backup_script.exists():
            self.passed_checks.append("✅ Backup script exists")

            # Check if executable
            if os.access(backup_script, os.X_OK):
                self.passed_checks.append("✅ Backup script is executable")
            else:
                self.warnings.append("⚠️  Backup script is not executable")

            # Check for compression functionality
            try:
                with open(backup_script, 'r') as f:
                    content = f.read()
                    if "tar -czf" in content:
                        self.passed_checks.append("✅ Backup script has compression")
                    else:
                        self.warnings.append("⚠️  Backup script missing compression")
            except Exception as e:
                self.warnings.append(f"⚠️  Could not read backup script: {e}")
        else:
            self.errors.append("❌ Backup script missing")
            backup_valid = False

        return backup_valid

    # TODO: Refactor function - Function 'generate_report' too long (53 lines)
    def generate_report(self) -> str:
        """Generate validation report."""
        report = []
        report.append("=" * 60)
        report.append("PROFESSIONAL PROJECT VALIDATION REPORT")
        report.append("=" * 60)
        report.append("")

        # Summary
        total_checks = len(self.passed_checks) + len(self.warnings) + len(self.errors)
        report.append(f"Total checks: {total_checks}")
        report.append(f"✅ Passed: {len(self.passed_checks)}")
        report.append(f"⚠️  Warnings: {len(self.warnings)}")
        report.append(f"❌ Errors: {len(self.errors)}")
        report.append("")

        # Passed checks
        if self.passed_checks:
            report.append("PASSED CHECKS:")
            report.append("-" * 20)
            for check in self.passed_checks:
                report.append(check)
            report.append("")

        # Warnings
        if self.warnings:
            report.append("WARNINGS:")
            report.append("-" * 20)
            for warning in self.warnings:
                report.append(warning)
            report.append("")

        # Errors
        if self.errors:
            report.append("ERRORS:")
            report.append("-" * 20)
            for error in self.errors:
                report.append(error)
            report.append("")

        # Overall status
        if not self.errors:
            if not self.warnings:
                report.append("🎉 PROJECT VALIDATION: EXCELLENT")
                report.append("✅ Ready for professional deployment")
            else:
                report.append("✅ PROJECT VALIDATION: GOOD")
                report.append("⚠️  Minor issues to address")
        else:
            report.append("❌ PROJECT VALIDATION: NEEDS WORK")
            report.append("🔧 Critical issues must be fixed")

        return "\n".join(report)

    def run_validation(self) -> bool:
        """Run complete project validation."""
        logger.info("🔍 Starting professional project validation...")

        validation_results = []

        # Run all validations
        validation_results.append(
            ("Project Structure",
            self.validate_project_structure())
        )
        validation_results.append(("Python Syntax", self.validate_python_syntax()))
        validation_results.append(("Imports", self.validate_imports()))
        validation_results.append(("Documentation", self.validate_documentation()))
        validation_results.append(("Configuration", self.validate_configuration()))
        validation_results.append(("File Naming", self.validate_file_naming()))
        validation_results.append(("Backup System", self.validate_backup_system()))

        # Print results
        logger.info("\nValidation Results:")
        for name, result in validation_results:
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{name}: {status}")

        # Generate and print report
        report = self.generate_report()
        logger.info("\n" + report)

        # Overall result
        overall_success = all(result for _, result in validation_results)

        if overall_success:
            logger.info("🎉 PROFESSIONAL PROJECT VALIDATION SUCCESSFUL!")
        else:
            logger.error("❌ PROFESSIONAL PROJECT VALIDATION FAILED!")

        return overall_success

def main():
    """Main function."""
    project_root = Path(__file__).parent.parent.parent
    validator = ProjectValidator(project_root)

    success = validator.run_validation()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
