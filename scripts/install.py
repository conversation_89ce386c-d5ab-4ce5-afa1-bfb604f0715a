#!/usr/bin/env python3
"""
AT3GUI Python Installation Script
Comprehensive installation and setup for AreTomo3 GUI
"""

import sys
import os
import subprocess
import shutil
from pathlib import Path

def print_status(message):
        """Execute print_status operation."""
        logger.info(f"✅ {message}")

def print_error(message):
        """Execute print_error operation."""
    logger.info(f"❌ {message}")

def print_info(message):
        """Execute print_info operation."""
    logger.info(f"ℹ️  {message}")

def print_warning(message):
        """Execute print_warning operation."""
    logger.info(f"⚠️  {message}")

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_error(f"Python 3.8+ required. Found: {version.major}.{version.minor}")
        return False
    print_status(f"Python version check passed: {version.major}.{version.minor}")
    return True

def install_dependencies():
    """Install required dependencies."""
    print_info("Installing dependencies...")

    dependencies = [
        "PyQt6",
        "numpy",
        "matplotlib",
        "mrcfile",
        "pytest",
        "pytest-qt",
        "pytest-cov",
        "pytest-mock"
    ]

    for dep in dependencies:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep],
                         check=True, capture_output=True)
            print_status(f"Installed {dep}")
        except subprocess.CalledProcessError as e:
            print_warning(f"Failed to install {dep}: {e}")

    return True

def setup_directories():
    """Create necessary directories."""
    print_info("Creating necessary directories...")

    directories = [
        "src/logs",
        "config/templates",
        "sample_data",
        "docs/reports"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print_status(f"Created directory: {directory}")

    return True

def install_package():
    """Install the package in development mode."""
    print_info("Installing AT3GUI in development mode...")

    try:
        if Path("pyproject.toml").exists():
            subprocess.run([sys.executable, "-m", "pip", "install", "-e", "."],
                         check=True)
        else:
            subprocess.run([sys.executable, "setup.py", "develop"],
                         check=True)
        print_status("AT3GUI installed in development mode")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to install package: {e}")
        return False

def make_scripts_executable():
    """Make shell scripts executable."""
    print_info("Making scripts executable...")

    script_dirs = ["scripts", "scripts/utilities", "scripts/development"]

    for script_dir in script_dirs:
        script_path = Path(script_dir)
        if script_path.exists():
            for script_file in script_path.glob("*.sh"):
                script_file.chmod(script_file.stat().st_mode | 0o755)
                print_status(f"Made executable: {script_file}")

    return True

def test_installation():
    """Test basic functionality."""
    print_info("Testing installation...")

    try:
        import aretomo3_gui
        print_status("Basic import test passed")
        return True
    except ImportError as e:
        print_warning(f"Import test failed: {e}")
        return False

def main():
    """Main installation function."""
    logger.info("🚀 AT3GUI Python Installation Script")
    logger.info("====================================")

    # Check if we're in the right directory
    if not (Path("pyproject.toml").exists() or Path("setup.py").exists()):
        print_error("Please run this script from the AT3GUI project root directory")
        return 1

    # Run installation steps
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Setting up directories", setup_directories),
        ("Installing package", install_package),
        ("Making scripts executable", make_scripts_executable),
        ("Testing installation", test_installation)
    ]

    for step_name, step_func in steps:
        logger.info(f"\n{step_name}...")
        if not step_func():
            print_error(f"Failed: {step_name}")
            return 1

    logger.info("\n🎉 AT3GUI Installation Complete!")
    logger.info("=================================")
    print_status("To run AT3GUI: python -m aretomo3_gui")
    print_status("To run tests: pytest tests/")
    print_status("Installation completed successfully!")

    return 0

if __name__ == "__main__":
    sys.exit(main())
