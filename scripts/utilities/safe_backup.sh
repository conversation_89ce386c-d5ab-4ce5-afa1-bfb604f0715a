#!/bin/bash
# Safe AT3GUI Project Backup - Auto-detects safe backup location
# Ensures backup is created outside the project directory

set -e

# Color codes
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# Get project info
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="AT3GUI"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# Auto-detect safe backup location (outside project directory)
detect_backup_location() {
    local project_parent="$(dirname "$PROJECT_DIR")"
    local possible_locations=(
        "/mnt/HDD/AT3GUI_backups"
        "$project_parent/AT3GUI_backups"
        "$HOME/AT3GUI_backups"
        "/tmp/AT3GUI_backups"
    )
    
    for location in "${possible_locations[@]}"; do
        # Skip if location is inside project directory
        if [[ "$location" == "$PROJECT_DIR"* ]]; then
            continue
        fi
        
        # Try to create directory (test write permission)
        if mkdir -p "$location" 2>/dev/null; then
            echo "$location"
            return 0
        fi
    done
    
    # Fallback: use /tmp
    echo "/tmp/AT3GUI_backups"
}

BACKUP_DIR=$(detect_backup_location)
BACKUP_NAME="${PROJECT_NAME}_safe_backup_${TIMESTAMP}"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"

echo "🔐 Safe AT3GUI Project Backup"
echo "================================"
print_info "Project directory: $PROJECT_DIR"
print_info "Backup directory: $BACKUP_DIR"
print_info "Backup name: $BACKUP_NAME"
echo ""

# Verify backup location is safe (outside project)
if [[ "$BACKUP_PATH" == "$PROJECT_DIR"* ]]; then
    print_error "Backup location is inside project directory!"
    print_error "This could cause recursion issues."
    exit 1
fi

print_success "Backup location is safe (outside project directory)"

# Create backup
print_info "Creating backup..."
mkdir -p "$BACKUP_DIR"

if command -v rsync &> /dev/null; then
    print_info "Using rsync for efficient backup..."
    rsync -av --progress \
          --exclude='build/' \
          --exclude='venv/' \
          --exclude='.git/' \
          --exclude='__pycache__/' \
          --exclude='*.pyc' \
          --exclude='.pytest_cache/' \
          --exclude='*.log' \
          --exclude='temp/' \
          --exclude='tmp/' \
          "$PROJECT_DIR"/ "$BACKUP_PATH"/
else
    print_info "Using tar for backup..."
    tar --exclude='build' \
        --exclude='venv' \
        --exclude='.git' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='.pytest_cache' \
        --exclude='*.log' \
        -czf "$BACKUP_PATH.tar.gz" \
        -C "$(dirname "$PROJECT_DIR")" \
        "$(basename "$PROJECT_DIR")"
    
    print_success "Compressed backup created: $BACKUP_PATH.tar.gz"
    echo ""
    print_info "Backup size: $(du -sh "$BACKUP_PATH.tar.gz" | cut -f1)"
    exit 0
fi

# Create backup info
cat > "$BACKUP_PATH/BACKUP_INFO.txt" << EOF
# AT3GUI Safe Backup Information
===============================

Backup Created: $(date)
Original Project: $PROJECT_DIR
Backup Location: $BACKUP_PATH
Backup Type: Safe (outside project directory)
Created by: $(whoami)@$(hostname)

Restoration:
1. Copy backup to desired location
2. cd backup_location
3. ./scripts/install.sh

EOF

print_success "Backup completed successfully!"
echo ""
print_info "📁 Backup location: $BACKUP_PATH"
print_info "📊 Backup size: $(du -sh "$BACKUP_PATH" | cut -f1 2>/dev/null || echo "Unknown")"
echo ""
print_info "🔐 Safety verified:"
print_info "  ✅ Backup is outside project directory"
print_info "  ✅ No recursion risk"
print_info "  ✅ Safe to restore anywhere"
echo ""

# Offer to create compressed archive
read -p "Create compressed archive for easier transport? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "Creating compressed archive..."
    tar -czf "$BACKUP_PATH.tar.gz" -C "$BACKUP_DIR" "$BACKUP_NAME"
    print_success "Archive created: $BACKUP_PATH.tar.gz"
    print_info "Archive size: $(du -sh "$BACKUP_PATH.tar.gz" | cut -f1)"
    
    read -p "Remove uncompressed backup to save space? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$BACKUP_PATH"
        print_success "Uncompressed backup removed to save space"
    fi
fi

echo ""
print_success "Safe backup process completed!"
print_info "Your project is safely backed up outside the working directory."