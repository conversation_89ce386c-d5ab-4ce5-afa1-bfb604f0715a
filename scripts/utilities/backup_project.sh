#!/bin/bash
# AT3GUI Project Backup Script
# Creates a comprehensive backup of the entire AT3GUI project

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="AT3GUI"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
DEFAULT_BACKUP_DIR="/mnt/HDD/ak_devel/AT3GUI_backups"
BACKUP_DIR="$DEFAULT_BACKUP_DIR"
BACKUP_NAME="${PROJECT_NAME}_backup_${TIMESTAMP}"
FULL_BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backup-dir)
            BACKUP_DIR="$2"
            FULL_BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
            shift 2
            ;;
        --name)
            BACKUP_NAME="$2"
            FULL_BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
            shift 2
            ;;
        --help)
            echo "AT3GUI Project Backup Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --backup-dir DIR    Backup directory (default: $DEFAULT_BACKUP_DIR)"
            echo "  --name NAME         Custom backup name (default: ${PROJECT_NAME}_backup_${TIMESTAMP})"
            echo "  --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Default backup"
            echo "  $0 --backup-dir /home/<USER>/backups  # Custom backup location"
            echo "  $0 --name my_backup                 # Custom backup name"
            echo ""
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Main backup function
main() {
    print_header "📦 AT3GUI Project Backup"
    print_header "========================================"
    echo ""
    
    print_info "Project directory: $PROJECT_DIR"
    print_info "Backup directory: $BACKUP_DIR"
    print_info "Backup name: $BACKUP_NAME"
    print_info "Full backup path: $FULL_BACKUP_PATH"
    echo ""
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_DIR/pyproject.toml" ]]; then
        print_error "pyproject.toml not found. Please run this script from the AT3GUI directory."
        exit 1
    fi
    
    # Create backup directory
    create_backup_dir
    
    # Calculate project size
    calculate_size
    
    # Create backup
    create_backup
    
    # Verify backup
    verify_backup
    
    # Create backup info file
    create_backup_info
    
    # Display summary
    display_summary
}

# Create backup directory
create_backup_dir() {
    print_info "Creating backup directory..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
        print_success "Created backup directory: $BACKUP_DIR"
    else
        print_info "Using existing backup directory: $BACKUP_DIR"
    fi
    
    if [[ -d "$FULL_BACKUP_PATH" ]]; then
        print_warning "Backup already exists: $FULL_BACKUP_PATH"
        read -p "Overwrite existing backup? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$FULL_BACKUP_PATH"
            print_success "Removed existing backup"
        else
            print_error "Backup cancelled to avoid overwriting"
            exit 1
        fi
    fi
    
    mkdir -p "$FULL_BACKUP_PATH"
    print_success "Backup directory created: $FULL_BACKUP_PATH"
}

# Calculate project size
calculate_size() {
    print_info "Calculating project size..."
    
    # Calculate total size excluding build and cache directories
    if command -v du &> /dev/null; then
        PROJECT_SIZE=$(du -sh "$PROJECT_DIR" --exclude="$PROJECT_DIR/build" --exclude="$PROJECT_DIR/venv" --exclude="$PROJECT_DIR/.git" --exclude="$PROJECT_DIR/__pycache__" 2>/dev/null | cut -f1 || echo "Unknown")
        print_info "Project size (excluding build/cache): $PROJECT_SIZE"
    else
        print_info "Size calculation not available (du command not found)"
    fi
}

# Create backup
create_backup() {
    print_info "Creating backup..."
    print_info "This may take a few minutes for large projects..."
    
    # Use rsync for efficient copying with progress
    if command -v rsync &> /dev/null; then
        print_info "Using rsync for backup..."
        rsync -av --progress \
              --exclude='build/' \
              --exclude='venv/' \
              --exclude='.git/' \
              --exclude='__pycache__/' \
              --exclude='*.pyc' \
              --exclude='.pytest_cache/' \
              --exclude='*.log' \
              --exclude='temp/' \
              --exclude='tmp/' \
              "$PROJECT_DIR"/ "$FULL_BACKUP_PATH"/
    else
        print_info "Using cp for backup..."
        # Copy files excluding problematic directories
        find "$PROJECT_DIR" -type f \
             ! -path "*/build/*" \
             ! -path "*/venv/*" \
             ! -path "*/.git/*" \
             ! -path "*/__pycache__/*" \
             ! -path "*/.pytest_cache/*" \
             ! -name "*.pyc" \
             ! -name "*.log" \
             -exec cp --parents {} "$FULL_BACKUP_PATH"/ \;
        
        # Copy directories (empty ones)
        find "$PROJECT_DIR" -type d \
             ! -path "*/build/*" \
             ! -path "*/venv/*" \
             ! -path "*/.git/*" \
             ! -path "*/__pycache__/*" \
             ! -path "*/.pytest_cache/*" \
             -exec mkdir -p "$FULL_BACKUP_PATH"/{} \;
    fi
    
    print_success "Project files backed up successfully"
}

# Verify backup
verify_backup() {
    print_info "Verifying backup..."
    
    # Check if key files exist in backup
    key_files=(
        "pyproject.toml"
        "README.md"
        "src/aretomo3_gui/__init__.py"
        "scripts/install.sh"
        "docs/README_FULL.md"
    )
    
    missing_files=()
    for file in "${key_files[@]}"; do
        if [[ ! -f "$FULL_BACKUP_PATH/$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        print_success "Backup verification passed - all key files present"
    else
        print_warning "Some key files missing from backup:"
        for file in "${missing_files[@]}"; do
            print_warning "  Missing: $file"
        done
    fi
    
    # Count files in backup
    if command -v find &> /dev/null; then
        BACKUP_FILE_COUNT=$(find "$FULL_BACKUP_PATH" -type f | wc -l)
        print_info "Backup contains $BACKUP_FILE_COUNT files"
    fi
}

# Create backup info file
create_backup_info() {
    print_info "Creating backup information file..."
    
    INFO_FILE="$FULL_BACKUP_PATH/BACKUP_INFO.txt"
    
    cat > "$INFO_FILE" << EOF
# AT3GUI Project Backup Information
======================================

Backup Details:
- Backup Date: $(date)
- Backup Name: $BACKUP_NAME
- Original Project Path: $PROJECT_DIR
- Backup Path: $FULL_BACKUP_PATH
- Created by: $(whoami)
- Host: $(hostname)

Project Information:
- Project: AT3GUI (AreTomo3 Graphical User Interface)
- Version: $(grep -o 'version = "[^"]*"' "$PROJECT_DIR/pyproject.toml" 2>/dev/null | cut -d'"' -f2 || echo "Unknown")
- Python Version: $(python3 --version 2>/dev/null || echo "Unknown")

Git Information:
$(cd "$PROJECT_DIR" && git log -1 --oneline 2>/dev/null || echo "No git information available")

Excluded Directories:
- build/        (Build artifacts)
- venv/         (Virtual environments)
- .git/         (Git repository)
- __pycache__/  (Python cache)
- .pytest_cache/ (Test cache)
- temp/, tmp/   (Temporary files)

Excluded Files:
- *.pyc         (Python bytecode)
- *.log         (Log files)

Backup Statistics:
- Project Size: ${PROJECT_SIZE:-Unknown}
- Files Backed Up: ${BACKUP_FILE_COUNT:-Unknown}
- Backup Size: $(du -sh "$FULL_BACKUP_PATH" 2>/dev/null | cut -f1 || echo "Unknown")

Restoration Instructions:
1. Copy backup to desired location
2. Navigate to backup directory
3. Run: ./scripts/install.sh
4. Follow installation instructions

Notes:
- This backup excludes build artifacts and virtual environments
- After restoration, you may need to reinstall dependencies
- Git history is not included in this backup
- For complete git backup, use: git clone or git bundle

EOF

    print_success "Backup information file created: $INFO_FILE"
}

# Display summary
display_summary() {
    echo ""
    print_header "🎉 Backup Complete!"
    print_header "========================================"
    echo ""
    print_success "AT3GUI project has been successfully backed up!"
    echo ""
    print_info "Backup Details:"
    print_info "  Name: $BACKUP_NAME"
    print_info "  Location: $FULL_BACKUP_PATH"
    print_info "  Size: $(du -sh "$FULL_BACKUP_PATH" 2>/dev/null | cut -f1 || echo "Unknown")"
    print_info "  Files: ${BACKUP_FILE_COUNT:-Unknown}"
    echo ""
    print_header "📝 Quick Commands:"
    echo ""
    echo "  # View backup contents"
    echo "  ls -la '$FULL_BACKUP_PATH'"
    echo ""
    echo "  # View backup info"
    echo "  cat '$FULL_BACKUP_PATH/BACKUP_INFO.txt'"
    echo ""
    echo "  # Create compressed archive"
    echo "  tar -czf '${FULL_BACKUP_PATH}.tar.gz' -C '$BACKUP_DIR' '$BACKUP_NAME'"
    echo ""
    echo "  # Restore backup (copy to new location and run)"
    echo "  cp -r '$FULL_BACKUP_PATH' /path/to/restore/"
    echo "  cd /path/to/restore/$BACKUP_NAME"
    echo "  ./scripts/install.sh"
    echo ""
    print_header "💡 Tips:"
    echo ""
    print_info "• Store backup in a safe location (external drive, cloud storage)"
    print_info "• Consider creating compressed archives for long-term storage"
    print_info "• Test restoration process periodically"
    print_info "• For git history backup, use: git bundle create backup.bundle --all"
    echo ""
    print_success "Backup process completed successfully!"
}

# Error handling
trap 'print_error "Backup failed on line $LINENO"; exit 1' ERR

# Run main backup function
main "$@"