#!/bin/bash
# Quick AT3GUI Project Backup
# Simple one-command backup for immediate use

# Get current timestamp
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_NAME="AT3GUI_backup_${TIMESTAMP}"
CURRENT_DIR="$(pwd)"
BACKUP_PATH="/mnt/HDD/AT3GUI_backups/$BACKUP_NAME"

echo "🔄 Creating quick backup of AT3GUI project..."
echo "Backup location: $BACKUP_PATH"

# Create backup directory
mkdir -p "/mnt/HDD/AT3GUI_backups"

# Create backup using rsync (faster and shows progress)
if command -v rsync &> /dev/null; then
    echo "📦 Using rsync for efficient backup..."
    rsync -av --progress \
          --exclude='build/' \
          --exclude='venv/' \
          --exclude='.git/' \
          --exclude='__pycache__/' \
          --exclude='*.pyc' \
          --exclude='.pytest_cache/' \
          --exclude='*.log' \
          "$CURRENT_DIR"/ "$BACKUP_PATH"/
else
    echo "📦 Using cp for backup..."
    cp -r "$CURRENT_DIR" "$BACKUP_PATH"
    # Clean up excluded directories
    rm -rf "$BACKUP_PATH/build" "$BACKUP_PATH/venv" "$BACKUP_PATH/.git" 2>/dev/null || true
    find "$BACKUP_PATH" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$BACKUP_PATH" -name "*.pyc" -delete 2>/dev/null || true
fi

# Create backup info
echo "# AT3GUI Backup Created: $(date)" > "$BACKUP_PATH/BACKUP_INFO.txt"
echo "# Original location: $CURRENT_DIR" >> "$BACKUP_PATH/BACKUP_INFO.txt"
echo "# Backup size: $(du -sh "$BACKUP_PATH" | cut -f1)" >> "$BACKUP_PATH/BACKUP_INFO.txt"

echo "✅ Backup completed successfully!"
echo "📁 Backup location: $BACKUP_PATH"
echo "📊 Backup size: $(du -sh "$BACKUP_PATH" | cut -f1)"

# Optional: Create compressed archive
read -p "Create compressed archive? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 Creating compressed archive..."
    tar -czf "${BACKUP_PATH}.tar.gz" -C "/mnt/HDD/AT3GUI_backups" "$BACKUP_NAME"
    echo "✅ Compressed archive created: ${BACKUP_PATH}.tar.gz"
    echo "📊 Archive size: $(du -sh "${BACKUP_PATH}.tar.gz" | cut -f1)"
fi

echo ""
echo "🎉 Backup process complete!"
echo "Use './scripts/backup_project.sh' for more advanced backup options."