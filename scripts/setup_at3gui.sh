#!/bin/bash

# AT3GUI Setup Script
# Comprehensive setup and installation for AreTomo3 GUI

set -e  # Exit on any error

echo "🚀 AT3GUI Setup Script"
echo "======================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ] && [ ! -f "setup.py" ]; then
    print_error "Please run this script from the AT3GUI project root directory"
    exit 1
fi

print_info "Setting up AreTomo3 GUI..."

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    print_error "Python 3.8+ required. Found: $python_version"
    exit 1
fi

print_status "Python version check passed: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_info "Creating virtual environment..."
    python3 -m venv venv
    print_status "Virtual environment created"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_info "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
print_info "Installing dependencies..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    print_status "Requirements installed from requirements.txt"
fi

# Install additional test dependencies
print_info "Installing test dependencies..."
pip install pytest pytest-qt pytest-cov pytest-mock

# Install the package in development mode
print_info "Installing AT3GUI in development mode..."
if [ -f "pyproject.toml" ]; then
    pip install -e .
else
    pip install -e .
fi

print_status "AT3GUI installed in development mode"

# Create necessary directories
print_info "Creating necessary directories..."
mkdir -p src/logs
mkdir -p config/templates
mkdir -p sample_data
mkdir -p docs/reports

print_status "Directory structure created"

# Make scripts executable
print_info "Making scripts executable..."
chmod +x scripts/*.sh 2>/dev/null || true
chmod +x scripts/utilities/*.sh 2>/dev/null || true
chmod +x scripts/development/*.sh 2>/dev/null || true

print_status "Scripts made executable"

# Run basic tests
print_info "Running basic tests..."
if python -c "import aretomo3_gui; print('✅ Import test passed')" 2>/dev/null; then
    print_status "Basic import test passed"
else
    print_warning "Import test failed - this may be expected during development"
fi

# Create desktop launcher (optional)
if command -v desktop-file-install >/dev/null 2>&1; then
    print_info "Creating desktop launcher..."
    cat > at3gui.desktop << EOF
[Desktop Entry]
Name=AreTomo3 GUI
Comment=Graphical interface for AreTomo3 tomographic reconstruction
Exec=$(pwd)/venv/bin/python -m aretomo3_gui
Icon=$(pwd)/docs/assets/icon.png
Terminal=false
Type=Application
Categories=Science;Education;
EOF
    print_status "Desktop launcher created"
fi

echo ""
echo "🎉 AT3GUI Setup Complete!"
echo "========================="
print_status "Virtual environment: $(pwd)/venv"
print_status "To activate: source venv/bin/activate"
print_status "To run AT3GUI: python -m aretomo3_gui"
print_status "To run tests: pytest tests/"

echo ""
print_info "Next steps:"
echo "  1. Activate the virtual environment: source venv/bin/activate"
echo "  2. Run the application: python -m aretomo3_gui"
echo "  3. Run tests: pytest tests/ -v"
echo "  4. Check documentation in docs/"

echo ""
print_status "Setup completed successfully!"
