#!/bin/bash

# Setup Automated Hourly Backups for AT3GUI
# This script sets up a cron job for automated hourly backups

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_SCRIPT="$SCRIPT_DIR/create_backup.sh"

echo "🕐 Setting up automated hourly backups for AT3GUI"
echo "=================================================="

# Check if backup script exists
if [ ! -f "$BACKUP_SCRIPT" ]; then
    echo "❌ Backup script not found: $BACKUP_SCRIPT"
    exit 1
fi

# Make backup script executable
chmod +x "$BACKUP_SCRIPT"

# Create cron job entry
CRON_JOB="0 * * * * cd $PROJECT_DIR && $BACKUP_SCRIPT >> /tmp/at3gui_backup.log 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT"; then
    echo "⚠️  Hourly backup cron job already exists"
    echo "📋 Current cron jobs:"
    crontab -l | grep "$BACKUP_SCRIPT"
else
    # Add cron job
    echo "📅 Adding hourly backup cron job..."
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "✅ Hourly backup cron job added successfully"
fi

echo ""
echo "🎯 Backup Configuration:"
echo "   📁 Project: $PROJECT_DIR"
echo "   📜 Script: $BACKUP_SCRIPT"
echo "   ⏰ Schedule: Every hour (0 * * * *)"
echo "   📝 Log: /tmp/at3gui_backup.log"

echo ""
echo "📋 Current cron jobs for AT3GUI:"
crontab -l | grep -E "(at3gui|AT3GUI)" || echo "   (none found)"

echo ""
echo "🔧 Management commands:"
echo "   View logs: tail -f /tmp/at3gui_backup.log"
echo "   List cron jobs: crontab -l"
echo "   Remove cron job: crontab -e (then delete the line)"
echo "   Manual backup: $BACKUP_SCRIPT"

echo ""
echo "✅ Automated hourly backups configured successfully!"
