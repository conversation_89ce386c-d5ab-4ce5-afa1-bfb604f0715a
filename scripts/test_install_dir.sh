#!/bin/bash
# Test script to verify default installation directory behavior

echo "🧪 Testing Installation Directory Configuration"
echo "==============================================="

# Test the default directory setting
source_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
project_dir="$(dirname "$source_dir")"

echo "Current working directory: $(pwd)"
echo "Project directory: $project_dir"

# Source the install.sh script to get default configuration
cd "$project_dir"
echo "Changed to project directory: $(pwd)"

# Extract default directory from install.sh
default_dir=$(grep 'DEFAULT_INSTALL_DIR=' scripts/install.sh | head -1 | cut -d'"' -f2)
echo "Default install directory from script: $default_dir"

# Evaluate the expression to get actual directory
actual_default=$(eval echo "$default_dir")
echo "Evaluated default directory: $actual_default"

# Compare with current directory
current_dir=$(pwd)
if [[ "$actual_default" == "$current_dir" ]]; then
    echo "✅ SUCCESS: Default directory correctly set to current working directory"
else
    echo "❌ FAILURE: Default directory mismatch"
    echo "   Expected: $current_dir"
    echo "   Got: $actual_default"
    exit 1
fi

echo ""
echo "🎯 Test completed successfully!"
echo "The installation script will now default to installing in the current directory."
