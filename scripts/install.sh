#!/bin/bash

# AT3GUI Shell Installation Script
# Simple shell-based installation for AreTomo3 GUI

set -e  # Exit on any error

echo "🚀 AT3GUI Shell Installation Script"
echo "==================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ] && [ ! -f "setup.py" ]; then
    print_error "Please run this script from the AT3GUI project root directory"
    exit 1
fi

print_info "Installing AreTomo3 GUI..."

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    print_error "Python 3.8+ required. Found: $python_version"
    exit 1
fi

print_status "Python version check passed: $python_version"

# Install dependencies
print_info "Installing dependencies..."
pip install PyQt6 numpy matplotlib mrcfile pytest pytest-qt pytest-cov pytest-mock

# Install the package
print_info "Installing AT3GUI..."
if [ -f "pyproject.toml" ]; then
    pip install -e .
else
    python setup.py develop
fi

print_status "AT3GUI installed successfully"

# Create directories
print_info "Creating directories..."
mkdir -p src/logs config/templates sample_data docs/reports

# Make scripts executable
print_info "Making scripts executable..."
chmod +x scripts/*.sh 2>/dev/null || true
chmod +x scripts/utilities/*.sh 2>/dev/null || true
chmod +x scripts/development/*.sh 2>/dev/null || true

print_status "Scripts made executable"

echo ""
echo "🎉 AT3GUI Installation Complete!"
echo "================================"
print_status "To run AT3GUI: python -m aretomo3_gui"
print_status "To run tests: pytest tests/"

echo ""
print_status "Installation completed successfully!"
