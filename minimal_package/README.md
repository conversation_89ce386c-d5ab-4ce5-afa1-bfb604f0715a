# AreTomo3 GUI - Minimal Package

## Overview
This is the minimal source package for AreTomo3 GUI containing only essential files.

## Contents
- `aretomo3_gui/` - Source code
- `setup.py` - Package setup
- `pyproject.toml` - Project configuration  
- `requirements.txt` - Dependencies
- `docs/` - Essential documentation

## Installation
```bash
pip install .
```

## Development Installation
```bash
pip install -e .
```

## Building Distribution
```bash
python setup.py sdist bdist_wheel
```

## Usage
```bash
# Set Qt environment
export QT_API=pyqt6

# Launch application
aretomo3-gui
```

## Documentation
See `docs/` directory for installation guides and quick start.
