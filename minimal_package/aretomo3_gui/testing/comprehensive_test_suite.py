#!/usr/bin/env python3
"""
Comprehensive Test Suite for Stability and Security
Tests all critical components for production readiness.
"""

import concurrent.futures
import gc
import logging
import shutil
import sqlite3
import tempfile
import threading
import time
import unittest
from pathlib import Path
from typing import Any, Dict, List

import psutil

# Import components to test
from ..core.enhanced_database_manager import EnhancedDatabaseManager, TransactionManager
from ..core.memory_manager import MemoryA<PERSON>Cache, MemoryManager
from ..core.secure_web_api import Secure<PERSON>eb<PERSON><PERSON>, SecurityConfig

logger = logging.getLogger(__name__)


class DatabaseStabilityTests(unittest.TestCase):
    """Test database stability and thread safety."""

    def setUp(self):
        """Set up test database."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.db_path = self.temp_dir / "test.db"
        self.db_manager = EnhancedDatabaseManager(
            {"database": str(self.db_path),
    "min_connections": 2,
     "max_connections": 5}
        )

    def tearDown(self):
        """Clean up test database."""
        self.db_manager.close()
        shutil.rmtree(self.temp_dir)

    def test_connection_pooling(self):
        """Test connection pool functionality."""
        # Test getting multiple connections
        connections = []
        for _ in range(3):
            with self.db_manager.get_connection() as conn:
                connections.append(conn)
                self.assertIsNotNone(conn.connection)

        # All connections should be returned to pool
        self.assertEqual(len(connections), 3)

    def test_transaction_safety(self):
        """Test transaction rollback on errors."""
        with self.db_manager.get_connection() as conn:
            # Test successful transaction
            with TransactionManager(conn.connection):
                conn.connection.execute(
                    "CREATE TABLE test (id INTEGER PRIMARY KEY)")
                conn.connection.execute("INSERT INTO test (id) VALUES (1)")

            # Verify data was committed
            cursor = conn.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM test")
            self.assertEqual(cursor.fetchone()[0], 1)

            # Test failed transaction
            try:
                with TransactionManager(conn.connection):
                    conn.connection.execute("INSERT INTO test (id) VALUES (2)")
                    raise Exception("Simulated error")
            except Exception:
                pass

            # Verify rollback occurred
            cursor.execute("SELECT COUNT(*) FROM test")
            self.assertEqual(cursor.fetchone()[0], 1)

    def test_concurrent_access(self):
        """Test concurrent database access."""

        def worker(worker_id: int, results: List):
            """Execute worker operation."""
            try:
                for i in range(10):
                    with self.db_manager.get_connection() as conn:
                        cursor = conn.connection.cursor()
                        cursor.execute("SELECT 1")
                        result = cursor.fetchone()
                        results.append((worker_id, i, result[0]))
                        time.sleep(0.01)  # Small delay
            except Exception as e:
                results.append((worker_id, -1, str(e)))

        # Run multiple workers concurrently
        results = []
        threads = []
        for worker_id in range(5):
            thread = threading.Thread(target=worker, args=(worker_id, results))
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join()

        # Verify all operations succeeded
        successful_operations = [r for r in results if r[1] != -1]
        self.assertEqual(
    len(successful_operations),
     50)  # 5 workers * 10 operations

    def test_connection_cleanup(self):
        """Test connection cleanup and resource management."""
        initial_connections = len(
    self.db_manager.connection_pool.active_connections)

        # Create and use connections
        for _ in range(10):
            with self.db_manager.get_connection() as conn:
                conn.connection.execute("SELECT 1")

        # Connections should be returned to pool
        final_connections = len(
    self.db_manager.connection_pool.active_connections)
        self.assertLessEqual(
            final_connections, self.db_manager.connection_pool.max_connections
        )


class SecurityTests(unittest.TestCase):
    """Test security features."""

    def setUp(self):
        """Set up security test environment."""
        self.security_config = SecurityConfig(
            allowed_origins=["http://localhost:3000"],
            rate_limit_requests=5,
            rate_limit_window_minutes=1,
        )
        self.api = SecureWebAPI(self.security_config)

    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        client_ip = "*************"

        # Should allow requests within limit
        for _ in range(5):
            self.assertTrue(
    self.api.security_manager.check_rate_limit(client_ip))

        # Should block after limit exceeded
        self.assertFalse(self.api.security_manager.check_rate_limit(client_ip))

    def test_path_traversal_protection(self):
        """Test path traversal attack prevention."""
        base_path = Path("/safe/base")

        # Safe paths should be allowed
        self.assertTrue(
    self.api.security_manager.is_safe_path(
        "file.txt", base_path))
        self.assertTrue(
            self.api.security_manager.is_safe_path(
                "subdir/file.txt", base_path)
        )

        # Dangerous paths should be blocked
        self.assertFalse(
            self.api.security_manager.is_safe_path(
                "../../../etc/passwd", base_path)
        )
        self.assertFalse(
            self.api.security_manager.is_safe_path(
                "..\\..\\windows\\system32", base_path
            )
        )

    def test_jwt_token_validation(self):
        """Test JWT token generation and validation."""
        # Generate token
        token = self.api.security_manager.generate_jwt_token(
            "test_user", ["read", "write"]
        )
        self.assertIsInstance(token, str)

        # Validate token
        payload = self.api.security_manager.validate_jwt_token(token)
        self.assertIsNotNone(payload)
        self.assertEqual(payload["user_id"], "test_user")
        self.assertEqual(payload["permissions"], ["read", "write"])

        # Invalid token should fail
        invalid_payload = self.api.security_manager.validate_jwt_token(
            "invalid_token")
        self.assertIsNone(invalid_payload)

    def test_api_key_management(self):
        """Test API key generation and validation."""
        # Generate API key
        api_key = self.api.security_manager.add_api_key()
        self.assertIsInstance(api_key, str)
        self.assertTrue(api_key.startswith("at3gui_"))

        # Validate API key
        self.assertTrue(self.api.security_manager.validate_api_key(api_key))

        # Revoke API key
        self.assertTrue(self.api.security_manager.revoke_api_key(api_key))
        self.assertFalse(self.api.security_manager.validate_api_key(api_key))


class MemoryManagementTests(unittest.TestCase):
    """Test memory management functionality."""

    def setUp(self):
        """Set up memory management tests."""
        self.memory_manager = MemoryManager(
            {
                "cache_max_memory_mb": 10,  # Small cache for testing
                "monitoring_interval": 1,
            }
        )

    def tearDown(self):
        """Clean up memory manager."""
        self.memory_manager.stop_monitoring()

    def test_memory_aware_cache(self):
        """Test memory-aware cache functionality."""
        cache = MemoryAwareCache(max_memory_mb=1, max_items=10)

        # Add items to cache
        for i in range(5):
            cache.put(f"key_{i}", f"value_{i}" * 1000)  # Larger values

        # Cache should have items
        self.assertGreater(len(cache.cache), 0)

        # Add more items to trigger eviction
        for i in range(10):
            cache.put(f"large_key_{i}", "x" * 100000)  # Very large values

        # Cache should have evicted items to stay within memory limit
        stats = cache.get_stats()
        self.assertLess(stats["memory_usage_mb"], 1.5)  # Some tolerance

    def test_object_tracking(self):
        """Test object tracking for memory management."""

        class TestObject:
            """Class TestObject implementation."""

            def __init__(self, data):
                """Initialize the instance."""
                self.data = data

        # Track objects
        objects = []
        for i in range(10):
            obj = TestObject(f"data_{i}")
            self.memory_manager.track_object(obj)
            objects.append(obj)

        # Should have tracked objects
        initial_count = len(self.memory_manager.tracked_objects)
        self.assertGreater(initial_count, 0)

        # Delete objects
        del objects
        gc.collect()

        # Give time for cleanup
        time.sleep(0.1)
        self.memory_manager._cleanup_dead_references()

        # Should have fewer tracked objects
        final_count = len(self.memory_manager.tracked_objects)
        self.assertLess(final_count, initial_count)

    def test_memory_pressure_detection(self):
        """Test memory pressure detection."""
        # Get initial memory stats
        stats = self.memory_manager.get_memory_stats()
        self.assertIn("system_memory", stats)
        self.assertIn("process_memory", stats)
        self.assertIn("cache_stats", stats)

    def test_cleanup_callbacks(self):
        """Test cleanup callback registration and execution."""
        cleanup_called = []

        def cleanup_callback():
            """Execute cleanup_callback operation."""
            cleanup_called.append(True)

        # Register callback
        self.memory_manager.register_cleanup_callback(cleanup_callback)

        # Force cleanup
        self.memory_manager.force_cleanup()

        # Callback should have been called
        self.assertTrue(cleanup_called)

class PerformanceTests(unittest.TestCase):
    """Test performance characteristics."""

    def test_database_query_performance(self):
        """Test database query performance."""
        temp_dir = Path(tempfile.mkdtemp())
        db_path = temp_dir / "perf_test.db"

        try:
            db_manager = EnhancedDatabaseManager(
                {"database": str(db_path), "min_connections": 3, "max_connections": 10}
            )

            # Measure query performance
            start_time = time.time()

            for i in range(100):
                with db_manager.get_connection() as conn:
                    cursor = conn.connection.cursor()
                    cursor.execute("SELECT ?", (i,))
                    cursor.fetchone()

            end_time = time.time()
            query_time = (end_time - start_time) / 100  # Average per query

            # Should be fast (less than 1ms per query)
            self.assertLess(query_time, 0.001)

            db_manager.close()

        finally:
            shutil.rmtree(temp_dir)

    def test_concurrent_performance(self):
        """Test performance under concurrent load."""

        def worker():
            """Execute worker operation."""
            results = []
            for _ in range(10):
                start = time.time()
                # Simulate some work
                time.sleep(0.001)
                end = time.time()
                results.append(end - start)
            return results

        # Run concurrent workers
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(worker) for _ in range(10)]
            all_results = []

            for future in concurrent.futures.as_completed(futures):
                all_results.extend(future.result())

        # Calculate average response time
        avg_time = sum(all_results) / len(all_results)

        # Should maintain reasonable performance under load
        self.assertLess(avg_time, 0.01)  # Less than 10ms average

class IntegrationTests(unittest.TestCase):
    """Integration tests for complete workflows."""

    def test_complete_workflow(self):
        """Test complete processing workflow."""
        # This would test the entire pipeline from data input to results
        # For now, just verify components can be initialized together

        temp_dir = Path(tempfile.mkdtemp())

        try:
            # Initialize all components
            db_manager = EnhancedDatabaseManager(
                {"database": str(temp_dir / "test.db")}
            )

            memory_manager = MemoryManager()

            security_config = SecurityConfig()
            api = SecureWebAPI(security_config)

            # Verify they work together
            self.assertIsNotNone(db_manager)
            self.assertIsNotNone(memory_manager)
            self.assertIsNotNone(api)

            # Clean up
            db_manager.close()
            memory_manager.stop_monitoring()

        finally:
            shutil.rmtree(temp_dir)

def run_comprehensive_tests():
    """Run all comprehensive tests."""
    # Create test suite
    test_suite = unittest.TestSuite()

    # Add test classes
    test_classes = [
        DatabaseStabilityTests,
        SecurityTests,
        MemoryManagementTests,
        PerformanceTests,
        IntegrationTests,
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Return results
    return {
        "tests_run": result.testsRun,
        "failures": len(result.failures),
        "errors": len(result.errors),
        "success_rate": (
            (
                (result.testsRun - len(result.failures) - len(result.errors))
                / result.testsRun
                * 100
            )
            if result.testsRun > 0
            else 0
        ),
    }

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)

    # Run comprehensive tests
    results = run_comprehensive_tests()

    print("\n" + "=" * 50)
    print("COMPREHENSIVE TEST RESULTS")
    print("=" * 50)
    print(f"Tests Run: {results['tests_run']}")
    print(f"Failures: {results['failures']}")
    print(f"Errors: {results['errors']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")

    if results["success_rate"] >= 95:
        print("✅ TESTS PASSED - System ready for production")
    else:
        print("❌ TESTS FAILED - System needs fixes before production")
