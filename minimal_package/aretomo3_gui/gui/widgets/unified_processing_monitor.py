#!/usr/bin/env python3
"""
Unified Processing Monitor for AT3GUI
Combines queue monitoring, system monitoring, and processing dashboard into one comprehensive interface.
"""

import logging
import sys
import time
from collections import deque
from datetime import datetime, timedelta

import matplotlib.pyplot as plt
import numpy as np
import psutil
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt6.QtCore import QMutex, Qt, QThread, QTimer, pyqtSignal
from PyQt6.QtGui import QColor, QFont, QPalette
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QListWidget,
    QListWidgetItem,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)

class SystemMonitorThread(QThread):
    """Background thread for system monitoring."""

    stats_updated = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.running = False
        self.mutex = QMutex()

    def run(self):
        self.running = True
        # Initialize CPU monitoring for non-blocking usage
        psutil.cpu_percent()  # Call once to initialize
        while self.running:
            try:
                # Collect system statistics (non-blocking)
                stats = {
                    "cpu_percent": psutil.cpu_percent(interval=None),  # Non-blocking
                    "memory_percent": psutil.virtual_memory().percent,
                    "memory_used": psutil.virtual_memory().used / (1024**3),  # GB
                    "memory_total": psutil.virtual_memory().total / (1024**3),  # GB
                    "disk_usage": psutil.disk_usage("/").percent,
                    "timestamp": time.time(),
                }

                # Try to get GPU stats (if available)
                try:
                    import GPUtil

                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu = gpus[0]  # Use first GPU
                        stats.update(
                            {
                                "gpu_percent": gpu.load * 100,
                                "gpu_memory_percent": gpu.memoryUtil * 100,
                                "gpu_memory_used": gpu.memoryUsed / 1024,  # GB
                                "gpu_memory_total": gpu.memoryTotal / 1024,  # GB
                                "gpu_temperature": gpu.temperature,
                            }
                        )
                except ImportError:
                    # GPU monitoring not available
                    stats.update(
                        {
                            "gpu_percent": 0,
                            "gpu_memory_percent": 0,
                            "gpu_memory_used": 0,
                            "gpu_memory_total": 0,
                            "gpu_temperature": 0,
                        }
                    )

                self.stats_updated.emit(stats)
                self.msleep(1000)  # Update every second

            except Exception as e:
                logger.error(f"Error in system monitoring: {e}")
                self.msleep(5000)  # Wait longer on error

    def stop(self):
        self.running = False
        self.wait()

class LiveGraphWidget(QWidget):
    """Widget for displaying live graphs."""

    def __init__(self, title, ylabel, color="#2196F3", max_points=60):
        super().__init__()
        self.title = title
        self.ylabel = ylabel
        self.color = color
        self.max_points = max_points
        self.data = deque(maxlen=max_points)
        self.timestamps = deque(maxlen=max_points)

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Create matplotlib figure
        self.figure = Figure(figsize=(4, 2), facecolor="white")
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # Style the plot
        self.ax.set_facecolor("#f8f9fa")
        self.ax.grid(True, alpha=0.3)
        self.ax.set_title(self.title, fontsize=9, fontweight="bold")
        self.ax.set_ylabel(self.ylabel, fontsize=8)

        # Set up the line plot
        (self.line,) = self.ax.plot([], [], color=self.color, linewidth=2)
        self.ax.set_ylim(0, 100)

        layout.addWidget(self.canvas)

    def add_data_point(self, value, timestamp=None):
        if timestamp is None:
            timestamp = time.time()

        self.data.append(value)
        self.timestamps.append(timestamp)
        self.update_plot()

    def update_plot(self):
        if len(self.data) < 2:
            return

        # Convert timestamps to relative seconds
        current_time = self.timestamps[-1]
        relative_times = [(t - current_time) for t in self.timestamps]

        # Update the line data
        self.line.set_data(relative_times, list(self.data))

        # Update axes
        self.ax.set_xlim(min(relative_times), 0)
        self.ax.set_ylim(0, max(100, max(self.data) * 1.1))

        # Update x-axis labels to show time
        self.ax.set_xlabel("Time (seconds ago)", fontsize=8)

        self.figure.tight_layout()
        self.canvas.draw()

class ProcessingQueueWidget(QWidget):
    """Widget for displaying and managing the processing queue."""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Queue header
        header_layout = QHBoxLayout()
        self.queue_label = QLabel("Processing Queue")
        self.queue_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.queue_stats = QLabel("0 jobs")
        self.queue_stats.setStyleSheet("color: #666;")

        header_layout.addWidget(self.queue_label)
        header_layout.addStretch()
        header_layout.addWidget(self.queue_stats)
        layout.addLayout(header_layout)

        # Job table
        self.job_table = QTableWidget()
        self.job_table.setColumnCount(5)
        self.job_table.setHorizontalHeaderLabels(
            ["Job Name", "Progress", "Status", "Time", "Details"]
        )

        # Set column widths
        header = self.job_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)

        self.job_table.setColumnWidth(1, 100)  # Progress
        self.job_table.setColumnWidth(2, 80)  # Status
        self.job_table.setColumnWidth(3, 100)  # Time

        layout.addWidget(self.job_table)

        # Current job info
        current_job_group = QGroupBox("Current Job")
        current_job_layout = QVBoxLayout(current_job_group)

        self.current_job_label = QLabel("No active job")
        self.current_job_label.setStyleSheet("font-weight: bold; color: #2e7d32;")
        current_job_layout.addWidget(self.current_job_label)

        self.current_progress = QProgressBar()
        self.current_progress.setMinimumHeight(25)
        current_job_layout.addWidget(self.current_progress)

        # Timing info
        timing_layout = QGridLayout()
        timing_layout.addWidget(QLabel("Elapsed:"), 0, 0)
        self.elapsed_label = QLabel("00:00:00")
        timing_layout.addWidget(self.elapsed_label, 0, 1)

        timing_layout.addWidget(QLabel("ETA:"), 0, 2)
        self.eta_label = QLabel("N/A")
        timing_layout.addWidget(self.eta_label, 0, 3)

        timing_layout.addWidget(QLabel("Speed:"), 1, 0)
        self.speed_label = QLabel("0.0 files/min")
        timing_layout.addWidget(self.speed_label, 1, 1)

        current_job_layout.addLayout(timing_layout)
        layout.addWidget(current_job_group)

    def add_job(self, job_name, status="Queued"):
        """Add a new job to the queue."""
        row = self.job_table.rowCount()
        self.job_table.insertRow(row)

        self.job_table.setItem(row, 0, QTableWidgetItem(job_name))

        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(0)
        self.job_table.setCellWidget(row, 1, progress)

        self.job_table.setItem(row, 2, QTableWidgetItem(status))
        self.job_table.setItem(row, 3, QTableWidgetItem("--:--"))
        self.job_table.setItem(row, 4, QTableWidgetItem("Waiting..."))

        self.update_queue_stats()

    def update_job_status(self, job_name, status, progress=None, details=None):
        """Update job status in the table."""
        for row in range(self.job_table.rowCount()):
            item = self.job_table.item(row, 0)
            if item and item.text() == job_name:
                # Update status
                self.job_table.setItem(row, 2, QTableWidgetItem(status))

                # Update progress
                if progress is not None:
                    progress_bar = self.job_table.cellWidget(row, 1)
                    if progress_bar:
                        progress_bar.setValue(progress)

                # Update details
                if details:
                    self.job_table.setItem(row, 4, QTableWidgetItem(details))

                # Update time
                current_time = datetime.now().strftime("%H:%M:%S")
                self.job_table.setItem(row, 3, QTableWidgetItem(current_time))

                break

        self.update_queue_stats()

    def update_current_job(
        self, job_name, progress=0, elapsed_time=0, eta=None, speed=None
    ):
        """Update current job information."""
        self.current_job_label.setText(job_name)
        self.current_progress.setValue(progress)

        # Format elapsed time
        hours = int(elapsed_time // 3600)
        minutes = int((elapsed_time % 3600) // 60)
        seconds = int(elapsed_time % 60)
        self.elapsed_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")

        # Update ETA
        if eta is not None:
            eta_hours = int(eta // 3600)
            eta_minutes = int((eta % 3600) // 60)
            eta_seconds = int(eta % 60)
            self.eta_label.setText(
                f"{eta_hours:02d}:{eta_minutes:02d}:{eta_seconds:02d}"
            )
        else:
            self.eta_label.setText("N/A")

        # Update speed
        if speed is not None:
            self.speed_label.setText(f"{speed:.1f} files/min")
        else:
            self.speed_label.setText("0.0 files/min")

    def update_queue_stats(self):
        """Update queue statistics."""
        total_jobs = self.job_table.rowCount()
        completed_jobs = 0
        running_jobs = 0

        for row in range(total_jobs):
            status_item = self.job_table.item(row, 2)
            if status_item:
                status = status_item.text()
                if status == "Completed":
                    completed_jobs += 1
                elif status == "Running":
                    running_jobs += 1

        if total_jobs == 0:
            self.queue_stats.setText("Queue empty")
        else:
            self.queue_stats.setText(
                f"{total_jobs} jobs ({completed_jobs} completed, {running_jobs} running)"
            )

class UnifiedProcessingMonitor(QWidget):
    """Enhanced unified processing monitor with command preview, configuration, and system monitoring."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.monitor_thread = None
        self.start_time = None
        self.setup_ui()
        self.start_monitoring()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Create tab widget for different monitoring sections
        self.monitor_tabs = QTabWidget()

        # Tab 1: System Performance
        self.system_tab = self.create_system_monitoring_tab()
        self.monitor_tabs.addTab(self.system_tab, "📊 System Performance")

        # Tab 2: Configuration Management (Command Preview moved to Parameters tab)
        self.config_tab = self.create_configuration_tab()
        self.monitor_tabs.addTab(self.config_tab, "⚙️ Configuration")

        # Tab 3: Processing Queue (optional - can be toggled)
        self.queue_tab = self.create_processing_queue_tab()
        self.monitor_tabs.addTab(self.queue_tab, "⏳ Processing Queue")

        layout.addWidget(self.monitor_tabs)

    def create_system_monitoring_tab(self):
        """Create the system monitoring tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)

        # System stats header
        stats_header = QLabel("🖥️ Real-time System Performance")
        stats_header.setStyleSheet(
            "font-weight: bold; font-size: 16px; color: #2c3e50; padding: 10px;"
        )
        layout.addWidget(stats_header)

        # Create main splitter (horizontal)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Live graphs (60%)
        graphs_widget = QWidget()
        graphs_layout = QGridLayout(graphs_widget)
        graphs_layout.setSpacing(10)

        # Create monitoring graphs
        self.cpu_graph = LiveGraphWidget("CPU Usage", "CPU %", "#FF5722")
        self.memory_graph = LiveGraphWidget("Memory Usage", "Memory %", "#4CAF50")
        self.gpu_graph = LiveGraphWidget("GPU Usage", "GPU %", "#2196F3")

        graphs_layout.addWidget(self.cpu_graph, 0, 0)
        graphs_layout.addWidget(self.memory_graph, 0, 1)
        graphs_layout.addWidget(self.gpu_graph, 1, 0, 1, 2)  # GPU spans 2 columns

        main_splitter.addWidget(graphs_widget)

        # Right side - System info and controls (40%)
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)

        # Current system status
        status_group = QGroupBox("📈 Current Status")
        status_layout = QVBoxLayout(status_group)

        self.cpu_label = QLabel("CPU: --")
        self.memory_label = QLabel("Memory: --")
        self.gpu_label = QLabel("GPU: --")
        self.disk_label = QLabel("Disk: --")

        # Style the labels
        for label in [
            self.cpu_label,
            self.memory_label,
            self.gpu_label,
            self.disk_label,
        ]:
            label.setStyleSheet(
                "font-size: 12px; padding: 5px; background-color: #f8f9fa; border-radius: 3px; margin: 2px;"
            )

        status_layout.addWidget(self.cpu_label)
        status_layout.addWidget(self.memory_label)
        status_layout.addWidget(self.gpu_label)
        status_layout.addWidget(self.disk_label)

        info_layout.addWidget(status_group)

        # System controls
        controls_group = QGroupBox("🔧 System Controls")
        controls_layout = QVBoxLayout(controls_group)

        # Monitoring controls
        monitor_controls = QHBoxLayout()

        self.pause_monitoring_btn = QPushButton("⏸️ Pause")
        self.pause_monitoring_btn.clicked.connect(self.toggle_monitoring)
        self.pause_monitoring_btn.setToolTip("Pause/Resume system monitoring")

        self.reset_graphs_btn = QPushButton("🔄 Reset")
        self.reset_graphs_btn.clicked.connect(self.reset_graphs)
        self.reset_graphs_btn.setToolTip("Reset monitoring graphs")

        monitor_controls.addWidget(self.pause_monitoring_btn)
        monitor_controls.addWidget(self.reset_graphs_btn)
        monitor_controls.addStretch()

        controls_layout.addLayout(monitor_controls)

        # Performance alerts
        alerts_label = QLabel("⚠️ Performance Alerts")
        alerts_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        self.alerts_list = QListWidget()
        self.alerts_list.setMaximumHeight(100)

        controls_layout.addWidget(alerts_label)
        controls_layout.addWidget(self.alerts_list)

        info_layout.addWidget(controls_group)
        info_layout.addStretch()

        main_splitter.addWidget(info_widget)

        # Set splitter proportions (60% graphs, 40% info)
        main_splitter.setStretchFactor(0, 6)
        main_splitter.setStretchFactor(1, 4)

        layout.addWidget(main_splitter)

        return tab

    def create_configuration_tab(self):
        """Create the configuration management tab (command preview moved to Parameters tab)."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # Header with note about moved functionality
        header_layout = QVBoxLayout()
        header = QLabel("⚙️ Configuration Management")
        header.setStyleSheet(
            "font-weight: bold; font-size: 16px; color: #2c3e50; padding: 10px;"
        )

        note_label = QLabel(
            "📋 Command Preview moved to Parameters → General tab for better workflow"
        )
        note_label.setStyleSheet(
            """
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 8px;
                border-radius: 4px;
                border-left: 3px solid #3498db;
            }
        """
        )
        note_label.setWordWrap(True)

        header_layout.addWidget(header)
        header_layout.addWidget(note_label)
        layout.addLayout(header_layout)

        # Configuration Management Section
        config_group = QGroupBox("⚙️ Configuration Templates & Management")
        config_layout = QVBoxLayout(config_group)

        # Template selection
        template_row = QHBoxLayout()
        template_row.addWidget(QLabel("Template:"))

        self.template_combo = QComboBox()
        self.template_combo.addItems(
            [
                "Custom Configuration",
                "High Resolution Template",
                "Fast Processing Template",
                "Live Acquisition Template",
                "Batch Processing Template",
            ]
        )
        self.template_combo.setMinimumWidth(250)
        self.template_combo.currentTextChanged.connect(self.load_template)
        self.template_combo.setToolTip("Load predefined parameter templates")
        template_row.addWidget(self.template_combo)
        template_row.addStretch()

        config_layout.addLayout(template_row)

        # Configuration buttons
        config_buttons_layout = QGridLayout()

        self.load_json_btn = QPushButton("📂 Load Configuration")
        self.load_json_btn.clicked.connect(self.load_json_config)
        self.load_json_btn.setToolTip("Load parameters from JSON file")

        self.save_json_btn = QPushButton("💾 Save Configuration")
        self.save_json_btn.clicked.connect(self.save_json_config)
        self.save_json_btn.setToolTip("Save current parameters to JSON file")

        self.validate_btn = QPushButton("✅ Validate Configuration")
        self.validate_btn.clicked.connect(self.validate_configuration)
        self.validate_btn.setToolTip("Validate current parameter configuration")

        self.goto_params_btn = QPushButton("📋 Go to Parameters")
        self.goto_params_btn.clicked.connect(self.goto_parameters_tab)
        self.goto_params_btn.setToolTip(
            "Switch to Parameters tab to see command preview"
        )
        self.goto_params_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """
        )

        config_buttons_layout.addWidget(self.load_json_btn, 0, 0)
        config_buttons_layout.addWidget(self.save_json_btn, 0, 1)
        config_buttons_layout.addWidget(self.validate_btn, 1, 0)
        config_buttons_layout.addWidget(self.goto_params_btn, 1, 1)

        config_layout.addLayout(config_buttons_layout)

        # Configuration status
        self.config_status = QLabel("📝 Configuration Status: Ready")
        self.config_status.setStyleSheet(
            "font-weight: bold; color: #27ae60; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
        )
        config_layout.addWidget(self.config_status)

        layout.addWidget(config_group)

        # Recent configurations
        recent_group = QGroupBox("📚 Recent Configurations")
        recent_layout = QVBoxLayout(recent_group)

        self.recent_configs = QComboBox()
        self.recent_configs.addItems(
            ["config_2024_01_15.json", "high_res_setup.json", "live_processing.json"]
        )
        recent_layout.addWidget(self.recent_configs)

        recent_actions = QHBoxLayout()
        load_recent_btn = QPushButton("📂 Load Selected")
        load_recent_btn.clicked.connect(self.load_recent_config)

        manage_configs_btn = QPushButton("🔧 Manage")
        manage_configs_btn.clicked.connect(self.manage_configurations)

        recent_actions.addWidget(load_recent_btn)
        recent_actions.addWidget(manage_configs_btn)
        recent_actions.addStretch()

        recent_layout.addLayout(recent_actions)
        layout.addWidget(recent_group)

        layout.addStretch()

        return tab

    def goto_parameters_tab(self):
        """Navigate to parameters tab."""
        if self.main_window:
            for i in range(self.main_window.tab_widget.count()):
                if "Parameters" in self.main_window.tab_widget.tabText(i):
                    self.main_window.tab_widget.setCurrentIndex(i)
                    break

    def create_processing_queue_tab(self):
        """Create the processing queue tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)

        # Header with toggle option
        header_layout = QHBoxLayout()
        header = QLabel("⏳ Processing Queue Management")
        header.setStyleSheet(
            "font-weight: bold; font-size: 16px; color: #2c3e50; padding: 10px;"
        )

        self.enable_queue_chk = QCheckBox("Enable Processing Queue")
        self.enable_queue_chk.setChecked(True)
        self.enable_queue_chk.toggled.connect(self.toggle_queue)
        self.enable_queue_chk.setToolTip(
            "Enable/disable processing queue functionality"
        )

        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(self.enable_queue_chk)

        layout.addLayout(header_layout)

        # Queue widget
        self.queue_widget = ProcessingQueueWidget()
        layout.addWidget(self.queue_widget)

        return tab

    def start_monitoring(self):
        """Start system monitoring."""
        # Stop any existing monitoring thread first
        if self.monitor_thread is not None:
            self.stop_monitoring()

        self.monitor_thread = SystemMonitorThread()
        self.monitor_thread.stats_updated.connect(self.update_system_stats)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """Stop system monitoring."""
        if self.monitor_thread:
            self.monitor_thread.stop()
            self.monitor_thread.wait(1000)  # Wait up to 1 second
            if self.monitor_thread.isRunning():
                self.monitor_thread.terminate()
                self.monitor_thread.wait(1000)
            self.monitor_thread = None

    def update_system_stats(self, stats):
        """Update system statistics displays."""
        timestamp = stats["timestamp"]

        # Update graphs
        self.cpu_graph.add_data_point(stats["cpu_percent"], timestamp)
        self.memory_graph.add_data_point(stats["memory_percent"], timestamp)
        self.gpu_graph.add_data_point(stats["gpu_percent"], timestamp)

        # Update text labels
        self.cpu_label.setText(f"CPU: {stats['cpu_percent']:.1f}%")
        self.memory_label.setText(
            f"Memory: {stats['memory_used']:.1f}/{stats['memory_total']:.1f} GB ({stats['memory_percent']:.1f}%)"
        )
        self.disk_label.setText(f"Disk: {stats['disk_usage']:.1f}%")

        if stats["gpu_percent"] > 0:
            self.gpu_label.setText(
                f"GPU: {stats['gpu_percent']:.1f}% | Memory: {stats['gpu_memory_used']:.1f}/{stats['gpu_memory_total']:.1f} GB | Temp: {stats['gpu_temperature']:.0f}°C"
            )
        else:
            self.gpu_label.setText("GPU: Not available")

        # Check for performance alerts
        self.check_performance_alerts(stats)

    def check_performance_alerts(self, stats):
        """Check for performance issues and add alerts."""
        alerts = []

        if stats["cpu_percent"] > 90:
            alerts.append("⚠️ High CPU usage detected")
        if stats["memory_percent"] > 85:
            alerts.append("⚠️ High memory usage detected")
        if stats["disk_usage"] > 90:
            alerts.append("⚠️ Low disk space")
        if stats["gpu_percent"] > 95:
            alerts.append("⚠️ High GPU usage detected")

        # Add new alerts to the list
        for alert in alerts:
            timestamp = datetime.now().strftime("%H:%M:%S")
            alert_text = f"[{timestamp}] {alert}"

            # Check if alert already exists
            existing_alerts = [
                self.alerts_list.item(i).text() for i in range(self.alerts_list.count())
            ]
            if alert_text not in existing_alerts:
                self.alerts_list.addItem(alert_text)

                # Keep only last 10 alerts
                if self.alerts_list.count() > 10:
                    self.alerts_list.takeItem(0)

    def toggle_monitoring(self):
        """Toggle system monitoring on/off."""
        if self.monitor_thread and self.monitor_thread.running:
            self.stop_monitoring()
            self.pause_monitoring_btn.setText("▶️ Resume")
        else:
            self.start_monitoring()
            self.pause_monitoring_btn.setText("⏸️ Pause")

    def reset_graphs(self):
        """Reset all monitoring graphs."""
        self.cpu_graph.data.clear()
        self.cpu_graph.timestamps.clear()
        self.memory_graph.data.clear()
        self.memory_graph.timestamps.clear()
        self.gpu_graph.data.clear()
        self.gpu_graph.timestamps.clear()

        # Clear the plots
        for graph in [self.cpu_graph, self.memory_graph, self.gpu_graph]:
            graph.line.set_data([], [])
            graph.canvas.draw()

        # Clear alerts
        self.alerts_list.clear()

    def update_command_preview(self):
        """Update the command preview with current parameters."""
        try:
            if self.main_window and hasattr(self.main_window, "parameters_tab"):
                # Get parameters from the parameters tab
                params = self.main_window.parameters_tab.get_all_parameters()

                # Generate AreTomo3 command
                command_parts = ["AreTomo3"]

                for param_name, param_value in params.items():
                    if param_value and str(param_value).strip():
                        command_parts.append(f"-{param_name} {param_value}")

                command = " \\\n    ".join(command_parts)

                # Add header comment
                header = f"# AreTomo3 Command Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                header += (
                    f"# Total parameters: {len([p for p in params.values() if p])}\n\n"
                )

                self.command_preview.setPlainText(header + command)
                self.config_status.setText("📝 Configuration Status: Command Updated")
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
                )

            else:
                self.command_preview.setPlainText(
                    "# Error: Cannot access parameters\n# Please ensure parameters tab is loaded"
                )
                self.config_status.setText("📝 Configuration Status: Error")
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #e74c3c; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
                )

        except Exception as e:
            logger.error(f"Error updating command preview: {e}")
            self.command_preview.setPlainText(f"# Error generating command: {e}")

    def copy_command(self):
        """Copy command to clipboard."""
        try:
            from PyQt6.QtWidgets import QApplication

            clipboard = QApplication.clipboard()
            command_text = self.command_preview.toPlainText()

            # Extract just the command (remove comments)
            lines = command_text.split("\n")
            command_lines = [
                line
                for line in lines
                if not line.strip().startswith("#") and line.strip()
            ]
            command = "\n".join(command_lines)

            clipboard.setText(command)

            # Show temporary status
            original_text = self.config_status.text()
            self.config_status.setText("📋 Command copied to clipboard!")
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #3498db; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
            )

            # Reset status after 2 seconds
            QTimer.singleShot(2000, lambda: self.config_status.setText(original_text))

        except Exception as e:
            logger.error(f"Error copying command: {e}")
            QMessageBox.warning(self, "Copy Error", f"Failed to copy command: {e}")

    def save_command(self):
        """Save command to file."""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save AreTomo3 Command",
                f"aretomo3_command_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sh",
                "Shell Scripts (*.sh);;Text Files (*.txt);;All Files (*)",
            )

            if filename:
                command_text = self.command_preview.toPlainText()

                # Add shebang for shell scripts
                if filename.endswith(".sh"):
                    command_text = "#!/bin/bash\n\n" + command_text

                with open(filename, "w") as f:
                    f.write(command_text)

                self.config_status.setText(f"💾 Command saved: {filename}")
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
                )

        except Exception as e:
            logger.error(f"Error saving command: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save command: {e}")

    def execute_command(self):
        """Execute the AreTomo3 command."""
        try:
            command_text = self.command_preview.toPlainText()

            # Extract just the command (remove comments)
            lines = command_text.split("\n")
            command_lines = [
                line
                for line in lines
                if not line.strip().startswith("#") and line.strip()
            ]
            command = " ".join(command_lines)

            if not command.strip():
                QMessageBox.warning(
                    self,
                    "Execute Error",
                    "No command to execute. Please update the preview first.",
                )
                return

            # Confirm execution
            reply = QMessageBox.question(
                self,
                "Execute Command",
                f"Execute the following command?\n\n{command[:200]}{'...' if len(command) > 200 else ''}",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Add to processing queue if enabled
                if (
                    hasattr(self, "enable_queue_chk")
                    and self.enable_queue_chk.isChecked()
                ):
                    job_name = f"AreTomo3_{datetime.now().strftime('%H%M%S')}"
                    self.add_job(job_name, "Queued")

                self.config_status.setText("▶️ Command execution started")
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #f39c12; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
                )

                # Here you would integrate with the actual execution system
                logger.info(f"Executing command: {command}")

        except Exception as e:
            logger.error(f"Error executing command: {e}")
            QMessageBox.critical(
                self, "Execution Error", f"Failed to execute command: {e}"
            )

    def load_from_history(self):
        """Load command from history."""
        selected_command = self.command_history.currentText()
        if selected_command:
            self.command_preview.setPlainText(
                f"# Loaded from history\n\n{selected_command}"
            )

    def clear_history(self):
        """Clear command history."""
        reply = QMessageBox.question(
            self,
            "Clear History",
            "Are you sure you want to clear the command history?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.command_history.clear()

    def load_template(self, template_name):
        """Load parameter template."""
        templates = {
            "High Resolution Template": {
                "VolZ": "2048",
                "AtBin": "1",
                "Gpu": "0,1,2,3",
                "OutBin": "1",
            },
            "Fast Processing Template": {
                "VolZ": "1024",
                "AtBin": "2",
                "Gpu": "0",
                "OutBin": "2",
            },
            "Live Acquisition Template": {
                "VolZ": "1536",
                "AtBin": "1",
                "Gpu": "0,1",
                "OutBin": "1",
            },
            "Batch Processing Template": {
                "VolZ": "2048",
                "AtBin": "2",
                "Gpu": "0,1,2,3",
                "OutBin": "2",
            },
        }

        if template_name in templates:
            # Apply template to parameters tab if available
            if self.main_window and hasattr(self.main_window, "parameters_tab"):
                template_params = templates[template_name]
                # Here you would apply the template parameters
                logger.info(f"Loading template: {template_name}")
                self.config_status.setText(f"📋 Template loaded: {template_name}")

    def load_json_config(self):
        """Load configuration from JSON file."""
        try:
            filename, _ = QFileDialog.getOpenFileName(
                self, "Load Configuration", "", "JSON Files (*.json);;All Files (*)"
            )

            if filename:
                import json

                with open(filename, "r") as f:
                    config = json.load(f)

                # Apply configuration
                logger.info(f"Loading configuration from: {filename}")
                self.config_status.setText(f"📂 Configuration loaded: {filename}")

        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            QMessageBox.critical(
                self, "Load Error", f"Failed to load configuration: {e}"
            )

    def save_json_config(self):
        """Save current configuration to JSON file."""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save Configuration",
                f"config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json);;All Files (*)",
            )

            if filename:
                # Get current parameters
                if self.main_window and hasattr(self.main_window, "parameters_tab"):
                    params = self.main_window.parameters_tab.get_all_parameters()

                    import json

                    with open(filename, "w") as f:
                        json.dump(params, f, indent=2)

                    self.config_status.setText(f"💾 Configuration saved: {filename}")

        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            QMessageBox.critical(
                self, "Save Error", f"Failed to save configuration: {e}"
            )

    def validate_configuration(self):
        """Validate current configuration."""
        try:
            # Perform validation checks
            validation_results = []

            if self.main_window and hasattr(self.main_window, "parameters_tab"):
                params = self.main_window.parameters_tab.get_all_parameters()

                # Basic validation
                if not params.get("InPrefix"):
                    validation_results.append("❌ Input prefix is required")
                if not params.get("OutDir"):
                    validation_results.append("❌ Output directory is required")

                # Add more validation rules as needed

            if not validation_results:
                validation_results.append("✅ Configuration is valid")
                self.config_status.setText("✅ Configuration validated successfully")
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
                )
            else:
                self.config_status.setText("❌ Configuration has issues")
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #e74c3c; padding: 10px; background-color: #f8f9fa; border-radius: 5px;"
                )

            # Show validation results
            QMessageBox.information(
                self, "Validation Results", "\n".join(validation_results)
            )

        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            QMessageBox.critical(
                self, "Validation Error", f"Failed to validate configuration: {e}"
            )

    def load_recent_config(self):
        """Load selected recent configuration."""
        selected_config = self.recent_configs.currentText()
        if selected_config:
            logger.info(f"Loading recent configuration: {selected_config}")

    def manage_configurations(self):
        """Open configuration management dialog."""
        QMessageBox.information(
            self,
            "Configuration Management",
            "Configuration management dialog would open here.",
        )

    def toggle_queue(self, enabled):
        """Toggle processing queue functionality."""
        if hasattr(self, "queue_widget"):
            self.queue_widget.setEnabled(enabled)

        if enabled:
            logger.info("Processing queue enabled")
        else:
            logger.info("Processing queue disabled")

    def add_job(self, job_name, status="Queued"):
        """Add a new job to the processing queue."""
        self.queue_widget.add_job(job_name, status)

    def update_job_status(self, job_name, status, progress=None, details=None):
        """Update job status."""
        self.queue_widget.update_job_status(job_name, status, progress, details)

    def start_processing(self, job_name):
        """Start processing a job."""
        self.start_time = time.time()
        self.queue_widget.update_current_job(job_name, 0, 0)

    def update_processing_progress(self, job_name, progress):
        """Update processing progress."""
        if self.start_time:
            elapsed = time.time() - self.start_time
            # Simple ETA calculation
            if progress > 0:
                eta = (elapsed / progress) * (100 - progress)
                speed = (progress / elapsed) * 60 if elapsed > 0 else 0
            else:
                eta = None
                speed = None

            self.queue_widget.update_current_job(
                job_name, progress, elapsed, eta, speed
            )

    def closeEvent(self, event):
        """Clean up when widget is closed."""
        self.stop_monitoring()
        super().closeEvent(event)
