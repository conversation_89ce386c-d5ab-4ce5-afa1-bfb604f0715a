#!/usr/bin/env python3
"""
Advanced Progress Visualization Widget for AreTomo3 GUI.
Provides comprehensive progress tracking with multiple visualization modes.
"""

import math
import sys
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from PyQt6.QtCore import QEasingCurve, QPropertyAnimation, Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QBrush, QColor, QFont, QLinearGradient, QPainter, QPen
from PyQt6.QtWidgets import (
    QComboBox,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QPushButton,
    QScrollArea,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

try:
    import matplotlib.pyplot as plt
    import numpy as np
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False


class CircularProgressWidget(QWidget):
    """Circular progress indicator with customizable styling."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.progress = 0
        self.max_value = 100
        self.text = ""
        self.setMinimumSize(120, 120)

        # Styling
        self.bg_color = QColor(240, 240, 240)
        self.progress_color = QColor(52, 152, 219)
        self.text_color = QColor(44, 62, 80)
        self.border_width = 8

    def set_progress(self, value: int, text: str = ""):
        """Set progress value and optional text."""
        self.progress = max(0, min(value, self.max_value))
        self.text = text
        self.update()

    def paintEvent(self, event):
        """Paint the circular progress indicator."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Calculate dimensions
        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 2 - self.border_width

        # Draw background circle
        painter.setPen(QPen(self.bg_color, self.border_width))
        painter.drawEllipse(
            center.x() - radius, center.y() - radius, radius * 2, radius * 2
        )

        # Draw progress arc
        if self.progress > 0:
            painter.setPen(
                QPen(
                    self.progress_color,
                    self.border_width,
                    Qt.PenStyle.SolidLine,
                    Qt.PenCapStyle.RoundCap,
                )
            )

            # Calculate angle (start from top, clockwise)
            start_angle = 90 * 16  # Qt uses 1/16th degree units
            span_angle = -int((self.progress / self.max_value) * 360 * 16)

            painter.drawArc(
                center.x() - radius,
                center.y() - radius,
                radius * 2,
                radius * 2,
                start_angle,
                span_angle,
            )

        # Draw text
        if self.text:
            painter.setPen(self.text_color)
            painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, self.text)


class TimelineProgressWidget(QWidget):
    """Timeline-based progress visualization."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setMinimumHeight(100)
        self.tasks = []
        self.current_time = datetime.now()

    def add_task(
        self,
        name: str,
        start_time: datetime,
        duration: timedelta,
        status: str = "pending",
    ):
        """Add a task to the timeline."""
        self.tasks.append(
            {
                "name": name,
                "start_time": start_time,
                "end_time": start_time + duration,
                "duration": duration,
                "status": status,  # pending, running, completed, failed
            }
        )
        self.update()

    def set_current_time(self, current_time: datetime):
        """Update current time marker."""
        self.current_time = current_time
        self.update()

    # TODO: Refactor function - Function 'paintEvent' too long (65 lines)
    def paintEvent(self, event):
        """Paint the timeline."""
        if not self.tasks:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        rect = self.rect()
        margin = 20
        timeline_rect = rect.adjusted(margin, margin, -margin, -margin)

        # Calculate time range
        min_time = min(task["start_time"] for task in self.tasks)
        max_time = max(task["end_time"] for task in self.tasks)
        total_duration = (max_time - min_time).total_seconds()

        if total_duration == 0:
            return

        # Draw timeline background
        painter.fillRect(timeline_rect, QColor(245, 245, 245))

        # Draw tasks
        task_height = timeline_rect.height() // max(len(self.tasks), 1)

        for i, task in enumerate(self.tasks):
            y = timeline_rect.top() + i * task_height

            # Calculate task position and width
            start_offset = (task["start_time"] - min_time).total_seconds()
            task_duration = task["duration"].total_seconds()

            x = timeline_rect.left() + int(
                (start_offset / total_duration) * timeline_rect.width()
            )
            width = int((task_duration / total_duration) * timeline_rect.width())

            # Choose color based on status
            colors = {
                "pending": QColor(189, 195, 199),
                "running": QColor(52, 152, 219),
                "completed": QColor(46, 204, 113),
                "failed": QColor(231, 76, 60),
            }
            color = colors.get(task["status"], QColor(149, 165, 166))

            # Draw task bar
            task_rect = rect.adjusted(x, y + 2, x + width, y + task_height - 2)
            painter.fillRect(task_rect, color)

            # Draw task name
            painter.setPen(QColor(44, 62, 80))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(task_rect, Qt.AlignmentFlag.AlignCenter, task["name"])

        # Draw current time marker
        if min_time <= self.current_time <= max_time:
            current_offset = (self.current_time - min_time).total_seconds()
            x = timeline_rect.left() + int(
                (current_offset / total_duration) * timeline_rect.width()
            )

            painter.setPen(QPen(QColor(231, 76, 60), 2))
            painter.drawLine(x, timeline_rect.top(), x, timeline_rect.bottom())


class AdvancedProgressWidget(QWidget):
    """Advanced progress visualization with multiple modes."""

    progress_updated = pyqtSignal(str, int, str)  # task_id, progress, status

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.tasks = {}
        self.overall_progress = 0
        self.setup_ui()

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_displays)
        self.update_timer.start(1000)  # Update every second

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)

        # Header with overall progress
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_layout = QHBoxLayout(header_frame)

        # Overall progress
        self.overall_circular = CircularProgressWidget()
        header_layout.addWidget(self.overall_circular)

        # Overall stats
        stats_layout = QVBoxLayout()
        self.overall_label = QLabel("Overall Progress")
        self.overall_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.stats_label = QLabel("0 / 0 tasks completed")
        self.eta_label = QLabel("ETA: --")

        stats_layout.addWidget(self.overall_label)
        stats_layout.addWidget(self.stats_label)
        stats_layout.addWidget(self.eta_label)
        stats_layout.addStretch()

        header_layout.addLayout(stats_layout)
        header_layout.addStretch()

        # View mode selector
        self.view_combo = QComboBox()
        self.view_combo.addItems(["List View", "Timeline View", "Chart View"])
        self.view_combo.currentTextChanged.connect(self.change_view_mode)
        header_layout.addWidget(QLabel("View:"))
        header_layout.addWidget(self.view_combo)

        layout.addWidget(header_frame)

        # Progress display area
        self.display_area = QScrollArea()
        self.display_widget = QWidget()
        self.display_layout = QVBoxLayout(self.display_widget)
        self.display_area.setWidget(self.display_widget)
        self.display_area.setWidgetResizable(True)

        layout.addWidget(self.display_area)

        # Initialize with list view
        self.current_view = "List View"
        self.setup_list_view()

    def add_task(self, task_id: str, name: str, total_steps: int = 100):
        """Add a new task to track."""
        self.tasks[task_id] = {
            "name": name,
            "progress": 0,
            "total_steps": total_steps,
            "status": "pending",
            "start_time": datetime.now(),
            "estimated_duration": timedelta(minutes=5),  # Default estimate
            "current_step": "",
            "error_message": "",
            "widget": None,
        }
        self.refresh_display()

    def update_task_progress(
        self, task_id: str, progress: int, current_step: str = "", status: str = None
    ):
        """Update progress for a specific task."""
        if task_id not in self.tasks:
            return

        task = self.tasks[task_id]
        task["progress"] = max(0, min(progress, task["total_steps"]))
        task["current_step"] = current_step

        if status:
            task["status"] = status

        # Update task widget if in list view
        if self.current_view == "List View" and task["widget"]:
            self.update_task_widget(task_id)

        # Calculate overall progress
        self.calculate_overall_progress()

        # Emit signal
        self.progress_updated.emit(task_id, progress, task["status"])

    def update_task_widget(self, task_id: str):
        """Update the widget for a specific task."""
        task = self.tasks[task_id]
        widget = task["widget"]

        if not widget:
            return

        # Update progress bar
        progress_bar = widget.findChild(QProgressBar)
        if progress_bar:
            progress_bar.setValue(task["progress"])

        # Update status label
        status_label = widget.findChild(QLabel, "status_label")
        if status_label:
            status_text = f"Status: {task['status'].title()}"
            if task["current_step"]:
                status_text += f" - {task['current_step']}"
            status_label.setText(status_text)

        # Update circular progress
        circular = widget.findChild(CircularProgressWidget)
        if circular:
            percentage = int((task["progress"] / task["total_steps"]) * 100)
            circular.set_progress(percentage, f"{percentage}%")

    def calculate_overall_progress(self):
        """Calculate overall progress across all tasks."""
        if not self.tasks:
            self.overall_progress = 0
            return

        total_progress = sum(task["progress"] for task in self.tasks.values())
        total_possible = sum(task["total_steps"] for task in self.tasks.values())

        if total_possible > 0:
            self.overall_progress = int((total_progress / total_possible) * 100)
        else:
            self.overall_progress = 0

        # Update overall display
        self.overall_circular.set_progress(
            self.overall_progress, f"{self.overall_progress}%"
        )

        # Update stats
        completed_tasks = sum(
            1 for task in self.tasks.values() if task["status"] == "completed"
        )
        total_tasks = len(self.tasks)
        self.stats_label.setText(f"{completed_tasks} / {total_tasks} tasks completed")

        # Calculate ETA
        self.calculate_eta()

    def calculate_eta(self):
        """Calculate estimated time of arrival."""
        running_tasks = [
            task for task in self.tasks.values() if task["status"] == "running"
        ]

        if not running_tasks:
            self.eta_label.setText("ETA: --")
            return

        # Simple ETA calculation based on current progress rate
        total_remaining = 0
        for task in running_tasks:
            if task["progress"] > 0:
                elapsed = datetime.now() - task["start_time"]
                rate = task["progress"] / elapsed.total_seconds()
                remaining_steps = task["total_steps"] - task["progress"]
                if rate > 0:
                    remaining_time = remaining_steps / rate
                    total_remaining += remaining_time

        if total_remaining > 0:
            eta = datetime.now() + timedelta(seconds=total_remaining)
            self.eta_label.setText(f"ETA: {eta.strftime('%H:%M:%S')}")
        else:
            self.eta_label.setText("ETA: --")

    def setup_list_view(self):
        """Set up list view of tasks."""
        # Clear existing layout
        self.clear_layout(self.display_layout)

        for task_id, task in self.tasks.items():
            task_widget = self.create_task_widget(task_id, task)
            task["widget"] = task_widget
            self.display_layout.addWidget(task_widget)

        self.display_layout.addStretch()

    def create_task_widget(self, task_id: str, task: Dict) -> QWidget:
        """Create widget for individual task."""
        widget = QGroupBox(task["name"])
        layout = QHBoxLayout(widget)

        # Circular progress
        circular = CircularProgressWidget()
        percentage = int((task["progress"] / task["total_steps"]) * 100)
        circular.set_progress(percentage, f"{percentage}%")
        layout.addWidget(circular)

        # Task details
        details_layout = QVBoxLayout()

        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setMaximum(task["total_steps"])
        progress_bar.setValue(task["progress"])
        details_layout.addWidget(progress_bar)

        # Status label
        status_label = QLabel(f"Status: {task['status'].title()}")
        status_label.setObjectName("status_label")
        details_layout.addWidget(status_label)

        # Current step
        if task["current_step"]:
            step_label = QLabel(f"Current: {task['current_step']}")
            details_layout.addWidget(step_label)

        layout.addLayout(details_layout)
        layout.addStretch()

        return widget

    def setup_timeline_view(self):
        """Set up timeline view of tasks."""
        # Clear existing layout
        self.clear_layout(self.display_layout)

        timeline = TimelineProgressWidget()

        # Add tasks to timeline
        for task_id, task in self.tasks.items():
            duration = task.get("estimated_duration", timedelta(minutes=5))
            timeline.add_task(
                task["name"], task["start_time"], duration, task["status"]
            )

        timeline.set_current_time(datetime.now())
        self.display_layout.addWidget(timeline)

    def setup_chart_view(self):
        """Set up chart view of tasks."""
        if not MATPLOTLIB_AVAILABLE:
            error_label = QLabel("Matplotlib not available for chart view")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.display_layout.addWidget(error_label)
            return

        # Clear existing layout
        self.clear_layout(self.display_layout)

        # Create matplotlib figure
        fig = Figure(figsize=(10, 6))
        canvas = FigureCanvas(fig)

        # Create progress chart
        ax = fig.add_subplot(111)

        task_names = [task["name"] for task in self.tasks.values()]
        progress_values = [
            task["progress"] / task["total_steps"] * 100 for task in self.tasks.values()
        ]

        bars = ax.barh(task_names, progress_values)

        # Color bars based on status
        colors = {
            "pending": "#bdc3c7",
            "running": "#3498db",
            "completed": "#2ecc71",
            "failed": "#e74c3c",
        }

        for bar, task in zip(bars, self.tasks.values()):
            bar.set_color(colors.get(task["status"], "#95a5a6"))

        ax.set_xlabel("Progress (%)")
        ax.set_title("Task Progress Overview")
        ax.set_xlim(0, 100)

        fig.tight_layout()
        self.display_layout.addWidget(canvas)

    def change_view_mode(self, mode: str):
        """Change the view mode."""
        self.current_view = mode
        self.refresh_display()

    def refresh_display(self):
        """Refresh the current display."""
        if self.current_view == "List View":
            self.setup_list_view()
        elif self.current_view == "Timeline View":
            self.setup_timeline_view()
        elif self.current_view == "Chart View":
            self.setup_chart_view()

    def clear_layout(self, layout):
        """Clear all widgets from a layout."""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def update_displays(self):
        """Update displays periodically."""
        if self.current_view == "Timeline View":
            # Update timeline current time
            timeline = self.display_widget.findChild(TimelineProgressWidget)
            if timeline:
                timeline.set_current_time(datetime.now())

        # Recalculate ETA
        self.calculate_eta()

    def remove_task(self, task_id: str):
        """Remove a task from tracking."""
        if task_id in self.tasks:
            del self.tasks[task_id]
            self.refresh_display()
            self.calculate_overall_progress()

    def clear_completed_tasks(self):
        """Clear all completed tasks."""
        completed_tasks = [
            task_id
            for task_id, task in self.tasks.items()
            if task["status"] == "completed"
        ]
        for task_id in completed_tasks:
            self.remove_task(task_id)
