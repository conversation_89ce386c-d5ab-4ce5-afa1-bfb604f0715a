#!/usr/bin/env python3
"""
Live Tilt Series Monitor with Active/Inactive Toggle System.
Shows all results but only highlights the latest for clear visualization.
"""

import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QSplitter,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class TiltSeriesToggleWidget(QWidget):
    """Widget for toggling tilt series visibility."""

    series_toggled = pyqtSignal(str, bool)  # series_name, is_active

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.series_checkboxes = {}
        self.setup_ui()

    def setup_ui(self):
        """Set up the toggle interface."""
        layout = QVBoxLayout(self)

        # Header
        header = QLabel("📊 Tilt Series Control")
        header.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(header)

        # Scroll area for series list
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setMaximumHeight(300)

        self.series_container = QWidget()
        self.series_layout = QVBoxLayout(self.series_container)
        scroll.setWidget(self.series_container)

        layout.addWidget(scroll)

        # Control buttons
        controls_layout = QHBoxLayout()

        self.activate_latest_btn = QPushButton("🎯 Latest Only")
        self.activate_latest_btn.clicked.connect(self.activate_latest_only)

        self.activate_all_btn = QPushButton("👁️ Show All")
        self.activate_all_btn.clicked.connect(self.activate_all)

        self.clear_all_btn = QPushButton("❌ Hide All")
        self.clear_all_btn.clicked.connect(self.clear_all)

        controls_layout.addWidget(self.activate_latest_btn)
        controls_layout.addWidget(self.activate_all_btn)
        controls_layout.addWidget(self.clear_all_btn)

        layout.addLayout(controls_layout)

    def add_series(self, series_name: str, is_latest: bool = False):
        """Add a new tilt series to the toggle list."""
        if series_name in self.series_checkboxes:
            return

        # Create checkbox with series info
        checkbox = QCheckBox(series_name)
        checkbox.setChecked(is_latest)  # Latest series is active by default

        # Style for latest series
        if is_latest:
            checkbox.setStyleSheet(
                """
                QCheckBox {
                    font-weight: bold;
                    color: #2E8B57;
                }
                QCheckBox::indicator:checked {
                    background-color: #2E8B57;
                }
            """
            )

        checkbox.stateChanged.connect(
            lambda state, name=series_name: self.series_toggled.emit(
                name, state == Qt.CheckState.Checked
            )
        )

        self.series_checkboxes[series_name] = checkbox
        self.series_layout.addWidget(checkbox)

        # Update latest styling
        self.update_latest_styling(series_name)

    def update_latest_styling(self, latest_series: str):
        """Update styling to highlight the latest series."""
        for name, checkbox in self.series_checkboxes.items():
            if name == latest_series:
                checkbox.setStyleSheet(
                    """
                    QCheckBox {
                        font-weight: bold;
                        color: #2E8B57;
                        background-color: rgba(46, 139, 87, 0.1);
                        padding: 5px;
                        border-radius: 3px;
                    }
                    QCheckBox::indicator:checked {
                        background-color: #2E8B57;
                    }
                """
                )
            else:
                checkbox.setStyleSheet(
                    """
                    QCheckBox {
                        font-weight: normal;
                        color: #333333;
                    }
                """
                )

    def activate_latest_only(self):
        """Activate only the latest series."""
        if not self.series_checkboxes:
            return

        # Get the latest series (last added)
        latest_series = list(self.series_checkboxes.keys())[-1]

        for name, checkbox in self.series_checkboxes.items():
            checkbox.setChecked(name == latest_series)

    def activate_all(self):
        """Activate all series."""
        for checkbox in self.series_checkboxes.values():
            checkbox.setChecked(True)

    def clear_all(self):
        """Deactivate all series."""
        for checkbox in self.series_checkboxes.values():
            checkbox.setChecked(False)

    def get_active_series(self) -> List[str]:
        """Get list of currently active series."""
        return [
            name
            for name, checkbox in self.series_checkboxes.items()
            if checkbox.isChecked()
        ]


class LivePlotCanvas(FigureCanvas):
    """Canvas for live plotting with active/inactive series support."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        self.figure = Figure(figsize=(12, 8))
        super().__init__(self.figure)
        self.setParent(parent)

        # Data storage
        self.series_data = {}
        self.active_series = set()
        self.latest_series = None

        # Plot styling
        self.active_style = {"linewidth": 3, "alpha": 1.0, "zorder": 10}
        self.inactive_style = {"linewidth": 1, "alpha": 0.3, "zorder": 1}
        self.latest_style = {
            "linewidth": 4,
            "alpha": 1.0,
            "zorder": 15,
            "linestyle": "-",
        }

        # Color cycle for different series
        self.colors = plt.cm.tab10(np.linspace(0, 1, 10))
        self.color_map = {}

        self.setup_plots()

    def setup_plots(self):
        """Set up the plot layout."""
        self.figure.clear()

        # Create subplots
        self.axes = {}
        self.axes["motion"] = self.figure.add_subplot(2, 2, 1)
        self.axes["ctf"] = self.figure.add_subplot(2, 2, 2)
        self.axes["alignment"] = self.figure.add_subplot(2, 2, 3)
        self.axes["resolution"] = self.figure.add_subplot(2, 2, 4)

        # Configure axes
        self.axes["motion"].set_title("Motion Correction")
        self.axes["motion"].set_xlabel("Tilt Angle (°)")
        self.axes["motion"].set_ylabel("Motion (pixels)")
        self.axes["motion"].grid(True, alpha=0.3)

        self.axes["ctf"].set_title("CTF Estimation")
        self.axes["ctf"].set_xlabel("Tilt Angle (°)")
        self.axes["ctf"].set_ylabel("Defocus (μm)")
        self.axes["ctf"].grid(True, alpha=0.3)

        self.axes["alignment"].set_title("Alignment Quality")
        self.axes["alignment"].set_xlabel("Tilt Angle (°)")
        self.axes["alignment"].set_ylabel("Alignment Score")
        self.axes["alignment"].grid(True, alpha=0.3)

        self.axes["resolution"].set_title("Resolution vs Tilt Angle")
        self.axes["resolution"].set_xlabel("Tilt Angle (°)")
        self.axes["resolution"].set_ylabel("Resolution (Å)")
        self.axes["resolution"].grid(True, alpha=0.3)

        self.figure.tight_layout()
        self.draw()

    def add_series_data(self, series_name: str, data: Dict, is_latest: bool = False):
        """Add data for a new tilt series."""
        self.series_data[series_name] = data

        # Assign color
        if series_name not in self.color_map:
            color_idx = len(self.color_map) % len(self.colors)
            self.color_map[series_name] = self.colors[color_idx]

        if is_latest:
            self.latest_series = series_name
            self.active_series.add(series_name)

        self.update_plots()

    def set_active_series(self, active_series: List[str]):
        """Set which series are currently active."""
        self.active_series = set(active_series)
        self.update_plots()

    # TODO: Refactor update_plots - complexity: 22 (target: <10)
    # TODO: Refactor function - Function 'update_plots' too long (93 lines)
    def update_plots(self):
        """Update all plots with current data and active series."""
        # Clear all axes
        for ax in self.axes.values():
            ax.clear()

        # Replot all series
        for series_name, data in self.series_data.items():
            is_active = series_name in self.active_series
            is_latest = series_name == self.latest_series

            # Determine style
            if is_latest and is_active:
                style = self.latest_style.copy()
            elif is_active:
                style = self.active_style.copy()
            else:
                style = self.inactive_style.copy()

            color = self.color_map[series_name]

            # Plot motion data
            if "motion" in data and data["motion"] is not None:
                motion_data = data["motion"]
                if len(motion_data) > 0:
                    tilt_angles = motion_data.get(
                        "tilt_angles", range(len(motion_data.get("motion_values", [])))
                    )
                    motion_values = motion_data.get("motion_values", [])

                    if len(tilt_angles) == len(motion_values):
                        self.axes["motion"].plot(
                            tilt_angles,
                            motion_values,
                            color=color,
                            label=series_name,
                            **style,
                        )

            # Plot CTF data
            if "ctf" in data and data["ctf"] is not None:
                ctf_data = data["ctf"]
                if len(ctf_data) > 0:
                    tilt_angles = ctf_data.get(
                        "tilt_angles", range(len(ctf_data.get("defocus", [])))
                    )
                    defocus = ctf_data.get("defocus", [])

                    if len(tilt_angles) == len(defocus):
                        self.axes["ctf"].plot(
                            tilt_angles,
                            defocus,
                            color=color,
                            label=series_name,
                            **style,
                        )

            # Plot alignment data
            if "alignment" in data and data["alignment"] is not None:
                alignment_data = data["alignment"]
                if len(alignment_data) > 0:
                    tilt_angles = alignment_data.get(
                        "tilt_angles", range(len(alignment_data.get("scores", [])))
                    )
                    scores = alignment_data.get("scores", [])

                    if len(tilt_angles) == len(scores):
                        self.axes["alignment"].plot(
                            tilt_angles, scores, color=color, label=series_name, **style
                        )

            # Plot resolution data
            if "resolution" in data and data["resolution"] is not None:
                resolution_data = data["resolution"]
                if len(resolution_data) > 0:
                    tilt_angles = resolution_data.get(
                        "tilt_angles",
                        range(len(resolution_data.get("resolution_values", []))),
                    )
                    resolution_values = resolution_data.get("resolution_values", [])

                    if len(tilt_angles) == len(resolution_values):
                        self.axes["resolution"].plot(
                            tilt_angles,
                            resolution_values,
                            color=color,
                            label=series_name,
                            **style,
                        )

        # Reconfigure axes
        self.setup_plot_styling()
        self.draw()

    def setup_plot_styling(self):
        """Apply styling to all plots."""
        for name, ax in self.axes.items():
            if name == "motion":
                ax.set_title("Motion Correction")
                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Motion (pixels)")
            elif name == "ctf":
                ax.set_title("CTF Estimation")
                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Defocus (μm)")
            elif name == "alignment":
                ax.set_title("Alignment Quality")
                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Alignment Score")
            elif name == "resolution":
                ax.set_title("Resolution vs Tilt Angle")
                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Resolution (Å)")

            ax.grid(True, alpha=0.3)

            # Add legend only if there are active series
            if self.active_series:
                ax.legend(loc="upper right", fontsize=8)

        self.figure.tight_layout()


class LiveTiltSeriesMonitor(QWidget):
    """Main widget for live tilt series monitoring with toggle controls."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        layout = QHBoxLayout(self)

        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel: Toggle controls
        self.toggle_widget = TiltSeriesToggleWidget()
        self.toggle_widget.setMaximumWidth(300)
        splitter.addWidget(self.toggle_widget)

        # Right panel: Live plots
        self.plot_canvas = LivePlotCanvas()
        splitter.addWidget(self.plot_canvas)

        # Set splitter proportions
        splitter.setSizes([300, 900])

        layout.addWidget(splitter)

    def connect_signals(self):
        """Connect widget signals."""
        self.toggle_widget.series_toggled.connect(self.on_series_toggled)

    def add_tilt_series(self, series_name: str, data: Dict, is_latest: bool = False):
        """Add a new tilt series to the monitor."""
        # Add to toggle widget
        self.toggle_widget.add_series(series_name, is_latest)

        # Add to plot canvas
        self.plot_canvas.add_series_data(series_name, data, is_latest)

        logger.info(
            f"Added tilt series to live monitor: {series_name} (latest: {is_latest})"
        )

    def on_series_toggled(self, series_name: str, is_active: bool):
        """Handle series toggle events."""
        active_series = self.toggle_widget.get_active_series()
        self.plot_canvas.set_active_series(active_series)

        logger.info(
            f"Toggled series {series_name}: {'active' if is_active else 'inactive'}"
        )

    def clear_all_series(self):
        """Clear all series data."""
        self.plot_canvas.series_data.clear()
        self.plot_canvas.active_series.clear()
        self.plot_canvas.latest_series = None

        # Clear toggle widget
        for checkbox in self.toggle_widget.series_checkboxes.values():
            checkbox.setParent(None)
        self.toggle_widget.series_checkboxes.clear()

        self.plot_canvas.update_plots()

        logger.info("Cleared all tilt series from live monitor")
