#!/usr/bin/env python3
"""
Enhanced SpinBox widgets for AreTomo3 GUI.
Provides better user experience with validation, tooltips, and styling.
"""

import sys
from typing import Any, Callable, Optional, Union

from PyQt6.QtCore import QEasingCurve, QPropertyAnimation, Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QColor, QFont, QLinearGradient, QPainter, QPalette
from PyQt6.QtWidgets import (
    QApplication,
    QDoubleSpinBox,
    QFrame,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QSlider,
    QSpinBox,
    QStyle,
    QToolTip,
    QVBoxLayout,
    QWidget,
)


class EnhancedSpinBox(QSpinBox):
    """Enhanced integer SpinBox with better styling and features."""

    valueChangedDelayed = pyqtSignal(int)  # Emitted after delay

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setup_enhanced_features()

    def setup_enhanced_features(self):
        """Set up enhanced features."""
        # Delayed value change signal
        self.delay_timer = QTimer()
        self.delay_timer.setSingleShot(True)
        self.delay_timer.timeout.connect(
            lambda: self.valueChangedDelayed.emit(self.value())
        )
        self.valueChanged.connect(self.on_value_changed)

        # Enhanced styling
        self.setStyleSheet(
            """
            QSpinBox {
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 5px 10px;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f8f9fa);
                selection-background-color: #3498db;
                selection-color: white;
            }
            QSpinBox:hover {
                border-color: #2980b9;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #ecf0f1);
            }
            QSpinBox:focus {
                border-color: #e74c3c;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #fdf2e9);
            }
            QSpinBox:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
                border-color: #95a5a6;
            }
            QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #bdc3c7;
                border-bottom: 1px solid #bdc3c7;
                border-top-right-radius: 6px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ecf0f1, stop: 1 #d5dbdb);
            }
            QSpinBox::up-button:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
            }
            QSpinBox::up-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-bottom: 6px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
            QSpinBox::up-arrow:hover {
                border-bottom-color: white;
            }
            QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                width: 20px;
                border-left: 1px solid #bdc3c7;
                border-top: 1px solid #bdc3c7;
                border-bottom-right-radius: 6px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ecf0f1, stop: 1 #d5dbdb);
            }
            QSpinBox::down-button:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
            }
            QSpinBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
            QSpinBox::down-arrow:hover {
                border-top-color: white;
            }
        """
        )

        # Mouse wheel behavior
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def on_value_changed(self):
        """Handle value change with delay."""
        self.delay_timer.stop()
        self.delay_timer.start(500)  # 500ms delay

    def wheelEvent(self, event):
        """Enhanced wheel event handling."""
        if self.hasFocus():
            super().wheelEvent(event)
        else:
            event.ignore()

class EnhancedDoubleSpinBox(QDoubleSpinBox):
    """Enhanced double SpinBox with better styling and features."""

    valueChangedDelayed = pyqtSignal(float)  # Emitted after delay

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setup_enhanced_features()

    def setup_enhanced_features(self):
        """Set up enhanced features."""
        # Delayed value change signal
        self.delay_timer = QTimer()
        self.delay_timer.setSingleShot(True)
        self.delay_timer.timeout.connect(
            lambda: self.valueChangedDelayed.emit(self.value())
        )
        self.valueChanged.connect(self.on_value_changed)

        # Enhanced styling (same as EnhancedSpinBox but for QDoubleSpinBox)
        self.setStyleSheet(
            """
            QDoubleSpinBox {
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 5px 10px;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f8f9fa);
                selection-background-color: #3498db;
                selection-color: white;
            }
            QDoubleSpinBox:hover {
                border-color: #2980b9;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #ecf0f1);
            }
            QDoubleSpinBox:focus {
                border-color: #e74c3c;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #fdf2e9);
            }
            QDoubleSpinBox:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
                border-color: #95a5a6;
            }
            QDoubleSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #bdc3c7;
                border-bottom: 1px solid #bdc3c7;
                border-top-right-radius: 6px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ecf0f1, stop: 1 #d5dbdb);
            }
            QDoubleSpinBox::up-button:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
            }
            QDoubleSpinBox::up-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-bottom: 6px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
            QDoubleSpinBox::up-arrow:hover {
                border-bottom-color: white;
            }
            QDoubleSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                width: 20px;
                border-left: 1px solid #bdc3c7;
                border-top: 1px solid #bdc3c7;
                border-bottom-right-radius: 6px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ecf0f1, stop: 1 #d5dbdb);
            }
            QDoubleSpinBox::down-button:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
            }
            QDoubleSpinBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
            QDoubleSpinBox::down-arrow:hover {
                border-top-color: white;
            }
        """
        )

        # Mouse wheel behavior
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def on_value_changed(self):
        """Handle value change with delay."""
        self.delay_timer.stop()
        self.delay_timer.start(500)  # 500ms delay

    def wheelEvent(self, event):
        """Enhanced wheel event handling."""
        if self.hasFocus():
            super().wheelEvent(event)
        else:
            event.ignore()

class SpinBoxWithSlider(QWidget):
    """SpinBox combined with a slider for better user experience."""

    valueChanged = pyqtSignal(int)
    valueChangedDelayed = pyqtSignal(int)

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(5)

        # SpinBox
        self.spinbox = EnhancedSpinBox()
        layout.addWidget(self.spinbox)

        # Slider
        self.slider = QSlider(Qt.Orientation.Horizontal)
        self.slider.setStyleSheet(
            """
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ecf0f1, stop:1 #bdc3c7);
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #3498db, stop:1 #2980b9);
                border: 1px solid #2c3e50;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #e74c3c, stop:1 #c0392b);
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #3498db, stop:1 #2980b9);
                border: 1px solid #2c3e50;
                height: 10px;
                border-radius: 4px;
            }
        """
        )
        layout.addWidget(self.slider)

    def connect_signals(self):
        """Connect signals between spinbox and slider."""
        self.spinbox.valueChanged.connect(self.on_spinbox_changed)
        self.spinbox.valueChangedDelayed.connect(self.valueChangedDelayed.emit)
        self.slider.valueChanged.connect(self.on_slider_changed)

    def on_spinbox_changed(self, value):
        """Handle spinbox value change."""
        self.slider.blockSignals(True)
        self.slider.setValue(value)
        self.slider.blockSignals(False)
        self.valueChanged.emit(value)

    def on_slider_changed(self, value):
        """Handle slider value change."""
        self.spinbox.blockSignals(True)
        self.spinbox.setValue(value)
        self.spinbox.blockSignals(False)
        self.valueChanged.emit(value)

    def setRange(self, minimum: int, maximum: int):
        """Set range for both spinbox and slider."""
        self.spinbox.setRange(minimum, maximum)
        self.slider.setRange(minimum, maximum)

    def setValue(self, value: int):
        """Set value for both spinbox and slider."""
        self.spinbox.setValue(value)
        self.slider.setValue(value)

    def value(self) -> int:
        """Get current value."""
        return self.spinbox.value()

    def setEnabled(self, enabled: bool):
        """Enable/disable both widgets."""
        self.spinbox.setEnabled(enabled)
        self.slider.setEnabled(enabled)

class DoubleSpinBoxWithSlider(QWidget):
    """Double SpinBox combined with a slider for better user experience."""

    valueChanged = pyqtSignal(float)
    valueChangedDelayed = pyqtSignal(float)

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(5)

        # SpinBox
        self.spinbox = EnhancedDoubleSpinBox()
        layout.addWidget(self.spinbox)

        # Slider (scaled for double values)
        self.slider = QSlider(Qt.Orientation.Horizontal)
        self.slider.setStyleSheet(
            """
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ecf0f1, stop:1 #bdc3c7);
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #e67e22, stop:1 #d35400);
                border: 1px solid #2c3e50;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #f39c12, stop:1 #e67e22);
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #e67e22, stop:1 #d35400);
                border: 1px solid #2c3e50;
                height: 10px;
                border-radius: 4px;
            }
        """
        )
        layout.addWidget(self.slider)

        # Scaling factor for double values
        self.scale_factor = 100

    def connect_signals(self):
        """Connect signals between spinbox and slider."""
        self.spinbox.valueChanged.connect(self.on_spinbox_changed)
        self.spinbox.valueChangedDelayed.connect(self.valueChangedDelayed.emit)
        self.slider.valueChanged.connect(self.on_slider_changed)

    def on_spinbox_changed(self, value):
        """Handle spinbox value change."""
        self.slider.blockSignals(True)
        self.slider.setValue(int(value * self.scale_factor))
        self.slider.blockSignals(False)
        self.valueChanged.emit(value)

    def on_slider_changed(self, value):
        """Handle slider value change."""
        double_value = value / self.scale_factor
        self.spinbox.blockSignals(True)
        self.spinbox.setValue(double_value)
        self.spinbox.blockSignals(False)
        self.valueChanged.emit(double_value)

    def setRange(self, minimum: float, maximum: float):
        """Set range for both spinbox and slider."""
        self.spinbox.setRange(minimum, maximum)
        self.slider.setRange(
            int(minimum * self.scale_factor), int(maximum * self.scale_factor)
        )

    def setValue(self, value: float):
        """Set value for both spinbox and slider."""
        self.spinbox.setValue(value)
        self.slider.setValue(int(value * self.scale_factor))

    def value(self) -> float:
        """Get current value."""
        return self.spinbox.value()

    def setDecimals(self, decimals: int):
        """Set decimal places."""
        self.spinbox.setDecimals(decimals)
        self.scale_factor = 10**decimals

    def setEnabled(self, enabled: bool):
        """Enable/disable both widgets."""
        self.spinbox.setEnabled(enabled)
        self.slider.setEnabled(enabled)

class ParameterSpinBoxWidget(QWidget):
    """Complete parameter widget with label, spinbox, help button, and validation."""

    valueChanged = pyqtSignal(object)  # Can emit int or float

    def __init__(
        self,
        param_name: str,
        param_type: str = "int",
        label: str = None,
        help_text: str = None,
        default_value: Union[int, float] = 0,
        minimum: Union[int, float] = 0,
        maximum: Union[int, float] = 100,
        with_slider: bool = True,
        parent=None,
    ):
        """Initialize the instance."""
        super().__init__(parent)

        self.param_name = param_name
        self.param_type = param_type
        self.help_text = help_text or f"Parameter: {param_name}"

        self.setup_ui(label or param_name, default_value, minimum, maximum, with_slider)
        self.connect_signals()

    def setup_ui(
        self,
        label: str,
        default_value: Union[int, float],
        minimum: Union[int, float],
        maximum: Union[int, float],
        with_slider: bool,
    ):
        """Set up the parameter widget UI."""
        layout = QHBoxLayout(self)
        layout.setSpacing(10)

        # Parameter label
        self.label = QLabel(label + ":")
        self.label.setMinimumWidth(120)
        self.label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(self.label)

        # SpinBox (with or without slider)
        if with_slider:
            if self.param_type == "float":
                self.spinbox_widget = DoubleSpinBoxWithSlider()
                if isinstance(default_value, (int, float)):
                    self.spinbox_widget.setDecimals(2)
            else:
                self.spinbox_widget = SpinBoxWithSlider()
        else:
            if self.param_type == "float":
                self.spinbox_widget = EnhancedDoubleSpinBox()
                if isinstance(default_value, (int, float)):
                    self.spinbox_widget.setDecimals(2)
            else:
                self.spinbox_widget = EnhancedSpinBox()

        self.spinbox_widget.setRange(minimum, maximum)
        self.spinbox_widget.setValue(default_value)
        self.spinbox_widget.setMinimumWidth(150)
        layout.addWidget(self.spinbox_widget)

        # Help button
        self.help_button = QPushButton("?")
        self.help_button.setMaximumSize(25, 25)
        self.help_button.setStyleSheet(
            """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """
        )
        self.help_button.setToolTip(self.help_text)
        self.help_button.clicked.connect(self.show_help)
        layout.addWidget(self.help_button)

        # Status indicator
        self.status_label = QLabel("✓")
        self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        self.status_label.setMaximumWidth(20)
        layout.addWidget(self.status_label)

        layout.addStretch()

    def connect_signals(self):
        """Connect widget signals."""
        self.spinbox_widget.valueChanged.connect(self.on_value_changed)

    def on_value_changed(self, value):
        """Handle value change."""
        self.validate_value(value)
        self.valueChanged.emit(value)

    def validate_value(self, value):
        """Validate the current value and update status."""
        # Basic validation - can be extended
        if isinstance(value, (int, float)) and not (
            value < 0 and self.param_name in ["PixSize", "FmDose"]
        ):
            self.status_label.setText("✓")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        else:
            self.status_label.setText("⚠")
            self.status_label.setStyleSheet("color: #f39c12; font-weight: bold;")

    def show_help(self):
        """Show help tooltip."""
        QToolTip.showText(
            self.help_button.mapToGlobal(self.help_button.rect().center()),
            self.help_text,
        )

    def value(self):
        """Get current value."""
        return self.spinbox_widget.value()

    def setValue(self, value):
        """Set current value."""
        self.spinbox_widget.setValue(value)

    def setEnabled(self, enabled: bool):
        """Enable/disable the widget."""
        self.spinbox_widget.setEnabled(enabled)
        self.help_button.setEnabled(enabled)

    def setRange(self, minimum, maximum):
        """Set value range."""
        self.spinbox_widget.setRange(minimum, maximum)

# Factory function for creating parameter widgets
def create_parameter_widget(
    param_name: str, param_config: dict, parent=None
) -> ParameterSpinBoxWidget:
    """Factory function to create parameter widgets from configuration."""
    return ParameterSpinBoxWidget(
        param_name=param_name,
        param_type=param_config.get("type", "int"),
        label=param_config.get("label", param_name),
        help_text=param_config.get("help", f"Parameter: {param_name}"),
        default_value=param_config.get("default", 0),
        minimum=param_config.get("min", 0),
        maximum=param_config.get("max", 100),
        with_slider=param_config.get("with_slider", True),
        parent=parent,
    )
