#!/usr/bin/env python3
"""
Live Processing Tab for AreTomo3 GUI.
Monitors directories for new files and processes them automatically.
"""

import glob
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set

from PyQt6.QtCore import Qt, QThread, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QColor, QFont
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QSpinBox,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from ..widgets.live_tilt_series_monitor import LiveTiltSeriesMonitor

# Import the real-time analysis components
from .realtime_analysis_tab import RealTimeAnalysisTab

logger = logging.getLogger(__name__)

class LiveFileMonitor(QThread):
    """Background thread for monitoring file system changes."""

    new_files_detected = pyqtSignal(list)  # List of new file paths
    status_update = pyqtSignal(str)  # Status message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitoring_dirs: List[str] = []
        self.known_files: Set[str] = set()
        self.file_patterns = ["*.eer", "*.tif", "*.tiff", "*.mrc", "*.mrcs", "*.mdoc"]
        self.running = False
        self.check_interval = 2.0  # seconds

    def add_monitoring_directory(self, directory: str):
        """Add a directory to monitor."""
        if directory not in self.monitoring_dirs:
            self.monitoring_dirs.append(directory)
            self.scan_existing_files()
            logger.info(f"Added monitoring directory: {directory}")

    def remove_monitoring_directory(self, directory: str):
        """Remove a directory from monitoring."""
        if directory in self.monitoring_dirs:
            self.monitoring_dirs.remove(directory)
            logger.info(f"Removed monitoring directory: {directory}")

    def scan_existing_files(self):
        """Scan for existing files in all monitored directories."""
        existing_files = set()
        for directory in self.monitoring_dirs:
            for pattern in self.file_patterns:
                files = glob.glob(
                    os.path.join(directory, "**", pattern), recursive=True
                )
                existing_files.update(files)

        self.known_files = existing_files
        logger.info(f"Scanned {len(existing_files)} existing files")

    def start_monitoring(self):
        """Start the monitoring thread."""
        self.running = True
        self.start()

    def stop_monitoring(self):
        """Stop the monitoring thread."""
        self.running = False
        self.wait()

    def run(self):
        """Main monitoring loop."""
        while self.running:
            try:
                self.check_for_new_files()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in file monitoring: {e}")
                self.status_update.emit(f"Monitoring error: {e}")

    def check_for_new_files(self):
        """Check for new files in monitored directories."""
        current_files = set()

        for directory in self.monitoring_dirs:
            if not os.path.exists(directory):
                continue

            for pattern in self.file_patterns:
                files = glob.glob(
                    os.path.join(directory, "**", pattern), recursive=True
                )
                current_files.update(files)

        new_files = current_files - self.known_files
        if new_files:
            new_files_list = sorted(list(new_files))
            self.new_files_detected.emit(new_files_list)
            self.known_files = current_files
            logger.info(f"Detected {len(new_files)} new files")

class LiveProcessor(QThread):
    """Background thread for processing files automatically."""

    processing_started = pyqtSignal(str)  # File being processed
    processing_finished = pyqtSignal(str, bool, str)  # File, success, message
    progress_update = pyqtSignal(str, int)  # File, progress percentage

    def __init__(self, parent=None):
        super().__init__(parent)
        self.processing_queue: List[str] = []
        self.current_file: Optional[str] = None
        self.running = False
        self.main_window = None  # Reference to main window for processing

    def set_main_window(self, main_window):
        """Set reference to main window for accessing processing functions."""
        self.main_window = main_window

    def add_file_to_queue(self, file_path: str):
        """Add a file to the processing queue."""
        if file_path not in self.processing_queue:
            self.processing_queue.append(file_path)
            logger.info(f"Added to processing queue: {file_path}")

    def start_processing(self):
        """Start the processing thread."""
        self.running = True
        self.start()

    def stop_processing(self):
        """Stop the processing thread."""
        self.running = False
        self.wait()

    def run(self):
        """Main processing loop."""
        while self.running:
            if self.processing_queue and self.main_window:
                file_path = self.processing_queue.pop(0)
                self.process_file(file_path)
            else:
                time.sleep(1.0)  # Wait for files to be added

    def process_file(self, file_path: str):
        """Process a single file."""
        try:
            self.current_file = file_path
            self.processing_started.emit(file_path)

            # Here you would implement the actual processing logic
            # For now, simulate processing
            logger.info(f"Processing file: {file_path}")

            # Simulate processing time and progress updates
            for i in range(0, 101, 10):
                if not self.running:
                    break
                self.progress_update.emit(file_path, i)
                time.sleep(0.5)  # Simulate work

            if self.running:
                self.processing_finished.emit(
                    file_path, True, "Processing completed successfully"
                )

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            self.processing_finished.emit(file_path, False, str(e))
        finally:
            self.current_file = None

class LiveProcessingTab(QWidget):
    """Live processing tab for real-time file monitoring and processing."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.file_monitor = LiveFileMonitor(self)
        self.live_processor = LiveProcessor(self)

        # Connect signals
        self.file_monitor.new_files_detected.connect(self.handle_new_files)
        self.file_monitor.status_update.connect(self.update_status)

        self.live_processor.processing_started.connect(self.handle_processing_started)
        self.live_processor.processing_finished.connect(self.handle_processing_finished)
        self.live_processor.progress_update.connect(self.handle_progress_update)

        if self.main_window:
            self.live_processor.set_main_window(self.main_window)

        self.setup_ui()
        logger.info("Live processor initialized")

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)

        # Create main splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Controls
        controls_widget = self.create_controls_widget()
        splitter.addWidget(controls_widget)

        # Right side - Status and analysis
        status_widget = self.create_status_widget()
        splitter.addWidget(status_widget)

        # Set splitter proportions
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)

        layout.addWidget(splitter)

        logger.info("Live processing tab setup completed successfully")

    def create_controls_widget(self):
        """Create the controls widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Monitoring controls
        monitor_group = QGroupBox("📡 Live Monitoring")
        monitor_layout = QVBoxLayout(monitor_group)

        # Directory selection
        dir_layout = QHBoxLayout()
        self.monitor_dir_edit = QLineEdit()
        self.monitor_dir_edit.setPlaceholderText("Select directory to monitor...")
        browse_btn = QPushButton("📁 Browse")
        browse_btn.clicked.connect(self.browse_monitor_directory)

        dir_layout.addWidget(QLabel("Monitor Directory:"))
        dir_layout.addWidget(self.monitor_dir_edit, 1)
        dir_layout.addWidget(browse_btn)
        monitor_layout.addLayout(dir_layout)

        # Monitoring controls
        controls_layout = QHBoxLayout()
        self.start_monitor_btn = QPushButton("▶️ Start Monitoring")
        self.stop_monitor_btn = QPushButton("⏹️ Stop Monitoring")
        self.stop_monitor_btn.setEnabled(False)

        self.start_monitor_btn.clicked.connect(self.start_monitoring)
        self.stop_monitor_btn.clicked.connect(self.stop_monitoring)

        controls_layout.addWidget(self.start_monitor_btn)
        controls_layout.addWidget(self.stop_monitor_btn)
        monitor_layout.addLayout(controls_layout)

        layout.addWidget(monitor_group)

        # Processing controls
        process_group = QGroupBox("⚙️ Auto Processing")
        process_layout = QVBoxLayout(process_group)

        self.auto_process_chk = QCheckBox("🔄 Auto-process new files")
        self.auto_process_chk.setChecked(True)
        process_layout.addWidget(self.auto_process_chk)

        # Processing delay
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("Processing delay:"))
        self.delay_spinbox = QSpinBox()
        self.delay_spinbox.setRange(0, 300)
        self.delay_spinbox.setValue(5)
        self.delay_spinbox.setSuffix(" seconds")
        delay_layout.addWidget(self.delay_spinbox)
        process_layout.addLayout(delay_layout)

        layout.addWidget(process_group)

        # File filters
        filter_group = QGroupBox("🔍 File Filters")
        filter_layout = QVBoxLayout(filter_group)

        self.eer_chk = QCheckBox("EER files (*.eer)")
        self.eer_chk.setChecked(True)
        self.tiff_chk = QCheckBox("TIFF files (*.tif, *.tiff)")
        self.tiff_chk.setChecked(True)
        self.mrc_chk = QCheckBox("MRC files (*.mrc, *.mrcs)")
        self.mrc_chk.setChecked(True)
        self.mdoc_chk = QCheckBox("MDOC files (*.mdoc) - Required for AreTomo3")
        self.mdoc_chk.setChecked(True)
        self.mdoc_chk.setStyleSheet("font-weight: bold; color: #007bff;")

        filter_layout.addWidget(self.eer_chk)
        filter_layout.addWidget(self.tiff_chk)
        filter_layout.addWidget(self.mrc_chk)
        filter_layout.addWidget(self.mdoc_chk)

        layout.addWidget(filter_group)

        layout.addStretch()
        return widget

    def create_status_widget(self):
        """Create the status and monitoring widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Status display
        status_group = QGroupBox("📊 Live Status")
        status_layout = QVBoxLayout(status_group)

        # Current status
        self.status_label = QLabel("🔴 Monitoring stopped")
        self.status_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        status_layout.addWidget(self.status_label)

        # Statistics
        stats_layout = QGridLayout()
        stats_layout.addWidget(QLabel("Files detected:"), 0, 0)
        self.files_detected_label = QLabel("0")
        stats_layout.addWidget(self.files_detected_label, 0, 1)

        stats_layout.addWidget(QLabel("Files processed:"), 1, 0)
        self.files_processed_label = QLabel("0")
        stats_layout.addWidget(self.files_processed_label, 1, 1)

        stats_layout.addWidget(QLabel("Processing queue:"), 2, 0)
        self.queue_size_label = QLabel("0")
        stats_layout.addWidget(self.queue_size_label, 2, 1)

        status_layout.addLayout(stats_layout)
        layout.addWidget(status_group)

        # File list and processing log
        tabs = QTabWidget()

        # Detected files tab
        files_tab = QWidget()
        files_layout = QVBoxLayout(files_tab)

        files_layout.addWidget(QLabel("📄 Recently Detected Files:"))
        self.detected_files_list = QListWidget()
        files_layout.addWidget(self.detected_files_list)

        tabs.addTab(files_tab, "📄 Files")

        # Processing log tab
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)

        log_layout.addWidget(QLabel("📝 Processing Log:"))
        self.processing_log = QTextEdit()
        self.processing_log.setReadOnly(True)
        self.processing_log.setMaximumHeight(200)
        log_layout.addWidget(self.processing_log)

        tabs.addTab(log_tab, "📝 Log")

        # Processing queue tab
        queue_tab = QWidget()
        queue_layout = QVBoxLayout(queue_tab)

        queue_layout.addWidget(QLabel("⏳ Processing Queue:"))
        self.queue_table = QTableWidget()
        self.queue_table.setColumnCount(3)
        self.queue_table.setHorizontalHeaderLabels(["File", "Status", "Progress"])
        self.queue_table.horizontalHeader().setStretchLastSection(True)
        queue_layout.addWidget(self.queue_table)

        tabs.addTab(queue_tab, "⏳ Queue")

        # Real-time analysis tab - use the actual RealTimeAnalysisTab
        self.analysis_widget = RealTimeAnalysisTab(self.main_window)

        # Set up for live processing mode
        if hasattr(self.analysis_widget, "set_live_processing_mode"):
            self.analysis_widget.set_live_processing_mode(True)

        tabs.addTab(self.analysis_widget, "📊 Analysis")

        # Live Tilt Series Monitor tab
        self.live_monitor = LiveTiltSeriesMonitor(self)
        tabs.addTab(self.live_monitor, "📈 Live Monitor")

        # Viewer tab for live processing
        viewer_tab = self.create_viewer_tab()
        tabs.addTab(viewer_tab, "🔬 Viewer")

        # Command review tab
        command_tab = self.create_command_review_tab()
        tabs.addTab(command_tab, "⚙️ Command")

        layout.addWidget(tabs)
        return widget

    def create_viewer_tab(self):
        """Create the viewer tab for live processing."""
        viewer_widget = QWidget()
        layout = QVBoxLayout(viewer_widget)

        # Viewer controls
        controls_layout = QHBoxLayout()

        # File selection for viewing
        self.viewer_file_combo = QComboBox()
        self.viewer_file_combo.setPlaceholderText("Select file to view...")
        self.viewer_file_combo.currentTextChanged.connect(self.load_file_in_viewer)

        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_viewer_files)

        auto_load_chk = QCheckBox("Auto-load latest")
        auto_load_chk.setChecked(True)
        self.auto_load_latest = auto_load_chk

        controls_layout.addWidget(QLabel("File:"))
        controls_layout.addWidget(self.viewer_file_combo, 1)
        controls_layout.addWidget(refresh_btn)
        controls_layout.addWidget(auto_load_chk)

        layout.addLayout(controls_layout)

        # Viewer area (placeholder for now - will integrate with Napari)
        viewer_area = QWidget()
        viewer_area.setMinimumHeight(300)
        viewer_area.setStyleSheet(
            """
            QWidget {
                background-color: #f8f9fa;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
            }
        """
        )
        viewer_layout = QVBoxLayout(viewer_area)

        self.viewer_status_label = QLabel(
            "🔬 Live Viewer\n\nSelect a file to view processed results"
        )
        self.viewer_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.viewer_status_label.setStyleSheet("color: #6c757d; font-size: 14px;")
        viewer_layout.addWidget(self.viewer_status_label)

        layout.addWidget(viewer_area)

        # File info
        info_group = QGroupBox("📄 File Information")
        info_layout = QGridLayout(info_group)

        self.file_path_label = QLabel("No file selected")
        self.file_size_label = QLabel("-")
        self.file_modified_label = QLabel("-")
        self.processing_status_label = QLabel("-")

        info_layout.addWidget(QLabel("Path:"), 0, 0)
        info_layout.addWidget(self.file_path_label, 0, 1)
        info_layout.addWidget(QLabel("Size:"), 1, 0)
        info_layout.addWidget(self.file_size_label, 1, 1)
        info_layout.addWidget(QLabel("Modified:"), 2, 0)
        info_layout.addWidget(self.file_modified_label, 2, 1)
        info_layout.addWidget(QLabel("Status:"), 3, 0)
        info_layout.addWidget(self.processing_status_label, 3, 1)

        layout.addWidget(info_group)

        return viewer_widget

    def create_command_review_tab(self):
        """Create the command review tab."""
        command_widget = QWidget()
        layout = QVBoxLayout(command_widget)

        # Command generation controls
        controls_group = QGroupBox("⚙️ Command Generation")
        controls_layout = QVBoxLayout(controls_group)

        # Quick parameters for live processing
        quick_params_layout = QGridLayout()

        # Essential parameters for live processing
        quick_params_layout.addWidget(QLabel("Pixel Size (Å):"), 0, 0)
        self.live_pixel_size = QDoubleSpinBox()
        self.live_pixel_size.setRange(0.1, 10.0)
        self.live_pixel_size.setValue(1.82)
        self.live_pixel_size.setDecimals(2)
        quick_params_layout.addWidget(self.live_pixel_size, 0, 1)

        quick_params_layout.addWidget(QLabel("Voltage (kV):"), 0, 2)
        self.live_voltage = QSpinBox()
        self.live_voltage.setRange(80, 300)
        self.live_voltage.setValue(300)
        quick_params_layout.addWidget(self.live_voltage, 0, 3)

        quick_params_layout.addWidget(QLabel("Tilt Axis (°):"), 1, 0)
        self.live_tilt_axis = QDoubleSpinBox()
        self.live_tilt_axis.setRange(-180, 180)
        self.live_tilt_axis.setValue(175.9)
        self.live_tilt_axis.setDecimals(1)
        quick_params_layout.addWidget(self.live_tilt_axis, 1, 1)

        quick_params_layout.addWidget(QLabel("GPU IDs:"), 1, 2)
        self.live_gpu = QLineEdit()
        self.live_gpu.setText("0")
        self.live_gpu.setPlaceholderText("e.g., 0 1 2")
        quick_params_layout.addWidget(self.live_gpu, 1, 3)

        controls_layout.addLayout(quick_params_layout)

        # Sync with parameters tab
        sync_layout = QHBoxLayout()
        sync_btn = QPushButton("🔄 Sync from Parameters Tab")
        sync_btn.clicked.connect(self.sync_from_parameters_tab)
        sync_layout.addWidget(sync_btn)
        sync_layout.addStretch()
        controls_layout.addLayout(sync_layout)

        layout.addWidget(controls_group)

        # Command preview
        preview_group = QGroupBox("📋 Live Command Preview")
        preview_layout = QVBoxLayout(preview_group)

        # Generate command button
        generate_layout = QHBoxLayout()
        generate_btn = QPushButton("🔧 Generate Command")
        generate_btn.clicked.connect(self.generate_live_command)
        copy_btn = QPushButton("📋 Copy Command")
        copy_btn.clicked.connect(self.copy_live_command)

        generate_layout.addWidget(generate_btn)
        generate_layout.addWidget(copy_btn)
        generate_layout.addStretch()
        preview_layout.addLayout(generate_layout)

        # Command display
        self.live_command_preview = QTextEdit()
        self.live_command_preview.setReadOnly(True)
        self.live_command_preview.setMinimumHeight(200)
        self.live_command_preview.setStyleSheet(
            """
            QTextEdit {
                background-color: white;
                color: black;
                font-family: 'Courier New', monospace;
                font-size: 10pt;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 10px;
                line-height: 1.4;
            }
        """
        )
        self.live_command_preview.setPlainText(
            "# Click 'Generate Command' to see the AreTomo3 command\n# This will combine parameters with monitored files"
        )
        preview_layout.addWidget(self.live_command_preview)

        layout.addWidget(preview_group)

        # AreTomo3 file requirements info
        info_group = QGroupBox("📚 AreTomo3 File Requirements")
        info_layout = QVBoxLayout(info_group)

        requirements_text = """
<b>Required Files for Live Processing:</b>
• <b>.mdoc files</b> - Tilt series metadata (SerialEM format)
• <b>Movie files</b> - .eer, .tif, .mrc formats supported
• <b>Gain reference</b> - Optional but recommended for .eer files
• <b>Dark reference</b> - Optional for noise correction

<b>AreTomo3 Processing Pipeline:</b>
1. <b>Motion Correction</b> - MotionCor3 integration
2. <b>CTF Estimation</b> - GCtfFind integration
3. <b>Tilt Series Alignment</b> - AreTomo2 integration
4. <b>3D Reconstruction</b> - Tomogram generation

<b>Live Processing Mode:</b>
AreTomo3 monitors directories and processes new .mdoc files automatically.
Each .mdoc file triggers processing of its associated movie files.
        """

        info_label = QLabel(requirements_text)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #495057; font-size: 11px;")
        info_layout.addWidget(info_label)

        layout.addWidget(info_group)

        return command_widget

    def browse_monitor_directory(self):
        """Browse for directory to monitor."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Directory to Monitor",
            str(Path.home()),
            QFileDialog.Option.ShowDirsOnly,
        )
        if directory:
            self.monitor_dir_edit.setText(directory)

    def start_monitoring(self):
        """Start file monitoring."""
        directory = self.monitor_dir_edit.text().strip()
        if not directory:
            QMessageBox.warning(
                self, "No Directory", "Please select a directory to monitor."
            )
            return

        if not os.path.exists(directory):
            QMessageBox.warning(
                self, "Invalid Directory", f"Directory does not exist: {directory}"
            )
            return

        # Update file patterns based on checkboxes
        patterns = []
        if self.eer_chk.isChecked():
            patterns.append("*.eer")
        if self.tiff_chk.isChecked():
            patterns.extend(["*.tif", "*.tiff"])
        if self.mrc_chk.isChecked():
            patterns.extend(["*.mrc", "*.mrcs"])
        if self.mdoc_chk.isChecked():
            patterns.append("*.mdoc")

        if not patterns:
            QMessageBox.warning(
                self,
                "No File Types",
                "Please select at least one file type to monitor.",
            )
            return

        # Warn if MDOC files are not selected (required for AreTomo3)
        if not self.mdoc_chk.isChecked():
            reply = QMessageBox.question(
                self,
                "MDOC Files Not Selected",
                "MDOC files are required for AreTomo3 live processing.\n\nContinue without MDOC monitoring?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )
            if reply == QMessageBox.StandardButton.No:
                return

        self.file_monitor.file_patterns = patterns
        self.file_monitor.add_monitoring_directory(directory)
        self.file_monitor.start_monitoring()

        if self.auto_process_chk.isChecked():
            self.live_processor.start_processing()

        # Start real-time analysis monitoring
        output_dir = directory.replace(
            "input", "output"
        )  # Assume output is parallel to input
        if hasattr(self, "analysis_widget"):
            # Set up the analysis widget for live monitoring
            if hasattr(self.analysis_widget, "set_monitoring_directory"):
                self.analysis_widget.set_monitoring_directory(output_dir)
            if hasattr(self.analysis_widget, "start_monitoring"):
                self.analysis_widget.start_monitoring()

        # Update UI
        self.status_label.setText("🟢 Monitoring active")
        self.status_label.setStyleSheet("font-weight: bold; color: #28a745;")
        self.start_monitor_btn.setEnabled(False)
        self.stop_monitor_btn.setEnabled(True)

        self.log_message(f"Started monitoring: {directory}")
        self.log_message(f"Started analysis monitoring: {output_dir}")
        logger.info(f"Started live monitoring of {directory}")

    def stop_monitoring(self):
        """Stop file monitoring."""
        self.file_monitor.stop_monitoring()
        self.live_processor.stop_processing()

        # Stop real-time analysis monitoring
        if hasattr(self, "analysis_widget"):
            if hasattr(self.analysis_widget, "stop_monitoring"):
                self.analysis_widget.stop_monitoring()

        # Update UI
        self.status_label.setText("🔴 Monitoring stopped")
        self.status_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        self.start_monitor_btn.setEnabled(True)
        self.stop_monitor_btn.setEnabled(False)

        self.log_message("Stopped monitoring")
        self.log_message("Stopped analysis monitoring")
        logger.info("Stopped live monitoring")

    @pyqtSlot(list)
    def handle_new_files(self, file_paths: List[str]):
        """Handle detection of new files."""
        for file_path in file_paths:
            # Add to detected files list
            item = QListWidgetItem(f"🆕 {os.path.basename(file_path)}")
            item.setToolTip(file_path)
            self.detected_files_list.insertItem(0, item)

            # Keep only recent 50 files
            while self.detected_files_list.count() > 50:
                self.detected_files_list.takeItem(self.detected_files_list.count() - 1)

            # Add to processing queue if auto-processing is enabled
            if self.auto_process_chk.isChecked():
                self.live_processor.add_file_to_queue(file_path)

            # Update live tilt series monitor
            self.update_live_monitor_with_file(file_path)

        # Update statistics
        current_count = int(self.files_detected_label.text())
        self.files_detected_label.setText(str(current_count + len(file_paths)))

        self.log_message(f"Detected {len(file_paths)} new files")

    def update_live_monitor_with_file(self, file_path: str):
        """Update the live tilt series monitor with new file data."""
        try:
            # Extract series information from file path
            series_name = self.extract_series_name_from_path(file_path)

            # Create mock data for demonstration
            # (in real implementation, this would come from processing)
            import random

            mock_data = {
                "motion": {
                    "tilt_angles": [-30, -20, -10, 0, 10, 20, 30],
                    "motion_values": [random.uniform(1.0, 3.0) for _ in range(7)],
                },
                "ctf": {
                    "tilt_angles": [-30, -20, -10, 0, 10, 20, 30],
                    "defocus": [random.uniform(1.5, 3.0) for _ in range(7)],
                },
                "alignment": {
                    "tilt_angles": [-30, -20, -10, 0, 10, 20, 30],
                    "scores": [random.uniform(0.8, 0.95) for _ in range(7)],
                },
                "resolution": {
                    "tilt_angles": [-30, -20, -10, 0, 10, 20, 30],
                    "resolution_values": [random.uniform(6.0, 10.0) for _ in range(7)],
                },
            }

            # Add to live monitor (mark as latest)
            is_latest = True  # Always mark new files as latest
            if hasattr(self, "live_monitor"):
                self.live_monitor.add_tilt_series(series_name, mock_data, is_latest)
                logger.info(f"Updated live monitor with series: {series_name}")

        except Exception as e:
            logger.error(f"Error updating live monitor with file {file_path}: {e}")

    def extract_series_name_from_path(self, file_path: str) -> str:
        """Extract series name from file path."""
        try:
            filename = os.path.basename(file_path)
            # Remove common suffixes and extract base name
            for suffix in ["_EER.eer", ".eer", ".tif", ".tiff", ".mrc", ".mrcs"]:
                if filename.endswith(suffix):
                    filename = filename[: -len(suffix)]
                    break

            # Extract position/series name (remove angle information)
            import re

            # Remove angle patterns like [45.0] or _45.0
            series_name = re.sub(r"\[[-\d.]+\]", "", filename)
            series_name = re.sub(r"_[-\d.]+$", "", series_name)

            return series_name or "Unknown_Series"

        except Exception as e:
            logger.error(f"Error extracting series name from {file_path}: {e}")
            return "Unknown_Series"

    @pyqtSlot(str)
    def update_status(self, message: str):
        """Update status message."""
        self.log_message(f"Status: {message}")

    @pyqtSlot(str)
    def handle_processing_started(self, file_path: str):
        """Handle start of file processing."""
        self.log_message(f"Started processing: {os.path.basename(file_path)}")

    @pyqtSlot(str, bool, str)
    def handle_processing_finished(self, file_path: str, success: bool, message: str):
        """Handle completion of file processing."""
        status = "✅ Success" if success else "❌ Failed"
        self.log_message(
            f"Finished processing {os.path.basename(file_path)}: {status} - {message}"
        )

        if success:
            current_count = int(self.files_processed_label.text())
            self.files_processed_label.setText(str(current_count + 1))

            # Refresh viewer files when processing completes
            self.refresh_viewer_files()

            # Auto-load latest file if enabled
            if hasattr(self, "auto_load_latest") and self.auto_load_latest.isChecked():
                if self.viewer_file_combo.count() > 0:
                    self.viewer_file_combo.setCurrentIndex(0)  # Load newest file

    @pyqtSlot(str, int)
    def handle_progress_update(self, file_path: str, progress: int):
        """Handle processing progress updates."""
        # Update queue table if needed
        pass

    def refresh_viewer_files(self):
        """Refresh the list of files available for viewing."""
        self.viewer_file_combo.clear()

        # Get output directory from monitoring directory
        monitor_dir = self.monitor_dir_edit.text().strip()
        if not monitor_dir:
            return

        # Look for processed files in output directory
        output_dir = monitor_dir.replace(
            "input", "output"
        )  # Assume output parallel to input
        if not os.path.exists(output_dir):
            output_dir = os.path.join(monitor_dir, "AreTomo3_Output")

        if os.path.exists(output_dir):
            # Look for reconstructed tomograms and aligned stacks
            patterns = ["*.mrc", "*.rec", "*_ali.mrc", "*_rec.mrc"]
            files = []
            for pattern in patterns:
                files.extend(
                    glob.glob(os.path.join(output_dir, "**", pattern), recursive=True)
                )

            # Sort by modification time (newest first)
            files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            for file_path in files:
                rel_path = os.path.relpath(file_path, output_dir)
                self.viewer_file_combo.addItem(rel_path, file_path)

    def load_file_in_viewer(self, file_name):
        """Load selected file in the viewer."""
        if not file_name:
            return

        file_path = self.viewer_file_combo.currentData()
        if not file_path or not os.path.exists(file_path):
            return

        # Update file information
        self.file_path_label.setText(file_path)

        try:
            stat = os.stat(file_path)
            size_mb = stat.st_size / (1024 * 1024)
            self.file_size_label.setText(f"{size_mb:.1f} MB")

            mod_time = datetime.fromtimestamp(stat.st_mtime)
            self.file_modified_label.setText(mod_time.strftime("%Y-%m-%d %H:%M:%S"))

            # Determine processing status based on file name
            if "_rec" in file_name or ".rec" in file_name:
                self.processing_status_label.setText("✅ Reconstructed")
            elif "_ali" in file_name:
                self.processing_status_label.setText("🔄 Aligned")
            else:
                self.processing_status_label.setText("📄 Raw/Processed")

            # Update viewer status
            self.viewer_status_label.setText(
                f"🔬 Viewing: {os.path.basename(file_name)}\n\nFile loaded successfully"
            )

        except Exception as e:
            logger.error(f"Error loading file info: {e}")
            self.viewer_status_label.setText(f"❌ Error loading file:\n{str(e)}")

    def sync_from_parameters_tab(self):
        """Sync parameters from the main parameters tab."""
        if not self.main_window:
            return

        try:
            # Get parameters tab
            params_tab = getattr(self.main_window, "parameters_tab", None)
            if not params_tab:
                QMessageBox.warning(
                    self, "No Parameters Tab", "Parameters tab not found."
                )
                return

            # Sync essential parameters
            if hasattr(params_tab, "pix_size"):
                self.live_pixel_size.setValue(params_tab.pix_size.value())
            if hasattr(params_tab, "kv"):
                self.live_voltage.setValue(params_tab.kv.value())
            if hasattr(params_tab, "tilt_axis"):
                self.live_tilt_axis.setValue(params_tab.tilt_axis.value())
            if hasattr(params_tab, "gpu"):
                self.live_gpu.setText(params_tab.gpu.text())

            QMessageBox.information(
                self, "Sync Complete", "Parameters synced from Parameters tab."
            )

        except Exception as e:
            logger.error(f"Error syncing parameters: {e}")
            QMessageBox.warning(
                self, "Sync Error", f"Failed to sync parameters: {str(e)}"
            )

    def generate_live_command(self):
        """Generate AreTomo3 command for live processing."""
        monitor_dir = self.monitor_dir_edit.text().strip()
        if not monitor_dir:
            QMessageBox.warning(
                self, "No Directory", "Please select a monitoring directory first."
            )
            return

        # Build command parts
        command_parts = ["AreTomo3"]

        # Input directory (AreTomo3 monitors for .mdoc files)
        command_parts.append(f"-InPrefix {monitor_dir}")

        # Output directory
        output_dir = monitor_dir.replace("input", "output")
        if not os.path.exists(output_dir):
            output_dir = os.path.join(monitor_dir, "AreTomo3_Output")
        command_parts.append(f"-OutDir {output_dir}")

        # Essential parameters
        command_parts.append(f"-PixSize {self.live_pixel_size.value()}")
        command_parts.append(f"-Kv {self.live_voltage.value()}")
        command_parts.append(f"-TiltAxis {self.live_tilt_axis.value()}")

        # GPU configuration
        if self.live_gpu.text().strip():
            command_parts.append(f"-Gpu {self.live_gpu.text().strip()}")

        # Live processing specific parameters
        command_parts.append("-Live 1")  # Enable live processing mode
        command_parts.append("-Resume 1")  # Resume processing for new files

        # Get additional parameters from main parameters tab if available
        if self.main_window and hasattr(self.main_window, "parameters_tab"):
            params_tab = self.main_window.parameters_tab

            # Add gain/dark references if specified
            if hasattr(params_tab, "gain_file") and params_tab.gain_file.text().strip():
                command_parts.append(f"-Gain {params_tab.gain_file.text().strip()}")
            if hasattr(params_tab, "dark_file") and params_tab.dark_file.text().strip():
                command_parts.append(f"-Dark {params_tab.dark_file.text().strip()}")

        # Format command with line breaks for readability
        command = " \\\n    ".join(command_parts)
        self.live_command_preview.setPlainText(command)

        self.log_message("Generated live processing command")

    def copy_live_command(self):
        """Copy the live command to clipboard."""
        command = self.live_command_preview.toPlainText()
        if command and not command.startswith("#"):
            from PyQt6.QtWidgets import QApplication

            QApplication.clipboard().setText(command)
            QMessageBox.information(self, "Copied", "Command copied to clipboard!")
        else:
            QMessageBox.warning(self, "No Command", "Please generate a command first.")

    def log_message(self, message: str):
        """Add a message to the processing log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.processing_log.append(formatted_message)

        # Keep log size manageable
        if self.processing_log.document().blockCount() > 1000:
            cursor = self.processing_log.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            cursor.movePosition(
                cursor.MoveOperation.Down, cursor.MoveMode.KeepAnchor, 100
            )
            cursor.removeSelectedText()

    def closeEvent(self, event):
        """Clean up when tab is closed."""
        self.stop_monitoring()
        super().closeEvent(event)
