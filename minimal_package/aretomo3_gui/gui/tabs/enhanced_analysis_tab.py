#!/usr/bin/env python3
"""
Enhanced Analysis Tab for AreTomo3 GUI.
Provides comprehensive analysis and visualization capabilities.
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from PyQt6.QtCore import Qt, QThread, QTimer, QUrl, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QScrollArea,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from ...analysis import interactive_plotter
from ...analysis.interactive_plotter import PLOTLY_AVAILABLE
from ...utils.aretomo3_parser import AreTomo3ResultsParser

logger = logging.getLogger(__name__)

# Try to import QWebEngineView for interactive plots
try:
    from PyQt6.QtWebEngineWidgets import QWebEngineView

    WEBENGINE_AVAILABLE = True
except ImportError:
    WEBENGINE_AVAILABLE = False
    logger.warning("QWebEngineView not available - interactive plots will be limited")

    # TODO: Refactor class - Class 'EnhancedAnalysisTab' too long (2592 lines)


class EnhancedAnalysisTab(QWidget):
    """
    Data Analysis Tab - Comprehensive post-processing analysis and visualization.

    Features:
    - CTF estimation analysis and quality assessment
    - Motion correction evaluation and drift plots
    - Tilt series alignment quality metrics
    - Interactive data visualization with Plotly
    - Export capabilities for multiple formats
    - Statistical analysis and reporting
    """

    # Signals
    analysis_updated = pyqtSignal(str)  # Emitted when analysis is updated
    results_detected = pyqtSignal(
        str, int
    )  # Emitted when results are detected (path, file_count)

    def __init__(self, parent=None):
        """Initialize the enhanced analysis tab."""
        super().__init__(parent)
        self.main_window = parent
        self.current_results_dir = None
        self.analysis_data = {}
        self.aretomo3_parser = None
        self.generated_plots = {}

        logger.info("Initializing Enhanced Analysis Tab")
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)

        # Create main splitter (horizontal)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - Controls and settings (30%)
        self.create_control_panel(main_splitter)

        # Right panel - Analysis display (70%)
        self.create_analysis_panel(main_splitter)

        # Set splitter proportions
        main_splitter.setStretchFactor(0, 3)
        main_splitter.setStretchFactor(1, 7)

        main_layout.addWidget(main_splitter)

    def create_control_panel(self, parent):
        """Create the control panel."""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setSpacing(15)

        # Results Detection Section
        self.create_detection_section(control_layout)

        # Analysis Options Section
        self.create_options_section(control_layout)

        # Export Section
        self.create_export_section(control_layout)

        # Status Section
        self.create_status_section(control_layout)

        control_layout.addStretch()
        parent.addWidget(control_widget)

    def create_detection_section(self, layout):
        """Create results detection section."""
        detection_group = QGroupBox("🔍 Results Detection")
        detection_layout = QVBoxLayout(detection_group)

        # Auto-detect button
        self.auto_detect_btn = QPushButton("🔍 Auto-Detect Results")
        self.auto_detect_btn.setToolTip(
            "Automatically search for AreTomo3 results based on output directory"
        )
        self.auto_detect_btn.clicked.connect(self.auto_detect_results)
        detection_layout.addWidget(self.auto_detect_btn)

        # Manual browse button
        self.browse_results_btn = QPushButton("📂 Browse Results Directory")
        self.browse_results_btn.setToolTip("Manually select results directory")
        self.browse_results_btn.clicked.connect(self.browse_results_directory)
        detection_layout.addWidget(self.browse_results_btn)

        # Results directory display
        self.results_dir_label = QLabel("No results directory selected")
        self.results_dir_label.setStyleSheet(
            """
            QLabel {
                font-family: monospace;
                padding: 8px;
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                overflow-wrap: break-word;
            }
        """
        )
        self.results_dir_label.setWordWrap(True)
        detection_layout.addWidget(self.results_dir_label)

        layout.addWidget(detection_group)

    def create_options_section(self, layout):
        """Create analysis options section."""
        options_group = QGroupBox("⚙️ Analysis Options")
        options_layout = QVBoxLayout(options_group)

        # Auto-refresh checkbox
        self.auto_refresh_chk = QCheckBox("Auto-refresh analysis")
        self.auto_refresh_chk.setChecked(True)
        self.auto_refresh_chk.setToolTip(
            "Automatically refresh analysis when results change"
        )
        options_layout.addWidget(self.auto_refresh_chk)

        # Include subdirectories checkbox
        self.include_subdirs_chk = QCheckBox("Include subdirectories")
        self.include_subdirs_chk.setChecked(True)
        self.include_subdirs_chk.setToolTip("Search for results in subdirectories")
        options_layout.addWidget(self.include_subdirs_chk)

        # Analysis depth
        depth_layout = QHBoxLayout()
        depth_layout.addWidget(QLabel("Search Depth:"))
        self.depth_combo = QComboBox()
        self.depth_combo.addItems(["1 level", "2 levels", "3 levels", "All levels"])
        self.depth_combo.setCurrentText("2 levels")
        self.depth_combo.setToolTip("Maximum directory depth to search")
        depth_layout.addWidget(self.depth_combo)
        options_layout.addLayout(depth_layout)

        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh Analysis")
        self.refresh_btn.setToolTip("Manually refresh analysis")
        self.refresh_btn.clicked.connect(self.refresh_analysis)
        options_layout.addWidget(self.refresh_btn)

        layout.addWidget(options_group)

    def create_export_section(self, layout):
        """Create export section."""
        export_group = QGroupBox("💾 Export & Save")
        export_layout = QVBoxLayout(export_group)

        # Generate plots button
        self.generate_plots_btn = QPushButton("📊 Generate Plots")
        self.generate_plots_btn.setToolTip(
            "Generate motion, CTF, and alignment plots from AreTomo3 results"
        )
        self.generate_plots_btn.clicked.connect(self.generate_aretomo3_plots)
        export_layout.addWidget(self.generate_plots_btn)

        # Save plots button
        self.save_plots_btn = QPushButton("💾 Save Plots as PNG")
        self.save_plots_btn.setToolTip("Save all analysis plots as PNG files")
        self.save_plots_btn.clicked.connect(self.save_analysis_plots)
        export_layout.addWidget(self.save_plots_btn)

        # Export data button
        self.export_data_btn = QPushButton("📊 Export Analysis Data")
        self.export_data_btn.setToolTip("Export analysis data as CSV/JSON")
        self.export_data_btn.clicked.connect(self.export_analysis_data)
        export_layout.addWidget(self.export_data_btn)

        # Generate report button
        self.generate_report_btn = QPushButton("📄 Generate Report")
        self.generate_report_btn.setToolTip("Generate comprehensive analysis report")
        self.generate_report_btn.clicked.connect(self.generate_analysis_report)
        export_layout.addWidget(self.generate_report_btn)

        layout.addWidget(export_group)

    def create_status_section(self, layout):
        """Create status section."""
        status_group = QGroupBox("📊 Status")
        status_layout = QVBoxLayout(status_group)

        # Status label
        self.status_label = QLabel("📝 Ready for analysis")
        self.status_label.setStyleSheet(
            "font-weight: bold; color: #2c3e50; padding: 5px;"
        )
        status_layout.addWidget(self.status_label)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        # File count label
        self.file_count_label = QLabel("Files found: 0")
        status_layout.addWidget(self.file_count_label)

        # Last updated label
        self.last_updated_label = QLabel("Last updated: Never")
        self.last_updated_label.setStyleSheet("color: #666; font-size: 10pt;")
        status_layout.addWidget(self.last_updated_label)

        layout.addWidget(status_group)

    def create_analysis_panel(self, parent):
        """Create the analysis display panel."""
        # Create tab widget for different analysis views
        self.analysis_tabs = QTabWidget()

        # Overview tab
        self.create_overview_tab()

        # Plots tab
        self.create_plots_tab()

        # Files tab
        self.create_files_tab()

        # Statistics tab
        self.create_statistics_tab()

        parent.addWidget(self.analysis_tabs)

    def create_overview_tab(self):
        """Create overview tab."""
        overview_widget = QWidget()
        overview_layout = QVBoxLayout(overview_widget)

        # Summary section
        summary_group = QGroupBox("📋 Processing Summary")
        summary_layout = QGridLayout(summary_group)

        # Summary labels (will be populated dynamically)
        self.summary_labels = {}
        summary_items = [
            ("Total Files", "total_files"),
            ("MRC Files", "mrc_files"),
            ("Log Files", "log_files"),
            ("Transform Files", "xf_files"),
            ("Processing Time", "processing_time"),
            ("Success Rate", "success_rate"),
        ]

        for i, (label, key) in enumerate(summary_items):
            row, col = divmod(i, 2)
            summary_layout.addWidget(QLabel(f"{label}:"), row, col * 2)
            value_label = QLabel("--")
            value_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
            summary_layout.addWidget(value_label, row, col * 2 + 1)
            self.summary_labels[key] = value_label

        overview_layout.addWidget(summary_group)

        # Recent activity section
        activity_group = QGroupBox("🕒 Recent Activity")
        activity_layout = QVBoxLayout(activity_group)

        self.activity_list = QListWidget()
        self.activity_list.setMaximumHeight(200)
        activity_layout.addWidget(self.activity_list)

        overview_layout.addWidget(activity_group)
        overview_layout.addStretch()

        self.analysis_tabs.addTab(overview_widget, "📋 Overview")

    # TODO: Refactor function - Function 'create_plots_tab' too long (176
    # lines)
    def create_plots_tab(self):
        """Create plots tab with interactive and matplotlib integration."""
        plots_widget = QWidget()
        plots_layout = QVBoxLayout(plots_widget)

        # Plot controls
        controls_layout = QHBoxLayout()

        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems(
            [
                "Interactive CTF Resolution",
                "Interactive CTF Defocus",
                "Combined CTF Analysis",
                "Motion Plots",
                "Enhanced CTF Analysis",
                "Alignment Plots",
                "Summary Plots",
                "File Distribution",
                "Processing Timeline",
                "All Plots",
            ]
        )

        # Plot mode selection
        self.plot_mode_combo = QComboBox()
        self.plot_mode_combo.addItems(["Interactive (Plotly)", "Static (Matplotlib)"])
        if PLOTLY_AVAILABLE and WEBENGINE_AVAILABLE:
            self.plot_mode_combo.setCurrentText("Interactive (Plotly)")
        else:
            self.plot_mode_combo.setCurrentText("Static (Matplotlib)")
        self.plot_mode_combo.currentTextChanged.connect(self.on_plot_mode_changed)

        # Add CTF-specific controls
        ctf_controls = QHBoxLayout()

        self.ctf_log_scale = QCheckBox("Log Scale")
        self.ctf_log_scale.setChecked(True)
        self.ctf_log_scale.toggled.connect(self.toggle_ctf_log_scale)
        ctf_controls.addWidget(self.ctf_log_scale)

        self.ctf_broken_axis = QCheckBox("Broken Axis")
        self.ctf_broken_axis.setChecked(True)
        self.ctf_broken_axis.setToolTip("Use broken axis to handle outliers")
        self.ctf_broken_axis.toggled.connect(self.toggle_broken_axis)
        ctf_controls.addWidget(self.ctf_broken_axis)

        self.ctf_sync_nav = QCheckBox("Sync Navigation")
        self.ctf_sync_nav.setChecked(True)
        self.ctf_sync_nav.setToolTip("Synchronize CTF visualizer with main analysis")
        ctf_controls.addWidget(self.ctf_sync_nav)

        self.open_ctf_viewer_btn = QPushButton("🔍 Open CTF Viewer")
        self.open_ctf_viewer_btn.clicked.connect(self.open_ctf_viewer)
        ctf_controls.addWidget(self.open_ctf_viewer_btn)

        self.open_motion_viewer_btn = QPushButton("🎯 Open Motion Viewer")
        self.open_motion_viewer_btn.clicked.connect(self.open_motion_viewer)
        ctf_controls.addWidget(self.open_motion_viewer_btn)

        ctf_controls.addStretch()
        plots_layout.addLayout(ctf_controls)

        self.plot_type_combo.currentTextChanged.connect(self.on_plot_type_changed)
        controls_layout.addWidget(QLabel("Plot Type:"))
        controls_layout.addWidget(self.plot_type_combo)
        controls_layout.addWidget(QLabel("Mode:"))
        controls_layout.addWidget(self.plot_mode_combo)
        controls_layout.addStretch()

        plots_layout.addLayout(controls_layout)

        # Create container for both plot types
        self.plot_container = QWidget()
        plot_container_layout = QVBoxLayout(self.plot_container)

        # Interactive plot area (WebEngine)
        if WEBENGINE_AVAILABLE:
            self.web_view = QWebEngineView()
            self.web_view.setVisible(PLOTLY_AVAILABLE and WEBENGINE_AVAILABLE)
            plot_container_layout.addWidget(self.web_view)
        else:
            self.web_view = None

        # Matplotlib canvas area
        try:
            # Configure matplotlib backend before importing
            import matplotlib

            # Try available Qt backends in order of preference
            backend_set = False
            try:
                # Try qtagg first (modern Qt backend)
                matplotlib.use("qtagg")
                from matplotlib.backends.backend_qtagg import (
                    FigureCanvasQTAgg as FigureCanvas,
                )

                backend_set = True
                logger.info("Using qtagg backend for matplotlib")
            except (ImportError, ValueError):
                try:
                    # Try qt5agg
                    matplotlib.use("qt5agg")
                    from matplotlib.backends.backend_qt5agg import (
                        FigureCanvasQTAgg as FigureCanvas,
                    )

                    backend_set = True
                    logger.info("Using qt5agg backend for matplotlib")
                except (ImportError, ValueError):
                    try:
                        # Fallback to Agg (non-interactive)
                        matplotlib.use("Agg")
                        from matplotlib.backends.backend_agg import (
                            FigureCanvasAgg as FigureCanvas,
                        )

                        logger.warning(
                            "Using Agg backend for matplotlib (non-interactive)"
                        )
                    except Exception as e:
                        logger.error(f"Failed to set any matplotlib backend: {e}")
                        raise

            import matplotlib.pyplot as plt
            from matplotlib.figure import Figure

            self.analysis_figure = Figure(figsize=(12, 8))
            self.analysis_canvas = FigureCanvas(self.analysis_figure)
            self.analysis_canvas.setVisible(
                not (PLOTLY_AVAILABLE and WEBENGINE_AVAILABLE)
            )
            plot_container_layout.addWidget(self.analysis_canvas)

            logger.info("Matplotlib canvas initialized successfully")

        except ImportError as e:
            logger.error(f"Matplotlib import error: {e}")
            # Fallback if matplotlib is not available
            fallback_label = QLabel(
                "📊 Matplotlib not available for plotting\n\nPlease install matplotlib:\npip install matplotlib"
            )
            fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            fallback_label.setStyleSheet(
                """
                QLabel {
                    border: 2px dashed #ccc;
                    padding: 40px;
                    font-size: 14pt;
                    color: #666;
                }
            """
            )
            plot_container_layout.addWidget(fallback_label)
            self.analysis_canvas = None
        except Exception as e:
            logger.error(f"Error setting up matplotlib canvas: {e}")
            # Fallback for other errors
            fallback_label = QLabel(
                f"📊 Error setting up plotting\n\nError: {
                    str(e)}"
            )
            fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            fallback_label.setStyleSheet(
                """
                QLabel {
                    border: 2px dashed #ccc;
                    padding: 40px;
                    font-size: 14pt;
                    color: #666;
                }
            """
            )
            plot_container_layout.addWidget(fallback_label)
            self.analysis_canvas = None

        plots_layout.addWidget(self.plot_container)
        self.analysis_tabs.addTab(plots_widget, "📈 Plots")

    def create_files_tab(self):
        """Create files tab with detailed file listing."""
        files_widget = QWidget()
        files_layout = QVBoxLayout(files_widget)

        # File filters
        filter_layout = QHBoxLayout()

        self.file_filter_combo = QComboBox()
        self.file_filter_combo.addItems(
            ["All Files", "MRC Files", "Log Files", "Transform Files", "Other Files"]
        )
        self.file_filter_combo.currentTextChanged.connect(self.filter_files)
        filter_layout.addWidget(QLabel("Filter:"))
        filter_layout.addWidget(self.file_filter_combo)
        filter_layout.addStretch()

        files_layout.addLayout(filter_layout)

        # File table
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(5)
        self.files_table.setHorizontalHeaderLabels(
            ["Name", "Type", "Size", "Modified", "Path"]
        )
        self.files_table.setSortingEnabled(True)
        files_layout.addWidget(self.files_table)

        self.analysis_tabs.addTab(files_widget, "📁 Files")

    def create_statistics_tab(self):
        """Create statistics tab with detailed metrics."""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)

        # Statistics display
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setFont(QFont("Courier", 10))
        stats_layout.addWidget(self.stats_text)

        self.analysis_tabs.addTab(stats_widget, "📊 Statistics")

    def connect_signals(self):
        """Connect signals and slots."""
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_check)
        self.refresh_timer.start(5000)  # Check every 5 seconds

    def on_plot_mode_changed(self, mode: str):
        """Handle plot mode change between interactive and static."""
        if mode == "Interactive (Plotly)" and PLOTLY_AVAILABLE and WEBENGINE_AVAILABLE:
            if self.web_view:
                self.web_view.setVisible(True)
            if self.analysis_canvas:
                self.analysis_canvas.setVisible(False)
        else:
            if self.web_view:
                self.web_view.setVisible(False)
            if self.analysis_canvas:
                self.analysis_canvas.setVisible(True)

        # Refresh current plot
        self.update_plots()

    def toggle_broken_axis(self, enabled: bool):
        """Toggle broken axis for interactive plots."""
        if self.plot_type_combo.currentText().startswith("Interactive"):
            self.update_plots()

    def toggle_ctf_log_scale(self, enabled: bool):
        """Toggle CTF log scale."""
        # This will be handled by the specific plot update methods
        self.update_plots()

    def auto_detect_results(self):
        """Automatically detect results directory."""
        try:
            # Try to get output directory from main window
            if hasattr(self.main_window, "main_tab"):
                output_dir = getattr(self.main_window.main_tab, "output_dir", None)
                if output_dir and hasattr(output_dir, "text"):
                    output_path = output_dir.text().strip()
                    if output_path and os.path.exists(output_path):
                        self.set_results_directory(output_path)
                        return

            # Fallback: ask user to select directory
            QMessageBox.information(
                self,
                "Auto-Detection",
                "Could not auto-detect results directory.\nPlease use 'Browse Results Directory' to select manually.",
            )

        except Exception as e:
            logger.error(f"Error in auto-detect results: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to auto-detect results: {str(e)}"
            )

    def browse_results_directory(self):
        """Browse for results directory."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Results Directory",
            self.current_results_dir or str(Path.home()),
        )

        if directory:
            self.set_results_directory(directory)

    def set_results_directory(self, directory):
        """Set the results directory and analyze."""
        try:
            # Smart directory detection - look for AreTomo3 output
            results_dir = self._find_aretomo3_results_dir(directory)
            self.current_results_dir = results_dir
            self.results_dir_label.setText(results_dir)

            # Update status
            self.status_label.setText("📝 Analyzing results...")
            self.status_label.setStyleSheet(
                "font-weight: bold; color: #f39c12; padding: 5px;"
            )

            # Analyze the directory
            self.analyze_results_directory()

        except Exception as e:
            logger.error(f"Error setting results directory: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to set results directory: {str(e)}"
            )

    def _find_aretomo3_results_dir(self, base_dir):
        """
        Smart detection of AreTomo3 results directory.
        Looks for common AreTomo3 output patterns recursively.
        """
        from pathlib import Path

        base_path = Path(base_dir)

        # Look for common AreTomo3 output indicators
        aretomo3_indicators = [
            "*_CTF.txt",
            "*_CTF.mrc",
            "*_Vol.mrc",
            "*_EVN.mrc",
            "*_ODD.mrc",
            "*.aln",
            "*_TLT.txt",
        ]

        # First check if the base directory itself has AreTomo3 files
        for pattern in aretomo3_indicators:
            if list(base_path.rglob(pattern)):
                logger.info(f"Found AreTomo3 files in base directory: {base_dir}")
                return str(base_dir)

        # Look for subdirectories that might contain AreTomo3 output
        potential_dirs = ["aretomo_output", "output", "results", "AreTomo3_output"]

        for subdir_name in potential_dirs:
            for subdir in base_path.rglob(subdir_name):
                if subdir.is_dir():
                    # Check if this subdirectory has AreTomo3 files
                    for pattern in aretomo3_indicators:
                        if list(subdir.rglob(pattern)):
                            logger.info(f"Found AreTomo3 results directory: {subdir}")
                            return str(subdir)

        # If no specific AreTomo3 directory found, return the base directory
        logger.warning(
            f"No specific AreTomo3 results directory found, using base: {base_dir}"
        )
        return str(base_dir)

    # TODO: Refactor function - Function 'analyze_results_directory' too long
    # (91 lines)
    def analyze_results_directory(self):
        """Analyze the results directory."""
        if not self.current_results_dir or not os.path.exists(self.current_results_dir):
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Search for files
            result_patterns = {
                "mrc": "*.mrc",
                "rec": "*.rec",
                "xf": "*.xf",
                "tlt": "*.tlt",
                "log": "*.log",
                "ctf": "*_ctf.txt",
                "motion": "*_motion.txt",
            }

            results_path = Path(self.current_results_dir)
            found_files = {}
            total_files = 0

            for file_type, pattern in result_patterns.items():
                if self.include_subdirs_chk.isChecked():
                    files = list(results_path.rglob(pattern))
                else:
                    files = list(results_path.glob(pattern))
                found_files[file_type] = files
                total_files += len(files)

            self.progress_bar.setValue(50)

            # Initialize AreTomo3 parser and parse results
            self.aretomo3_parser = AreTomo3ResultsParser(self.current_results_dir)
            parsed_data = self.aretomo3_parser.parse_all_results()

            # Update analysis data
            self.analysis_data = {
                "directory": self.current_results_dir,
                "files": found_files,
                "total_files": total_files,
                "analysis_time": Path(self.current_results_dir).stat().st_mtime,
                "aretomo3_data": parsed_data,
            }

            self.progress_bar.setValue(70)

            # Automatically generate plots if results are found
            if parsed_data and any(parsed_data.values()):
                logger.info("Automatically generating AreTomo3 plots...")
                self.generate_aretomo3_plots(auto_mode=True)

            self.progress_bar.setValue(90)

            # Update UI
            self.update_overview()
            self.update_files_table()
            self.update_statistics()
            self.update_plots()

            self.progress_bar.setValue(100)

            # Update status
            self.status_label.setText(
                f"📝 Analysis complete - {total_files} files found"
            )
            self.status_label.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 5px;"
            )
            self.file_count_label.setText(f"Files found: {total_files}")

            from datetime import datetime

            self.last_updated_label.setText(
                f"Last updated: {datetime.now().strftime('%H:%M:%S')}"
            )

            # Emit signal
            self.results_detected.emit(self.current_results_dir, total_files)

            self.progress_bar.setVisible(False)

        except Exception as e:
            logger.error(f"Error analyzing results directory: {e}")
            self.status_label.setText(f"📝 Error: {str(e)}")
            self.status_label.setStyleSheet(
                "font-weight: bold; color: #e74c3c; padding: 5px;"
            )
            self.progress_bar.setVisible(False)

    def update_overview(self):
        """Update the overview tab."""
        if not self.analysis_data:
            return

        files = self.analysis_data.get("files", {})

        # Update summary labels
        self.summary_labels["total_files"].setText(
            str(self.analysis_data.get("total_files", 0))
        )
        self.summary_labels["mrc_files"].setText(str(len(files.get("mrc", []))))
        self.summary_labels["log_files"].setText(str(len(files.get("log", []))))
        self.summary_labels["xf_files"].setText(str(len(files.get("xf", []))))

        # Calculate processing time estimate
        total_mrc = len(files.get("mrc", []))
        if total_mrc > 0:
            # Rough estimate: 2 minutes per tomogram
            est_time = total_mrc * 2
            self.summary_labels["processing_time"].setText(f"~{est_time} min")
        else:
            self.summary_labels["processing_time"].setText("--")

        # Calculate success rate
        total_expected = len(files.get("tlt", []))  # Based on tilt series
        total_completed = len(files.get("mrc", []))
        if total_expected > 0:
            success_rate = (total_completed / total_expected) * 100
            self.summary_labels["success_rate"].setText(f"{success_rate:.1f}%")
        else:
            self.summary_labels["success_rate"].setText("--")

        # Update activity list
        self.activity_list.clear()
        recent_files = []
        for file_type, file_list in files.items():
            # Show only recent 5 files per type
            for file_path in file_list[:5]:
                recent_files.append((file_path.stat().st_mtime, file_path, file_type))

        # Sort by modification time (newest first)
        recent_files.sort(reverse=True)

        for mtime, file_path, file_type in recent_files[:10]:  # Show top 10
            from datetime import datetime

            time_str = datetime.fromtimestamp(mtime).strftime("%H:%M:%S")
            self.activity_list.addItem(
                f"[{time_str}] {file_type.upper()}: {file_path.name}"
            )

    def update_files_table(self):
        """Update the files table."""
        if not self.analysis_data:
            return

        files = self.analysis_data.get("files", {})
        all_files = []

        # Collect all files
        for file_type, file_list in files.items():
            for file_path in file_list:
                try:
                    stat = file_path.stat()
                    all_files.append(
                        {
                            "name": file_path.name,
                            "type": file_type.upper(),
                            "size": self.format_file_size(stat.st_size),
                            "modified": self.format_timestamp(stat.st_mtime),
                            "path": str(file_path),
                        }
                    )
                except Exception:
                    continue

        # Update table
        self.files_table.setRowCount(len(all_files))

        for row, file_info in enumerate(all_files):
            self.files_table.setItem(row, 0, QTableWidgetItem(file_info["name"]))
            self.files_table.setItem(row, 1, QTableWidgetItem(file_info["type"]))
            self.files_table.setItem(row, 2, QTableWidgetItem(file_info["size"]))
            self.files_table.setItem(row, 3, QTableWidgetItem(file_info["modified"]))
            self.files_table.setItem(row, 4, QTableWidgetItem(file_info["path"]))

        # Resize columns
        self.files_table.resizeColumnsToContents()

    # TODO: Refactor function - Function 'update_statistics' too long (69
    # lines)
    def update_statistics(self):
        """Update the statistics tab."""
        if not self.analysis_data:
            return

        files = self.analysis_data.get("files", {})
        stats_text = []

        stats_text.append("=" * 60)
        stats_text.append("ARETOMO3 ANALYSIS STATISTICS")
        stats_text.append("=" * 60)
        stats_text.append("")

        # Directory info
        stats_text.append(
            f"Results Directory: {
                self.analysis_data['directory']}"
        )
        stats_text.append(
            f"Analysis Time: {
                self.format_timestamp(
                    self.analysis_data['analysis_time'])}"
        )
        stats_text.append("")

        # File counts
        stats_text.append("FILE COUNTS:")
        stats_text.append("-" * 20)
        total_files = 0
        for file_type, file_list in files.items():
            count = len(file_list)
            total_files += count
            stats_text.append(f"{file_type.upper():>12}: {count:>6}")
        stats_text.append("-" * 20)
        stats_text.append(f"{'TOTAL':>12}: {total_files:>6}")
        stats_text.append("")

        # Size analysis
        stats_text.append("SIZE ANALYSIS:")
        stats_text.append("-" * 20)
        total_size = 0
        for file_type, file_list in files.items():
            type_size = 0
            for file_path in file_list:
                try:
                    type_size += file_path.stat().st_size
                except Exception:
                    continue
            total_size += type_size
            if type_size > 0:
                stats_text.append(
                    f"{file_type.upper():>12}: {self.format_file_size(type_size):>10}"
                )

        stats_text.append("-" * 20)
        stats_text.append(f"{'TOTAL':>12}: {self.format_file_size(total_size):>10}")
        stats_text.append("")

        # Processing analysis
        mrc_count = len(files.get("mrc", []))
        tlt_count = len(files.get("tlt", []))
        log_count = len(files.get("log", []))

        stats_text.append("PROCESSING ANALYSIS:")
        stats_text.append("-" * 25)
        stats_text.append(f"Tilt Series Found: {tlt_count}")
        stats_text.append(f"Tomograms Generated: {mrc_count}")
        stats_text.append(f"Log Files: {log_count}")

        if tlt_count > 0:
            success_rate = (mrc_count / tlt_count) * 100
            stats_text.append(f"Success Rate: {success_rate:.1f}%")

        self.stats_text.setPlainText("\n".join(stats_text))

    def on_plot_type_changed(self, plot_type: str):
        """Handle plot type change with auto-mode switching."""
        try:
            # Auto-switch to interactive mode for interactive plot types
            if "Interactive" in plot_type:
                current_mode = self.plot_mode_combo.currentText()
                if current_mode != "Interactive (Plotly)":
                    logger.info(f"Auto-switching to Interactive mode for {plot_type}")
                    self.plot_mode_combo.setCurrentText("Interactive (Plotly)")

            # Update plots
            self.update_plots()

        except Exception as e:
            logger.error(f"Error handling plot type change: {e}")

    def update_plots(self):
        """Update the plots based on current selection."""
        if not self.analysis_data:
            return

        try:
            plot_type = self.plot_type_combo.currentText()
            plot_mode = self.plot_mode_combo.currentText()

            # Handle interactive plots
            if (
                plot_mode == "Interactive (Plotly)"
                and PLOTLY_AVAILABLE
                and WEBENGINE_AVAILABLE
                and self.web_view
            ):
                self._update_interactive_plots(plot_type)
            else:
                self._update_static_plots(plot_type)

        except Exception as e:
            logger.error(f"Error updating plots: {e}")

    # TODO: Refactor _update_interactive_plots - complexity: 27 (target: <10)
    # TODO: Refactor function - Function '_update_interactive_plots' too long
    # (133 lines)
    def _update_interactive_plots(self, plot_type: str):
        """Update interactive Plotly plots."""
        try:
            if not PLOTLY_AVAILABLE:
                self._show_error_message(
                    "Plotly not available. Install with: pip install plotly"
                )
                return

            # Ensure we have a results directory
            if not hasattr(self, "current_results_dir") or not self.current_results_dir:
                if (
                    hasattr(self.main_window, "output_dir")
                    and self.main_window.output_dir
                ):
                    self.current_results_dir = Path(self.main_window.output_dir)
                    logger.info(
                        f"Using output directory from main window: {
                            self.current_results_dir}"
                    )
                else:
                    self._show_no_data_message(
                        "No results directory available. Please load data and run processing first."
                    )
                    return

            if not WEBENGINE_AVAILABLE and not self.web_view:
                # Fallback: Save HTML file and show message
                self._handle_interactive_plot_fallback(plot_type)
                return

            if plot_type == "Interactive CTF Resolution":
                # Parse CTF data with improved error handling
                from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data

                try:
                    ctf_data = parse_ctf_data(self.current_results_dir)
                except Exception as e:
                    logger.warning(f"CTF parsing failed: {e}")
                    ctf_data = None

                if ctf_data and ctf_data.get("ctf_parameters"):
                    try:
                        use_broken_axis = self.ctf_broken_axis.isChecked()
                        html_content = interactive_plotter.create_ctf_resolution_plot(
                            ctf_data, use_broken_axis=use_broken_axis
                        )
                        if self.web_view:
                            self.web_view.setHtml(html_content)
                            logger.info(
                                "Interactive CTF resolution plot loaded in web view"
                            )
                        else:
                            self._save_and_show_html(
                                html_content, "ctf_resolution_plot.html"
                            )
                    except Exception as e:
                        logger.error(f"Error creating CTF resolution plot: {e}")
                        self._show_error_message(f"Error creating plot: {str(e)}")
                else:
                    self._show_no_data_message(
                        "No CTF data available for interactive plotting. Check if CTF estimation was enabled."
                    )

            elif plot_type == "Interactive CTF Defocus":
                # Parse CTF data with improved error handling
                from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data

                try:
                    ctf_data = parse_ctf_data(self.current_results_dir)
                except Exception as e:
                    logger.warning(f"CTF parsing failed: {e}")
                    ctf_data = None

                if ctf_data and ctf_data.get("ctf_parameters"):
                    try:
                        html_content = interactive_plotter.create_ctf_defocus_plot(
                            ctf_data
                        )
                        if self.web_view:
                            self.web_view.setHtml(html_content)
                            logger.info(
                                "Interactive CTF defocus plot loaded in web view"
                            )
                        else:
                            self._save_and_show_html(
                                html_content, "ctf_defocus_plot.html"
                            )
                    except Exception as e:
                        logger.error(f"Error creating CTF defocus plot: {e}")
                        self._show_error_message(f"Error creating plot: {str(e)}")
                else:
                    self._show_no_data_message(
                        "No CTF data available for interactive plotting. Check if CTF estimation was enabled."
                    )

            elif plot_type == "Combined CTF Analysis":
                # Parse CTF data with improved error handling
                from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data

                try:
                    ctf_data = parse_ctf_data(self.current_results_dir)
                except Exception as e:
                    logger.warning(f"CTF parsing failed: {e}")
                    ctf_data = None

                if ctf_data and ctf_data.get("ctf_parameters"):
                    try:
                        html_content = interactive_plotter.create_combined_ctf_plot(
                            ctf_data
                        )
                        if self.web_view:
                            self.web_view.setHtml(html_content)
                            logger.info("Combined CTF plot loaded in web view")
                        else:
                            self._save_and_show_html(
                                html_content, "combined_ctf_plot.html"
                            )
                    except Exception as e:
                        logger.error(f"Error creating combined CTF plot: {e}")
                        self._show_error_message(f"Error creating plot: {str(e)}")
                else:
                    self._show_no_data_message(
                        "No CTF data available for interactive plotting. Check if CTF estimation was enabled."
                    )

            else:
                # Fallback to static plots for other types
                logger.info(f"Falling back to static plots for: {plot_type}")
                self._update_static_plots(plot_type)

        except Exception as e:
            logger.error(f"Error updating interactive plots: {e}")
            self._show_error_message(
                f"Error creating interactive plot: {
                    str(e)}"
            )

    def _handle_interactive_plot_fallback(self, plot_type: str):
        """Handle interactive plots when WebEngine is not available."""
        message = f"""
        <div style="text-align: center; padding: 40px; font-family: Arial, sans-serif;">
            <h3>📊 Interactive Plot: {plot_type}</h3>
            <p>QWebEngineView not available for interactive plots.</p>
            <p><strong>Options:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <li>Switch to "Static (Matplotlib)" mode for basic plots</li>
                <li>Install PyQt6-WebEngine: pip install PyQt6-WebEngine</li>
                <li>Use web dashboard at <a href="http://localhost:8080">http://localhost:8080</a></li>
            </ul>
        </div>
        """

        # Show message in a label
        if hasattr(self, "plot_container"):
            # Clear container and show message
            for i in reversed(range(self.plot_container.layout().count())):
                child = self.plot_container.layout().itemAt(i).widget()
                if child:
                    child.setParent(None)

            from PyQt6.QtCore import Qt
            from PyQt6.QtWidgets import QLabel

            message_label = QLabel(message)
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setStyleSheet(
                """
                QLabel {
                    border: 2px dashed #ccc;
                    padding: 20px;
                    font-size: 12pt;
                    color: #666;
                    background-color: #f9f9f9;
                }
            """
            )
            message_label.setWordWrap(True)
            self.plot_container.layout().addWidget(message_label)

    def _save_and_show_html(self, html_content: str, filename: str):
        """Save HTML content and show instructions."""
        try:
            output_dir = Path.cwd() / "interactive_plots"
            output_dir.mkdir(exist_ok=True)

            html_file = output_dir / filename
            html_file.write_text(html_content)

            message = f"""
            <div style="text-align: center; padding: 40px; font-family: Arial, sans-serif;">
                <h3>📊 Interactive Plot Generated</h3>
                <p>Plot saved to: <code>{html_file}</code></p>
                <p>Open this file in your web browser to view the interactive plot.</p>
            </div>
            """

            self._show_no_data_message(message)
            logger.info(f"Interactive plot saved to: {html_file}")

        except Exception as e:
            logger.error(f"Error saving HTML plot: {e}")
            self._show_error_message(f"Error saving plot: {str(e)}")

    # TODO: Refactor _update_static_plots - complexity: 11 (target: <10)

    # TODO: Refactor function - Function '_update_static_plots' too long (80
    # lines)
    def _update_static_plots(self, plot_type: str):
        """Update static matplotlib plots."""
        if not self.analysis_canvas:
            logger.warning("Analysis canvas not available for static plots")
            return

        try:
            # Ensure matplotlib is properly configured
            import matplotlib

            # Don't set backend again if already set
            import matplotlib.pyplot as plt
            import numpy as np

            # Clear previous plots
            self.analysis_figure.clear()
            files = self.analysis_data.get("files", {})

            # Check if we have generated AreTomo3 plots to display
            if self.generated_plots and plot_type in [
                "Motion Plots",
                "CTF Plots",
                "Alignment Plots",
                "Summary Plots",
            ]:
                self.display_aretomo3_plots(plot_type)
            elif plot_type == "All Plots":
                if self.generated_plots:
                    # Display all AreTomo3 plots
                    self.display_all_aretomo3_plots()
                else:
                    # Fallback to basic plots
                    axes = self.analysis_figure.subplots(2, 2)
                    self.analysis_figure.suptitle(
                        "AreTomo3 Analysis Overview", fontsize=14
                    )
                    self.plot_file_distribution(axes[0, 0], files)
                    self.plot_tilt_angles(axes[0, 1], files)
                    self.plot_directory_structure(axes[1, 0], files)
                    self.plot_processing_timeline(axes[1, 1], files)
            else:
                # Single plot
                ax = self.analysis_figure.add_subplot(111)

                if plot_type == "File Distribution":
                    self.plot_file_distribution(ax, files)
                elif plot_type == "Processing Timeline":
                    self.plot_processing_timeline(ax, files)
                elif plot_type == "Enhanced CTF Analysis":
                    self.plot_enhanced_ctf_analysis()
                elif plot_type.startswith("Interactive"):
                    # Show message for interactive plots in static mode
                    ax.text(
                        0.5,
                        0.5,
                        f"{plot_type}\nSwitch to Interactive mode for full functionality",
                        ha="center",
                        va="center",
                        transform=ax.transAxes,
                        fontsize=12,
                    )
                    ax.set_title(plot_type)
                else:
                    # Fallback for other plot types
                    ax.text(
                        0.5,
                        0.5,
                        f"{plot_type} not available\nGenerate AreTomo3 plots first",
                        ha="center",
                        va="center",
                        transform=ax.transAxes,
                        fontsize=12,
                    )
                    ax.set_title(plot_type)

            self.analysis_figure.tight_layout()
            self.analysis_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating static plots: {e}")

    def _show_no_data_message(self, message: str):
        """Show no data message in web view."""
        html_content = f"""
        <div style="text-align: center; padding: 40px; font-family: Arial, sans-serif;">
            <h3>📊 No Data Available</h3>
            <p>{message}</p>
            <p><small>Please ensure CTF analysis has been performed and results are available.</small></p>
        </div>
        """
        if self.web_view:
            self.web_view.setHtml(html_content)

    def _show_error_message(self, message: str):
        """Show error message in web view."""
        html_content = f"""
        <div style="text-align: center; padding: 40px; font-family: Arial, sans-serif; color: #d32f2f;">
            <h3>⚠️ Error</h3>
            <p>{message}</p>
        </div>
        """
        if self.web_view:
            self.web_view.setHtml(html_content)

    # TODO: Refactor function - Function 'display_aretomo3_plots' too long (60
    # lines)
    def display_aretomo3_plots(self, plot_type: str):
        """Display specific AreTomo3 plot."""
        try:
            plot_mapping = {
                "Motion Plots": "motion",
                "CTF Plots": "ctf",
                "Alignment Plots": "alignment",
                "Summary Plots": "summary",
            }

            plot_key = plot_mapping.get(plot_type)
            if plot_key and plot_key in self.generated_plots:
                plot_path = self.generated_plots[plot_key]
                if Path(plot_path).exists():
                    ax = self.analysis_figure.add_subplot(1, 1, 1)

                    import matplotlib.image as mpimg

                    img = mpimg.imread(plot_path)
                    ax.imshow(img)
                    ax.set_title(f"AreTomo3 {plot_type}")
                    ax.axis("off")
                else:
                    ax = self.analysis_figure.add_subplot(1, 1, 1)
                    ax.text(
                        0.5,
                        0.5,
                        f"{plot_type} file not found",
                        ha="center",
                        va="center",
                        transform=ax.transAxes,
                        fontsize=12,
                    )
                    ax.set_title(plot_type)
            else:
                ax = self.analysis_figure.add_subplot(1, 1, 1)
                ax.text(
                    0.5,
                    0.5,
                    f"{plot_type} not available\nGenerate plots first",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                    fontsize=12,
                )
                ax.set_title(plot_type)

        except Exception as e:
            logger.error(f"Error displaying AreTomo3 plot {plot_type}: {e}")
            ax = self.analysis_figure.add_subplot(1, 1, 1)
            ax.text(
                0.5,
                0.5,
                f"Error loading {plot_type}",
                ha="center",
                va="center",
                transform=ax.transAxes,
                fontsize=12,
            )
            ax.set_title(plot_type)

    # TODO: Refactor function - Function 'display_all_aretomo3_plots' too long
    # (62 lines)
    def display_all_aretomo3_plots(self):
        """Display all generated AreTomo3 plots in a grid."""
        try:
            plot_count = len(self.generated_plots)
            if plot_count == 0:
                ax = self.analysis_figure.add_subplot(1, 1, 1)
                ax.text(
                    0.5,
                    0.5,
                    "No AreTomo3 plots available\nGenerate plots first",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                    fontsize=12,
                )
                ax.set_title("AreTomo3 Analysis Plots")
                return

            # Calculate grid layout
            cols = min(2, plot_count)
            rows = (plot_count + cols - 1) // cols

            self.analysis_figure.suptitle("AreTomo3 Analysis Results", fontsize=14)

            for i, (plot_name, plot_path) in enumerate(self.generated_plots.items()):
                if Path(plot_path).exists():
                    ax = self.analysis_figure.add_subplot(rows, cols, i + 1)

                    # Load and display the plot image
                    import matplotlib.image as mpimg

                    img = mpimg.imread(plot_path)
                    ax.imshow(img)
                    ax.set_title(plot_name.replace("_", " ").title())
                    ax.axis("off")
                else:
                    ax = self.analysis_figure.add_subplot(rows, cols, i + 1)
                    ax.text(
                        0.5,
                        0.5,
                        "Plot not found",
                        ha="center",
                        va="center",
                        transform=ax.transAxes,
                        fontsize=10,
                    )
                    ax.set_title(plot_name.replace("_", " ").title())
                    ax.axis("off")

        except Exception as e:
            logger.error(f"Error displaying all AreTomo3 plots: {e}")
            ax = self.analysis_figure.add_subplot(1, 1, 1)
            ax.text(
                0.5,
                0.5,
                "Error loading plots",
                ha="center",
                va="center",
                transform=ax.transAxes,
                fontsize=12,
            )
            ax.set_title("AreTomo3 Analysis Plots")

    def plot_file_distribution(self, ax, files):
        """Plot file type distribution."""
        file_counts = {
            file_type: len(file_list)
            for file_type, file_list in files.items()
            if len(file_list) > 0
        }

        if file_counts:
            types = list(file_counts.keys())
            counts = list(file_counts.values())
            colors = [
                "#3498db",
                "#e74c3c",
                "#f39c12",
                "#27ae60",
                "#9b59b6",
                "#1abc9c",
                "#34495e",
            ]

            bars = ax.bar(types, counts, color=colors[: len(types)])
            ax.set_title("File Type Distribution")
            ax.set_ylabel("File Count")
            ax.tick_params(axis="x", rotation=45)

            # Add value labels
            for bar, count in zip(bars, counts):
                ax.text(
                    bar.get_x() + bar.get_width() / 2,
                    bar.get_height() + 0.1,
                    str(count),
                    ha="center",
                    va="bottom",
                )
        else:
            ax.text(
                0.5,
                0.5,
                "No files found",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("File Type Distribution")

    def plot_tilt_angles(self, ax, files):
        """Plot tilt angles if available."""
        tlt_files = files.get("tlt", [])

        if tlt_files:
            try:
                import numpy as np

                tlt_file = tlt_files[0]  # Use first tilt file
                tilt_angles = np.loadtxt(tlt_file)

                ax.plot(tilt_angles, "bo-", markersize=4, linewidth=1)
                ax.set_title("Tilt Angles")
                ax.set_xlabel("Image Index")
                ax.set_ylabel("Tilt Angle (degrees)")
                ax.grid(True, alpha=0.3)

                # Add statistics
                ax.text(
                    0.02,
                    0.98,
                    f"Range: {
                        np.min(tilt_angles):.1f}° to {
                        np.max(tilt_angles):.1f}°",
                    transform=ax.transAxes,
                    va="top",
                    bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
                )

            except Exception as e:
                ax.text(
                    0.5,
                    0.5,
                    f"Error loading tilt data:\n{str(e)}",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("Tilt Angles")
        else:
            ax.text(
                0.5,
                0.5,
                "No tilt files found",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Tilt Angles")

    def plot_directory_structure(self, ax, files):
        """Plot directory structure."""
        if not self.current_results_dir:
            ax.text(
                0.5,
                0.5,
                "No directory selected",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Directory Structure")
            return

        try:
            results_path = Path(self.current_results_dir)
            subdirs = [d for d in results_path.iterdir() if d.is_dir()]

            if subdirs:
                # Limit to 10 subdirs
                subdir_names = [d.name for d in subdirs[:10]]
                subdir_counts = [len(list(d.iterdir())) for d in subdirs[:10]]

                ax.barh(subdir_names, subdir_counts, color="#9b59b6")
                ax.set_title("Files per Subdirectory")
                ax.set_xlabel("File Count")
            else:
                ax.text(
                    0.5,
                    0.5,
                    "No subdirectories found",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("Directory Structure")
        except Exception as e:
            ax.text(
                0.5,
                0.5,
                f"Error: {str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Directory Structure")

    # TODO: Refactor function - Function 'plot_processing_timeline' too long
    # (64 lines)
    def plot_processing_timeline(self, ax, files):
        """Plot processing timeline based on file modification times."""
        try:
            all_files = []
            for file_type, file_list in files.items():
                for file_path in file_list:
                    try:
                        mtime = file_path.stat().st_mtime
                        all_files.append((mtime, file_type))
                    except Exception:
                        continue

            if all_files:
                # Sort by time
                all_files.sort()

                # Group by hour
                from datetime import datetime

                import numpy as np

                times = [datetime.fromtimestamp(mtime) for mtime, _ in all_files]

                if len(times) > 1:
                    # Create histogram of processing times
                    hours = [t.hour for t in times]
                    unique_hours, counts = np.unique(hours, return_counts=True)

                    ax.bar(unique_hours, counts, color="#e67e22", alpha=0.7)
                    ax.set_title("Processing Timeline (by Hour)")
                    ax.set_xlabel("Hour of Day")
                    ax.set_ylabel("Files Processed")
                    ax.set_xticks(range(24))
                else:
                    ax.text(
                        0.5,
                        0.5,
                        "Insufficient data for timeline",
                        ha="center",
                        va="center",
                        transform=ax.transAxes,
                    )
                    ax.set_title("Processing Timeline")
            else:
                ax.text(
                    0.5,
                    0.5,
                    "No files with timestamps",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("Processing Timeline")

        except Exception as e:
            ax.text(
                0.5,
                0.5,
                f"Error: {str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Processing Timeline")

    # TODO: Refactor function - Function 'plot_enhanced_ctf_analysis' too long
    # (71 lines)
    def plot_enhanced_ctf_analysis(self):
        """Plot enhanced CTF analysis with multiple subplots."""
        try:
            # Check if we have CTF data
            if not self.analysis_data or "aretomo3_data" not in self.analysis_data:
                ax = self.analysis_figure.add_subplot(111)
                ax.text(
                    0.5,
                    0.5,
                    "Enhanced CTF Analysis not available\nGenerate AreTomo3 plots first",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                    fontsize=12,
                )
                ax.set_title("Enhanced CTF Analysis")
                return

            # Parse CTF data from results
            from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data

            ctf_data = parse_ctf_data(self.current_results_dir)

            if not ctf_data or not any(ctf_data.values()):
                ax = self.analysis_figure.add_subplot(111)
                ax.text(
                    0.5,
                    0.5,
                    "No CTF data found in results\nCheck if CTF analysis was performed",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                    fontsize=12,
                )
                ax.set_title("Enhanced CTF Analysis")
                return

            # Create 2x2 subplot layout for CTF analysis
            axes = self.analysis_figure.subplots(2, 2)
            self.analysis_figure.suptitle(
                "Enhanced CTF Analysis", fontsize=14, fontweight="bold"
            )
            self.analysis_figure.subplots_adjust(
                left=0.08, right=0.95, top=0.92, bottom=0.12, hspace=0.4, wspace=0.3
            )

            # Plot 1: Defocus vs Tilt Angle
            self.plot_ctf_defocus(axes[0, 0], ctf_data)

            # Plot 2: Resolution vs Tilt Angle
            self.plot_ctf_resolution(axes[0, 1], ctf_data)

            # Plot 3: CTF Quality Distribution
            self.plot_ctf_quality_distribution(axes[1, 0], ctf_data)

            # Plot 4: Astigmatism Analysis
            self.plot_ctf_astigmatism(axes[1, 1], ctf_data)

        except Exception as e:
            logger.error(f"Error in enhanced CTF analysis: {e}")
            ax = self.analysis_figure.add_subplot(111)
            ax.text(
                0.5,
                0.5,
                f"Error loading CTF analysis:\n{str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
                fontsize=12,
            )
            ax.set_title("Enhanced CTF Analysis")

    def plot_ctf_defocus(self, ax, ctf_data):
        """Plot CTF defocus vs tilt angle."""
        try:
            if "ctf_parameters" in ctf_data:
                for series_name, data in ctf_data["ctf_parameters"].items():
                    if "parameters" in data and not data["parameters"].empty:
                        df = data["parameters"]
                        if "tilt_angle" in df.columns and "defocus1_A" in df.columns:
                            ax.scatter(
                                df["tilt_angle"],
                                df["defocus1_A"] / 10000,
                                alpha=0.7,
                                s=30,
                                label=f"{series_name} Defocus U",
                            )
                            if "defocus2_A" in df.columns:
                                ax.scatter(
                                    df["tilt_angle"],
                                    df["defocus2_A"] / 10000,
                                    alpha=0.7,
                                    s=30,
                                    label=f"{series_name} Defocus V",
                                )

                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Defocus (μm)")
                ax.set_title("CTF Defocus vs Tilt Angle")
                ax.grid(True, alpha=0.3)
                ax.legend()
            else:
                ax.text(
                    0.5,
                    0.5,
                    "No CTF defocus data available",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("CTF Defocus")
        except Exception as e:
            ax.text(
                0.5,
                0.5,
                f"Error: {str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("CTF Defocus")

    def plot_ctf_resolution(self, ax, ctf_data):
        """Plot CTF resolution vs tilt angle."""
        try:
            if "ctf_parameters" in ctf_data:
                for series_name, data in ctf_data["ctf_parameters"].items():
                    if "parameters" in data and not data["parameters"].empty:
                        df = data["parameters"]
                        if (
                            "tilt_angle" in df.columns
                            and "resolution_limit_A" in df.columns
                        ):
                            ax.scatter(
                                df["tilt_angle"],
                                df["resolution_limit_A"],
                                alpha=0.7,
                                s=30,
                                label=series_name,
                            )

                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Resolution Limit (Å)")
                ax.set_title("CTF Resolution vs Tilt Angle")
                ax.grid(True, alpha=0.3)
                ax.legend()
            else:
                ax.text(
                    0.5,
                    0.5,
                    "No CTF resolution data available",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("CTF Resolution")
        except Exception as e:
            ax.text(
                0.5,
                0.5,
                f"Error: {str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("CTF Resolution")

    def plot_ctf_quality_distribution(self, ax, ctf_data):
        """Plot CTF quality score distribution."""
        try:
            if "ctf_parameters" in ctf_data:
                all_cc = []
                for series_name, data in ctf_data["ctf_parameters"].items():
                    if "parameters" in data and not data["parameters"].empty:
                        df = data["parameters"]
                        if "cross_correlation" in df.columns:
                            all_cc.extend(df["cross_correlation"].tolist())

                if all_cc:
                    ax.hist(
                        all_cc, bins=20, alpha=0.7, color="skyblue", edgecolor="black"
                    )
                    ax.set_xlabel("Cross Correlation")
                    ax.set_ylabel("Frequency")
                    ax.set_title("CTF Quality Distribution")
                    ax.grid(True, alpha=0.3)
                else:
                    ax.text(
                        0.5,
                        0.5,
                        "No CTF quality data available",
                        ha="center",
                        va="center",
                        transform=ax.transAxes,
                    )
                    ax.set_title("CTF Quality Distribution")
            else:
                ax.text(
                    0.5,
                    0.5,
                    "No CTF quality data available",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("CTF Quality Distribution")
        except Exception as e:
            ax.text(
                0.5,
                0.5,
                f"Error: {str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("CTF Quality Distribution")

    def plot_ctf_astigmatism(self, ax, ctf_data):
        """Plot CTF astigmatism analysis."""
        try:
            if "ctf_parameters" in ctf_data:
                for series_name, data in ctf_data["ctf_parameters"].items():
                    if "parameters" in data and not data["parameters"].empty:
                        df = data["parameters"]
                        if all(
                            col in df.columns for col in ["defocus1_A", "defocus2_A"]
                        ):
                            astigmatism = abs(df["defocus1_A"] - df["defocus2_A"])
                            if "tilt_angle" in df.columns:
                                ax.scatter(
                                    df["tilt_angle"],
                                    astigmatism,
                                    alpha=0.7,
                                    s=30,
                                    label=series_name,
                                )

                ax.set_xlabel("Tilt Angle (°)")
                ax.set_ylabel("Astigmatism (Å)")
                ax.set_title("CTF Astigmatism vs Tilt Angle")
                ax.grid(True, alpha=0.3)
                ax.legend()
            else:
                ax.text(
                    0.5,
                    0.5,
                    "No CTF astigmatism data available",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title("CTF Astigmatism")
        except Exception as e:
            ax.text(
                0.5,
                0.5,
                f"Error: {str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("CTF Astigmatism")

    # TODO: Refactor function - Function 'plot_enhanced_ctf_analysis' too long
    # (71 lines)
    def plot_enhanced_ctf_analysis(self):
        """Plot enhanced CTF analysis with multiple subplots."""
        try:
            # Check if we have CTF data
            if not self.analysis_data or "aretomo3_data" not in self.analysis_data:
                ax = self.analysis_figure.add_subplot(111)
                ax.text(
                    0.5,
                    0.5,
                    "Enhanced CTF Analysis not available\nGenerate AreTomo3 plots first",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                    fontsize=12,
                )
                ax.set_title("Enhanced CTF Analysis")
                return

            # Parse CTF data from results
            from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data

            ctf_data = parse_ctf_data(self.current_results_dir)

            if not ctf_data or not any(ctf_data.values()):
                ax = self.analysis_figure.add_subplot(111)
                ax.text(
                    0.5,
                    0.5,
                    "No CTF data found in results\nCheck if CTF analysis was performed",
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                    fontsize=12,
                )
                ax.set_title("Enhanced CTF Analysis")
                return

            # Create 2x2 subplot layout for CTF analysis
            axes = self.analysis_figure.subplots(2, 2)
            self.analysis_figure.suptitle(
                "Enhanced CTF Analysis", fontsize=14, fontweight="bold"
            )
            self.analysis_figure.subplots_adjust(
                left=0.08, right=0.95, top=0.92, bottom=0.12, hspace=0.4, wspace=0.3
            )

            # Plot 1: Defocus vs Tilt Angle
            self.plot_ctf_defocus(axes[0, 0], ctf_data)

            # Plot 2: Resolution vs Tilt Angle
            self.plot_ctf_resolution(axes[0, 1], ctf_data)

            # Plot 3: CTF Quality Distribution
            self.plot_ctf_quality_distribution(axes[1, 0], ctf_data)

            # Plot 4: Astigmatism Analysis
            self.plot_ctf_astigmatism(axes[1, 1], ctf_data)

        except Exception as e:
            logger.error(f"Error in enhanced CTF analysis: {e}")
            ax = self.analysis_figure.add_subplot(111)
            ax.text(
                0.5,
                0.5,
                f"Error loading CTF analysis:\n{str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
                fontsize=12,
            )
            ax.set_title("Enhanced CTF Analysis")

    # Utility methods
    def format_file_size(self, size_bytes):
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math

        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def format_timestamp(self, timestamp):
        """Format timestamp in human readable format."""
        from datetime import datetime

        return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

    # Event handlers
    def refresh_analysis(self):
        """Manually refresh analysis."""
        if self.current_results_dir:
            self.analyze_results_directory()
        else:
            QMessageBox.warning(
                self, "No Directory", "Please select a results directory first."
            )

    def auto_refresh_check(self):
        """Check if auto-refresh is needed."""
        if not self.auto_refresh_chk.isChecked() or not self.current_results_dir:
            return

        try:
            # Check if directory has been modified
            current_mtime = Path(self.current_results_dir).stat().st_mtime
            if (
                hasattr(self, "last_check_time")
                and current_mtime > self.last_check_time
            ):
                self.analyze_results_directory()
            self.last_check_time = current_mtime
        except Exception:
            # TODO: Refactor filter_files - complexity: 12 (target: <10)
            pass

    def filter_files(self):
        """Filter files table based on selection."""
        filter_type = self.file_filter_combo.currentText()

        for row in range(self.files_table.rowCount()):
            item = self.files_table.item(row, 1)  # Type column
            if item:
                file_type = item.text()
                if filter_type == "All Files":
                    self.files_table.setRowHidden(row, False)
                elif filter_type == "MRC Files" and file_type != "MRC":
                    self.files_table.setRowHidden(row, True)
                elif filter_type == "Log Files" and file_type != "LOG":
                    self.files_table.setRowHidden(row, True)
                elif filter_type == "Transform Files" and file_type != "XF":
                    self.files_table.setRowHidden(row, True)
                elif filter_type == "Other Files" and file_type in ["MRC", "LOG", "XF"]:
                    self.files_table.setRowHidden(row, True)
                else:
                    self.files_table.setRowHidden(row, False)

    def save_analysis_plots(self):
        """Save analysis plots as PNG files."""
        if not self.analysis_canvas or not self.current_results_dir:
            QMessageBox.warning(self, "No Data", "No analysis data or plots to save.")
            return

        try:
            # Create plots subdirectory
            plots_dir = Path(self.current_results_dir) / "analysis_plots"
            plots_dir.mkdir(exist_ok=True)

            # Save the current figure
            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_filename = plots_dir / f"aretomo3_analysis_{timestamp}.png"

            self.analysis_figure.savefig(plot_filename, dpi=300, bbox_inches="tight")

            self.status_label.setText(f"📝 Plots saved to {plots_dir}")
            self.status_label.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 5px;"
            )

            QMessageBox.information(
                self, "Plots Saved", f"Analysis plots saved to:\n{plot_filename}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Save Error",
                f"Failed to save plots: {str(e)}",
            )

    # TODO: Refactor function - Function 'generate_aretomo3_plots' too long
    # (63 lines)
    def generate_aretomo3_plots(self, auto_mode: bool = False):
        """Generate AreTomo3 analysis plots."""
        try:
            if not self.aretomo3_parser:
                if not auto_mode:
                    QMessageBox.warning(
                        self,
                        "Warning",
                        "No AreTomo3 results parsed. Please analyze a directory first.",
                    )
                return

            if not auto_mode:
                self.status_label.setText("📊 Generating AreTomo3 plots...")
                self.status_label.setStyleSheet(
                    "font-weight: bold; color: #f39c12; padding: 5px;"
                )

            # Generate plots
            plots_dir = Path(self.current_results_dir) / "analysis_plots"
            self.generated_plots = self.aretomo3_parser.generate_plots(str(plots_dir))

            if self.generated_plots:
                plot_count = len(self.generated_plots)
                logger.info(f"Generated {plot_count} AreTomo3 analysis plots")

                if not auto_mode:
                    QMessageBox.information(
                        self,
                        "Success",
                        f"Generated {plot_count} analysis plots:\n"
                        + "\n".join(
                            [
                                f"• {plot_type.title()}: {Path(path).name}"
                                for plot_type, path in self.generated_plots.items()
                            ]
                        ),
                    )

                # Update plots display
                self.update_plots()

                # Update status
                self.status_label.setText(f"📊 Generated {plot_count} analysis plots")
                self.status_label.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 5px;"
                )

            else:
                if not auto_mode:
                    QMessageBox.warning(
                        self,
                        "Warning",
                        "No plots could be generated from the available data.",
                    )
                logger.warning("No AreTomo3 plots could be generated")

        except Exception as e:
            logger.error(f"Error generating AreTomo3 plots: {e}")
            if not auto_mode:
                QMessageBox.critical(
                    self, "Error", f"Failed to generate plots: {str(e)}"
                )

    def export_analysis_data(self):
        """Export analysis data as CSV/JSON."""
        if not self.analysis_data:
            QMessageBox.warning(self, "No Data", "No analysis data to export.")
            return

        try:
            # Ask user for format
            from PyQt6.QtWidgets import QInputDialog

            formats = ["CSV", "JSON", "Both"]
            format_choice, ok = QInputDialog.getItem(
                self, "Export Format", "Choose export format:", formats, 0, False
            )

            if not ok:
                return

            # Create export directory
            export_dir = Path(self.current_results_dir) / "analysis_export"
            export_dir.mkdir(exist_ok=True)

            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if format_choice in ["CSV", "Both"]:
                self.export_csv(export_dir, timestamp)

            if format_choice in ["JSON", "Both"]:
                self.export_json(export_dir, timestamp)

            QMessageBox.information(
                self, "Export Complete", f"Analysis data exported to:\n{export_dir}"
            )

        except Exception as e:
            QMessageBox.critical(
                self, "Export Error", f"Failed to export data: {str(e)}"
            )

    def export_csv(self, export_dir, timestamp):
        """Export data as CSV."""
        import csv

        csv_file = export_dir / f"aretomo3_analysis_{timestamp}.csv"

        with open(csv_file, "w", newline="") as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow(
                ["File_Type", "File_Name", "File_Path", "Size_Bytes", "Modified_Time"]
            )

            # Write data
            files = self.analysis_data.get("files", {})
            for file_type, file_list in files.items():
                for file_path in file_list:
                    try:
                        stat = file_path.stat()
                        writer.writerow(
                            [
                                file_type,
                                file_path.name,
                                str(file_path),
                                stat.st_size,
                                self.format_timestamp(stat.st_mtime),
                            ]
                        )
                    except Exception:
                        continue

    def export_json(self, export_dir, timestamp):
        """Export data as JSON."""
        import json

        json_file = export_dir / f"aretomo3_analysis_{timestamp}.json"

        # Prepare data for JSON serialization
        export_data = {
            "analysis_info": {
                "directory": self.analysis_data["directory"],
                "analysis_time": self.format_timestamp(
                    self.analysis_data["analysis_time"]
                ),
                "total_files": self.analysis_data["total_files"],
            },
            "files": {},
        }

        files = self.analysis_data.get("files", {})
        for file_type, file_list in files.items():
            export_data["files"][file_type] = []
            for file_path in file_list:
                try:
                    stat = file_path.stat()
                    export_data["files"][file_type].append(
                        {
                            "name": file_path.name,
                            "path": str(file_path),
                            "size": stat.st_size,
                            "modified": self.format_timestamp(stat.st_mtime),
                        }
                    )
                except Exception:
                    continue

        with open(json_file, "w") as f:
            json.dump(export_data, f, indent=2)

    def generate_analysis_report(self):
        """Generate comprehensive analysis report."""
        if not self.analysis_data:
            QMessageBox.warning(self, "No Data", "No analysis data to generate report.")
            return

        try:
            # Create report directory
            report_dir = Path(self.current_results_dir) / "analysis_reports"
            report_dir.mkdir(exist_ok=True)

            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"aretomo3_report_{timestamp}.html"

            # Generate HTML report
            html_content = self.generate_html_report()

            with open(report_file, "w") as f:
                f.write(html_content)

            QMessageBox.information(
                self, "Report Generated", f"Analysis report generated:\n{report_file}"
            )

        except Exception as e:
            QMessageBox.critical(
                self, "Report Error", f"Failed to generate report: {str(e)}"
            )

    # TODO: Refactor function - Function 'generate_html_report' too long (71
    # lines)
    def generate_html_report(self):
        """Generate HTML report content."""
        files = self.analysis_data.get("files", {})

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>AreTomo3 Analysis Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #3498db; color: white; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .stats {{ display: grid; grid-template-columns: repeat(
                    auto-fit,
                    minmax(200px,
                    1fr)
                ); gap: 10px; }}
                .stat-box {{ background-color: #f8f9fa; padding: 10px; border-radius: 3px; text-align: center; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>AreTomo3 Analysis Report</h1>
                <p>Generated: {self.format_timestamp(self.analysis_data['analysis_time'])}</p>
                <p>Directory: {self.analysis_data['directory']}</p>
            </div>

            <div class="section">
                <h2>Summary Statistics</h2>
                <div class="stats">
                    <div class="stat-box">
                        <h3>{self.analysis_data['total_files']}</h3>
                        <p>Total Files</p>
                    </div>
                    <div class="stat-box">
                        <h3>{len(files.get('mrc', []))}</h3>
                        <p>MRC Files</p>
                    </div>
                    <div class="stat-box">
                        <h3>{len(files.get('log', []))}</h3>
                        <p>Log Files</p>
                    </div>
                    <div class="stat-box">
                        <h3>{len(files.get('xf', []))}</h3>
                        <p>Transform Files</p>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>File Details</h2>
                <table>
                    <tr><th>Type</th><th>Count</th><th>Total Size</th></tr>
        """

        for file_type, file_list in files.items():
            total_size = sum(f.stat().st_size for f in file_list if f.exists())
            html += f"<tr><td>{file_type.upper()}</td><td>{len(file_list)}</td><td>{self.format_file_size(total_size)}</td></tr>"

        html += """
                </table>
            </div>
        </body>
        </html>
        """

        return html

    def toggle_ctf_log_scale(self, checked):
        """Toggle CTF log scale display."""
        logger.info(f"CTF log scale toggled: {checked}")
        # Update CTF display if currently showing CTF analysis
        # TODO: Refactor open_ctf_viewer - complexity: 24 (target: <10)
        if self.plot_type_combo.currentText() == "Enhanced CTF Analysis":
            self.update_plots()

    # TODO: Refactor function - Function 'open_ctf_viewer' too long (135 lines)
    def open_ctf_viewer(self):
        """Open the interactive CTF viewer."""
        try:
            # First check if we have any results directory
            if not hasattr(self, "current_results_dir") or not self.current_results_dir:
                # Try to get results directory from main window
                if (
                    hasattr(self.main_window, "output_dir")
                    and self.main_window.output_dir
                ):
                    self.current_results_dir = Path(self.main_window.output_dir)
                    logger.info(
                        f"Using output directory from main window: {self.current_results_dir}"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "No Data",
                        "No results directory available. Please:\n"
                        "1. Load data in the Control Center tab\n"
                        "2. Run processing\n"
                        "3. Try opening CTF viewer again",
                    )
                    return

            # Import CTF parser
            from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data

            # Parse CTF data from results directory
            logger.info(f"Parsing CTF data from: {self.current_results_dir}")

            # Try multiple approaches to find CTF data
            ctf_data = None

            # Approach 1: Direct parsing
            try:
                ctf_data = parse_ctf_data(self.current_results_dir)
            except Exception as e:
                logger.warning(f"Direct CTF parsing failed: {e}")

            # Approach 2: Look for CTF files recursively
            if not ctf_data:
                ctf_files = list(self.current_results_dir.rglob("*_CTF.txt"))
                if ctf_files:
                    logger.info(f"Found CTF files: {[f.name for f in ctf_files]}")
                    try:
                        from ...analysis.ctf_analysis.ctf_parser import CTFDataParser

                        parser = CTFDataParser(
                            ctf_files[0].parent, ctf_files[0].stem.replace("_CTF", "")
                        )
                        ctf_data = parser.parse_all()
                    except Exception as e:
                        logger.warning(f"CTF file parsing failed: {e}")

            if not ctf_data:
                # Show detailed error message
                error_msg = f"No CTF data found in {
                    self.current_results_dir}.\n\n"
                error_msg += "Searched for:\n"
                error_msg += "• *_CTF.txt files\n"
                error_msg += "• Power spectra files\n"
                error_msg += "• MDOC files\n\n"
                error_msg += (
                    "Make sure AreTomo3 has been run with CTF estimation enabled."
                )

                QMessageBox.warning(self, "No CTF Data", error_msg)
                return

            # Check if we have CTF parameters - handle both old and new format
            has_ctf_params = False
            series_data = None

            if "ctf_parameters" in ctf_data and ctf_data["ctf_parameters"]:
                # New format: nested by series
                first_series = list(ctf_data["ctf_parameters"].keys())[0]
                series_data = ctf_data["ctf_parameters"][first_series]
                if "parameters" in series_data and not series_data["parameters"].empty:
                    has_ctf_params = True
            elif "parameters" in ctf_data and not ctf_data["parameters"].empty:
                # Old format: direct parameters
                series_data = ctf_data
                has_ctf_params = True

            if not has_ctf_params:
                # Try to show what we do have
                available_data = []
                if (
                    "power_spectra" in ctf_data
                    and ctf_data["power_spectra"] is not None
                ):
                    available_data.append("Power spectra")
                if "tilt_angles" in ctf_data and ctf_data["tilt_angles"]:
                    available_data.append("Tilt angles")

                if available_data:
                    msg = f"CTF data found but no parameters available.\n\n"
                    msg += f"Available data: {', '.join(available_data)}\n\n"
                    msg += (
                        "This might indicate CTF estimation is still running or failed."
                    )
                else:
                    msg = "No CTF parameters or data available.\n\nCheck if CTF estimation completed successfully."

                QMessageBox.warning(self, "No CTF Parameters", msg)
                return

            # Try to import and create CTF visualizer
            try:
                from ...analysis.ctf_analysis.ctf_visualizer import CTF2DVisualizer

                # Create and show CTF visualizer with proper data format
                self.ctf_visualizer = CTF2DVisualizer(series_data)
                fig = self.ctf_visualizer.create_interactive_viewer()

                # Show the figure
                import matplotlib.pyplot as plt

                plt.show()

                logger.info("Advanced CTF visualizer opened successfully")

            except ImportError as ie:
                logger.warning(f"CTF visualizer not available: {ie}")
                # Fallback: Show CTF data in a simple dialog
                self._show_ctf_data_summary(ctf_data)
            except Exception as ve:
                logger.error(f"Error creating CTF visualizer: {ve}")
                # Fallback: Show CTF data in a simple dialog
                self._show_ctf_data_summary(ctf_data)

        # TODO: Refactor open_motion_viewer - complexity: 13 (target: <10)
        except Exception as e:
            logger.error(f"Error opening CTF viewer: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open CTF viewer: {
                    str(e)}",
            )

    # TODO: Refactor function - Function 'open_motion_viewer' too long (78
    # lines)
    def open_motion_viewer(self):
        """Open the advanced motion correction visualizer."""
        try:
            # First check if we have any results directory
            if not hasattr(self, "current_results_dir") or not self.current_results_dir:
                # Try to get results directory from main window
                if (
                    hasattr(self.main_window, "output_dir")
                    and self.main_window.output_dir
                ):
                    self.current_results_dir = Path(self.main_window.output_dir)
                    logger.info(
                        f"Using output directory from main window: {
                            self.current_results_dir}"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "No Data",
                        "No results directory available. Please:\n"
                        "1. Load data in the Control Center tab\n"
                        "2. Run processing\n"
                        "3. Try opening Motion viewer again",
                    )
                    return

            # Parse AreTomo3 results if not already done
            if not hasattr(self, "aretomo3_data") or not self.aretomo3_data:
                self.parse_aretomo3_results()

            # Check if we have motion data
            if not self.aretomo3_data or "motion_data" not in self.aretomo3_data:
                QMessageBox.warning(
                    self,
                    "No Motion Data",
                    "No motion correction data found. Please ensure motion correction was performed.",
                )
                return

            motion_data = self.aretomo3_data["motion_data"]

            # Check if we have motion vectors
            if "motion_vectors" not in motion_data or not motion_data["motion_vectors"]:
                QMessageBox.warning(
                    self, "No Motion Vectors", "No motion vectors found in the data."
                )
                return

            # Try to import and create Motion visualizer
            try:
                from ...analysis.motion_analysis.motion_visualizer import (
                    MotionCorrectionVisualizer,
                )

                # Create and show motion visualizer
                self.motion_visualizer = MotionCorrectionVisualizer(motion_data)
                fig = self.motion_visualizer.create_comprehensive_view()

                # Show the figure
                import matplotlib.pyplot as plt

                plt.show()

                logger.info("Advanced Motion visualizer opened successfully")

            except ImportError as ie:
                logger.warning(f"Motion visualizer not available: {ie}")
                # Fallback: Show motion data in a simple dialog
                self._show_motion_data_summary(motion_data)
            except Exception as ve:
                logger.error(f"Error creating Motion visualizer: {ve}")
                # Fallback: Show motion data in a simple dialog
                self._show_motion_data_summary(motion_data)

        except Exception as e:
            logger.error(f"Error opening Motion viewer: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to open Motion viewer: {str(e)}"
            )

    def _show_ctf_data_summary(self, ctf_data: Dict[str, Any]):
        """Show CTF data summary when full visualizer is not available."""
        try:
            from PyQt6.QtWidgets import QDialog, QPushButton, QTextEdit, QVBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("CTF Data Summary")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # Create text display
            text_edit = QTextEdit()
            text_edit.setReadOnly(True)

            # Format CTF data summary
            summary_text = "CTF Analysis Summary\n" + "=" * 50 + "\n\n"

            if "ctf_parameters" in ctf_data:
                for series_name, data in ctf_data["ctf_parameters"].items():
                    summary_text += f"Series: {series_name}\n"
                    if "parameters" in data and not data["parameters"].empty:
                        df = data["parameters"]
                        summary_text += f"  - Number of micrographs: {
                            len(df)}\n"
                        if "resolution_limit_A" in df.columns:
                            summary_text += f"  - Resolution range: {
                                df['resolution_limit_A'].min():.1f} - {
                                df['resolution_limit_A'].max():.1f} Å\n"
                        if "defocus1_A" in df.columns:
                            summary_text += f"  - Defocus range: {
                                df['defocus1_A'].min() / 10000:.2f} - {
                                df['defocus1_A'].max() / 10000:.2f} μm\n"
                    summary_text += "\n"

            text_edit.setPlainText(summary_text)
            layout.addWidget(text_edit)

            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            logger.error(f"Error showing CTF summary: {e}")
            QMessageBox.information(
                self, "CTF Data", "CTF data is available but cannot display summary."
            )

    # TODO: Refactor function - Function '_show_motion_data_summary' too long
    # (57 lines)
    def _show_motion_data_summary(self, motion_data: Dict[str, Any]):
        """Show motion data summary when full visualizer is not available."""
        try:
            from PyQt6.QtWidgets import QDialog, QPushButton, QTextEdit, QVBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("Motion Correction Summary")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # Create text display
            text_edit = QTextEdit()
            text_edit.setReadOnly(True)

            # Format motion data summary
            summary_text = "Motion Correction Summary\n" + "=" * 50 + "\n\n"

            if "motion_vectors" in motion_data:
                motion_vectors = motion_data["motion_vectors"]
                summary_text += f"Number of frames: {len(motion_vectors)}\n"

                if motion_vectors:
                    import numpy as np

                    magnitudes = [
                        np.sqrt(mv["x"] ** 2 + mv["y"] ** 2) for mv in motion_vectors
                    ]
                    summary_text += (
                        f"Average motion: {np.mean(magnitudes):.2f} pixels\n"
                    )
                    summary_text += f"Maximum motion: {
                        np.max(magnitudes):.2f} pixels\n"
                    summary_text += f"Total drift: {
                        np.sum(magnitudes):.2f} pixels\n"
                    summary_text += f"Motion std: {
                        np.std(magnitudes):.2f} pixels\n"

            if "frame_data" in motion_data:
                frame_data = motion_data["frame_data"]
                summary_text += f"\nFrame data entries: {len(frame_data)}\n"

            text_edit.setPlainText(summary_text)
            layout.addWidget(text_edit)

            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            logger.error(f"Error showing motion summary: {e}")
            QMessageBox.information(
                self,
                "Motion Data",
                "Motion data is available but cannot display summary.",
            )
