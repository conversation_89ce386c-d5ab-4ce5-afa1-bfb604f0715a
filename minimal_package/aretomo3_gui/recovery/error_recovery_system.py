#!/usr/bin/env python3
"""
AreTomo3 GUI Intelligent Error Recovery System
Advanced error detection, diagnosis, and automatic recovery mechanisms.
"""

import hashlib
import json
import logging
import subprocess
import sys
import threading
import time
import traceback
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple

import psutil

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryAction(Enum):
    """Recovery action types."""

    RETRY = "retry"
    RESTART_COMPONENT = "restart_component"
    RESET_STATE = "reset_state"
    FALLBACK_MODE = "fallback_mode"
    USER_INTERVENTION = "user_intervention"
    SYSTEM_RESTART = "system_restart"


@dataclass
class ErrorRecord:
    """Error record with context and recovery information."""

    error_id: str
    timestamp: datetime
    error_type: str
    error_message: str
    stack_trace: str
    component: str
    severity: ErrorSeverity
    context: Dict[str, Any]
    recovery_attempts: int
    recovery_actions: List[str]
    resolved: bool
    resolution_time: Optional[datetime]


@dataclass
class RecoveryStrategy:
    """Recovery strategy definition."""

    strategy_id: str
    error_pattern: str
    component: str
    actions: List[RecoveryAction]
    max_attempts: int
    timeout_seconds: int
    prerequisites: List[str]
    success_criteria: Callable[[Dict[str, Any]], bool]


class IntelligentErrorRecoverySystem:
    """
    Intelligent error recovery system for AreTomo3 GUI.
    Provides automatic error detection, diagnosis, and recovery.
    """

    def __init__(self, recovery_config: Dict[str, Any] = None):
        """Initialize the error recovery system."""
        self.recovery_config = recovery_config or {
            "max_recovery_attempts": 3,
            "recovery_timeout": 300,
            "auto_recovery_enabled": True,
            "log_all_errors": True,
            "notify_user": True,
        }

        # Error tracking
        self.error_records: List[ErrorRecord] = []
        self.error_patterns: Dict[str, int] = {}
        self.component_health: Dict[str, Dict[str, Any]] = {}

        # Recovery strategies
        self.recovery_strategies: Dict[str, RecoveryStrategy] = {}
        self.active_recoveries: Dict[str, threading.Thread] = {}

        # System monitoring
        self.monitoring_active = False
        self.monitoring_thread = None

        # Event callbacks
        self.error_callbacks: List[Callable] = []
        self.recovery_callbacks: List[Callable] = []

        # Initialize built-in strategies
        self._initialize_recovery_strategies()

        # Setup exception handler
        self._setup_exception_handler()

        logger.info("Intelligent Error Recovery System initialized")

    def _initialize_recovery_strategies(self):
        """Initialize built-in recovery strategies."""
        # GUI Component Recovery
        self.recovery_strategies["gui_freeze"] = RecoveryStrategy(
            strategy_id="gui_freeze",
            error_pattern=".*GUI.*freeze.*|.*not responding.*",
            component="gui",
            actions=[RecoveryAction.RESET_STATE, RecoveryAction.RESTART_COMPONENT],
            max_attempts=2,
            timeout_seconds=30,
            prerequisites=[],
            success_criteria=lambda ctx: ctx.get("gui_responsive", False),
        )

        # Processing Error Recovery
        self.recovery_strategies["processing_failure"] = RecoveryStrategy(
            strategy_id="processing_failure",
            error_pattern=".*AreTomo3.*failed.*|.*processing.*error.*",
            component="processing",
            actions=[RecoveryAction.RETRY, RecoveryAction.FALLBACK_MODE],
            max_attempts=3,
            timeout_seconds=60,
            prerequisites=["check_input_files", "check_disk_space"],
            success_criteria=lambda ctx: ctx.get("processing_completed", False),
        )

        # Memory Error Recovery
        self.recovery_strategies["memory_error"] = RecoveryStrategy(
            strategy_id="memory_error",
            error_pattern=".*MemoryError.*|.*out of memory.*",
            component="system",
            actions=[RecoveryAction.RESET_STATE, RecoveryAction.FALLBACK_MODE],
            max_attempts=2,
            timeout_seconds=45,
            prerequisites=["check_memory_usage"],
            success_criteria=lambda ctx: ctx.get("memory_available", 0) > 1000,  # MB
        )

        # File I/O Error Recovery
        self.recovery_strategies["file_io_error"] = RecoveryStrategy(
            strategy_id="file_io_error",
            error_pattern=".*FileNotFoundError.*|.*PermissionError.*|.*IOError.*",
            component="filesystem",
            actions=[RecoveryAction.RETRY, RecoveryAction.RESET_STATE],
            max_attempts=3,
            timeout_seconds=30,
            prerequisites=["check_file_permissions", "check_disk_space"],
            success_criteria=lambda ctx: ctx.get("file_accessible", False),
        )

        # Network Error Recovery
        self.recovery_strategies["network_error"] = RecoveryStrategy(
            strategy_id="network_error",
            error_pattern=".*ConnectionError.*|.*TimeoutError.*|.*NetworkError.*",
            component="network",
            actions=[RecoveryAction.RETRY, RecoveryAction.FALLBACK_MODE],
            max_attempts=5,
            timeout_seconds=60,
            prerequisites=["check_network_connectivity"],
            success_criteria=lambda ctx: ctx.get("network_available", False),
        )

    def _setup_exception_handler(self):
        """Setup global exception handler."""

        def exception_handler(exc_type, exc_value, exc_traceback):
            """Execute exception_handler operation."""
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            # Log the error
            error_msg = "".join(
                traceback.format_exception(exc_type, exc_value, exc_traceback)
            )
            logger.error(f"Uncaught exception: {error_msg}")

            # Record and attempt recovery
            self.record_error(
                error_type=exc_type.__name__,
                error_message=str(exc_value),
                stack_trace=error_msg,
                component="system",
                context={"uncaught_exception": True},
            )

        sys.excepthook = exception_handler

    def record_error(
        self,
        error_type: str,
        error_message: str,
        stack_trace: str,
        component: str,
        context: Dict[str, Any] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    ) -> str:
        """Record an error and initiate recovery if appropriate."""
        error_id = self._generate_error_id(error_type, error_message)

        error_record = ErrorRecord(
            error_id=error_id,
            timestamp=datetime.now(),
            error_type=error_type,
            error_message=error_message,
            stack_trace=stack_trace,
            component=component,
            severity=severity,
            context=context or {},
            recovery_attempts=0,
            recovery_actions=[],
            resolved=False,
            resolution_time=None,
        )

        self.error_records.append(error_record)

        # Update error patterns
        pattern_key = f"{error_type}:{component}"
        self.error_patterns[pattern_key] = self.error_patterns.get(pattern_key, 0) + 1

        # Notify callbacks
        for callback in self.error_callbacks:
            try:
                callback(error_record)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")

        # Attempt automatic recovery if enabled
        if self.recovery_config.get("auto_recovery_enabled", True):
            self._initiate_recovery(error_record)

        logger.info(f"Error recorded: {error_id} - {error_type}: {error_message}")
        return error_id

    def _initiate_recovery(self, error_record: ErrorRecord):
        """Initiate recovery process for an error."""
        # Find matching recovery strategy
        strategy = self._find_recovery_strategy(error_record)

        if not strategy:
            logger.warning(
                f"No recovery strategy found for error: {
                    error_record.error_id}"
            )
            return

        # Check if already attempting recovery for this error
        if error_record.error_id in self.active_recoveries:
            logger.info(
                f"Recovery already in progress for: {
                    error_record.error_id}"
            )
            return

        # Start recovery in separate thread
        recovery_thread = threading.Thread(
            target=self._execute_recovery, args=(error_record, strategy), daemon=True
        )

        self.active_recoveries[error_record.error_id] = recovery_thread
        recovery_thread.start()

        logger.info(
            f"Recovery initiated for: {
                error_record.error_id} using strategy: {
                strategy.strategy_id}"
        )

    def _find_recovery_strategy(
        self, error_record: ErrorRecord
    ) -> Optional[RecoveryStrategy]:
        """Find appropriate recovery strategy for an error."""
        import re

        for strategy in self.recovery_strategies.values():
            # Check component match
            if (
                strategy.component != error_record.component
                and strategy.component != "system"
            ):
                continue

            # Check error pattern match
            pattern_match = re.search(
                strategy.error_pattern, error_record.error_message, re.IGNORECASE
            ) or re.search(
                strategy.error_pattern, error_record.error_type, re.IGNORECASE
            )

            if pattern_match:
                return strategy

        return None

    def _execute_recovery(self, error_record: ErrorRecord, strategy: RecoveryStrategy):
        """Execute recovery strategy."""
        try:
            logger.info(f"Executing recovery strategy: {strategy.strategy_id}")

            # Check prerequisites
            if not self._check_prerequisites(strategy.prerequisites):
                logger.warning(
                    f"Prerequisites not met for strategy: {
                        strategy.strategy_id}"
                )
                return

            # Execute recovery actions
            for attempt in range(strategy.max_attempts):
                error_record.recovery_attempts += 1

                success = False
                for action in strategy.actions:
                    action_result = self._execute_recovery_action(
                        action, error_record, strategy
                    )
                    error_record.recovery_actions.append(
                        f"{action.value}:{action_result}"
                    )

                    if action_result:
                        # Check success criteria
                        context = self._gather_recovery_context(error_record.component)
                        if strategy.success_criteria(context):
                            success = True
                            break

                if success:
                    error_record.resolved = True
                    error_record.resolution_time = datetime.now()
                    logger.info(
                        f"Recovery successful for: {
                            error_record.error_id}"
                    )
                    break

                # Wait before retry
                if attempt < strategy.max_attempts - 1:
                    # Exponential backoff
                    time.sleep(min(5 * (attempt + 1), 30))

            # Notify callbacks
            for callback in self.recovery_callbacks:
                try:
                    callback(error_record, success)
                except Exception as e:
                    logger.error(f"Error in recovery callback: {e}")

        except Exception as e:
            logger.error(f"Error during recovery execution: {e}")

        finally:
            # Clean up
            if error_record.error_id in self.active_recoveries:
                del self.active_recoveries[error_record.error_id]

    def _execute_recovery_action(
        self,
        action: RecoveryAction,
        error_record: ErrorRecord,
        strategy: RecoveryStrategy,
    ) -> bool:
        """Execute a specific recovery action."""
        try:
            if action == RecoveryAction.RETRY:
                return self._retry_operation(error_record)

            elif action == RecoveryAction.RESTART_COMPONENT:
                return self._restart_component(error_record.component)

            elif action == RecoveryAction.RESET_STATE:
                return self._reset_component_state(error_record.component)

            elif action == RecoveryAction.FALLBACK_MODE:
                return self._enable_fallback_mode(error_record.component)

            elif action == RecoveryAction.USER_INTERVENTION:
                return self._request_user_intervention(error_record)

            elif action == RecoveryAction.SYSTEM_RESTART:
                return self._restart_system()

            else:
                logger.warning(f"Unknown recovery action: {action}")
                return False

        except Exception as e:
            logger.error(f"Error executing recovery action {action}: {e}")
            return False

    def _retry_operation(self, error_record: ErrorRecord) -> bool:
        """Retry the failed operation."""
        # This would be implemented based on the specific operation
        logger.info(f"Retrying operation for: {error_record.error_id}")
        time.sleep(2)  # Simulate retry delay
        return True  # Simplified - would check actual operation result

    def _restart_component(self, component: str) -> bool:
        """Restart a specific component."""
        logger.info(f"Restarting component: {component}")

        if component == "gui":
            # Restart GUI components
            return self._restart_gui_components()
        elif component == "processing":
            # Restart processing engine
            return self._restart_processing_engine()
        elif component == "network":
            # Restart network components
            return self._restart_network_components()

        return False

    def _reset_component_state(self, component: str) -> bool:
        """Reset component state."""
        logger.info(f"Resetting state for component: {component}")

        # Clear component-specific state
        if component in self.component_health:
            self.component_health[component] = {
                "status": "reset",
                "last_reset": datetime.now().isoformat(),
                "error_count": 0,
            }

        return True

    def _enable_fallback_mode(self, component: str) -> bool:
        """Enable fallback mode for component."""
        logger.info(f"Enabling fallback mode for: {component}")

        # Set fallback mode flags
        self.component_health[component] = self.component_health.get(component, {})
        self.component_health[component]["fallback_mode"] = True
        self.component_health[component][
            "fallback_enabled_at"
        ] = datetime.now().isoformat()

        return True

    def _request_user_intervention(self, error_record: ErrorRecord) -> bool:
        """Request user intervention."""
        logger.info(
            f"Requesting user intervention for: {
                error_record.error_id}"
        )

        # This would trigger a user notification/dialog
        # For now, just log the request
        return False  # User intervention required

    def _restart_system(self) -> bool:
        """Restart the entire system."""
        logger.critical("System restart requested")
        # This would be a last resort action
        return False  # Don't actually restart in this implementation

    def _check_prerequisites(self, prerequisites: List[str]) -> bool:
        """Check if prerequisites are met."""
        for prereq in prerequisites:
            if not self._check_prerequisite(prereq):
                logger.warning(f"Prerequisite not met: {prereq}")
                return False
        return True

    def _check_prerequisite(self, prerequisite: str) -> bool:
        """Check a specific prerequisite."""
        if prerequisite == "check_memory_usage":
            return self._check_memory_usage()
        elif prerequisite == "check_disk_space":
            return self._check_disk_space()
        elif prerequisite == "check_file_permissions":
            return self._check_file_permissions()
        elif prerequisite == "check_network_connectivity":
            return self._check_network_connectivity()
        elif prerequisite == "check_input_files":
            return self._check_input_files()

        return True  # Unknown prerequisite - assume OK

    def _check_memory_usage(self) -> bool:
        """Check system memory usage."""
        try:
            memory = psutil.virtual_memory()
            available_mb = memory.available / (1024 * 1024)
            return available_mb > 500  # At least 500MB available
        except Exception:
            return True  # Assume OK if can't check

    def _check_disk_space(self) -> bool:
        """Check available disk space."""
        try:
            disk = psutil.disk_usage("/")
            available_gb = disk.free / (1024 * 1024 * 1024)
            return available_gb > 1.0  # At least 1GB available
        except Exception:
            return True

    def _check_file_permissions(self) -> bool:
        """Check file permissions."""
        # Simplified check
        return True

    def _check_network_connectivity(self) -> bool:
        """Check network connectivity."""
        try:
            import socket

            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return True
        except Exception:
            return False

    def _check_input_files(self) -> bool:
        """Check input files availability."""
        # Simplified check
        return True

    def _gather_recovery_context(self, component: str) -> Dict[str, Any]:
        """Gather context for recovery success evaluation."""
        context = {"timestamp": datetime.now().isoformat(), "component": component}

        # Add component-specific context
        if component == "gui":
            context["gui_responsive"] = True  # Simplified
        elif component == "processing":
            # Would check actual status
            context["processing_completed"] = False
        elif component == "system":
            context["memory_available"] = self._get_available_memory()
        elif component == "filesystem":
            context["file_accessible"] = True  # Would check actual file
        elif component == "network":
            context["network_available"] = self._check_network_connectivity()

        return context

    def _get_available_memory(self) -> float:
        """Get available memory in MB."""
        try:
            memory = psutil.virtual_memory()
            return memory.available / (1024 * 1024)
        except Exception:
            return 0.0

    def _restart_gui_components(self) -> bool:
        """Restart GUI components."""
        # This would restart specific GUI components
        logger.info("Restarting GUI components")
        return True

    def _restart_processing_engine(self) -> bool:
        """Restart processing engine."""
        # This would restart the processing engine
        logger.info("Restarting processing engine")
        return True

    def _restart_network_components(self) -> bool:
        """Restart network components."""
        # This would restart network components
        logger.info("Restarting network components")
        return True

    def _generate_error_id(self, error_type: str, error_message: str) -> str:
        """Generate unique error ID."""
        content = f"{error_type}:{error_message}:{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics."""
        total_errors = len(self.error_records)
        resolved_errors = sum(1 for e in self.error_records if e.resolved)

        return {
            "total_errors": total_errors,
            "resolved_errors": resolved_errors,
            "resolution_rate": (
                (resolved_errors / total_errors * 100) if total_errors > 0 else 0
            ),
            "error_patterns": dict(self.error_patterns),
            "component_health": dict(self.component_health),
            "active_recoveries": len(self.active_recoveries),
        }

    def add_error_callback(self, callback: Callable):
        """Add error event callback."""
        self.error_callbacks.append(callback)

    def add_recovery_callback(self, callback: Callable):
        """Add recovery event callback."""
        self.recovery_callbacks.append(callback)


# Global error recovery system instance
error_recovery_system = IntelligentErrorRecoverySystem()


def record_error(
    error_type: str,
    error_message: str,
    component: str = "unknown",
    context: Dict[str, Any] = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
) -> str:
    """Convenience function to record an error."""
    stack_trace = traceback.format_exc()
    return error_recovery_system.record_error(
        error_type=error_type,
        error_message=error_message,
        stack_trace=stack_trace,
        component=component,
        context=context,
        severity=severity,
    )
