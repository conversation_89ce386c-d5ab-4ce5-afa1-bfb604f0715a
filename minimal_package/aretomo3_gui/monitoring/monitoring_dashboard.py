#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Monitoring Dashboard
Real-time system monitoring, performance tracking, and health assessment.
"""

import json
import logging
import queue
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import numpy as np
import psutil

# GPU monitoring
try:
    import GPUtil

    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

# Web dashboard
try:
    import plotly.graph_objects as go
    import plotly.offline as pyo
    from plotly.subplots import make_subplots

    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """System performance metrics."""

    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_available_gb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    gpu_metrics: List[Dict[str, Any]]
    process_count: int
    load_average: List[float]


@dataclass
class ProcessMetrics:
    """Process-specific metrics."""

    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    create_time: datetime
    num_threads: int


@dataclass
class AlertRule:
    """Monitoring alert rule."""

    rule_id: str
    name: str
    metric_path: str
    operator: str  # >, <, >=, <=, ==, !=
    threshold: float
    duration_seconds: int
    severity: str  # info, warning, critical
    enabled: bool
    callback: Optional[Callable]

    # TODO: Refactor class - Class 'MonitoringDashboard' too long (734 lines)


class MonitoringDashboard:
    """
    Advanced monitoring dashboard for AreTomo3 GUI.
    Provides real-time system monitoring and performance tracking.
    """

    def __init__(self, update_interval: float = 1.0):
        """Initialize the monitoring dashboard."""
        self.update_interval = update_interval
        self.monitoring_active = False
        self.monitoring_thread = None

        # Data storage
        self.metrics_history: List[SystemMetrics] = []
        self.process_metrics: Dict[int, ProcessMetrics] = {}
        self.max_history_size = 3600  # 1 hour at 1-second intervals

        # Alert system
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        self.alert_callbacks: List[Callable] = []

        # Performance baselines
        self.baselines = {
            "cpu_normal": 20.0,
            "memory_normal": 50.0,
            "disk_normal": 80.0,
            "gpu_normal": 30.0,
        }

        # Dashboard configuration
        self.dashboard_config = {
            "auto_refresh": True,
            "refresh_interval": 5,  # seconds
            "show_gpu": GPU_AVAILABLE,
            "show_processes": True,
            "max_processes": 10,
        }

        # Initialize default alert rules
        self._initialize_default_alerts()

        logger.info(
            f"Monitoring Dashboard initialized - GPU monitoring: {GPU_AVAILABLE}"
        )

    def start_monitoring(self):
        """Start the monitoring system."""
        if self.monitoring_active:
            logger.warning("Monitoring already active")
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitoring_thread.start()

        logger.info("Monitoring dashboard started")

    def stop_monitoring(self):
        """Stop the monitoring system."""
        if not self.monitoring_active:
            return

        self.monitoring_active = False

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)

        logger.info("Monitoring dashboard stopped")

    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                # Collect system metrics
                metrics = self._collect_system_metrics()

                # Store metrics
                self.metrics_history.append(metrics)

                # Trim history if too large
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history = self.metrics_history[
                        -self.max_history_size :
                    ]

                # Collect process metrics
                self._collect_process_metrics()

                # Check alerts
                self._check_alerts(metrics)

                # Sleep until next update
                time.sleep(self.update_interval)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.update_interval)

    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=None)

        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available_gb = memory.available / (1024**3)

        # Disk metrics
        disk = psutil.disk_usage("/")
        disk_usage_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024**3)

        # Network metrics
        network = psutil.net_io_counters()
        network_bytes_sent = network.bytes_sent
        network_bytes_recv = network.bytes_recv

        # Process count
        process_count = len(psutil.pids())

        # Load average (Unix-like systems)
        try:
            load_average = list(psutil.getloadavg())
        except AttributeError:
            load_average = [0.0, 0.0, 0.0]  # Windows doesn't have load average

        # GPU metrics
        gpu_metrics = self._collect_gpu_metrics()

        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_available_gb=memory_available_gb,
            disk_usage_percent=disk_usage_percent,
            disk_free_gb=disk_free_gb,
            network_bytes_sent=network_bytes_sent,
            network_bytes_recv=network_bytes_recv,
            gpu_metrics=gpu_metrics,
            process_count=process_count,
            load_average=load_average,
        )

    def _collect_gpu_metrics(self) -> List[Dict[str, Any]]:
        """Collect GPU metrics."""
        if not GPU_AVAILABLE:
            return []

        try:
            gpus = GPUtil.getGPUs()
            gpu_metrics = []

            for gpu in gpus:
                gpu_metrics.append(
                    {
                        "id": gpu.id,
                        "name": gpu.name,
                        "load": gpu.load * 100,  # Convert to percentage
                        "memory_used": gpu.memoryUsed,
                        "memory_total": gpu.memoryTotal,
                        "memory_percent": (gpu.memoryUsed / gpu.memoryTotal) * 100,
                        "temperature": gpu.temperature,
                    }
                )

            return gpu_metrics

        except Exception as e:
            logger.error(f"Error collecting GPU metrics: {e}")
            return []

    def _collect_process_metrics(self):
        """Collect process-specific metrics."""
        try:
            current_processes = {}

            for proc in psutil.process_iter(
                [
                    "pid",
                    "name",
                    "cpu_percent",
                    "memory_percent",
                    "memory_info",
                    "status",
                    "create_time",
                    "num_threads",
                ]
            ):
                try:
                    pinfo = proc.info

                    # Filter for relevant processes (AreTomo3, Python, etc.)
                    if self._is_relevant_process(pinfo["name"]):
                        process_metrics = ProcessMetrics(
                            pid=pinfo["pid"],
                            name=pinfo["name"],
                            cpu_percent=pinfo["cpu_percent"] or 0.0,
                            memory_percent=pinfo["memory_percent"] or 0.0,
                            memory_mb=(
                                (pinfo["memory_info"].rss / (1024**2))
                                if pinfo["memory_info"]
                                else 0.0
                            ),
                            status=pinfo["status"],
                            create_time=datetime.fromtimestamp(pinfo["create_time"]),
                            num_threads=pinfo["num_threads"] or 0,
                        )

                        current_processes[pinfo["pid"]] = process_metrics

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            self.process_metrics = current_processes

        except Exception as e:
            logger.error(f"Error collecting process metrics: {e}")

    def _is_relevant_process(self, process_name: str) -> bool:
        """Check if process is relevant for monitoring."""
        relevant_names = [
            "python",
            "aretomo3",
            "AreTomo3",
            "imod",
            "relion",
            "warp",
            "ctffind",
            "gctf",
            "motioncor",
            "topaz",
        ]

        return any(name.lower() in process_name.lower() for name in relevant_names)

    # TODO: Refactor function - Function '_initialize_default_alerts' too long
    # (54 lines)
    def _initialize_default_alerts(self):
        """Initialize default alert rules."""
        # High CPU usage alert
        self.alert_rules["high_cpu"] = AlertRule(
            rule_id="high_cpu",
            name="High CPU Usage",
            metric_path="cpu_percent",
            operator=">",
            threshold=80.0,
            duration_seconds=30,
            severity="warning",
            enabled=True,
            callback=None,
        )

        # High memory usage alert
        self.alert_rules["high_memory"] = AlertRule(
            rule_id="high_memory",
            name="High Memory Usage",
            metric_path="memory_percent",
            operator=">",
            threshold=90.0,
            duration_seconds=60,
            severity="critical",
            enabled=True,
            callback=None,
        )

        # Low disk space alert
        self.alert_rules["low_disk"] = AlertRule(
            rule_id="low_disk",
            name="Low Disk Space",
            metric_path="disk_free_gb",
            operator="<",
            threshold=5.0,
            duration_seconds=300,
            severity="warning",
            enabled=True,
            callback=None,
        )

        # GPU overheating alert (if GPU available)
        if GPU_AVAILABLE:
            self.alert_rules["gpu_overheat"] = AlertRule(
                rule_id="gpu_overheat",
                name="GPU Overheating",
                metric_path="gpu_metrics.0.temperature",
                operator=">",
                threshold=80.0,
                duration_seconds=60,
                severity="critical",
                enabled=True,
                callback=None,
            )

    def _check_alerts(self, metrics: SystemMetrics):
        """Check alert conditions."""
        for rule_id, rule in self.alert_rules.items():
            if not rule.enabled:
                continue

            try:
                # Get metric value
                metric_value = self._get_metric_value(metrics, rule.metric_path)

                if metric_value is None:
                    continue

                # Check condition
                condition_met = self._evaluate_condition(
                    metric_value, rule.operator, rule.threshold
                )

                if condition_met:
                    # Check if alert is already active
                    if rule_id in self.active_alerts:
                        # Update existing alert
                        alert = self.active_alerts[rule_id]
                        alert["last_triggered"] = datetime.now()

                        # Check if duration threshold is met
                        duration = (
                            alert["last_triggered"] - alert["first_triggered"]
                        ).total_seconds()
                        if duration >= rule.duration_seconds and not alert["notified"]:
                            self._trigger_alert(rule, metric_value, duration)
                            alert["notified"] = True
                    else:
                        # Create new alert
                        self.active_alerts[rule_id] = {
                            "rule": rule,
                            "first_triggered": datetime.now(),
                            "last_triggered": datetime.now(),
                            "metric_value": metric_value,
                            "notified": False,
                        }
                else:
                    # Condition not met, clear alert if active
                    if rule_id in self.active_alerts:
                        del self.active_alerts[rule_id]
                        logger.info(f"Alert cleared: {rule.name}")

            except Exception as e:
                logger.error(f"Error checking alert {rule_id}: {e}")

    def _get_metric_value(
        self, metrics: SystemMetrics, metric_path: str
    ) -> Optional[float]:
        """Get metric value from path."""
        try:
            parts = metric_path.split(".")
            value = metrics

            for part in parts:
                if part.isdigit():
                    # Array index
                    value = value[int(part)]
                else:
                    # Attribute access
                    value = getattr(value, part)

            return float(value)

        except (AttributeError, IndexError, ValueError, TypeError):
            return None

    def _evaluate_condition(
        self, value: float, operator: str, threshold: float
    ) -> bool:
        """Evaluate alert condition."""
        if operator == ">":
            return value > threshold
        elif operator == "<":
            return value < threshold
        elif operator == ">=":
            return value >= threshold
        elif operator == "<=":
            return value <= threshold
        elif operator == "==":
            return value == threshold
        elif operator == "!=":
            return value != threshold
        else:
            return False

    def _trigger_alert(self, rule: AlertRule, metric_value: float, duration: float):
        """Trigger an alert."""
        alert_data = {
            "rule_id": rule.rule_id,
            "rule_name": rule.name,
            "severity": rule.severity,
            "metric_value": metric_value,
            "threshold": rule.threshold,
            "duration": duration,
            "timestamp": datetime.now(),
        }

        logger.warning(
            f"ALERT: {rule.name} - Value: {metric_value}, Threshold: {rule.threshold}"
        )

        # Call rule-specific callback
        if rule.callback:
            try:
                rule.callback(alert_data)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")

        # Call global alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                logger.error(f"Error in global alert callback: {e}")

    # TODO: Refactor function - Function 'create_dashboard_html' too long (157
    # lines)
    def create_dashboard_html(self) -> str:
        """Create HTML dashboard."""
        if not PLOTLY_AVAILABLE:
            return self._create_simple_dashboard()

        try:
            # Create subplots
            fig = make_subplots(
                rows=3,
                cols=2,
                subplot_titles=[
                    "CPU Usage (%)",
                    "Memory Usage (%)",
                    "Disk Usage (%)",
                    "Network I/O (MB)",
                    "GPU Usage (%)",
                    "Process Count",
                ],
                specs=[
                    [{"type": "scatter"}, {"type": "scatter"}],
                    [{"type": "scatter"}, {"type": "scatter"}],
                    [{"type": "scatter"}, {"type": "scatter"}],
                ],
            )

            if not self.metrics_history:
                return self._create_no_data_dashboard()

            # Extract time series data
            timestamps = [
                m.timestamp for m in self.metrics_history[-100:]
            ]  # Last 100 points
            cpu_data = [m.cpu_percent for m in self.metrics_history[-100:]]
            memory_data = [m.memory_percent for m in self.metrics_history[-100:]]
            disk_data = [m.disk_usage_percent for m in self.metrics_history[-100:]]

            # Network data (convert to MB)
            network_sent = [
                (m.network_bytes_sent / (1024**2)) for m in self.metrics_history[-100:]
            ]
            network_recv = [
                (m.network_bytes_recv / (1024**2)) for m in self.metrics_history[-100:]
            ]

            process_counts = [m.process_count for m in self.metrics_history[-100:]]

            # Add CPU plot
            fig.add_trace(
                go.Scatter(
                    x=timestamps, y=cpu_data, name="CPU", line=dict(color="blue")
                ),
                row=1,
                col=1,
            )

            # Add memory plot
            fig.add_trace(
                go.Scatter(
                    x=timestamps, y=memory_data, name="Memory", line=dict(color="green")
                ),
                row=1,
                col=2,
            )

            # Add disk plot
            fig.add_trace(
                go.Scatter(
                    x=timestamps, y=disk_data, name="Disk", line=dict(color="orange")
                ),
                row=2,
                col=1,
            )

            # Add network plot
            fig.add_trace(
                go.Scatter(
                    x=timestamps, y=network_sent, name="Sent", line=dict(color="red")
                ),
                row=2,
                col=2,
            )
            fig.add_trace(
                go.Scatter(
                    x=timestamps,
                    y=network_recv,
                    name="Received",
                    line=dict(color="purple"),
                ),
                row=2,
                col=2,
            )

            # Add GPU plot if available
            if GPU_AVAILABLE and self.metrics_history[-1].gpu_metrics:
                gpu_data = []
                for m in self.metrics_history[-100:]:
                    if m.gpu_metrics:
                        gpu_data.append(m.gpu_metrics[0]["load"])
                    else:
                        gpu_data.append(0)

                fig.add_trace(
                    go.Scatter(
                        x=timestamps, y=gpu_data, name="GPU", line=dict(color="cyan")
                    ),
                    row=3,
                    col=1,
                )

            # Add process count plot
            fig.add_trace(
                go.Scatter(
                    x=timestamps,
                    y=process_counts,
                    name="Processes",
                    line=dict(color="magenta"),
                ),
                row=3,
                col=2,
            )

            # Update layout
            fig.update_layout(
                title="AreTomo3 GUI System Monitoring Dashboard",
                height=800,
                showlegend=True,
                template="plotly_white",
            )

            # Add alert indicators
            self._add_alert_indicators(fig)

            # Convert to HTML
            html_content = fig.to_html(
                include_plotlyjs="cdn",
                div_id="monitoring_dashboard",
                config={"displayModeBar": True, "displaylogo": False},
            )

            # Add auto-refresh if enabled
            if self.dashboard_config["auto_refresh"]:
                refresh_script = f"""
                <script>
                setTimeout(function(){{
                    location.reload();
                }}, {self.dashboard_config['refresh_interval'] * 1000});
                </script>
                """
                html_content = html_content.replace(
                    "</body>", refresh_script + "</body>"
                )

            return html_content

        except Exception as e:
            logger.error(f"Error creating dashboard: {e}")
            return self._create_error_dashboard(str(e))

    def _add_alert_indicators(self, fig):
        """Add alert indicators to dashboard."""
        if not self.active_alerts:
            return

        # Add alert annotations
        for alert_id, alert_info in self.active_alerts.items():
            rule = alert_info["rule"]

            # Add alert indicator based on severity
            color = "red" if rule.severity == "critical" else "orange"

            fig.add_annotation(
                text=f"⚠️ {rule.name}",
                xref="paper",
                yref="paper",
                x=0.02,
                y=0.98,
                showarrow=False,
                font=dict(color=color, size=12),
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor=color,
                borderwidth=1,
            )

    # TODO: Refactor function - Function '_create_simple_dashboard' too long
    # (73 lines)
    def _create_simple_dashboard(self) -> str:
        """Create simple HTML dashboard without Plotly."""
        if not self.metrics_history:
            return "<html><body><h1>No monitoring data available</h1></body></html>"

        latest = self.metrics_history[-1]

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>AreTomo3 GUI Monitoring</title>
            <meta http-equiv="refresh" content="{self.dashboard_config['refresh_interval']}">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .metric {{ margin: 10px 0; padding: 10px; border: 1px solid #ccc; }}
                .critical {{ background-color: #ffebee; }}
                .warning {{ background-color: #fff3e0; }}
                .normal {{ background-color: #e8f5e8; }}
            </style>
        </head>
        <body>
            <h1>AreTomo3 GUI System Monitoring</h1>
            <p>Last updated: {latest.timestamp}</p>

            <div class="metric {'critical' if latest.cpu_percent > 80 else 'warning' if latest.cpu_percent > 60 else 'normal'}">
                <strong>CPU Usage:</strong> {latest.cpu_percent:.1f}%
            </div>

            <div class="metric {'critical' if latest.memory_percent > 90 else 'warning' if latest.memory_percent > 70 else 'normal'}">
                <strong>Memory Usage:</strong> {latest.memory_percent:.1f}% ({latest.memory_available_gb:.1f} GB available)
            </div>

            <div class="metric {'critical' if latest.disk_usage_percent > 95 else 'warning' if latest.disk_usage_percent > 85 else 'normal'}">
                <strong>Disk Usage:</strong> {latest.disk_usage_percent:.1f}% ({latest.disk_free_gb:.1f} GB free)
            </div>

            <div class="metric normal">
                <strong>Process Count:</strong> {latest.process_count}
            </div>
        """

        # Add GPU info if available
        if latest.gpu_metrics:
            for i, gpu in enumerate(latest.gpu_metrics):
                gpu_class = (
                    "critical"
                    if gpu["load"] > 90
                    else "warning" if gpu["load"] > 70 else "normal"
                )
                html += f"""
                <div class="metric {gpu_class}">
                    <strong>GPU {i} ({gpu['name']}):</strong> {gpu['load']:.1f}% load, {gpu['memory_percent']:.1f}% memory, {gpu['temperature']}°C
                </div>
                """

        # Add active alerts
        if self.active_alerts:
            html += "<h2>Active Alerts</h2>"
            for alert_id, alert_info in self.active_alerts.items():
                rule = alert_info["rule"]
                html += f"""
                <div class="metric {'critical' if rule.severity == 'critical' else 'warning'}">
                    <strong>⚠️ {rule.name}:</strong> {alert_info['metric_value']:.1f} (threshold: {rule.threshold})
                </div>
                """

        html += """
        </body>
        </html>
        """

        return html

    def _create_no_data_dashboard(self) -> str:
        """Create dashboard for no data state."""
        return """
        <html>
        <head><title>AreTomo3 GUI Monitoring</title></head>
        <body>
            <h1>AreTomo3 GUI Monitoring Dashboard</h1>
            <p>No monitoring data available yet. Please wait for data collection to begin.</p>
        </body>
        </html>
        """

    def _create_error_dashboard(self, error: str) -> str:
        """Create dashboard for error state."""
        return f"""
        <html>
        <head><title>AreTomo3 GUI Monitoring - Error</title></head>
        <body>
            <h1>Monitoring Dashboard Error</h1>
            <p>Error creating dashboard: {error}</p>
        </body>
        </html>
        """

    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """Get the most recent metrics."""
        return self.metrics_history[-1] if self.metrics_history else None

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of metrics."""
        if not self.metrics_history:
            return {}

        recent_metrics = self.metrics_history[-60:]  # Last minute

        return {
            "current": asdict(self.metrics_history[-1]),
            "averages": {
                "cpu_percent": np.mean([m.cpu_percent for m in recent_metrics]),
                "memory_percent": np.mean([m.memory_percent for m in recent_metrics]),
                "disk_usage_percent": np.mean(
                    [m.disk_usage_percent for m in recent_metrics]
                ),
            },
            "peaks": {
                "cpu_percent": np.max([m.cpu_percent for m in recent_metrics]),
                "memory_percent": np.max([m.memory_percent for m in recent_metrics]),
                "disk_usage_percent": np.max(
                    [m.disk_usage_percent for m in recent_metrics]
                ),
            },
            "active_alerts": len(self.active_alerts),
            "process_count": len(self.process_metrics),
        }

    def add_alert_callback(self, callback: Callable):
        """Add alert callback."""
        self.alert_callbacks.append(callback)

    def add_alert_rule(self, rule: AlertRule):
        """Add custom alert rule."""
        self.alert_rules[rule.rule_id] = rule
        logger.info(f"Added alert rule: {rule.name}")


# Global monitoring dashboard instance
monitoring_dashboard = MonitoringDashboard()


def get_monitoring_dashboard() -> MonitoringDashboard:
    """Get the global monitoring dashboard instance."""
    return monitoring_dashboard
