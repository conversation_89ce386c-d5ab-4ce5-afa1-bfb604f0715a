#!/usr/bin/env python3
"""
AreTomo3 GUI Enhanced Export Manager
Robust export functionality based on IMOD, RELION, and Warp insights.
"""

import json
import logging
import shutil
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class ExportFormat:
    """Export format specification."""

    name: str
    extension: str
    description: str
    supports_metadata: bool
    supports_coordinates: bool
    supports_transformations: bool
    target_software: List[str]


class EnhancedExportManager:
    """
    Enhanced export manager with robust format support.
    Based on insights from IMOD, RELION, and Warp repositories.
    """

    # TODO: Refactor function - Function '__init__' too long (71 lines)
    def __init__(self):
        """Initialize the enhanced export manager."""
        self.supported_formats = {
            "relion_star": ExportFormat(
                name="RELION STAR",
                extension=".star",
                description="RELION metadata format with comprehensive tomography support",
                supports_metadata=True,
                supports_coordinates=True,
                supports_transformations=True,
                target_software=["RELION", "Warp", "cryoSPARC"],
            ),
            "imod_mod": ExportFormat(
                name="IMOD Model",
                extension=".mod",
                description="IMOD model format for fiducials and annotations",
                supports_metadata=False,
                supports_coordinates=True,
                supports_transformations=False,
                target_software=["IMOD", "eTomo"],
            ),
            "imod_xf": ExportFormat(
                name="IMOD Transform",
                extension=".xf",
                description="IMOD transformation matrices for alignment",
                supports_metadata=False,
                supports_coordinates=False,
                supports_transformations=True,
                target_software=["IMOD", "eTomo"],
            ),
            "warp_settings": ExportFormat(
                name="Warp Settings",
                extension=".settings",
                description="Warp processing settings and metadata",
                supports_metadata=True,
                supports_coordinates=False,
                supports_transformations=True,
                target_software=["Warp", "M"],
            ),
            "dynamo_table": ExportFormat(
                name="Dynamo Table",
                extension=".tbl",
                description="Dynamo particle table format",
                supports_metadata=True,
                supports_coordinates=True,
                supports_transformations=True,
                target_software=["Dynamo"],
            ),
            "stopgap_motl": ExportFormat(
                name="STOPGAP MOTL",
                extension=".em",
                description="STOPGAP motive list format",
                supports_metadata=True,
                supports_coordinates=True,
                supports_transformations=True,
                target_software=["STOPGAP"],
            ),
            "json_metadata": ExportFormat(
                name="JSON Metadata",
                extension=".json",
                description="Universal JSON format with all metadata",
                supports_metadata=True,
                supports_coordinates=True,
                supports_transformations=True,
                target_software=["Custom", "Python", "MATLAB"],
            ),
        }

        logger.info(
            f"Enhanced Export Manager initialized with {len(self.supported_formats)} formats"
        )

    # TODO: Refactor export_to_relion_star - complexity: 11 (target: <10)
    # TODO: Refactor function - Function 'export_to_relion_star' too long (70
    # lines)
    def export_to_relion_star(self, data: Dict[str, Any], output_path: Path) -> bool:
        """Export data to RELION STAR format with comprehensive metadata."""
        try:
            logger.info(f"Exporting to RELION STAR format: {output_path}")

            # Create STAR file content
            star_content = []

            # Header with version info
            star_content.append("# RELION STAR file")
            star_content.append(
                f"# Created by AreTomo3 GUI on {datetime.now().isoformat()}"
            )
            star_content.append("# Based on AreTomo3 processing results")
            star_content.append("")

            # Global parameters
            star_content.append("data_global")
            star_content.append("")
            star_content.append("_rlnVoltage 300.000000")
            star_content.append("_rlnSphericalAberration 2.700000")
            star_content.append("_rlnAmplitudeContrast 0.100000")

            if "metadata" in data:
                metadata = data["metadata"]
                if "voltage" in metadata:
                    star_content[-3] = f"_rlnVoltage {metadata['voltage']:.6f}"
                if "cs" in metadata:
                    star_content[-2] = f"_rlnSphericalAberration {metadata['cs']:.6f}"
                if "amp_contrast" in metadata:
                    star_content[-1] = (
                        f"_rlnAmplitudeContrast {metadata['amp_contrast']:.6f}"
                    )

            star_content.append("")

            # Tomogram data
            if "aretomo3_data" in data:
                aretomo3_data = data["aretomo3_data"]

                # CTF data
                if "ctf_data" in aretomo3_data:
                    star_content.extend(
                        self._create_relion_ctf_table(aretomo3_data["ctf_data"])
                    )

                # Motion data
                if "motion_data" in aretomo3_data:
                    star_content.extend(
                        self._create_relion_motion_table(aretomo3_data["motion_data"])
                    )

                # Alignment data
                if "alignment_data" in aretomo3_data:
                    star_content.extend(
                        self._create_relion_alignment_table(
                            aretomo3_data["alignment_data"]
                        )
                    )

            # Write STAR file
            with open(output_path, "w") as f:
                f.write("\n".join(star_content))

            logger.info(f"Successfully exported RELION STAR file: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting to RELION STAR: {e}")
            return False

    def _create_relion_ctf_table(self, ctf_data: Dict[str, Any]) -> List[str]:
        """Create RELION CTF table from AreTomo3 CTF data."""
        table = []

        table.append("data_micrographs")
        table.append("")
        table.append("loop_")
        table.append("_rlnMicrographName #1")
        table.append("_rlnCtfImage #2")
        table.append("_rlnDefocusU #3")
        table.append("_rlnDefocusV #4")
        table.append("_rlnDefocusAngle #5")
        table.append("_rlnCtfFigureOfMerit #6")
        table.append("_rlnCtfMaxResolution #7")
        table.append("_rlnAngleRot #8")
        table.append("_rlnAngleTilt #9")
        table.append("_rlnAnglePsi #10")

        # Extract CTF parameters
        if "ctf_parameters" in ctf_data:
            for series_name, series_data in ctf_data["ctf_parameters"].items():
                if "parameters" in series_data and not series_data["parameters"].empty:
                    df = series_data["parameters"]

                    for idx, row in df.iterrows():
                        # Convert AreTomo3 format to RELION format
                        defocus_u = row.get("defocus1_A", 0) / 10000  # Convert to μm
                        defocus_v = row.get("defocus2_A", defocus_u) / 10000
                        defocus_angle = row.get("astigmatism_angle", 0)
                        ctf_fom = row.get("cross_correlation", 0)
                        ctf_resolution = row.get("resolution_limit_A", 0)
                        tilt_angle = row.get("tilt_angle", 0)

                        micrograph_name = f"{series_name}_{idx:04d}.mrc"
                        ctf_image = f"{series_name}_{idx:04d}_ctf.mrc"

                        table.append(
                            f"{micrograph_name} {ctf_image} {
                                defocus_u:.6f} {
                                defocus_v:.6f} "
                            f"{
                                defocus_angle:.6f} {
                                ctf_fom:.6f} {
                                ctf_resolution:.6f} "
                            f"0.000000 {
                                tilt_angle:.6f} 0.000000"
                        )

        table.append("")
        return table

    def _create_relion_motion_table(self, motion_data: Dict[str, Any]) -> List[str]:
        """Create RELION motion table from AreTomo3 motion data."""
        table = []

        table.append("data_motion")
        table.append("")
        table.append("loop_")
        table.append("_rlnMicrographName #1")
        table.append("_rlnAccumMotionTotal #2")
        table.append("_rlnAccumMotionEarly #3")
        table.append("_rlnAccumMotionLate #4")

        if "motion_vectors" in motion_data:
            motion_vectors = motion_data["motion_vectors"]

            for i, mv in enumerate(motion_vectors):
                total_motion = np.sqrt(mv["x"] ** 2 + mv["y"] ** 2)
                early_motion = total_motion * 0.3  # Approximate
                late_motion = total_motion * 0.7

                micrograph_name = f"frame_{i:04d}.mrc"
                table.append(
                    f"{micrograph_name} {
                        total_motion:.6f} {
                        early_motion:.6f} {
                        late_motion:.6f}"
                )

        table.append("")
        return table

    def _create_relion_alignment_table(
        self, alignment_data: Dict[str, Any]
    ) -> List[str]:
        """Create RELION alignment table from AreTomo3 alignment data."""
        table = []

        table.append("data_tilt_series")
        table.append("")
        table.append("loop_")
        table.append("_rlnTomoName #1")
        table.append("_rlnAngleTilt #2")
        table.append("_rlnOriginXAngst #3")
        table.append("_rlnOriginYAngst #4")
        table.append("_rlnAngleRot #5")
        table.append("_rlnAnglePsi #6")

        if "parameters" in alignment_data and not alignment_data["parameters"].empty:
            df = alignment_data["parameters"]

            for idx, row in df.iterrows():
                tomo_name = f"tomo_{idx:04d}.mrc"
                tilt_angle = row.get("tilt_angle", 0)
                origin_x = row.get("trans_x", 0)
                origin_y = row.get("trans_y", 0)
                rotation = row.get("rotation", 0)

                table.append(
                    f"{tomo_name} {
                        tilt_angle:.6f} {
                        origin_x:.6f} {
                        origin_y:.6f} "
                    f"{
                        rotation:.6f} 0.000000"
                )

        table.append("")
        return table

    def export_to_imod_format(
        self, data: Dict[str, Any], output_dir: Path, export_type: str = "both"
    ) -> bool:
        """Export data to IMOD format (models and transforms)."""
        try:
            logger.info(f"Exporting to IMOD format: {output_dir}")
            output_dir.mkdir(parents=True, exist_ok=True)

            success = True

            if export_type in ["both", "transforms"]:
                # Export transformation matrices
                if (
                    "aretomo3_data" in data
                    and "alignment_data" in data["aretomo3_data"]
                ):
                    xf_path = output_dir / "alignment.xf"
                    success &= self._export_imod_transforms(
                        data["aretomo3_data"]["alignment_data"], xf_path
                    )

            if export_type in ["both", "models"]:
                # Export model files (if fiducials or annotations exist)
                if "fiducials" in data:
                    mod_path = output_dir / "fiducials.mod"
                    success &= self._export_imod_model(data["fiducials"], mod_path)

            return success

        except Exception as e:
            logger.error(f"Error exporting to IMOD format: {e}")
            return False

    def _export_imod_transforms(
        self, alignment_data: Dict[str, Any], output_path: Path
    ) -> bool:
        """Export IMOD transformation file."""
        try:
            if "parameters" not in alignment_data or alignment_data["parameters"].empty:
                logger.warning("No alignment data available for IMOD export")
                return False

            df = alignment_data["parameters"]

            with open(output_path, "w") as f:
                for idx, row in df.iterrows():
                    # IMOD transform format: a11 a12 a21 a22 dx dy
                    # For simple translation and rotation
                    rotation = np.radians(row.get("rotation", 0))
                    trans_x = row.get("trans_x", 0)
                    trans_y = row.get("trans_y", 0)

                    a11 = np.cos(rotation)
                    a12 = -np.sin(rotation)
                    a21 = np.sin(rotation)
                    a22 = np.cos(rotation)

                    f.write(
                        f"{a11:12.6f} {a12:12.6f} {a21:12.6f} {a22:12.6f} "
                        f"{trans_x:12.6f} {trans_y:12.6f}\n"
                    )

            logger.info(f"Exported IMOD transforms: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting IMOD transforms: {e}")
            return False

    def _export_imod_model(
        self, fiducial_data: Dict[str, Any], output_path: Path
    ) -> bool:
        """Export IMOD model file."""
        try:
            # This would require the IMOD Python library or custom binary format writing
            # For now, export as text format that can be converted

            with open(output_path.with_suffix(".txt"), "w") as f:
                f.write("# IMOD Model Export from AreTomo3 GUI\n")
                f.write("# Convert to .mod format using IMOD tools\n")

                if "points" in fiducial_data:
                    for i, point in enumerate(fiducial_data["points"]):
                        f.write(
                            f"Point {i}: {
                                point['x']:.3f} {
                                point['y']:.3f} {
                                point['z']:.3f}\n"
                        )

            logger.info(f"Exported IMOD model (text format): {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting IMOD model: {e}")
            return False

    def export_to_json(self, data: Dict[str, Any], output_path: Path) -> bool:
        """Export comprehensive data to JSON format."""
        try:
            logger.info(f"Exporting to JSON format: {output_path}")

            # Create comprehensive export data
            export_data = {
                "export_info": {
                    "format": "AreTomo3_GUI_JSON",
                    "version": "1.0",
                    "created": datetime.now().isoformat(),
                    "source": "AreTomo3 GUI Enhanced Export",
                },
                "data": data,
            }

            # Convert numpy arrays to lists for JSON serialization
            export_data = self._convert_numpy_for_json(export_data)

            with open(output_path, "w") as f:
                json.dump(export_data, f, indent=2, default=str)

            logger.info(f"Successfully exported JSON file: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting to JSON: {e}")
            return False

    def _convert_numpy_for_json(self, obj):
        """Convert numpy arrays to lists for JSON serialization."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {
                key: self._convert_numpy_for_json(value) for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [self._convert_numpy_for_json(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict("records")
        else:
            return obj

    def get_supported_formats(self) -> Dict[str, ExportFormat]:
        """Get all supported export formats."""
        return self.supported_formats

    def validate_export_data(self, data: Dict[str, Any], format_name: str) -> List[str]:
        """Validate data for export format."""
        warnings = []

        if format_name not in self.supported_formats:
            warnings.append(f"Unsupported format: {format_name}")
            return warnings

        format_spec = self.supported_formats[format_name]

        # Check for required data based on format capabilities
        if format_spec.supports_metadata and "metadata" not in data:
            warnings.append("Metadata missing - some information may be incomplete")

        if format_spec.supports_coordinates and "coordinates" not in data:
            warnings.append("Coordinate data missing")

        if format_spec.supports_transformations and "alignment_data" not in data.get(
            "aretomo3_data", {}
        ):
            warnings.append("Transformation data missing")

        return warnings


# Global export manager instance
enhanced_export_manager = EnhancedExportManager()


def export_data(data: Dict[str, Any], output_path: Path, format_name: str) -> bool:
    """Convenience function to export data in specified format."""
    if format_name == "relion_star":
        return enhanced_export_manager.export_to_relion_star(data, output_path)
    elif format_name in ["imod_mod", "imod_xf"]:
        export_type = "models" if format_name == "imod_mod" else "transforms"
        return enhanced_export_manager.export_to_imod_format(
            data, output_path.parent, export_type
        )
    elif format_name == "json_metadata":
        return enhanced_export_manager.export_to_json(data, output_path)
    else:
        logger.error(f"Unsupported export format: {format_name}")
        return False
