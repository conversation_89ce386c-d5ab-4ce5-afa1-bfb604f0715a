#!/usr/bin/env python3
"""
EER (Electron Event Representation) file metadata reader for AreTomo3 GUI.

This module provides functionality to read metadata from EER files,
which are specialized TIFF files used in electron microscopy.
"""

import logging
import os
import struct
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

try:
    import tifffile

    TIFFFILE_AVAILABLE = True
except ImportError:
    TIFFFILE_AVAILABLE = False
    logging.warning("tifffile not available - EER reading will be limited")

try:
    import xmltodict

    XMLTODICT_AVAILABLE = True
except ImportError:
    XMLTODICT_AVAILABLE = False
    logging.warning("xmltodict not available - EER metadata parsing will be limited")

logger = logging.getLogger(__name__)


class EERMetadata:
    """Container class for EER file metadata."""

    def __init__(self):
        """Initialize the instance."""
        self.width: int = 0
        self.height: int = 0
        self.num_frames: int = 0
        self.pixel_size: float = 0.0
        self.voltage: float = 0.0
        self.exposure_time: float = 0.0
        self.dose_rate: float = 0.0
        self.magnification: float = 0.0
        self.acquisition_date: str = ""
        self.acquisition_time: str = ""
        self.detector: str = ""
        self.camera_length: float = 0.0
        self.defocus: float = 0.0
        self.metadata_xml: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary."""
        return {
            "width": self.width,
            "height": self.height,
            "num_frames": self.num_frames,
            "pixel_size": self.pixel_size,
            "voltage": self.voltage,
            "exposure_time": self.exposure_time,
            "dose_rate": self.dose_rate,
            "magnification": self.magnification,
            "acquisition_date": self.acquisition_date,
            "acquisition_time": self.acquisition_time,
            "detector": self.detector,
            "camera_length": self.camera_length,
            "defocus": self.defocus,
            "metadata_xml": self.metadata_xml,
        }

    def __str__(self) -> str:
        """String representation of metadata."""
        return (
            f"EER Metadata:\n"
            f"  Dimensions: {self.width} x {self.height}\n"
            f"  Frames: {self.num_frames}\n"
            f"  Pixel size: {self.pixel_size} Å\n"
            f"  Voltage: {self.voltage} kV\n"
            f"  Exposure time: {self.exposure_time} s\n"
            f"  Dose rate: {self.dose_rate} e/px/s\n"
            f"  Magnification: {self.magnification}x\n"
            f"  Detector: {self.detector}"
        )


def read_eer_metadata(filepath: str) -> Optional[EERMetadata]:
    """
    Read metadata from an EER file.

    Args:
        filepath: Path to the EER file

    Returns:
        EERMetadata object or None if reading fails
    """
    if not os.path.exists(filepath):
        logger.error(f"EER file not found: {filepath}")
        return None

    if not TIFFFILE_AVAILABLE:
        logger.error("tifffile library not available for EER reading")
        return None

    try:
        # Read EER file using tifffile
        with tifffile.TiffFile(filepath) as tif:
            metadata = EERMetadata()

            # Basic image properties
            if tif.pages:
                page = tif.pages[0]
                metadata.width = page.imagewidth
                metadata.height = page.imagelength
                metadata.num_frames = len(tif.pages)

                # Extract metadata from TIFF tags
                metadata = _extract_tiff_metadata(tif, metadata)

                # Try to extract metadata from XML if available
                xml_metadata = _extract_xml_metadata(tif)
                if xml_metadata:
                    metadata = _parse_xml_metadata(xml_metadata, metadata)

            logger.info(f"Successfully read EER metadata from {filepath}")
            return metadata

    except Exception as e:
        logger.error(f"Error reading EER file {filepath}: {e}")
        return None

    # TODO: Refactor _extract_tiff_metadata - complexity: 20 (target: <10)
    # TODO: Refactor function - Function '_extract_tiff_metadata' too long (56
    # lines)


def _extract_tiff_metadata(
    tif: "tifffile.TiffFile", metadata: EERMetadata
) -> EERMetadata:
    """Extract metadata from TIFF tags."""
    try:
        # Common TIFF tags that might contain useful information
        for page in tif.pages:
            tags = page.tags

            # Check for standard TIFF tags
            if "XResolution" in tags:
                try:
                    x_res = tags["XResolution"].value
                    if isinstance(x_res, tuple) and len(x_res) == 2:
                        metadata.pixel_size = (
                            x_res[1] / x_res[0]
                        )  # Convert to actual pixel size
                except Exception:
                    pass

            if "YResolution" in tags:
                try:
                    y_res = tags["YResolution"].value
                    if isinstance(y_res, tuple) and len(y_res) == 2:
                        if metadata.pixel_size == 0.0:
                            metadata.pixel_size = y_res[1] / y_res[0]
                except Exception:
                    pass

            # Check for custom tags that might contain metadata
            if "Software" in tags:
                software = tags["Software"].value
                if isinstance(software, str):
                    metadata.detector = software

            if "DateTime" in tags:
                datetime_str = tags["DateTime"].value
                if isinstance(datetime_str, str):
                    # Try to parse date and time
                    parts = datetime_str.split(" ")
                    if len(parts) >= 2:
                        metadata.acquisition_date = parts[0]
                        metadata.acquisition_time = parts[1]

            # Check for description field which might contain XML
            if "ImageDescription" in tags:
                desc = tags["ImageDescription"].value
                if isinstance(desc, str) and desc.strip().startswith("<"):
                    metadata.metadata_xml = desc

            break  # Only process first page for metadata

    except Exception as e:
        logger.warning(f"Error extracting TIFF metadata: {e}")

    return metadata

    # TODO: Refactor _extract_xml_metadata - complexity: 11 (target: <10)


def _extract_xml_metadata(tif: "tifffile.TiffFile") -> Optional[str]:
    """Extract XML metadata from TIFF file."""
    try:
        # Check for XML in various possible locations
        for page in tif.pages:
            tags = page.tags

            # Common locations for XML metadata
            xml_candidates = ["ImageDescription", "Software", "Artist", "Copyright"]

            for tag_name in xml_candidates:
                if tag_name in tags:
                    value = tags[tag_name].value
                    if isinstance(value, str) and value.strip().startswith("<"):
                        return value

        # Check for separate metadata in the file structure
        if hasattr(tif, "fstat") and hasattr(tif.fstat, "description"):
            desc = tif.fstat.description
            if desc and desc.strip().startswith("<"):
                return desc

    except Exception as e:
        logger.warning(f"Error extracting XML metadata: {e}")

    return None


def _parse_xml_metadata(xml_string: str, metadata: EERMetadata) -> EERMetadata:
    """Parse XML metadata and update EERMetadata object."""
    if not XMLTODICT_AVAILABLE:
        logger.warning("xmltodict not available - skipping XML parsing")
        return metadata

    try:
        # Parse XML to dictionary
        xml_dict = xmltodict.parse(xml_string)

        # Extract common fields - this will vary based on the actual XML structure
        # These are common patterns found in EER files

        def get_nested_value(d, keys):
            """Safely get nested dictionary value."""
            for key in keys:
                if isinstance(d, dict) and key in d:
                    d = d[key]
                else:
                    return None
            return d

        # Try various XML structures commonly found in EER files
        possible_paths = [
            ["microscopeData", "acquisition"],
            ["metadata", "acquisition"],
            ["acquisition"],
            ["microscope"],
            ["instrument"],
        ]

        for path in possible_paths:
            data = get_nested_value(xml_dict, path)
            if data:
                metadata = _extract_acquisition_data(data, metadata)
                break

        # Store the original XML for reference
        metadata.metadata_xml = xml_string

    except Exception as e:
        logger.warning(f"Error parsing XML metadata: {e}")

    return metadata
    # TODO: Refactor _extract_acquisition_data - complexity: 14 (target: <10)

    # TODO: Refactor function - Function '_extract_acquisition_data' too long
    # (62 lines)


def _extract_acquisition_data(data: Dict, metadata: EERMetadata) -> EERMetadata:
    """Extract acquisition parameters from parsed XML data."""
    try:
        # Common field mappings - adjust based on actual EER XML structure
        field_mappings = {
            "pixelSize": ["pixelSize", "pixel_size", "PixelSize"],
            "voltage": ["voltage", "acceleratingVoltage", "Voltage", "kV"],
            "exposureTime": ["exposureTime", "exposure_time", "ExposureTime"],
            "doseRate": ["doseRate", "dose_rate", "DoseRate", "electronDose"],
            "magnification": ["magnification", "Magnification", "mag"],
            "detector": ["detector", "camera", "Detector", "Camera"],
            "defocus": ["defocus", "Defocus", "C1"],
            "cameraLength": ["cameraLength", "camera_length", "CameraLength"],
        }

        def find_value(data, possible_keys):
            """Find value using possible key names."""
            for key in possible_keys:
                if key in data:
                    value = data[key]
                    if isinstance(value, dict) and "#text" in value:
                        return value["#text"]
                    return value
            return None

        # Extract values using the mappings
        pixel_size = find_value(data, field_mappings["pixelSize"])
        if pixel_size:
            metadata.pixel_size = float(pixel_size)

        voltage = find_value(data, field_mappings["voltage"])
        if voltage:
            metadata.voltage = float(voltage)

        exposure_time = find_value(data, field_mappings["exposureTime"])
        if exposure_time:
            metadata.exposure_time = float(exposure_time)

        dose_rate = find_value(data, field_mappings["doseRate"])
        if dose_rate:
            metadata.dose_rate = float(dose_rate)

        magnification = find_value(data, field_mappings["magnification"])
        if magnification:
            metadata.magnification = float(magnification)

        detector = find_value(data, field_mappings["detector"])
        if detector:
            metadata.detector = str(detector)

        defocus = find_value(data, field_mappings["defocus"])
        if defocus:
            metadata.defocus = float(defocus)

        camera_length = find_value(data, field_mappings["cameraLength"])
        if camera_length:
            metadata.camera_length = float(camera_length)

    except Exception as e:
        logger.warning(f"Error extracting acquisition data: {e}")

    return metadata


def get_eer_info(filepath: str) -> Dict[str, Any]:
    """
    Get basic information about an EER file.

    Args:
        filepath: Path to the EER file

    Returns:
        Dictionary with basic file information
    """
    info = {
        "filepath": filepath,
        "exists": False,
        "size_bytes": 0,
        "size_mb": 0.0,
        "readable": False,
        "metadata": None,
    }

    try:
        if os.path.exists(filepath):
            info["exists"] = True
            stat = os.stat(filepath)
            info["size_bytes"] = stat.st_size
            info["size_mb"] = stat.st_size / (1024 * 1024)

            # Try to read metadata
            metadata = read_eer_metadata(filepath)
            if metadata:
                info["readable"] = True
                info["metadata"] = metadata.to_dict()

    except Exception as e:
        logger.error(f"Error getting EER info for {filepath}: {e}")

    return info


def is_eer_file(filepath: str) -> bool:
    """
    Check if a file is an EER file based on extension and content.

    Args:
        filepath: Path to check

    Returns:
        True if file appears to be an EER file
    """
    try:
        # Check extension
        if not filepath.lower().endswith(".eer"):
            return False

        # Check if file exists
        if not os.path.exists(filepath):
            return False

        # Try to open as TIFF to verify format
        if TIFFFILE_AVAILABLE:
            try:
                with tifffile.TiffFile(filepath) as tif:
                    # Basic validation - should have pages
                    return len(tif.pages) > 0
            except Exception:
                return False
        else:
            # Fallback - just check extension and existence
            return True

    except Exception:
        return False


# Convenience functions
def read_eer_dimensions(filepath: str) -> Tuple[int, int, int]:
    """
    Get dimensions of EER file.

    Returns:
        Tuple of (width, height, num_frames)
    """
    metadata = read_eer_metadata(filepath)
    if metadata:
        return metadata.width, metadata.height, metadata.num_frames
    return 0, 0, 0


def read_eer_pixel_size(filepath: str) -> float:
    """
    Get pixel size from EER file.

    Returns:
        Pixel size in Angstroms
    """
    metadata = read_eer_metadata(filepath)
    if metadata:
        return metadata.pixel_size
    return 0.0


# Command line interface for testing
if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        logger.info("Usage: python eer_reader.py <eer_file>")
        sys.exit(1)

    filepath = sys.argv[1]

    logger.info(f"Reading EER file: {filepath}")
    logger.info("-" * 50)

    # Get basic info
    info = get_eer_info(filepath)
    logger.info(f"File exists: {info['exists']}")
    logger.info(f"File size: {info['size_mb']:.2f} MB")
    logger.info(f"Readable: {info['readable']}")

    if info["readable"] and info["metadata"]:
        logger.info("\nMetadata:")
        metadata = EERMetadata()
        for key, value in info["metadata"].items():
            setattr(metadata, key, value)
        logger.info(metadata)
    else:
        logger.info("\nNo metadata could be extracted")
