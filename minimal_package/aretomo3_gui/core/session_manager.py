"""
Session Management System for AreTomo3 GUI
Handles automatic session saving, loading, and web interface integration.
"""

import json
import logging
import os
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class ProcessingSession:
    """Data class for processing session information."""

    session_id: str
    created_at: str
    last_updated: str
    session_name: str
    input_directory: str
    output_directory: str
    processing_mode: str  # 'batch', 'live', 'analysis'
    parameters: Dict[str, Any]
    series_data: Dict[str, Any]
    quality_metrics: Dict[str, Any]
    processing_status: str
    completed_series: List[str]
    failed_series: List[str]
    total_series: int
    progress_percentage: float
    estimated_completion: Optional[str]
    notes: str


class SessionManager:
    """Manages AreTomo3 GUI sessions with automatic saving and web integration."""

    def __init__(self, session_dir: Optional[str] = None):
        """Initialize session manager."""
        self.session_dir = (
            Path(session_dir)
            if session_dir
            else Path.home() / ".aretomo3_gui" / "sessions"
        )
        self.session_dir.mkdir(parents=True, exist_ok=True)

        self.current_session: Optional[ProcessingSession] = None
        self.auto_save_interval = 30  # seconds
        self.auto_save_thread = None
        self.auto_save_enabled = True
        self.session_history: List[ProcessingSession] = []

        # Load session history
        self.load_session_history()

        logger.info(
            f"Session manager initialized with directory: {
                self.session_dir}"
        )

    def create_new_session(
        self,
        session_name: str,
        processing_mode: str,
        input_dir: str = "",
        output_dir: str = "",
    ) -> ProcessingSession:
        """Create a new processing session."""
        session_id = f"session_{int(time.time())}_{processing_mode}"
        current_time = datetime.now().isoformat()

        session = ProcessingSession(
            session_id=session_id,
            created_at=current_time,
            last_updated=current_time,
            session_name=session_name,
            input_directory=input_dir,
            output_directory=output_dir,
            processing_mode=processing_mode,
            parameters={},
            series_data={},
            quality_metrics={},
            processing_status="initialized",
            completed_series=[],
            failed_series=[],
            total_series=0,
            progress_percentage=0.0,
            estimated_completion=None,
            notes="",
        )

        self.current_session = session
        self.save_session(session)
        self.start_auto_save()

        logger.info(f"Created new session: {session_id}")
        return session

    def load_session(self, session_id: str) -> Optional[ProcessingSession]:
        """Load a session from file."""
        try:
            session_file = self.session_dir / f"{session_id}.json"
            if not session_file.exists():
                logger.warning(f"Session file not found: {session_file}")
                return None

            with open(session_file, "r") as f:
                session_data = json.load(f)

            session = ProcessingSession(**session_data)
            self.current_session = session

            logger.info(f"Loaded session: {session_id}")
            return session

        except Exception as e:
            logger.error(f"Error loading session {session_id}: {e}")
            return None

    def save_session(self, session: Optional[ProcessingSession] = None) -> bool:
        """Save session to file."""
        try:
            if session is None:
                session = self.current_session

            if session is None:
                logger.warning("No session to save")
                return False

            # Update last_updated timestamp
            session.last_updated = datetime.now().isoformat()

            session_file = self.session_dir / f"{session.session_id}.json"
            with open(session_file, "w") as f:
                json.dump(asdict(session), f, indent=2)

            # Update session history
            self.update_session_history(session)

            logger.debug(f"Saved session: {session.session_id}")
            return True

        except Exception as e:
            logger.error(f"Error saving session: {e}")
            return False

    def update_session(self, **kwargs) -> bool:
        """Update current session with new data."""
        if self.current_session is None:
            logger.warning("No current session to update")
            return False

        try:
            for key, value in kwargs.items():
                if hasattr(self.current_session, key):
                    setattr(self.current_session, key, value)
                else:
                    logger.warning(f"Unknown session attribute: {key}")

            return self.save_session()

        except Exception as e:
            logger.error(f"Error updating session: {e}")
            return False

    def get_session_for_web(self) -> Dict[str, Any]:
        """Get session data formatted for web interface."""
        if self.current_session is None:
            return {"session_active": False, "message": "No active session"}

        return {
            "session_active": True,
            "session_id": self.current_session.session_id,
            "session_name": self.current_session.session_name,
            "created_at": self.current_session.created_at,
            "last_updated": self.current_session.last_updated,
            "processing_mode": self.current_session.processing_mode,
            "input_directory": self.current_session.input_directory,
            "output_directory": self.current_session.output_directory,
            "processing_status": self.current_session.processing_status,
            "progress_percentage": self.current_session.progress_percentage,
            "total_series": self.current_session.total_series,
            "completed_series": len(self.current_session.completed_series),
            "failed_series": len(self.current_session.failed_series),
            "estimated_completion": self.current_session.estimated_completion,
            "quality_summary": self.get_quality_summary(),
            "recent_activity": self.get_recent_activity(),
        }

    def get_quality_summary(self) -> Dict[str, Any]:
        """Get quality metrics summary for web interface."""
        if not self.current_session or not self.current_session.quality_metrics:
            return {"total": 0, "good": 0, "fair": 0, "poor": 0}

        quality_counts = {"good": 0, "fair": 0, "poor": 0}

        for series_name, metrics in self.current_session.quality_metrics.items():
            overall_quality = metrics.get("overall_quality", "poor").lower()
            if overall_quality in quality_counts:
                quality_counts[overall_quality] += 1

        return {"total": sum(quality_counts.values()), **quality_counts}

    def get_recent_activity(self) -> List[Dict[str, str]]:
        """Get recent activity for web interface."""
        activities = []

        if self.current_session:
            # Add recent completed series
            for series in self.current_session.completed_series[-5:]:
                activities.append(
                    {
                        "timestamp": self.current_session.last_updated,
                        "type": "completion",
                        "message": f"Completed processing: {series}",
                    }
                )

            # Add recent failed series
            for series in self.current_session.failed_series[-3:]:
                activities.append(
                    {
                        "timestamp": self.current_session.last_updated,
                        "type": "error",
                        "message": f"Failed processing: {series}",
                    }
                )

        return sorted(activities, key=lambda x: x["timestamp"], reverse=True)[:10]

    def start_auto_save(self):
        """Start automatic session saving."""
        if self.auto_save_thread and self.auto_save_thread.is_alive():
            return

        self.auto_save_enabled = True
        self.auto_save_thread = threading.Thread(
            target=self._auto_save_worker, daemon=True
        )
        self.auto_save_thread.start()

        logger.info("Auto-save started")

    def stop_auto_save(self):
        """Stop automatic session saving."""
        self.auto_save_enabled = False
        if self.auto_save_thread:
            self.auto_save_thread.join(timeout=5)

        logger.info("Auto-save stopped")

    def _auto_save_worker(self):
        """Worker thread for automatic session saving."""
        while self.auto_save_enabled:
            try:
                if self.current_session:
                    self.save_session()
                time.sleep(self.auto_save_interval)
            except Exception as e:
                logger.error(f"Error in auto-save worker: {e}")
                time.sleep(self.auto_save_interval)

    def load_session_history(self):
        """Load session history from files."""
        try:
            session_files = list(self.session_dir.glob("session_*.json"))
            self.session_history = []

            for session_file in session_files:
                try:
                    with open(session_file, "r") as f:
                        session_data = json.load(f)
                    session = ProcessingSession(**session_data)
                    self.session_history.append(session)
                except Exception as e:
                    logger.warning(f"Could not load session file {session_file}: {e}")

            # Sort by creation time
            self.session_history.sort(key=lambda x: x.created_at, reverse=True)

            logger.info(f"Loaded {len(self.session_history)} sessions from history")

        except Exception as e:
            logger.error(f"Error loading session history: {e}")

    def update_session_history(self, session: ProcessingSession):
        """Update session in history list."""
        # Remove existing session with same ID
        self.session_history = [
            s for s in self.session_history if s.session_id != session.session_id
        ]

        # Add updated session
        self.session_history.insert(0, session)

        # Keep only last 50 sessions
        self.session_history = self.session_history[:50]

    def get_session_list(self) -> List[Dict[str, Any]]:
        """Get list of all sessions for web interface."""
        return [
            {
                "session_id": session.session_id,
                "session_name": session.session_name,
                "created_at": session.created_at,
                "last_updated": session.last_updated,
                "processing_mode": session.processing_mode,
                "processing_status": session.processing_status,
                "total_series": session.total_series,
                "progress_percentage": session.progress_percentage,
            }
            for session in self.session_history
        ]

    def cleanup_old_sessions(self, days_old: int = 30):
        """Clean up sessions older than specified days."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            for session_file in self.session_dir.glob("session_*.json"):
                try:
                    with open(session_file, "r") as f:
                        session_data = json.load(f)

                    created_at = datetime.fromisoformat(
                        session_data.get("created_at", "")
                    )
                    if created_at < cutoff_date:
                        session_file.unlink()
                        logger.info(
                            f"Cleaned up old session: {
                                session_file.name}"
                        )

                except Exception as e:
                    logger.warning(f"Error processing session file {session_file}: {e}")

            # Reload session history
            self.load_session_history()

        except Exception as e:
            logger.error(f"Error cleaning up old sessions: {e}")
