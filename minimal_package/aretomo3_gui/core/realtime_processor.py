#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
realtime_processor.py - AreTomo3 GUI Professional Edition

Part of the AreTomo3 GUI suite for tomographic reconstruction.
This file implements real-time processing capabilities.

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License
"""

"""
Real-time processing system for AreTomo3 GUI.
Monitors directories for new files and processes them automatically.
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import numpy as np
from PyQt6.QtCore import QObject, QThread, QTimer, pyqtSignal
from watchdog.events import FileCreatedEvent, FileSystemEventHandler
from watchdog.observers import Observer

logger = logging.getLogger(__name__)


@dataclass
class ProcessingJob:
    """Represents a single processing job."""

    file_path: Path
    job_id: str
    created_at: datetime
    status: str = "pending"  # pending, processing, completed, failed
    progress: float = 0.0
    result_path: Optional[Path] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProcessingStats:
    """Real-time processing statistics."""

    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    avg_processing_time: float = 0.0
    throughput_per_hour: float = 0.0
    queue_size: int = 0
    active_jobs: int = 0


class RealTimeFileHandler(FileSystemEventHandler):
    """Handles file system events for real-time processing."""

    def __init__(self, processor: "RealTimeProcessor"):
        """Initialize the instance."""
        super().__init__()
        self.processor = processor
        self.supported_extensions = {".mrc", ".eer", ".tif", ".tiff"}
        self.min_file_size = 1024  # Minimum file size in bytes

    def on_created(self, event):
        """Handle file creation events."""
        if event.is_directory:
            return

        file_path = Path(event.src_path)

        # Check if file extension is supported
        if file_path.suffix.lower() not in self.supported_extensions:
            return

        # Wait for file to be completely written
        asyncio.create_task(self._wait_and_queue_file(file_path))

    async def _wait_and_queue_file(self, file_path: Path):
        """Wait for file to be completely written, then queue for processing."""
        max_wait_time = 30  # Maximum wait time in seconds
        check_interval = 0.5  # Check every 0.5 seconds

        start_time = time.time()
        last_size = 0
        stable_count = 0

        while time.time() - start_time < max_wait_time:
            try:
                if not file_path.exists():
                    await asyncio.sleep(check_interval)
                    continue

                current_size = file_path.stat().st_size

                if current_size < self.min_file_size:
                    await asyncio.sleep(check_interval)
                    continue

                # Check if file size is stable (not growing)
                if current_size == last_size:
                    stable_count += 1
                    if stable_count >= 3:  # File stable for 1.5 seconds
                        break
                else:
                    stable_count = 0
                    last_size = current_size

                await asyncio.sleep(check_interval)

            except (OSError, PermissionError):
                # File might still be locked, wait more
                await asyncio.sleep(check_interval)
                continue

        # Queue file for processing
        if file_path.exists() and file_path.stat().st_size >= self.min_file_size:
            await self.processor.queue_file(file_path)
        else:
            logger.warning(f"File not ready for processing: {file_path}")


class RealTimeProcessor(QObject):
    """Real-time processing engine for AreTomo3."""

    # Signals for GUI updates
    job_queued = pyqtSignal(str)  # job_id
    job_started = pyqtSignal(str)  # job_id
    job_progress = pyqtSignal(str, float)  # job_id, progress
    job_completed = pyqtSignal(str, str)  # job_id, result_path
    job_failed = pyqtSignal(str, str)  # job_id, error_message
    stats_updated = pyqtSignal(object)  # ProcessingStats

    def __init__(self, watch_directories: List[Path], output_directory: Path):
        """Initialize the instance."""
        super().__init__()
        self.watch_directories = watch_directories
        self.output_directory = output_directory
        self.output_directory.mkdir(parents=True, exist_ok=True)

        # Processing state
        self.jobs: Dict[str, ProcessingJob] = {}
        self.processing_queue = asyncio.Queue()
        self.is_running = False
        self.max_concurrent_jobs = 2
        self.active_jobs = set()

        # Statistics
        self.stats = ProcessingStats()
        self.processing_times = []

        # File monitoring
        self.observer = Observer()
        self.file_handlers = []

        # Setup monitoring
        self._setup_file_monitoring()

        # Stats update timer
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self._update_stats)
        self.stats_timer.start(1000)  # Update every second

    def _setup_file_monitoring(self):
        """Setup file system monitoring for watch directories."""
        for watch_dir in self.watch_directories:
            if watch_dir.exists():
                handler = RealTimeFileHandler(self)
                self.file_handlers.append(handler)
                self.observer.schedule(handler, str(watch_dir), recursive=True)
                logger.info(f"Monitoring directory: {watch_dir}")
            else:
                logger.warning(f"Watch directory does not exist: {watch_dir}")

    async def start_processing(self):
        """Start the real-time processing system."""
        if self.is_running:
            logger.warning("Real-time processor already running")
            return

        self.is_running = True
        logger.info("Starting real-time processor")

        # Start file monitoring
        self.observer.start()

        # Start processing workers
        workers = [
            asyncio.create_task(self._processing_worker(f"worker-{i}"))
            for i in range(self.max_concurrent_jobs)
        ]

        try:
            await asyncio.gather(*workers)
        except Exception as e:
            logger.error(f"Error in processing workers: {e}")
        finally:
            self.observer.stop()
            self.observer.join()

    async def stop_processing(self):
        """Stop the real-time processing system."""
        logger.info("Stopping real-time processor")
        self.is_running = False

        # Stop file monitoring
        self.observer.stop()
        self.observer.join()

        # Wait for active jobs to complete
        while self.active_jobs:
            await asyncio.sleep(0.1)

    async def queue_file(self, file_path: Path):
        """Queue a file for processing."""
        job_id = f"{file_path.stem}_{int(time.time() * 1000)}"

        job = ProcessingJob(
            file_path=file_path, job_id=job_id, created_at=datetime.now()
        )

        self.jobs[job_id] = job
        await self.processing_queue.put(job)

        self.stats.total_files += 1
        self.stats.queue_size += 1

        self.job_queued.emit(job_id)
        logger.info(f"Queued file for processing: {file_path}")

    async def _processing_worker(self, worker_name: str):
        """Worker coroutine for processing files."""
        logger.info(f"Starting processing worker: {worker_name}")

        while self.is_running:
            try:
                # Get job from queue
                job = await asyncio.wait_for(self.processing_queue.get(), timeout=1.0)

                # Process the job
                await self._process_job(job, worker_name)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error in worker {worker_name}: {e}")

    async def _process_job(self, job: ProcessingJob, worker_name: str):
        """Process a single job."""
        job.status = "processing"
        self.active_jobs.add(job.job_id)
        self.stats.queue_size -= 1
        self.stats.active_jobs += 1

        self.job_started.emit(job.job_id)
        logger.info(f"Worker {worker_name} processing: {job.file_path}")

        start_time = time.time()

        try:
            # Simulate processing with progress updates
            result_path = await self._run_aretomo3_processing(job)

            # Job completed successfully
            processing_time = time.time() - start_time
            job.status = "completed"
            job.result_path = result_path
            job.processing_time = processing_time
            job.progress = 100.0

            self.processing_times.append(processing_time)
            self.stats.processed_files += 1

            self.job_completed.emit(job.job_id, str(result_path))
            logger.info(
                f"Completed processing: {
                    job.file_path} in {
                    processing_time:.2f}s"
            )

        except Exception as e:
            # Job failed
            job.status = "failed"
            job.error_message = str(e)
            job.progress = 0.0

            self.stats.failed_files += 1

            self.job_failed.emit(job.job_id, str(e))
            logger.error(f"Failed to process {job.file_path}: {e}")

        finally:
            self.active_jobs.remove(job.job_id)
            self.stats.active_jobs -= 1

    async def _run_aretomo3_processing(self, job: ProcessingJob) -> Path:
        """Run AreTomo3 processing on a file."""
        # This is a placeholder - integrate with actual AreTomo3 processing
        output_file = self.output_directory / f"{job.file_path.stem}_processed.mrc"

        # Simulate processing with progress updates
        for progress in range(0, 101, 10):
            job.progress = progress
            self.job_progress.emit(job.job_id, progress)
            await asyncio.sleep(0.1)  # Simulate processing time

        # Create dummy output file for demonstration
        output_file.touch()

        return output_file

    def _update_stats(self):
        """Update processing statistics."""
        if self.processing_times:
            self.stats.avg_processing_time = np.mean(
                self.processing_times[-100:]
            )  # Last 100 jobs
            self.stats.throughput_per_hour = (
                3600 / self.stats.avg_processing_time
                if self.stats.avg_processing_time > 0
                else 0
            )

        self.stats_updated.emit(self.stats)

    def get_job_status(self, job_id: str) -> Optional[ProcessingJob]:
        """Get status of a specific job."""
        return self.jobs.get(job_id)

    def get_all_jobs(self) -> List[ProcessingJob]:
        """Get all jobs."""
        return list(self.jobs.values())

    def get_stats(self) -> ProcessingStats:
        """Get current processing statistics."""
        return self.stats
