#!/usr/bin/env python3
"""
Thread Management Module
=======================

This module provides a robust thread management system for handling
background tasks, progress monitoring, and resource management.

Features:
    - Dynamic thread pool sizing
    - Priority-based task execution
    - Progress reporting via Qt signals
    - Automatic error handling and recovery
    - Resource cleanup on task completion

Example usage:
    from core.thread_manager import ThreadManager, Task, TaskPriority

    manager = ThreadManager()
    task = Task(
        func=process_data,
        args=(data,),
        priority=TaskPriority.HIGH,
        on_complete=handle_result
    )
    manager.submit_task(task)
    manager.progress_updated.connect(update_progress_bar)

Classes:
    ThreadManager: Main thread pool manager
    Task: Represents a background task
    TaskPriority: Priority levels for tasks
    TaskStatus: Task execution status

Author: AreTomo3 GUI Team
Date: May 2025
"""

# Standard library imports
import asyncio
import logging
import queue
import threading
import time
import traceback
import uuid
from concurrent.futures import Future, ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum
from queue import Empty, PriorityQueue
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Qt imports
from PyQt6.QtCore import QObject, pyqtSignal

# Local imports
from .error_handling import AreTomo3Error, ErrorSeverity, ProcessingError, try_operation

# Logger Configuration
logger = logging.getLogger("thread_manager")

# =============================================================================
# Enumerations and Data Classes
# =============================================================================


class TaskPriority(Enum):
    """Priority levels for thread pool tasks."""

    HIGH = 0  # Critical tasks that need immediate execution
    NORMAL = 1  # Standard tasks with no special priority
    LOW = 2  # Background tasks that can wait


class TaskStatus(Enum):
    """Status indicators for task lifecycle."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """
    Represents a task to be executed by the thread pool.

    This class encapsulates all information needed to execute
    and manage a background task including function, arguments,
    priority level, callbacks, status tracking, and progress reporting.

    Attributes:
        id (str): Unique task identifier
        priority (TaskPriority): Execution priority
        status (TaskStatus): Current execution status
        progress (float): Execution progress (0-1)
        result: Task result after completion
        error: Exception if task failed
    """

    def __init__(
        self,
        func: Callable[..., Any],
        args: Tuple = (),
        kwargs: Dict = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        task_id: Optional[str] = None,
        on_complete: Optional[Callable] = None,
        on_error: Optional[Callable] = None,
    ):
        """
        Initialize a new task for background execution.

        Args:
            func: Function to execute in background
            args: Positional arguments for the function
            kwargs: Keyword arguments for the function
            priority: Task execution priority
            task_id: Optional unique identifier (generated if None)
            on_complete: Callback for successful completion
            on_error: Callback for error handling
        """
        self.func = func
        self.args = args
        self.kwargs = kwargs or {}
        self.priority = priority
        self.id = task_id or f"{func.__name__}_{uuid.uuid4().hex[:8]}"
        self.on_complete = on_complete
        self.on_error = on_error

        # Runtime state
        self.status = TaskStatus.PENDING
        self.progress = 0.0
        self.result = None
        self.error = None
        self.start_time = None
        self.end_time = None
        self._lock = threading.Lock()

    def __lt__(self, other):
        """
        Compare tasks for priority queue ordering.

        Args:
            other: Another task to compare with

        Returns:
            bool: True if this task has higher priority
        """
        if not isinstance(other, Task):
            return NotImplemented
        return self.priority.value < other.priority.value

    def update_progress(self, progress: float) -> None:
        """
        Update task progress.

        Args:
            progress: Progress value between 0 and 1
        """
        with self._lock:
            self.progress = max(0.0, min(1.0, progress))

    def run(self) -> Any:
        """
        Execute the task function with error handling.

        Returns:
            Any: Result of the task function

        Raises:
            Any exception from the task function
        """
        self.status = TaskStatus.RUNNING
        self.start_time = time.time()

        try:
            result = self.func(*self.args, **self.kwargs)
            self.result = result
            self.status = TaskStatus.COMPLETED
            self.progress = 1.0
            self.end_time = time.time()

            if self.on_complete:
                self.on_complete(result)

            return result

        except Exception as e:
            self.error = e
            self.status = TaskStatus.FAILED
            self.end_time = time.time()

            logger.error(f"Task {self.id} failed: {e}")
            logger.debug(f"Task traceback: {traceback.format_exc()}")

            if self.on_error:
                self.on_error(e)

            raise


# =============================================================================
# Worker Thread Classes
# =============================================================================


class WorkerSignals(QObject):
    """Signals emitted by worker threads"""

    started = pyqtSignal(str)  # task_id
    progress = pyqtSignal(str, float)  # task_id, progress
    completed = pyqtSignal(str, object)  # task_id, result
    error = pyqtSignal(str, Exception)  # task_id, exception


class WorkerThread(threading.Thread):
    """Worker thread that executes tasks from a queue"""

    def __init__(self, task_queue, worker_id: str, max_retries: int = 3):
        """
        Initialize a worker thread.

        Args:
            task_queue: Queue to get tasks from
            worker_id: Unique identifier for this worker
            max_retries: Maximum number of retries for failed tasks
        """
        super().__init__(name=f"Worker-{worker_id}")
        self.task_queue = task_queue
        self.worker_id = worker_id
        self.max_retries = max_retries
        self.signals = WorkerSignals()
        self._stop_event = threading.Event()
        self.daemon = True

    def run(self):
        """Main worker thread loop"""
        logger.info(f"Worker {self.worker_id} started")

        while not self._stop_event.is_set():
            try:
                # Get task with timeout to allow checking stop event
                task = self.task_queue.get(timeout=1.0)

                if task is None:  # Poison pill
                    break

                self.process_task(task)
                self.task_queue.task_done()

            except Empty:
                continue
            except Exception as e:
                logger.error(f"Worker {self.worker_id} error: {e}")

        logger.info(f"Worker {self.worker_id} stopped")

    def process_task(self, task: Task):
        """Process a single task with retries"""
        retries = 0

        while retries <= self.max_retries:
            try:
                self.signals.started.emit(task.id)
                result = task.run()
                self.signals.completed.emit(task.id, result)
                return

            except Exception as e:
                retries += 1
                if retries > self.max_retries:
                    self.signals.error.emit(task.id, e)
                    return
                else:
                    logger.warning(
                        f"Task {
                            task.id} failed (attempt {retries}), retrying..."
                    )
                    time.sleep(0.1 * retries)  # Exponential backoff

    def stop(self):
        """Signal the worker to stop"""
        self._stop_event.set()


# =============================================================================
# Thread Manager
# =============================================================================


class ThreadManager(QObject):
    """
    Main thread pool manager for executing tasks.

    This class provides a high-level interface for managing background
    tasks with priority queuing, progress monitoring, and error handling.

    Features include priority-based task scheduling, dynamic worker thread
    management, progress reporting via Qt signals, task cancellation support,
    and resource cleanup and monitoring.

    Signals:
        task_started: Emitted when a task begins execution
        task_progress: Emitted to report task progress
        task_completed: Emitted when a task finishes successfully
        task_failed: Emitted when a task encounters an error
    """

    # Qt signals for task lifecycle events
    task_started = pyqtSignal(str)  # task_id
    task_progress = pyqtSignal(str, float)  # task_id, progress
    task_completed = pyqtSignal(str, object)  # task_id, result
    task_failed = pyqtSignal(str, Exception)  # task_id, exception

    def __init__(self, max_workers: int = 4):
        """
        Initialize the thread manager.

        Args:
            max_workers: Maximum number of worker threads
        """
        super().__init__()
        self.max_workers = max_workers
        self.task_queue = PriorityQueue()
        self.workers = []
        self.active_tasks = {}
        self._lock = threading.Lock()
        self._shutdown_requested = False

        # Start worker threads
        self._start_workers()

        logger.info(f"ThreadManager initialized with {max_workers} workers")

    def _start_workers(self):
        """Start worker threads"""
        for i in range(self.max_workers):
            worker = WorkerThread(self.task_queue, f"worker-{i}")
            worker.signals.started.connect(self.task_started)
            worker.signals.progress.connect(self.task_progress)
            worker.signals.completed.connect(self._on_task_completed)
            worker.signals.error.connect(self._on_task_failed)
            worker.start()
            self.workers.append(worker)

    def submit_task(self, task: Task) -> str:
        """
        Submit a task for execution.

        Args:
            task: Task to execute

        Returns:
            str: Task ID for tracking
        """
        if self._shutdown_requested:
            raise RuntimeError("ThreadManager is shutting down")

        with self._lock:
            self.active_tasks[task.id] = task

        self.task_queue.put(task)
        logger.debug(f"Task {task.id} submitted with priority {task.priority}")

        return task.id

    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending task.

        Args:
            task_id: ID of task to cancel

        Returns:
            bool: True if task was cancelled
        """
        with self._lock:
            task = self.active_tasks.get(task_id)
            if task and task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                return True
        return False

    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        Get the status of a task.

        Args:
            task_id: ID of task to check

        Returns:
            TaskStatus or None if task not found
        """
        with self._lock:
            task = self.active_tasks.get(task_id)
            return task.status if task else None

    def _on_task_completed(self, task_id: str, result: Any):
        """Handle task completion"""
        with self._lock:
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

        self.task_completed.emit(task_id, result)
        logger.debug(f"Task {task_id} completed successfully")

    def _on_task_failed(self, task_id: str, error: Exception):
        """Handle task failure"""
        with self._lock:
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

        self.task_failed.emit(task_id, error)
        logger.error(f"Task {task_id} failed: {error}")

    def shutdown(self, wait: bool = True):
        """
        Shutdown the thread manager.

        Args:
            wait: Whether to wait for pending tasks to complete
        """
        logger.info("Shutting down ThreadManager")
        self._shutdown_requested = True

        # Stop accepting new tasks
        for _ in range(len(self.workers)):
            self.task_queue.put(None)  # Poison pill

        if wait:
            for worker in self.workers:
                worker.join(timeout=5.0)

        logger.info("ThreadManager shutdown complete")


# =============================================================================
# Singleton Access
# =============================================================================

_thread_manager_instance = None


def get_thread_manager() -> ThreadManager:
    """
    Get the singleton thread manager instance.

    Returns:
        ThreadManager: The global thread manager instance
    """
    global _thread_manager_instance
    if _thread_manager_instance is None:
        _thread_manager_instance = ThreadManager()
    return _thread_manager_instance
