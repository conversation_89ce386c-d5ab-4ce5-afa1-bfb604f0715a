# AreTomo3 GUI Production Manifest
# Specifies additional files to include in the distribution package

# Documentation
include README.md
include QUICK_START.md
include LICENSE
include CHANGELOG.md
recursive-include docs *.md *.rst *.txt *.png *.jpg *.svg

# Configuration files
include requirements.txt
include pyproject.toml
include pytest.ini
recursive-include config *.yaml *.yml *.json *.toml *.ini *.conf

# Web interface assets
recursive-include aretomo3_gui/web/static *.css *.js *.html *.png *.jpg *.svg *.ico
recursive-include aretomo3_gui/web/templates *.html *.jinja2

# GUI assets
recursive-include aretomo3_gui/gui/icons *.png *.svg *.ico
recursive-include aretomo3_gui/gui/styles *.qss *.css
recursive-include aretomo3_gui/gui/resources *.qrc *.ui

# Sample data and examples
recursive-include examples *.py *.md *.txt *.json *.yaml
recursive-include sample_data *.mrc *.tif *.dm4 *.mdoc *.txt

# Scripts and utilities
recursive-include scripts *.py *.sh *.bat *.ps1
include scripts/README.md

# Deployment configurations
recursive-include deployment *.yml *.yaml *.conf *.service *.sh *.md
recursive-include deployment/docker Dockerfile docker-compose*.yml
recursive-include deployment/kubernetes *.yaml
recursive-include deployment/nginx *.conf
recursive-include deployment/systemd *.service

# Test data (minimal)
recursive-include tests/data *.json *.yaml *.txt
include tests/conftest.py
include tests/README.md

# Exclude development and build artifacts
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .pytest_cache
global-exclude *.egg-info
global-exclude build
global-exclude dist
global-exclude .coverage
global-exclude htmlcov
global-exclude .tox
global-exclude .venv
global-exclude venv
global-exclude env
global-exclude .env
global-exclude node_modules
global-exclude *.log
global-exclude *.tmp
global-exclude *.temp
global-exclude *~
global-exclude .DS_Store
global-exclude Thumbs.db

# Exclude backup files
global-exclude *backup*
global-exclude *.bak
global-exclude *.orig

# Exclude IDE files
global-exclude .vscode
global-exclude .idea
global-exclude *.swp
global-exclude *.swo

# Exclude test artifacts
global-exclude test_*.py
global-exclude *_test.py
global-exclude test_output
global-exclude test_results
