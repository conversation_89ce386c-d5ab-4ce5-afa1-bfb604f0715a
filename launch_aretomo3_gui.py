#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AreTomo3 GUI Professional Edition Launcher v2.0.0

Professional launcher for the AreTomo3 GUI with comprehensive error handling,
logging, and professional configuration.

Features:
- Professional application initialization
- Comprehensive error handling and logging
- High DPI support
- Professional window configuration
- Graceful shutdown handling

Usage:
    python launch_aretomo3_gui.py

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License
"""

import sys
import os
import logging
from pathlib import Path
from typing import Optional
import traceback

# Add the project directory to Python path
project_root = Path(__file__).parent / "AT3GUI_working"
if project_root.exists():
    sys.path.insert(0, str(project_root))

# Configure professional logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('aretomo3_gui.log')
    ]
)
logger = logging.getLogger(__name__)


class AreTomo3Launcher:
    """Professional launcher for AreTomo3 GUI."""
    
    def __init__(self):
        """Initialize the launcher."""
        self.app: Optional['QApplication'] = None
        self.window: Optional['AreTomoGUI'] = None
        logger.info("AreTomo3 GUI Launcher initialized")
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available.
        
        Returns:
            bool: True if all dependencies are available, False otherwise
        """
        logger.info("🔍 Checking dependencies...")
        
        try:
            # Check PyQt6
            import PyQt6
            logger.info("✅ PyQt6 available")
        except ImportError:
            logger.error("❌ PyQt6 not available - please install with: pip install PyQt6")
            return False
        
        try:
            # Check AreTomo3 GUI package
            import aretomo3_gui
            logger.info("✅ AreTomo3 GUI package available")
        except ImportError:
            logger.error("❌ AreTomo3 GUI package not available")
            logger.error("   Please install with: pip install -e . (from project directory)")
            return False
        
        try:
            # Check GUI components
            from aretomo3_gui.gui.main_window import AreTomoGUI
            logger.info("✅ GUI components available")
        except ImportError as e:
            logger.error(f"❌ GUI components not available: {e}")
            return False
        
        logger.info("✅ All dependencies satisfied")
        return True
    
    def create_application(self) -> bool:
        """Create and configure the QApplication.
        
        Returns:
            bool: True if application created successfully, False otherwise
        """
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            
            # Create application
            self.app = QApplication(sys.argv)
            
            # Configure application
            self.app.setApplicationName("AreTomo3 GUI Professional")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("AreTomo3 GUI Development Team")
            self.app.setOrganizationDomain("aretomo3-gui.org")
            
            # Enable high DPI support (Qt6 handles this automatically)
            try:
                # Only set these attributes if they exist (Qt5 compatibility)
                if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
                    self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
                if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
                    self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
            except AttributeError:
                # Qt6 handles high DPI automatically, no action needed
                pass
            
            logger.info("✅ QApplication created and configured")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create QApplication: {e}")
            return False
    
    def create_main_window(self) -> bool:
        """Create and configure the main window.
        
        Returns:
            bool: True if window created successfully, False otherwise
        """
        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            
            # Create main window
            self.window = AreTomoGUI()
            
            # Configure window
            self.window.setWindowTitle("AreTomo3 GUI Professional Edition v2.0.0")
            
            # Set window icon if available
            icon_path = project_root / "aretomo3_gui" / "gui" / "icons" / "app_icon.png"
            if icon_path.exists():
                from PyQt6.QtGui import QIcon
                self.window.setWindowIcon(QIcon(str(icon_path)))
            
            logger.info("✅ Main window created and configured")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create main window: {e}")
            logger.error(f"Error details: {traceback.format_exc()}")
            return False
    
    def launch_application(self) -> int:
        """Launch the AreTomo3 GUI application.
        
        Returns:
            int: Exit code (0 for success, 1 for error)
        """
        logger.info("🚀 Launching AreTomo3 GUI Professional Edition v2.0.0")
        logger.info("=" * 60)
        
        try:
            # Check dependencies
            if not self.check_dependencies():
                logger.error("❌ Dependency check failed")
                return 1
            
            # Create application
            if not self.create_application():
                logger.error("❌ Application creation failed")
                return 1
            
            # Create main window
            if not self.create_main_window():
                logger.error("❌ Main window creation failed")
                return 1
            
            # Show window
            self.window.show()
            logger.info("✅ AreTomo3 GUI window displayed")
            
            # Log successful launch
            logger.info("🎉 AreTomo3 GUI Professional Edition launched successfully!")
            logger.info("=" * 60)
            
            # Run application event loop
            exit_code = self.app.exec()
            
            logger.info(f"Application exited with code: {exit_code}")
            return exit_code
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user (Ctrl+C)")
            return 0
        except Exception as e:
            logger.error(f"❌ Unexpected error during launch: {e}")
            logger.error(f"Error details: {traceback.format_exc()}")
            return 1
        finally:
            logger.info("Application cleanup completed")
    
    def show_usage_info(self):
        """Show usage information."""
        print("""
🚀 AreTomo3 GUI Professional Edition Launcher v2.0.0
====================================================

This launcher provides professional startup for the AreTomo3 GUI with:
- Comprehensive dependency checking
- Professional error handling and logging
- High DPI support
- Graceful shutdown handling

Usage:
    python launch_aretomo3_gui.py

Requirements:
- Python 3.8+
- PyQt6
- AreTomo3 GUI package (installed)

For installation help:
    pip install PyQt6
    pip install -e . (from AreTomo3 GUI directory)

Logs are saved to: aretomo3_gui.log

Copyright (c) 2025 AreTomo3 GUI Development Team
""")


def main() -> int:
    """Main entry point for the launcher.
    
    Returns:
        int: Exit code (0 for success, 1 for error)
    """
    # Check for help flag
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        launcher = AreTomo3Launcher()
        launcher.show_usage_info()
        return 0
    
    # Launch application
    launcher = AreTomo3Launcher()
    return launcher.launch_application()


if __name__ == "__main__":
    sys.exit(main())
