#!/usr/bin/env python3
"""
Find ALL syntax errors in the codebase systematically.
"""

import ast
import os
import sys
from pathlib import Path

def find_all_syntax_errors():
    """Find all syntax errors in Python files."""
    print("🔍 SYSTEMATIC SYNTAX ERROR DETECTION")
    print("=" * 40)
    
    project_root = Path.cwd()
    python_files = list(project_root.rglob("aretomo3_gui/**/*.py"))
    
    print(f"📁 Checking {len(python_files)} Python files...")
    print()
    
    syntax_errors = []
    checked_files = 0
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to parse the file
            ast.parse(content, filename=str(py_file))
            checked_files += 1
            print(f"✅ {py_file.relative_to(project_root)}")
            
        except SyntaxError as e:
            syntax_errors.append((py_file, e))
            print(f"❌ {py_file.relative_to(project_root)}: {e}")
            
        except UnicodeDecodeError as e:
            syntax_errors.append((py_file, f"Encoding error: {e}"))
            print(f"❌ {py_file.relative_to(project_root)}: Encoding error: {e}")
            
        except Exception as e:
            syntax_errors.append((py_file, f"Unexpected error: {e}"))
            print(f"❌ {py_file.relative_to(project_root)}: Unexpected error: {e}")
    
    print()
    print("=" * 40)
    print("📊 SYNTAX CHECK RESULTS")
    print("=" * 40)
    print(f"✅ Files checked: {checked_files}")
    print(f"❌ Files with errors: {len(syntax_errors)}")
    
    if syntax_errors:
        print()
        print("🔧 FILES THAT NEED FIXING:")
        print("-" * 30)
        for file_path, error in syntax_errors:
            print(f"📄 {file_path.relative_to(project_root)}")
            print(f"   Error: {error}")
            print()
        
        return False
    else:
        print()
        print("🎉 ALL FILES HAVE VALID SYNTAX!")
        return True

if __name__ == "__main__":
    success = find_all_syntax_errors()
    sys.exit(0 if success else 1)
