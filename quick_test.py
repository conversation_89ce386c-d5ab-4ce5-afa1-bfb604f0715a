#!/usr/bin/env python3
"""
Quick test script for AreTomo3 GUI package.
"""

import os
import sys

# Set Qt environment
os.environ['QT_API'] = 'pyqt6'
os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'

def test_import():
    """Test basic import."""
    try:
        import aretomo3_gui
        print("✅ Package import: SUCCESS")
        return True
    except Exception as e:
        print(f"❌ Package import: FAILED - {e}")
        return False

def test_dependencies():
    """Test key dependencies."""
    deps = ['numpy', 'matplotlib', 'PyQt6.QtWidgets']
    for dep in deps:
        try:
            __import__(dep)
            print(f"✅ {dep}: OK")
        except ImportError:
            print(f"❌ {dep}: MISSING")

def test_entry_point():
    """Test entry point."""
    import subprocess
    try:
        result = subprocess.run([
            sys.executable, '-c', 
            'import os; os.environ["QT_API"]="pyqt6"; from aretomo3_gui.main import main; print("Entry point OK")'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ Entry point: SUCCESS")
            return True
        else:
            print(f"❌ Entry point: FAILED - {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Entry point: ERROR - {e}")
        return False

if __name__ == "__main__":
    print("🧪 AreTomo3 GUI Quick Test")
    print("=" * 30)
    
    success = True
    success &= test_import()
    test_dependencies()
    success &= test_entry_point()
    
    print("=" * 30)
    if success:
        print("🎉 QUICK TEST PASSED!")
        print("Package is ready for use.")
    else:
        print("💥 QUICK TEST FAILED!")
        print("Check installation.")
    
    sys.exit(0 if success else 1)
