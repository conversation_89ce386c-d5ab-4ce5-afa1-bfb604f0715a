#!/usr/bin/env python3
"""
Create a complete distribution package for AreTomo3 GUI.
"""

import os
import shutil
import zipfile
from pathlib import Path
import datetime


def create_distribution_package():
    """Create a complete distribution package."""
    print("📦 Creating AreTomo3 GUI Distribution Package")
    print("=" * 50)
    
    # Create distribution directory
    dist_name = f"aretomo3_gui_distribution_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    dist_dir = Path(dist_name)
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    print(f"📁 Created distribution directory: {dist_dir}")
    
    # Files to include in distribution
    files_to_include = [
        # Main distribution
        "dist/aretomo3_gui-1.0.0-py3-none-any.whl",
        
        # Launcher and utilities
        "aretomo3_gui_launcher.py",
        "fix_qt_conflicts.py",
        
        # Verification scripts
        "test_basic_functionality.py",
        "quick_test.py",
        "final_verification.py",
        
        # Documentation
        "INSTALLATION_GUIDE.md",
        "PACKAGE_SUMMARY.md",
        "README.md",
        
        # Configuration files
        "requirements.txt",
        "setup.py",
        "pyproject.toml",
    ]
    
    # Copy files
    copied_files = []
    missing_files = []
    
    for file_path in files_to_include:
        src = Path(file_path)
        if src.exists():
            dst = dist_dir / src.name
            if src.is_file():
                shutil.copy2(src, dst)
                copied_files.append(file_path)
                print(f"✅ Copied: {file_path}")
            else:
                print(f"⚠️  Skipped directory: {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ Missing: {file_path}")
    
    # Create installation script
    install_script = dist_dir / "install.py"
    install_content = '''#!/usr/bin/env python3
"""
AreTomo3 GUI Installation Script
"""

import subprocess
import sys
import os

def main():
    print("🚀 AreTomo3 GUI Installation")
    print("=" * 40)
    
    # Set Qt environment
    os.environ['QT_API'] = 'pyqt6'
    os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'
    
    try:
        # Install the wheel
        print("📦 Installing AreTomo3 GUI...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "aretomo3_gui-1.0.0-py3-none-any.whl"
        ], check=True)
        
        print("✅ Installation completed!")
        print("\\n🧪 Running verification...")
        
        # Run quick test
        subprocess.run([sys.executable, "quick_test.py"], check=True)
        
        print("\\n🎉 Installation successful!")
        print("\\nUsage:")
        print("  python aretomo3_gui_launcher.py")
        print("  QT_API=pyqt6 aretomo3-gui")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        print("\\nTroubleshooting:")
        print("  1. Check Python version (3.8+ required)")
        print("  2. Ensure pip is up to date")
        print("  3. Check system requirements")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    with open(install_script, 'w') as f:
        f.write(install_content)
    os.chmod(install_script, 0o755)
    print(f"✅ Created: {install_script}")
    
    # Create README for distribution
    dist_readme = dist_dir / "README_DISTRIBUTION.md"
    readme_content = f'''# AreTomo3 GUI Distribution Package

## Quick Start

1. **Install the package:**
   ```bash
   python install.py
   ```

2. **Launch the application:**
   ```bash
   python aretomo3_gui_launcher.py
   ```

3. **Verify installation:**
   ```bash
   python quick_test.py
   ```

## Files Included

### Core Distribution
- `aretomo3_gui-1.0.0-py3-none-any.whl` - Main package wheel
- `install.py` - Automated installation script
- `aretomo3_gui_launcher.py` - Application launcher

### Verification Tools
- `quick_test.py` - Quick functionality test
- `test_basic_functionality.py` - Core functionality tests
- `final_verification.py` - Comprehensive verification
- `fix_qt_conflicts.py` - Qt conflict resolution

### Documentation
- `INSTALLATION_GUIDE.md` - Detailed installation guide
- `PACKAGE_SUMMARY.md` - Package overview and features
- `README_DISTRIBUTION.md` - This file

### Configuration
- `requirements.txt` - Python dependencies
- `setup.py` - Package setup script
- `pyproject.toml` - Project configuration

## System Requirements

- Python 3.8 or higher
- 4GB RAM (8GB recommended)
- 2GB free disk space
- Graphics card with OpenGL support (recommended)

## Installation Methods

### Method 1: Automated (Recommended)
```bash
python install.py
```

### Method 2: Manual
```bash
pip install aretomo3_gui-1.0.0-py3-none-any.whl
export QT_API=pyqt6
aretomo3-gui --version
```

### Method 3: Using Launcher
```bash
pip install aretomo3_gui-1.0.0-py3-none-any.whl
python aretomo3_gui_launcher.py
```

## Troubleshooting

If you encounter issues:

1. **Run the Qt conflict fixer:**
   ```bash
   python fix_qt_conflicts.py
   ```

2. **Check installation:**
   ```bash
   python final_verification.py
   ```

3. **Manual Qt setup:**
   ```bash
   export QT_API=pyqt6
   export NAPARI_QT_BACKEND=pyqt6
   ```

## Support

For detailed troubleshooting, see `INSTALLATION_GUIDE.md`.

---
Package created: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Distribution ready for deployment on fresh systems.
'''
    
    with open(dist_readme, 'w') as f:
        f.write(readme_content)
    print(f"✅ Created: {dist_readme}")
    
    # Create archive
    archive_name = f"{dist_name}.zip"
    print(f"🗜️  Creating archive: {archive_name}")
    
    with zipfile.ZipFile(archive_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in dist_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(dist_dir.parent)
                zipf.write(file_path, arcname)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Distribution Package Summary")
    print(f"✅ Files copied: {len(copied_files)}")
    print(f"❌ Files missing: {len(missing_files)}")
    print(f"📁 Distribution directory: {dist_dir}")
    print(f"📦 Archive created: {archive_name}")
    
    if missing_files:
        print(f"\n⚠️  Missing files: {missing_files}")
    
    print("\n🎉 Distribution package created successfully!")
    print(f"📤 Ready for distribution: {archive_name}")
    print("\n📋 Next steps:")
    print("1. Test the distribution on a fresh system")
    print("2. Extract the archive and run: python install.py")
    print("3. Verify with: python quick_test.py")
    
    return dist_dir, archive_name


if __name__ == "__main__":
    create_distribution_package()
