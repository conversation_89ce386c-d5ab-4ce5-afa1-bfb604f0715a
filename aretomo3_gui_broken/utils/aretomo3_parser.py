#!/usr/bin/env python3
"""
AreTomo3 output file parser and plot generator.
Automatically parses AreTomo3 output files and generates analysis plots.
"""

import json
import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np

logger = logging.getLogger(__name__)


# TODO: Refactor class - Class 'AreTomo3ResultsParser' too long (1400 lines)
class AreTomo3ResultsParser:
    """Parser for AreTomo3 output files and automatic plot generation."""

    def __init__(self, results_directory: str):
        """Initialize the parser with results directory."""
        self.results_dir = Path(results_directory)
        self.parsed_data = {}
        self.plots_generated = {}

    def parse_all_results(self) -> Dict[str, Any]:
        """Parse all AreTomo3 results in the directory."""
        try:
            logger.info(f"Parsing AreTomo3 results in: {self.results_dir}")

            # Find all result files
            result_files = self._find_result_files()

            # Parse each type of result file
            self.parsed_data = {
                "motion_data": self._parse_motion_files(result_files.get("motion", [])),
                "ctf_data": self._parse_ctf_files(result_files.get("ctf", [])),
                "alignment_data": self._parse_alignment_files(
                    result_files.get("alignment", [])
                ),
                "log_data": self._parse_log_files(result_files.get("log", [])),
                "tomogram_files": result_files.get("tomograms", []),
                "summary": self._generate_summary(),
            }

            logger.info(
                f"Successfully parsed {
                    len(result_files)} result file types"
            )
            return self.parsed_data

        except Exception as e:
            logger.error(f"Error parsing AreTomo3 results: {e}")
            return {}

    def _find_result_files(self) -> Dict[str, List[Path]]:
        """Find all AreTomo3 result files."""
        file_patterns = {
            "motion": ["*_MC_GL.csv", "*_motion.txt", "*_mc.txt", "*MotionCor*.txt"],
            "ctf": ["*_CTF.txt", "*_ctf.txt", "*CTF*.log"],
            "alignment": ["*_AT_GL.csv", "*.aln", "*.xf", "*.tlt"],
            "metrics": ["TiltSeries_Metrics.csv", "*_Metrics.csv"],
            "tilt_angles": ["*_TLT.txt", "*_st.tlt"],
            "log": ["*.log", "*_output.txt"],
            "tomograms": ["*.mrc", "*.rec", "*_Vol.mrc", "*_Rec.mrc"],
        }

        found_files = {}
        for file_type, patterns in file_patterns.items():
            files = []
            for pattern in patterns:
                files.extend(self.results_dir.rglob(pattern))
            found_files[file_type] = sorted(files)

        return found_files

    def _parse_motion_files(self, motion_files: List[Path]) -> Dict[str, Any]:
        """Parse motion correction files."""
        motion_data = {}

        for motion_file in motion_files:
            try:
                # Handle AreTomo3 CSV format (*_MC_GL.csv)
                if motion_file.suffix == ".csv" and "_MC_GL" in motion_file.name:
                    motion_data.update(self._parse_aretomo3_motion_csv(motion_file))
                else:
                    # Handle other motion file formats
                    with open(motion_file, "r") as f:
                        content = f.read()

                    series_name = motion_file.stem.replace("_motion", "").replace(
                        "_mc", ""
                    )
                    frames, x_shifts, y_shifts = self._extract_motion_data(content)

                    if frames:
                        motion_data[series_name] = {
                            "file": str(motion_file),
                            "frames": frames,
                            "x_shifts": x_shifts,
                            "y_shifts": y_shifts,
                            "total_motion": np.sqrt(
                                np.array(x_shifts) ** 2 + np.array(y_shifts) ** 2
                            ),
                            "drift_rate": self._calculate_drift_rate(
                                x_shifts, y_shifts
                            ),
                        }

            except Exception as e:
                logger.warning(f"Could not parse motion file {motion_file}: {e}")

        return motion_data

    def _parse_aretomo3_motion_csv(self, csv_file: Path) -> Dict[str, Any]:
        """Parse AreTomo3 motion CSV file (*_MC_GL.csv)."""
        motion_data = {}

        try:
            # Extract series name from filename (e.g., tomo24_MC_GL.csv ->
            # tomo24)
            series_name = csv_file.stem.replace("_MC_GL", "")

            # Read CSV data (no header in AreTomo3 format)
            data = np.loadtxt(csv_file, delimiter=None)

            if data.size > 0 and data.ndim == 2:
                # AreTomo3 format: Frame#, Tilt#, Angle, PixelSize, X_shift,
                # Y_shift
                frames = data[:, 0].astype(int)
                tilt_numbers = data[:, 1].astype(int)
                tilt_angles = data[:, 2]
                pixel_sizes = data[:, 3]
                x_shifts = data[:, 4]
                y_shifts = data[:, 5]

                # Calculate total motion
                total_motion = np.sqrt(x_shifts**2 + y_shifts**2)

                motion_data[series_name] = {
                    "file": str(csv_file),
                    "frames": frames.tolist(),
                    "tilt_numbers": tilt_numbers.tolist(),
                    "tilt_angles": tilt_angles.tolist(),
                    "pixel_sizes": pixel_sizes.tolist(),
                    "x_shifts": x_shifts.tolist(),
                    "y_shifts": y_shifts.tolist(),
                    "total_motion": total_motion.tolist(),
                    "drift_rate": self._calculate_drift_rate(
                        x_shifts.tolist(), y_shifts.tolist()
                    ),
                    "max_motion": float(np.max(total_motion)),
                    "mean_motion": float(np.mean(total_motion)),
                    "format": "aretomo3_csv",
                }

                logger.info(
                    f"Parsed AreTomo3 motion data for {series_name}: {
                        len(frames)} frames"
                )

        except Exception as e:
            logger.error(f"Error parsing AreTomo3 motion CSV {csv_file}: {e}")

        return motion_data

    def _parse_ctf_files(self, ctf_files: List[Path]) -> Dict[str, Any]:
        """Parse CTF estimation files."""
        ctf_data = {}

        for ctf_file in ctf_files:
            try:
                # Handle AreTomo3 CTF format
                if ctf_file.suffix == ".txt" and "_CTF" in ctf_file.name:
                    ctf_data.update(self._parse_aretomo3_ctf_txt(ctf_file))
                else:
                    # Handle other CTF file formats
                    with open(ctf_file, "r") as f:
                        content = f.read()

                    series_name = ctf_file.stem.replace("_ctf", "").replace("_CTF", "")
                    ctf_params = self._extract_ctf_data(content)

                    if ctf_params:
                        ctf_data[series_name] = {"file": str(ctf_file), **ctf_params}

            except Exception as e:
                logger.warning(f"Could not parse CTF file {ctf_file}: {e}")

        return ctf_data

    # TODO: Refactor _parse_aretomo3_ctf_txt - complexity: 12 (target: <10)
    # TODO: Refactor function - Function '_parse_aretomo3_ctf_txt' too long
    # (88 lines)
    def _parse_aretomo3_ctf_txt(self, ctf_file: Path) -> Dict[str, Any]:
        """Parse AreTomo3 CTF text file (*_CTF.txt)."""
        ctf_data = {}

        try:
            # Extract series name from filename (e.g., tomo24_CTF.txt ->
            # tomo24)
            series_name = ctf_file.stem.replace("_CTF", "")

            with open(ctf_file, "r") as f:
                lines = f.readlines()

            # Parse header to understand column format
            header_line = None
            data_lines = []

            for line in lines:
                line = line.strip()
                if line.startswith("#"):
                    if "Columns:" in line:
                        header_line = line
                elif line and not line.startswith("#"):
                    data_lines.append(line)

            if not data_lines:
                return ctf_data

            # Parse data lines
            micrograph_nums = []
            defocus1_values = []
            defocus2_values = []
            astigmatism_angles = []
            phase_shifts = []
            cross_correlations = []
            resolutions = []
            df_hands = []

            for line in data_lines:
                parts = line.split()
                if len(parts) >= 8:
                    micrograph_nums.append(int(parts[0]))
                    defocus1_values.append(float(parts[1]))
                    defocus2_values.append(float(parts[2]))
                    astigmatism_angles.append(float(parts[3]))
                    phase_shifts.append(float(parts[4]))
                    cross_correlations.append(float(parts[5]))
                    resolutions.append(float(parts[6]))
                    df_hands.append(int(parts[7]))

            if micrograph_nums:
                # Calculate derived parameters
                astigmatism_values = [
                    abs(d1 - d2) for d1, d2 in zip(defocus1_values, defocus2_values)
                ]
                mean_defocus = [
                    (d1 + d2) / 2 for d1, d2 in zip(defocus1_values, defocus2_values)
                ]

                ctf_data[series_name] = {
                    "file": str(ctf_file),
                    "micrograph_numbers": micrograph_nums,
                    "defocus1": defocus1_values,
                    "defocus2": defocus2_values,
                    "astigmatism_angles": astigmatism_angles,
                    "phase_shifts": phase_shifts,
                    "cross_correlations": cross_correlations,
                    "resolutions": resolutions,
                    "df_hands": df_hands,
                    "astigmatism_values": astigmatism_values,
                    "mean_defocus": mean_defocus,
                    "mean_defocus_value": float(np.mean(mean_defocus)),
                    "mean_astigmatism": float(np.mean(astigmatism_values)),
                    "mean_resolution": (
                        float(np.mean([r for r in resolutions if r > 0]))
                        if any(r > 0 for r in resolutions)
                        else 0.0
                    ),
                    "mean_cc": float(np.mean(cross_correlations)),
                    "format": "aretomo3_ctf",
                }

                logger.info(
                    f"Parsed AreTomo3 CTF data for {series_name}: {
                        len(micrograph_nums)} micrographs"
                )

        except Exception as e:
            logger.error(f"Error parsing AreTomo3 CTF file {ctf_file}: {e}")

        return ctf_data

    def _parse_alignment_files(self, alignment_files: List[Path]) -> Dict[str, Any]:
        """Parse alignment files (.aln, .xf, .tlt)."""
        alignment_data = {}

        for aln_file in alignment_files:
            try:
                # Handle AreTomo3 alignment CSV format
                if aln_file.suffix == ".csv" and "_AT_GL" in aln_file.name:
                    alignment_data.update(self._parse_aretomo3_alignment_csv(aln_file))
                else:
                    series_name = aln_file.stem

                    if aln_file.suffix == ".aln":
                        # Parse .aln file (IMOD alignment format)
                        data = self._parse_aln_file(aln_file)
                    elif aln_file.suffix == ".xf":
                        # Parse .xf file (transform file)
                        data = self._parse_xf_file(aln_file)
                    elif aln_file.suffix == ".tlt":
                        # Parse .tlt file (tilt angles)
                        data = self._parse_tlt_file(aln_file)
                    else:
                        continue

                    if data:
                        if series_name not in alignment_data:
                            alignment_data[series_name] = {}
                        alignment_data[series_name].update(data)

            except Exception as e:
                logger.warning(f"Could not parse alignment file {aln_file}: {e}")

        return alignment_data

    # TODO: Refactor function - Function '_parse_aretomo3_alignment_csv' too
    # long (58 lines)
    def _parse_aretomo3_alignment_csv(self, csv_file: Path) -> Dict[str, Any]:
        """Parse AreTomo3 alignment CSV file (*_AT_GL.csv)."""
        alignment_data = {}

        try:
            # Extract series name from filename (e.g., tomo24_AT_GL.csv ->
            # tomo24)
            series_name = csv_file.stem.replace("_AT_GL", "")

            # Read CSV data (no header in AreTomo3 format)
            data = np.loadtxt(csv_file, delimiter=None)

            if data.size > 0 and data.ndim == 2:
                # AreTomo3 format: Index, Tilt#, Angle, PixelSize, Score,
                # TiltAxis, X_shift, Y_shift
                indices = data[:, 0].astype(int)
                tilt_numbers = data[:, 1].astype(int)
                tilt_angles = data[:, 2]
                pixel_sizes = data[:, 3]
                alignment_scores = data[:, 4]
                tilt_axes = data[:, 5]
                x_shifts = data[:, 6]
                y_shifts = data[:, 7]

                # Calculate derived parameters
                total_shifts = np.sqrt(x_shifts**2 + y_shifts**2)

                alignment_data[series_name] = {
                    "file": str(csv_file),
                    "indices": indices.tolist(),
                    "tilt_numbers": tilt_numbers.tolist(),
                    "tilt_angles": tilt_angles.tolist(),
                    "pixel_sizes": pixel_sizes.tolist(),
                    "alignment_scores": alignment_scores.tolist(),
                    "tilt_axes": tilt_axes.tolist(),
                    "x_shifts": x_shifts.tolist(),
                    "y_shifts": y_shifts.tolist(),
                    "total_shifts": total_shifts.tolist(),
                    "mean_score": float(np.mean(alignment_scores)),
                    "std_score": float(np.std(alignment_scores)),
                    "mean_tilt_axis": float(np.mean(tilt_axes)),
                    "std_tilt_axis": float(np.std(tilt_axes)),
                    "max_shift": float(np.max(total_shifts)),
                    "mean_shift": float(np.mean(total_shifts)),
                    "tilt_range": [
                        float(np.min(tilt_angles)),
                        float(np.max(tilt_angles)),
                    ],
                    "num_tilts": len(tilt_angles),
                    "format": "aretomo3_alignment",
                }

                logger.info(
                    f"Parsed AreTomo3 alignment data for {series_name}: {
                        len(tilt_angles)} tilts"
                )

        except Exception as e:
            logger.error(f"Error parsing AreTomo3 alignment CSV {csv_file}: {e}")

        return alignment_data

    def _parse_log_files(self, log_files: List[Path]) -> Dict[str, Any]:
        """Parse AreTomo3 log files."""
        log_data = {}

        for log_file in log_files:
            try:
                with open(log_file, "r") as f:
                    content = f.read()

                series_name = log_file.stem.replace("_output", "").replace("_log", "")

                # Extract processing parameters and timing
                log_info = self._extract_log_info(content)

                if log_info:
                    log_data[series_name] = {"file": str(log_file), **log_info}

            except Exception as e:
                logger.warning(f"Could not parse log file {log_file}: {e}")

        return log_data

    def _extract_motion_data(
        self, content: str
    ) -> Tuple[List[int], List[float], List[float]]:
        """Extract motion data from motion correction output."""
        frames, x_shifts, y_shifts = [], [], []

        # Try different motion file formats
        patterns = [
            r"Frame\s+(\d+):\s+([-\d.]+)\s+([-\d.]+)",  # Standard format
            r"(\d+)\s+([-\d.]+)\s+([-\d.]+)",  # Simple format
            # Verbose format
            r"Frame\s*(\d+)\s*:\s*X\s*=\s*([-\d.]+)\s*Y\s*=\s*([-\d.]+)",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content)
            if matches:
                for match in matches:
                    frames.append(int(match[0]))
                    x_shifts.append(float(match[1]))
                    y_shifts.append(float(match[2]))
                break

        return frames, x_shifts, y_shifts

    def _extract_ctf_data(self, content: str) -> Dict[str, Any]:
        """Extract CTF parameters from CTF estimation output."""
        ctf_params = {}

        # Common CTF parameters to extract
        patterns = {
            "defocus1": r"Defocus1[:\s]+([-\d.]+)",
            "defocus2": r"Defocus2[:\s]+([-\d.]+)",
            "astigmatism": r"Astigmatism[:\s]+([-\d.]+)",
            "phase_shift": r"Phase[:\s]+shift[:\s]+([-\d.]+)",
            "resolution": r"Resolution[:\s]+([-\d.]+)",
            "cc_value": r"CC[:\s]+([-\d.]+)",
            "fit_quality": r"Fit[:\s]+quality[:\s]+([-\d.]+)",
        }

        for param, pattern in patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                ctf_params[param] = float(match.group(1))

        return ctf_params

    def _parse_aln_file(self, aln_file: Path) -> Dict[str, Any]:
        """Parse IMOD .aln alignment file."""
        try:
            data = np.loadtxt(aln_file)
            if data.size > 0:
                return {
                    "alignment_matrix": data.tolist(),
                    "num_tilts": len(data),
                    "alignment_quality": np.std(data[:, -1]) if data.ndim > 1 else 0,
                }
        except Exception:
            pass
        return {}

    def _parse_xf_file(self, xf_file: Path) -> Dict[str, Any]:
        """Parse transform .xf file."""
        try:
            data = np.loadtxt(xf_file)
            if data.size > 0:
                return {"transforms": data.tolist(), "num_transforms": len(data)}
        except Exception:
            pass
        return {}

    def _parse_tlt_file(self, tlt_file: Path) -> Dict[str, Any]:
        """Parse tilt angles .tlt file."""
        try:
            angles = np.loadtxt(tlt_file)
            if angles.size > 0:
                return {
                    "tilt_angles": angles.tolist(),
                    "tilt_range": [float(np.min(angles)), float(np.max(angles))],
                    "tilt_step": float(np.mean(np.diff(np.sort(angles)))),
                }
        except Exception:
            pass
        return {}

    def _extract_log_info(self, content: str) -> Dict[str, Any]:
        """Extract processing information from log files."""
        log_info = {}

        # Extract processing parameters
        param_patterns = {
            "pixel_size": r"PixSize[:\s]+([-\d.]+)",
            "voltage": r"Voltage[:\s]+([-\d.]+)",
            "cs": r"Cs[:\s]+([-\d.]+)",
            "processing_time": r"Total[:\s]+time[:\s]+([-\d.]+)",
            "gpu_used": r"GPU[:\s]+(\d+)",
            "binning": r"Binning[:\s]+([-\d.]+)",
        }

        for param, pattern in param_patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                try:
                    log_info[param] = float(match.group(1))
                except Exception:
                    log_info[param] = match.group(1)

        return log_info

    def _calculate_drift_rate(
        self, x_shifts: List[float], y_shifts: List[float]
    ) -> float:
        """Calculate drift rate from motion data."""
        if len(x_shifts) < 2:
            return 0.0

        total_motion = np.sqrt(np.array(x_shifts) ** 2 + np.array(y_shifts) ** 2)
        return float(np.mean(np.diff(total_motion)))

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary statistics."""
        summary = {
            "total_series": len(self.parsed_data.get("motion_data", {})),
            "total_tomograms": len(self.parsed_data.get("tomogram_files", [])),
            "avg_motion": 0.0,
            "avg_resolution": 0.0,
            "processing_success_rate": 0.0,
        }

        # Calculate averages
        motion_data = self.parsed_data.get("motion_data", {})
        if motion_data:
            motions = [data["drift_rate"] for data in motion_data.values()]
            summary["avg_motion"] = float(np.mean(motions)) if motions else 0.0

        ctf_data = self.parsed_data.get("ctf_data", {})
        if ctf_data:
            resolutions = [data.get("resolution", 0) for data in ctf_data.values()]
            resolutions = [r for r in resolutions if r > 0]
            summary["avg_resolution"] = (
                float(np.mean(resolutions)) if resolutions else 0.0
            )

        return summary

    # TODO: Refactor function - Function 'extract_quality_metrics' too long
    # (101 lines)
    def extract_quality_metrics(self) -> Dict[str, Any]:
        """Extract quality metrics for web interface display."""
        metrics = {}

        try:
            # Motion quality metrics
            motion_data = self.parsed_data.get("motion_data", {})
            if motion_data:
                motion_metrics = []
                for series, data in motion_data.items():
                    motion_metrics.append(
                        {
                            "series": series,
                            "mean_motion": data.get(
                                "mean_motion", np.mean(data["total_motion"])
                            ),
                            "max_motion": data.get(
                                "max_motion", np.max(data["total_motion"])
                            ),
                            "drift_rate": data.get("drift_rate", 0),
                            "quality": (
                                "Good"
                                if data.get(
                                    "mean_motion", np.mean(data["total_motion"])
                                )
                                < 1.0
                                else (
                                    "Fair"
                                    if data.get(
                                        "mean_motion", np.mean(data["total_motion"])
                                    )
                                    < 2.0
                                    else "Poor"
                                )
                            ),
                        }
                    )
                metrics["motion"] = motion_metrics

            # CTF quality metrics
            ctf_data = self.parsed_data.get("ctf_data", {})
            if ctf_data:
                ctf_metrics = []
                for series, data in ctf_data.items():
                    ctf_metrics.append(
                        {
                            "series": series,
                            "mean_defocus": data.get("mean_defocus_value", 0),
                            "mean_astigmatism": data.get("mean_astigmatism", 0),
                            "mean_resolution": data.get("mean_resolution", 0),
                            "mean_cc": data.get("mean_cc", 0),
                            "quality": (
                                "Good"
                                if data.get("mean_cc", 0) > 0.8
                                else "Fair" if data.get("mean_cc", 0) > 0.6 else "Poor"
                            ),
                        }
                    )
                metrics["ctf"] = ctf_metrics

            # Alignment quality metrics
            alignment_data = self.parsed_data.get("alignment_data", {})
            if alignment_data:
                alignment_metrics = []
                for series, data in alignment_data.items():
                    alignment_metrics.append(
                        {
                            "series": series,
                            "mean_score": data.get("mean_score", 0),
                            "mean_shift": data.get("mean_shift", 0),
                            "tilt_axis_stability": data.get("std_tilt_axis", 0),
                            "num_tilts": data.get("num_tilts", 0),
                            "tilt_range": data.get("tilt_range", [0, 0]),
                            "quality": (
                                "Good"
                                if data.get("mean_score", 0) > 0.8
                                else (
                                    "Fair"
                                    if data.get("mean_score", 0) > 0.5
                                    else "Poor"
                                )
                            ),
                        }
                    )
                metrics["alignment"] = alignment_metrics

            # Overall summary
            metrics["summary"] = {
                "total_series": len(motion_data) + len(ctf_data) + len(alignment_data),
                "motion_series": len(motion_data),
                "ctf_series": len(ctf_data),
                "alignment_series": len(alignment_data),
                "overall_quality": self._calculate_overall_quality(
                    motion_data, ctf_data, alignment_data
                ),
            }

        except Exception as e:
            logger.error(f"Error extracting quality metrics: {e}")

        return metrics

    # TODO: Refactor _calculate_overall_quality - complexity: 15 (target: <10)

    # TODO: Refactor function - Function '_calculate_overall_quality' too long
    # (53 lines)
    def _calculate_overall_quality(
        self, motion_data: Dict, ctf_data: Dict, alignment_data: Dict
    ) -> str:
        """Calculate overall processing quality."""
        try:
            quality_scores = []

            # Motion quality
            for data in motion_data.values():
                mean_motion = data.get("mean_motion", np.mean(data["total_motion"]))
                if mean_motion < 1.0:
                    quality_scores.append(1.0)
                elif mean_motion < 2.0:
                    quality_scores.append(0.7)
                else:
                    quality_scores.append(0.3)

            # CTF quality
            for data in ctf_data.values():
                cc = data.get("mean_cc", 0)
                if cc > 0.8:
                    quality_scores.append(1.0)
                elif cc > 0.6:
                    quality_scores.append(0.7)
                else:
                    quality_scores.append(0.3)

            # Alignment quality
            for data in alignment_data.values():
                score = data.get("mean_score", 0)
                if score > 0.8:
                    quality_scores.append(1.0)
                elif score > 0.5:
                    quality_scores.append(0.7)
                else:
                    quality_scores.append(0.3)

            if quality_scores:
                avg_quality = np.mean(quality_scores)
                if avg_quality > 0.8:
                    return "Excellent"
                elif avg_quality > 0.6:
                    return "Good"
                elif avg_quality > 0.4:
                    return "Fair"
                else:
                    return "Poor"
            else:
                return "Unknown"

        except Exception as e:
            logger.error(f"Error calculating overall quality: {e}")
            return "Unknown"

    def generate_plots(self, output_dir: Optional[str] = None) -> Dict[str, str]:
        """Generate all analysis plots and save as PNG files."""
        if output_dir is None:
            output_dir = self.results_dir / "analysis_plots"
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(exist_ok=True)

        plot_files = {}

        try:
            # Generate motion plots
            if self.parsed_data.get("motion_data"):
                motion_plot = self._generate_motion_plot(output_dir)
                if motion_plot:
                    plot_files["motion"] = motion_plot

            # Generate CTF plots
            if self.parsed_data.get("ctf_data"):
                ctf_plot = self._generate_ctf_plot(output_dir)
                if ctf_plot:
                    plot_files["ctf"] = ctf_plot

            # Generate alignment plots
            if self.parsed_data.get("alignment_data"):
                alignment_plot = self._generate_alignment_plot(output_dir)
                if alignment_plot:
                    plot_files["alignment"] = alignment_plot

            # Generate summary plot
            summary_plot = self._generate_summary_plot(output_dir)
            if summary_plot:
                plot_files["summary"] = summary_plot

            logger.info(
                f"Generated {
                    len(plot_files)} analysis plots in {output_dir}"
            )

        except Exception as e:
            logger.error(f"Error generating plots: {e}")

        # TODO: Refactor _generate_motion_plot - complexity: 12 (target: <10)
        return plot_files

    # TODO: Refactor function - Function '_generate_motion_plot' too long (189
    # lines)
    def _generate_motion_plot(self, output_dir: Path) -> Optional[str]:
        """Generate enhanced motion correction plots for AreTomo3."""
        try:
            motion_data = self.parsed_data["motion_data"]
            if not motion_data:
                return None

            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(
                "AreTomo3 Motion Correction Analysis", fontsize=16, fontweight="bold"
            )

            # Get first series data for detailed plots
            first_series = list(motion_data.keys())[0]
            data = motion_data[first_series]

            # Plot 1: Frame motion trajectories with tilt angles
            ax1 = axes[0, 0]
            if "tilt_angles" in data:
                # Plot motion vs tilt angle for AreTomo3 data
                ax1.scatter(
                    data["tilt_angles"],
                    data["x_shifts"],
                    c="blue",
                    alpha=0.6,
                    s=30,
                    label="X shifts",
                )
                ax1.scatter(
                    data["tilt_angles"],
                    data["y_shifts"],
                    c="red",
                    alpha=0.6,
                    s=30,
                    label="Y shifts",
                )
                ax1.set_xlabel("Tilt Angle (degrees)")
                ax1.set_ylabel("Shift (pixels)")
                ax1.set_title(f"Motion vs Tilt Angle: {first_series}")
            else:
                # Fallback to frame-based plot
                ax1.plot(
                    data["frames"],
                    data["x_shifts"],
                    "b-",
                    label="X shifts",
                    linewidth=2,
                )
                ax1.plot(
                    data["frames"],
                    data["y_shifts"],
                    "r-",
                    label="Y shifts",
                    linewidth=2,
                )
                ax1.set_xlabel("Frame Number")
                ax1.set_ylabel("Shift (pixels)")
                ax1.set_title(f"Frame Motion: {first_series}")
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Plot 2: Total motion magnitude
            ax2 = axes[0, 1]
            if "tilt_angles" in data:
                ax2.scatter(
                    data["tilt_angles"],
                    data["total_motion"],
                    c="green",
                    alpha=0.7,
                    s=40,
                )
                ax2.set_xlabel("Tilt Angle (degrees)")
                ax2.set_ylabel("Total Motion (pixels)")
                ax2.set_title("Total Motion vs Tilt Angle")
            else:
                ax2.plot(data["frames"], data["total_motion"], "g-", linewidth=2)
                ax2.set_xlabel("Frame Number")
                ax2.set_ylabel("Total Motion (pixels)")
                ax2.set_title("Total Motion per Frame")
            ax2.grid(True, alpha=0.3)

            # Plot 3: Motion statistics across all series
            ax3 = axes[1, 0]
            series_names = []
            mean_motions = []
            max_motions = []

            for series, series_data in motion_data.items():
                series_names.append(series[:12] + "..." if len(series) > 12 else series)
                mean_motions.append(
                    series_data.get("mean_motion", np.mean(series_data["total_motion"]))
                )
                max_motions.append(
                    series_data.get("max_motion", np.max(series_data["total_motion"]))
                )

            x_pos = np.arange(len(series_names))
            width = 0.35

            bars1 = ax3.bar(
                x_pos - width / 2,
                mean_motions,
                width,
                label="Mean Motion",
                color="skyblue",
                alpha=0.7,
            )
            bars2 = ax3.bar(
                x_pos + width / 2,
                max_motions,
                width,
                label="Max Motion",
                color="orange",
                alpha=0.7,
            )

            ax3.set_xlabel("Tilt Series")
            ax3.set_ylabel("Motion (pixels)")
            ax3.set_title("Motion Statistics Comparison")
            ax3.set_xticks(x_pos)
            ax3.set_xticklabels(series_names, rotation=45, ha="right")
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # Add value labels on bars
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax3.text(
                        bar.get_x() + bar.get_width() / 2.0,
                        height + 0.01,
                        f"{height:.2f}",
                        ha="center",
                        va="bottom",
                        fontsize=8,
                    )

            # Plot 4: Motion quality assessment
            ax4 = axes[1, 1]
            all_motions = []
            for series_data in motion_data.values():
                all_motions.extend(series_data["total_motion"])

            # Create histogram with quality zones
            counts, bins, patches = ax4.hist(
                all_motions, bins=20, color="lightgreen", alpha=0.7, edgecolor="black"
            )

            # Color code quality zones
            for i, patch in enumerate(patches):
                bin_center = (bins[i] + bins[i + 1]) / 2
                if bin_center < 1.0:
                    patch.set_facecolor("green")
                    patch.set_alpha(0.7)
                elif bin_center < 2.0:
                    patch.set_facecolor("yellow")
                    patch.set_alpha(0.7)
                else:
                    patch.set_facecolor("red")
                    patch.set_alpha(0.7)

            ax4.set_xlabel("Total Motion (pixels)")
            ax4.set_ylabel("Frequency")
            ax4.set_title("Motion Quality Distribution")
            ax4.grid(True, alpha=0.3)

            # Add quality legend
            from matplotlib.patches import Patch

            legend_elements = [
                Patch(facecolor="green", alpha=0.7, label="Good (<1px)"),
                Patch(facecolor="yellow", alpha=0.7, label="Fair (1-2px)"),
                Patch(facecolor="red", alpha=0.7, label="Poor (>2px)"),
            ]
            ax4.legend(handles=legend_elements, loc="upper right")

            plt.tight_layout()

            # Save plot
            plot_file = output_dir / "motion_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches="tight")
            plt.close()

            return str(plot_file)

        except Exception as e:
            # TODO: Refactor _generate_ctf_plot - complexity: 17 (target: <10)
            logger.error(f"Error generating motion plot: {e}")
            return None

    # TODO: Refactor function - Function '_generate_ctf_plot' too long (188
    # lines)
    def _generate_ctf_plot(self, output_dir: Path) -> Optional[str]:
        """Generate enhanced CTF analysis plots for AreTomo3."""
        try:
            ctf_data = self.parsed_data["ctf_data"]
            if not ctf_data:
                return None

            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle("AreTomo3 CTF Analysis", fontsize=16, fontweight="bold")

            # Get first series for detailed analysis
            first_series = list(ctf_data.keys())[0]
            first_data = ctf_data[first_series]

            # Plot 1: CTF parameters vs tilt angle (if available)
            ax1 = axes[0, 0]
            if "micrograph_numbers" in first_data and "defocus1" in first_data:
                # For AreTomo3 data, plot defocus vs micrograph number (tilt
                # order)
                mic_nums = first_data["micrograph_numbers"]
                defocus1 = first_data["defocus1"]
                defocus2 = first_data["defocus2"]

                ax1.scatter(
                    mic_nums, defocus1, c="blue", alpha=0.7, s=40, label="Defocus 1"
                )
                ax1.scatter(
                    mic_nums, defocus2, c="red", alpha=0.7, s=40, label="Defocus 2"
                )
                ax1.set_xlabel("Micrograph Number")
                ax1.set_ylabel("Defocus (μm)")
                ax1.set_title(f"Defocus vs Tilt Order: {first_series}")
                ax1.legend()
                ax1.grid(True, alpha=0.3)

            # Plot 2: Resolution and Cross-correlation
            ax2 = axes[0, 1]
            if "resolutions" in first_data and "cross_correlations" in first_data:
                resolutions = [r for r in first_data["resolutions"] if r > 0]
                cc_values = first_data["cross_correlations"][: len(resolutions)]

                if resolutions:
                    scatter = ax2.scatter(
                        resolutions,
                        cc_values,
                        c=first_data["micrograph_numbers"][: len(resolutions)],
                        cmap="viridis",
                        alpha=0.7,
                        s=50,
                    )
                    ax2.set_xlabel("Resolution (Å)")
                    ax2.set_ylabel("Cross-Correlation")
                    ax2.set_title("CTF Fit Quality vs Resolution")
                    ax2.grid(True, alpha=0.3)
                    plt.colorbar(scatter, ax=ax2, label="Tilt Order")

            # Plot 3: Defocus distribution across all series
            ax3 = axes[1, 0]
            all_defocus1 = []
            all_defocus2 = []
            all_astigmatism = []

            for series, data in ctf_data.items():
                if "defocus1" in data:
                    if isinstance(data["defocus1"], list):
                        all_defocus1.extend(data["defocus1"])
                        all_defocus2.extend(data["defocus2"])
                        all_astigmatism.extend(data.get("astigmatism_values", []))
                    else:
                        all_defocus1.append(data["defocus1"])
                        all_defocus2.append(data["defocus2"])
                        all_astigmatism.append(abs(data["defocus1"] - data["defocus2"]))

            if all_defocus1:
                # Create 2D histogram for defocus1 vs defocus2
                ax3.hist2d(all_defocus1, all_defocus2, bins=20, cmap="Blues", alpha=0.7)
                ax3.set_xlabel("Defocus 1 (μm)")
                ax3.set_ylabel("Defocus 2 (μm)")
                ax3.set_title("Defocus Distribution (2D)")

                # Add diagonal line for reference
                min_def = min(min(all_defocus1), min(all_defocus2))
                max_def = max(max(all_defocus1), max(all_defocus2))
                ax3.plot(
                    [min_def, max_def],
                    [min_def, max_def],
                    "r--",
                    alpha=0.5,
                    label="Equal defocus",
                )
                ax3.legend()

            # Plot 4: Quality metrics summary
            ax4 = axes[1, 1]
            series_names = []
            mean_resolutions = []
            mean_cc_values = []
            mean_astigmatism = []

            for series, data in ctf_data.items():
                series_names.append(series[:12] + "..." if len(series) > 12 else series)

                if "mean_resolution" in data:
                    mean_resolutions.append(data["mean_resolution"])
                    mean_cc_values.append(data["mean_cc"])
                    mean_astigmatism.append(data["mean_astigmatism"])
                else:
                    # Calculate from individual values
                    if "resolutions" in data:
                        valid_res = [r for r in data["resolutions"] if r > 0]
                        mean_resolutions.append(np.mean(valid_res) if valid_res else 0)
                        mean_cc_values.append(np.mean(data["cross_correlations"]))
                        mean_astigmatism.append(
                            np.mean(data.get("astigmatism_values", [0]))
                        )
                    else:
                        mean_resolutions.append(0)
                        mean_cc_values.append(0)
                        mean_astigmatism.append(0)

            if mean_resolutions and any(r > 0 for r in mean_resolutions):
                # Create quality assessment plot
                x_pos = np.arange(len(series_names))

                # Normalize values for comparison
                norm_res = (
                    np.array(mean_resolutions) / max(mean_resolutions)
                    if max(mean_resolutions) > 0
                    else np.zeros_like(mean_resolutions)
                )
                norm_cc = (
                    np.array(mean_cc_values) / max(mean_cc_values)
                    if max(mean_cc_values) > 0
                    else np.zeros_like(mean_cc_values)
                )
                norm_astig = (
                    1 - (np.array(mean_astigmatism) / max(mean_astigmatism))
                    if max(mean_astigmatism) > 0
                    else np.ones_like(mean_astigmatism)
                )

                width = 0.25
                bars1 = ax4.bar(
                    x_pos - width,
                    norm_res,
                    width,
                    label="Resolution (norm)",
                    color="blue",
                    alpha=0.7,
                )
                bars2 = ax4.bar(
                    x_pos,
                    norm_cc,
                    width,
                    label="Cross-Corr (norm)",
                    color="green",
                    alpha=0.7,
                )
                bars3 = ax4.bar(
                    x_pos + width,
                    norm_astig,
                    width,
                    label="Low Astig (norm)",
                    color="orange",
                    alpha=0.7,
                )

                ax4.set_xlabel("Tilt Series")
                ax4.set_ylabel("Normalized Quality Score")
                ax4.set_title("CTF Quality Comparison")
                ax4.set_xticks(x_pos)
                ax4.set_xticklabels(series_names, rotation=45, ha="right")
                ax4.legend()
                ax4.grid(True, alpha=0.3)
                ax4.set_ylim(0, 1.1)

            plt.tight_layout()

            # Save plot
            plot_file = output_dir / "ctf_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches="tight")
            plt.close()

            return str(plot_file)

        # TODO: Refactor _generate_alignment_plot - complexity: 16 (target:
        # <10)
        except Exception as e:
            logger.error(f"Error generating CTF plot: {e}")
            return None

    # TODO: Refactor function - Function '_generate_alignment_plot' too long
    # (202 lines)
    def _generate_alignment_plot(self, output_dir: Path) -> Optional[str]:
        """Generate enhanced alignment analysis plots for AreTomo3."""
        try:
            alignment_data = self.parsed_data["alignment_data"]
            if not alignment_data:
                return None

            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle("AreTomo3 Alignment Analysis", fontsize=16, fontweight="bold")

            # Get first series for detailed analysis
            first_series = list(alignment_data.keys())[0]
            first_data = alignment_data[first_series]

            # Plot 1: Alignment scores vs tilt angle
            ax1 = axes[0, 0]
            if "alignment_scores" in first_data and "tilt_angles" in first_data:
                tilt_angles = first_data["tilt_angles"]
                scores = first_data["alignment_scores"]

                # Color code by score quality
                colors = [
                    "red" if s < 0.5 else "yellow" if s < 0.8 else "green"
                    for s in scores
                ]
                scatter = ax1.scatter(tilt_angles, scores, c=colors, alpha=0.7, s=50)

                ax1.set_xlabel("Tilt Angle (degrees)")
                ax1.set_ylabel("Alignment Score")
                ax1.set_title(f"Alignment Quality vs Tilt Angle: {first_series}")
                ax1.grid(True, alpha=0.3)

                # Add quality threshold lines
                ax1.axhline(
                    y=0.5, color="red", linestyle="--", alpha=0.5, label="Poor (<0.5)"
                )
                ax1.axhline(
                    y=0.8,
                    color="orange",
                    linestyle="--",
                    alpha=0.5,
                    label="Good (>0.8)",
                )
                ax1.legend()

            # Plot 2: Tilt axis refinement
            ax2 = axes[0, 1]
            if "tilt_axes" in first_data and "tilt_angles" in first_data:
                tilt_angles = first_data["tilt_angles"]
                tilt_axes = first_data["tilt_axes"]

                ax2.scatter(tilt_angles, tilt_axes, c="blue", alpha=0.7, s=40)
                ax2.set_xlabel("Tilt Angle (degrees)")
                ax2.set_ylabel("Tilt Axis (degrees)")
                ax2.set_title("Tilt Axis Refinement")
                ax2.grid(True, alpha=0.3)

                # Add mean tilt axis line
                mean_axis = np.mean(tilt_axes)
                ax2.axhline(
                    y=mean_axis,
                    color="red",
                    linestyle="-",
                    alpha=0.7,
                    label=f"Mean: {mean_axis:.2f}°",
                )
                ax2.legend()

            # Plot 3: Global shifts analysis
            ax3 = axes[1, 0]
            if "x_shifts" in first_data and "y_shifts" in first_data:
                x_shifts = first_data["x_shifts"]
                y_shifts = first_data["y_shifts"]
                tilt_angles = first_data["tilt_angles"]

                # Create trajectory plot
                ax3.plot(
                    x_shifts,
                    y_shifts,
                    "b-",
                    alpha=0.7,
                    linewidth=2,
                    label="Shift trajectory",
                )
                scatter = ax3.scatter(
                    x_shifts, y_shifts, c=tilt_angles, cmap="viridis", s=50, alpha=0.8
                )

                ax3.set_xlabel("X Shift (pixels)")
                ax3.set_ylabel("Y Shift (pixels)")
                ax3.set_title("Global Shift Trajectory")
                ax3.grid(True, alpha=0.3)
                ax3.legend()

                # Add colorbar for tilt angles
                plt.colorbar(scatter, ax=ax3, label="Tilt Angle (°)")

                # Mark start and end points
                ax3.plot(x_shifts[0], y_shifts[0], "go", markersize=8, label="Start")
                ax3.plot(x_shifts[-1], y_shifts[-1], "ro", markersize=8, label="End")

            # Plot 4: Quality metrics comparison across series
            ax4 = axes[1, 1]
            series_names = []
            mean_scores = []
            std_scores = []
            mean_shifts = []
            tilt_axis_stds = []

            for series, data in alignment_data.items():
                series_names.append(series[:12] + "..." if len(series) > 12 else series)

                if "mean_score" in data:
                    mean_scores.append(data["mean_score"])
                    std_scores.append(data["std_score"])
                    mean_shifts.append(data["mean_shift"])
                    tilt_axis_stds.append(data["std_tilt_axis"])
                else:
                    # Calculate from individual values
                    if "alignment_scores" in data:
                        scores = data["alignment_scores"]
                        mean_scores.append(np.mean(scores))
                        std_scores.append(np.std(scores))

                        if "total_shifts" in data:
                            mean_shifts.append(np.mean(data["total_shifts"]))
                        else:
                            mean_shifts.append(0)

                        if "tilt_axes" in data:
                            tilt_axis_stds.append(np.std(data["tilt_axes"]))
                        else:
                            tilt_axis_stds.append(0)
                    else:
                        mean_scores.append(0)
                        std_scores.append(0)
                        mean_shifts.append(0)
                        tilt_axis_stds.append(0)

            if mean_scores and any(s > 0 for s in mean_scores):
                x_pos = np.arange(len(series_names))
                width = 0.2

                # Normalize metrics for comparison
                norm_scores = np.array(mean_scores)
                norm_shifts = (
                    1 - (np.array(mean_shifts) / max(mean_shifts))
                    if max(mean_shifts) > 0
                    else np.ones_like(mean_shifts)
                )
                norm_axis_std = (
                    1 - (np.array(tilt_axis_stds) / max(tilt_axis_stds))
                    if max(tilt_axis_stds) > 0
                    else np.ones_like(tilt_axis_stds)
                )

                bars1 = ax4.bar(
                    x_pos - width,
                    norm_scores,
                    width,
                    label="Alignment Score",
                    color="blue",
                    alpha=0.7,
                )
                bars2 = ax4.bar(
                    x_pos,
                    norm_shifts,
                    width,
                    label="Low Shift (norm)",
                    color="green",
                    alpha=0.7,
                )
                bars3 = ax4.bar(
                    x_pos + width,
                    norm_axis_std,
                    width,
                    label="Axis Stability (norm)",
                    color="orange",
                    alpha=0.7,
                )

                ax4.set_xlabel("Tilt Series")
                ax4.set_ylabel("Quality Score")
                ax4.set_title("Alignment Quality Comparison")
                ax4.set_xticks(x_pos)
                ax4.set_xticklabels(series_names, rotation=45, ha="right")
                ax4.legend()
                ax4.grid(True, alpha=0.3)
                ax4.set_ylim(0, 1.1)

            plt.tight_layout()

            # Save plot
            plot_file = output_dir / "alignment_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches="tight")
            plt.close()

            return str(plot_file)

        except Exception as e:
            logger.error(f"Error generating alignment plot: {e}")
            return None

    # TODO: Refactor function - Function '_generate_summary_plot' too long
    # (100 lines)
    def _generate_summary_plot(self, output_dir: Path) -> Optional[str]:
        """Generate summary overview plot."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle("Processing Summary", fontsize=16, fontweight="bold")

            summary = self.parsed_data.get("summary", {})

            # Plot 1: File type distribution
            ax1 = axes[0, 0]
            file_counts = {
                "Motion Files": len(self.parsed_data.get("motion_data", {})),
                "CTF Files": len(self.parsed_data.get("ctf_data", {})),
                "Alignment Files": len(self.parsed_data.get("alignment_data", {})),
                "Tomograms": len(self.parsed_data.get("tomogram_files", [])),
            }

            if any(file_counts.values()):
                colors = ["lightblue", "lightcoral", "lightgreen", "gold"]
                wedges, texts, autotexts = ax1.pie(
                    file_counts.values(),
                    labels=file_counts.keys(),
                    autopct="%1.1f%%",
                    colors=colors,
                    startangle=90,
                )
                ax1.set_title("File Type Distribution")

            # Plot 2: Processing statistics
            ax2 = axes[0, 1]
            stats = [
                ("Total Series", summary.get("total_series", 0)),
                ("Total Tomograms", summary.get("total_tomograms", 0)),
                (
                    "Avg Motion",
                    f"{
                            summary.get(
                                'avg_motion', 0):.2f} px",
                ),
                (
                    "Avg Resolution",
                    f"{
                                    summary.get(
                                        'avg_resolution', 0):.1f} Å",
                ),
            ]

            ax2.axis("off")
            for i, (label, value) in enumerate(stats):
                ax2.text(
                    0.1, 0.8 - i * 0.2, f"{label}:", fontsize=12, fontweight="bold"
                )
                ax2.text(0.6, 0.8 - i * 0.2, str(value), fontsize=12)
            ax2.set_title("Processing Statistics")

            # Plot 3: Success rate (if available)
            ax3 = axes[1, 0]
            success_rate = summary.get("processing_success_rate", 0)
            if success_rate > 0:
                ax3.pie(
                    [success_rate, 100 - success_rate],
                    labels=["Success", "Failed"],
                    autopct="%1.1f%%",
                    colors=["lightgreen", "lightcoral"],
                    startangle=90,
                )
                ax3.set_title("Processing Success Rate")
            else:
                ax3.text(
                    0.5,
                    0.5,
                    "Success rate\nnot available",
                    ha="center",
                    va="center",
                    transform=ax3.transAxes,
                    fontsize=12,
                )
                ax3.set_title("Processing Success Rate")

            # Plot 4: Directory structure
            ax4 = axes[1, 1]
            ax4.axis("off")
            ax4.text(0.1, 0.9, "Results Directory:", fontsize=12, fontweight="bold")
            ax4.text(0.1, 0.8, str(self.results_dir), fontsize=10, wrap=True)
            ax4.text(
                0.1,
                0.6,
                f'Total Files Found: {summary.get("total_series", 0)}',
                fontsize=11,
            )
            ax4.text(
                0.1,
                0.5,
                f"Analysis Date: {Path(self.results_dir).stat().st_mtime}",
                fontsize=10,
            )
            ax4.set_title("Directory Information")

            plt.tight_layout()

            # Save plot
            plot_file = output_dir / "processing_summary.png"
            plt.savefig(plot_file, dpi=300, bbox_inches="tight")
            plt.close()

            return str(plot_file)

        except Exception as e:
            logger.error(f"Error generating summary plot: {e}")
            return None
