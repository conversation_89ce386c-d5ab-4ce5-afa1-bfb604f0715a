#!/usr/bin/env python3
"""
AreTomo3 GUI Plugin System
Extensible plugin architecture for custom functionality.
"""

import importlib
import inspect
import json
import logging
import sys
import threading
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Type

logger = logging.getLogger(__name__)


class PluginType(Enum):
    """Plugin types for categorization."""

    ANALYSIS = "analysis"
    VISUALIZATION = "visualization"
    PROCESSING = "processing"
    EXPORT = "export"
    IMPORT = "import"
    UTILITY = "utility"
    THEME = "theme"
    WORKFLOW = "workflow"


@dataclass
class PluginMetadata:
    """Plugin metadata information."""

    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str] = field(default_factory=list)
    min_gui_version: str = "1.0.0"
    max_gui_version: str = "999.0.0"
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)


class PluginInterface(ABC):
    """Base interface for all plugins."""

    def __init__(self, metadata: PluginMetadata):
        """Initialize plugin with metadata."""
        self.metadata = metadata
        self.is_loaded = False
        self.is_active = False
        self.gui_reference = None
        self.config = metadata.config.copy()

    @abstractmethod
    def initialize(self, gui_reference: Any) -> bool:
        """
        Initialize the plugin with GUI reference.

        Args:
            gui_reference: Reference to main GUI application

        Returns:
            bool: True if initialization successful
        """
        pass

    @abstractmethod
    def activate(self) -> bool:
        """
        Activate the plugin.

        Returns:
            bool: True if activation successful
        """
        pass

    @abstractmethod
    def deactivate(self) -> bool:
        """
        Deactivate the plugin.

        Returns:
            bool: True if deactivation successful
        """
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """
        Clean up plugin resources.

        Returns:
            bool: True if cleanup successful
        """
        pass

    def get_menu_items(self) -> List[Dict[str, Any]]:
        """
        Get menu items to add to GUI.

        Returns:
            List of menu item dictionaries
        """
        return []

    def get_toolbar_items(self) -> List[Dict[str, Any]]:
        """
        Get toolbar items to add to GUI.

        Returns:
            List of toolbar item dictionaries
        """
        return []

    def get_config_widget(self) -> Optional[Any]:
        """
        Get configuration widget for plugin settings.

        Returns:
            Configuration widget or None
        """
        return None


class AnalysisPlugin(PluginInterface):
    """Base class for analysis plugins."""

    @abstractmethod
    def analyze_data(self, data: Any, parameters: Dict[str, Any]) -> Any:
        """
        Analyze data with given parameters.

        Args:
            data: Input data to analyze
            parameters: Analysis parameters

        Returns:
            Analysis results
        """
        pass


class VisualizationPlugin(PluginInterface):
    """Base class for visualization plugins."""

    @abstractmethod
    def create_visualization(self, data: Any, parameters: Dict[str, Any]) -> Any:
        """
        Create visualization from data.

        Args:
            data: Input data to visualize
            parameters: Visualization parameters

        Returns:
            Visualization widget or figure
        """
        pass


class ProcessingPlugin(PluginInterface):
    """Base class for processing plugins."""

    @abstractmethod
    def process_data(self, data: Any, parameters: Dict[str, Any]) -> Any:
        """
        Process data with given parameters.

        Args:
            data: Input data to process
            parameters: Processing parameters

        Returns:
            Processed data
        """
        pass


class PluginManager:
    """
    Manages plugin loading, activation, and lifecycle.
    """

    def __init__(self, plugin_directories: List[Path] = None):
        """Initialize the plugin manager."""
        self.plugin_directories = plugin_directories or []
        self.loaded_plugins: Dict[str, PluginInterface] = {}
        self.active_plugins: Dict[str, PluginInterface] = {}
        self.plugin_metadata: Dict[str, PluginMetadata] = {}
        self.gui_reference = None

        # Default plugin directories
        self.plugin_directories.extend(
            [
                Path.cwd() / "plugins",
                Path.home() / ".aretomo3_gui" / "plugins",
                Path(__file__).parent.parent / "plugins",
            ]
        )

        # Plugin registry
        self.plugin_registry: Dict[PluginType, List[str]] = {
            plugin_type: [] for plugin_type in PluginType
        }

        # Event hooks
        self.event_hooks: Dict[str, List[Callable]] = {}

        logger.info("Plugin Manager initialized")

    def set_gui_reference(self, gui_reference: Any):
        """Set reference to main GUI application."""
        self.gui_reference = gui_reference

    def discover_plugins(self):
        """Discover plugins in plugin directories."""
        discovered_count = 0

        for plugin_dir in self.plugin_directories:
            if not plugin_dir.exists():
                continue

            logger.info(f"Scanning for plugins in: {plugin_dir}")

            # Look for plugin.json files
            for plugin_file in plugin_dir.rglob("plugin.json"):
                try:
                    plugin_path = plugin_file.parent
                    metadata = self._load_plugin_metadata(plugin_file)

                    if metadata:
                        self.plugin_metadata[metadata.name] = metadata
                        self.plugin_registry[metadata.plugin_type].append(metadata.name)
                        discovered_count += 1
                        logger.info(
                            f"Discovered plugin: {
                                metadata.name} v{
                                metadata.version}"
                        )

                except Exception as e:
                    logger.error(f"Error discovering plugin {plugin_file}: {e}")

        logger.info(f"Discovered {discovered_count} plugins")

    def _load_plugin_metadata(self, plugin_file: Path) -> Optional[PluginMetadata]:
        """Load plugin metadata from plugin.json file."""
        try:
            with open(plugin_file, "r") as f:
                data = json.load(f)

            # Validate required fields
            required_fields = [
                "name",
                "version",
                "description",
                "author",
                "type",
                "main_module",
            ]
            for field in required_fields:
                if field not in data:
                    logger.error(
                        f"Plugin {plugin_file} missing required field: {field}"
                    )
                    return None

            # Create metadata
            metadata = PluginMetadata(
                name=data["name"],
                version=data["version"],
                description=data["description"],
                author=data["author"],
                plugin_type=PluginType(data["type"]),
                dependencies=data.get("dependencies", []),
                min_gui_version=data.get("min_gui_version", "1.0.0"),
                max_gui_version=data.get("max_gui_version", "999.0.0"),
                enabled=data.get("enabled", True),
                config=data.get("config", {}),
            )

            # Store main module path
            metadata.config["main_module"] = data["main_module"]
            metadata.config["plugin_path"] = str(plugin_file.parent)

            return metadata

        except Exception as e:
            logger.error(f"Error loading plugin metadata from {plugin_file}: {e}")
            return None

    # TODO: Refactor function - Function 'load_plugin' too long (63 lines)
    def load_plugin(self, plugin_name: str) -> bool:
        """
        Load a specific plugin.

        Args:
            plugin_name: Name of plugin to load

        Returns:
            bool: True if loading successful
        """
        if plugin_name in self.loaded_plugins:
            logger.warning(f"Plugin {plugin_name} already loaded")
            return True

        metadata = self.plugin_metadata.get(plugin_name)
        if not metadata:
            logger.error(f"Plugin metadata not found: {plugin_name}")
            return False

        if not metadata.enabled:
            logger.info(f"Plugin {plugin_name} is disabled")
            return False

        try:
            # Check dependencies
            if not self._check_dependencies(metadata):
                logger.error(f"Plugin {plugin_name} dependencies not met")
                return False

            # Add plugin path to sys.path
            plugin_path = Path(metadata.config["plugin_path"])
            if str(plugin_path) not in sys.path:
                sys.path.insert(0, str(plugin_path))

            # Import plugin module
            module_name = metadata.config["main_module"]
            module = importlib.import_module(module_name)

            # Find plugin class
            plugin_class = self._find_plugin_class(module, metadata.plugin_type)
            if not plugin_class:
                logger.error(f"No valid plugin class found in {module_name}")
                return False

            # Create plugin instance
            plugin_instance = plugin_class(metadata)

            # Initialize plugin
            if self.gui_reference:
                if not plugin_instance.initialize(self.gui_reference):
                    logger.error(f"Plugin {plugin_name} initialization failed")
                    return False

            # Store loaded plugin
            self.loaded_plugins[plugin_name] = plugin_instance
            plugin_instance.is_loaded = True

            logger.info(f"Plugin {plugin_name} loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Error loading plugin {plugin_name}: {e}")
            return False

    def _check_dependencies(self, metadata: PluginMetadata) -> bool:
        """Check if plugin dependencies are met."""
        for dependency in metadata.dependencies:
            if dependency not in self.loaded_plugins:
                # Try to load dependency
                if not self.load_plugin(dependency):
                    return False
        return True

    def _find_plugin_class(
        self, module: Any, plugin_type: PluginType
    ) -> Optional[Type[PluginInterface]]:
        """Find the plugin class in the module."""
        base_classes = {
            PluginType.ANALYSIS: AnalysisPlugin,
            PluginType.VISUALIZATION: VisualizationPlugin,
            PluginType.PROCESSING: ProcessingPlugin,
        }

        base_class = base_classes.get(plugin_type, PluginInterface)

        for name, obj in inspect.getmembers(module, inspect.isclass):
            if (
                issubclass(obj, base_class)
                and obj != base_class
                and obj != PluginInterface
            ):
                return obj

        return None

    def activate_plugin(self, plugin_name: str) -> bool:
        """
        Activate a loaded plugin.

        Args:
            plugin_name: Name of plugin to activate

        Returns:
            bool: True if activation successful
        """
        if plugin_name not in self.loaded_plugins:
            if not self.load_plugin(plugin_name):
                return False

        plugin = self.loaded_plugins[plugin_name]

        if plugin.is_active:
            logger.warning(f"Plugin {plugin_name} already active")
            return True

        try:
            if plugin.activate():
                self.active_plugins[plugin_name] = plugin
                plugin.is_active = True
                logger.info(f"Plugin {plugin_name} activated")

                # Trigger activation event
                self._trigger_event("plugin_activated", plugin_name, plugin)
                return True
            else:
                logger.error(f"Plugin {plugin_name} activation failed")
                return False

        except Exception as e:
            logger.error(f"Error activating plugin {plugin_name}: {e}")
            return False

    def deactivate_plugin(self, plugin_name: str) -> bool:
        """
        Deactivate an active plugin.

        Args:
            plugin_name: Name of plugin to deactivate

        Returns:
            bool: True if deactivation successful
        """
        if plugin_name not in self.active_plugins:
            logger.warning(f"Plugin {plugin_name} not active")
            return True

        plugin = self.active_plugins[plugin_name]

        try:
            if plugin.deactivate():
                del self.active_plugins[plugin_name]
                plugin.is_active = False
                logger.info(f"Plugin {plugin_name} deactivated")

                # Trigger deactivation event
                self._trigger_event("plugin_deactivated", plugin_name, plugin)
                return True
            else:
                logger.error(f"Plugin {plugin_name} deactivation failed")
                return False

        except Exception as e:
            logger.error(f"Error deactivating plugin {plugin_name}: {e}")
            return False

    def unload_plugin(self, plugin_name: str) -> bool:
        """
        Unload a plugin completely.

        Args:
            plugin_name: Name of plugin to unload

        Returns:
            bool: True if unloading successful
        """
        # Deactivate first if active
        if plugin_name in self.active_plugins:
            if not self.deactivate_plugin(plugin_name):
                return False

        if plugin_name not in self.loaded_plugins:
            logger.warning(f"Plugin {plugin_name} not loaded")
            return True

        plugin = self.loaded_plugins[plugin_name]

        try:
            if plugin.cleanup():
                del self.loaded_plugins[plugin_name]
                plugin.is_loaded = False
                logger.info(f"Plugin {plugin_name} unloaded")

                # Trigger unload event
                self._trigger_event("plugin_unloaded", plugin_name, plugin)
                return True
            else:
                logger.error(f"Plugin {plugin_name} cleanup failed")
                return False

        except Exception as e:
            logger.error(f"Error unloading plugin {plugin_name}: {e}")
            return False

    def get_plugins_by_type(self, plugin_type: PluginType) -> List[str]:
        """Get list of plugin names by type."""
        return self.plugin_registry.get(plugin_type, [])

    def get_active_plugins_by_type(
        self, plugin_type: PluginType
    ) -> List[PluginInterface]:
        """Get list of active plugins by type."""
        plugin_names = self.get_plugins_by_type(plugin_type)
        return [
            self.active_plugins[name]
            for name in plugin_names
            if name in self.active_plugins
        ]

    def register_event_hook(self, event_name: str, callback: Callable):
        """Register an event hook."""
        if event_name not in self.event_hooks:
            self.event_hooks[event_name] = []
        self.event_hooks[event_name].append(callback)

    def _trigger_event(self, event_name: str, *args, **kwargs):
        """Trigger event hooks."""
        if event_name in self.event_hooks:
            for callback in self.event_hooks[event_name]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Error in event hook {callback}: {e}")

    def get_plugin_status(self) -> Dict[str, Any]:
        """Get comprehensive plugin status."""
        return {
            "discovered": len(self.plugin_metadata),
            "loaded": len(self.loaded_plugins),
            "active": len(self.active_plugins),
            "by_type": {
                plugin_type.value: {
                    "total": len(plugins),
                    "active": len([p for p in plugins if p in self.active_plugins]),
                }
                for plugin_type, plugins in self.plugin_registry.items()
            },
            "plugin_details": {
                name: {
                    "version": metadata.version,
                    "type": metadata.plugin_type.value,
                    "loaded": name in self.loaded_plugins,
                    "active": name in self.active_plugins,
                    "enabled": metadata.enabled,
                }
                for name, metadata in self.plugin_metadata.items()
            },
        }

    def shutdown(self):
        """Shutdown the plugin manager gracefully."""
        logger.info("Shutting down Plugin Manager...")

        # Deactivate all active plugins
        for plugin_name in list(self.active_plugins.keys()):
            self.deactivate_plugin(plugin_name)

        # Unload all loaded plugins
        for plugin_name in list(self.loaded_plugins.keys()):
            self.unload_plugin(plugin_name)

        logger.info("Plugin Manager shutdown complete")


# Global plugin manager instance
plugin_manager = PluginManager()
