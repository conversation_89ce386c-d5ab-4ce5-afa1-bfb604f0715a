#!/usr/bin/env python3
"""
Motion Correction Visualizer for AreTomo3 Output

Interactive visualizer for motion correction results including:
- Motion corrected images display
- Motion trajectory plots
- Frame-by-frame motion statistics
- Quality assessment visualization
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.patches import Circle
from matplotlib.widgets import Button, Slider

logger = logging.getLogger(__name__)


# TODO: Refactor class - Class 'MotionCorrectionVisualizer' too long (521
# lines)
class MotionCorrectionVisualizer:
    """
    Interactive visualizer for motion correction results.

    Features:
    - Motion corrected image display
    - Motion trajectory visualization
    - Interactive tilt navigation
    - Quality metrics display
    - Before/after comparison (when available)
    """

    def __init__(self, motion_data: Dict, theme: Optional[Dict] = None):
        """
        Initialize motion correction visualizer.

        Args:
            motion_data: Dictionary containing motion correction data
            theme: Optional theme dictionary for styling
        """
        self.motion_data = motion_data
        self.series_name = motion_data.get("series_name", "Unknown")
        self.parameters = motion_data.get("parameters", pd.DataFrame())
        self.statistics = motion_data.get("statistics", {})
        self.motion_images = motion_data.get("motion_images", None)
        self.tilt_angles = motion_data.get("tilt_angles", [])
        self.n_tilts = motion_data.get("n_tilts", 0)

        # Current state
        self.current_tilt_idx = 0
        self.show_trajectories = True
        self.show_statistics = True

        # UI elements
        self.fig = None
        self.ax_main = None  # Motion corrected image
        self.ax_trajectory = None  # Motion trajectory plot
        self.ax_stats = None  # Statistics plot
        self.ax_info = None  # Information panel
        self.slider = None
        self.im = None

        # Theme setup
        self.theme = theme or self._get_default_theme()

        # Validate data
        if self.n_tilts == 0:
            raise ValueError("No motion correction data available")

        logger.info(
            f"Initialized motion visualizer for {
                self.series_name} with {
                self.n_tilts} tilts"
        )

    def _get_default_theme(self) -> Dict:
        """Get default dark theme matching the GUI."""
        return {
            "bg_color": "#2b2b2b",
            "text_color": "#ffffff",
            "accent_color": "#4a9eff",
            "button_color": "#404040",
            "button_hover": "#505050",
            "grid_color": "#404040",
            "motion_good": "#4CAF50",
            "motion_fair": "#FF9800",
            "motion_poor": "#F44336",
            "trajectory_color": "#00BCD4",
        }

    # TODO: Refactor function - Function 'create_interactive_viewer' too long
    # (70 lines)
    def create_interactive_viewer(self):
        """Create the interactive motion correction viewer."""
        # Set up matplotlib for dark theme
        plt.style.use("dark_background")

        # Create figure with subplots
        self.fig = plt.figure(figsize=(16, 10), facecolor=self.theme["bg_color"])
        self.fig.suptitle(
            f"Motion Correction Analysis: {self.series_name}",
            color=self.theme["text_color"],
            fontsize=14,
            fontweight="bold",
        )

        # Create subplot layout
        # Main image (top left, large)
        self.ax_main = plt.subplot2grid(
            (6, 6), (0, 0), colspan=3, rowspan=3, facecolor=self.theme["bg_color"]
        )

        # Motion trajectory plot (top right)
        self.ax_trajectory = plt.subplot2grid(
            (6, 6), (0, 3), colspan=3, rowspan=2, facecolor=self.theme["bg_color"]
        )

        # Statistics plot (middle right)
        self.ax_stats = plt.subplot2grid(
            (6, 6), (2, 3), colspan=3, rowspan=1, facecolor=self.theme["bg_color"]
        )

        # Information panel (bottom left)
        self.ax_info = plt.subplot2grid(
            (6, 6), (3, 0), colspan=3, rowspan=2, facecolor=self.theme["bg_color"]
        )

        # Slider for tilt navigation (bottom)
        self.ax_slider = plt.subplot2grid(
            (6, 6), (5, 0), colspan=6, facecolor=self.theme["bg_color"]
        )

        # Initialize slider
        self.ax_slider.set_facecolor(self.theme["bg_color"])
        self.slider = Slider(
            self.ax_slider,
            "Tilt Navigation",
            0,
            self.n_tilts - 1,
            valinit=0,
            valfmt="%d",
            valstep=1,
            facecolor=self.theme["accent_color"],
            edgecolor=self.theme["text_color"],
        )
        self.slider.label.set_color(self.theme["text_color"])
        self.slider.valtext.set_color(self.theme["text_color"])
        self.slider.on_changed(self.update_tilt)

        # Add control buttons
        self._create_control_buttons()

        # Display initial tilt
        self.update_display()

        # Connect mouse events
        self.fig.canvas.mpl_connect("scroll_event", self.on_scroll)
        self.fig.canvas.mpl_connect("button_press_event", self.on_mouse_press)
        self.fig.canvas.mpl_connect("motion_notify_event", self.on_mouse_motion)

        plt.tight_layout()
        return self.fig

    # TODO: Refactor function - Function '_create_control_buttons' too long
    # (57 lines)
    def _create_control_buttons(self):
        """Create control buttons for the interface."""
        button_width = 0.12
        button_height = 0.04

        # Toggle trajectory button
        ax_traj = plt.axes(
            [0.68, 0.35, button_width, button_height],
            facecolor=self.theme["button_color"],
        )
        self.btn_trajectory = Button(
            ax_traj,
            "Trajectories",
            color=self.theme["button_color"],
            hovercolor=self.theme["button_hover"],
        )
        self.btn_trajectory.label.set_color(self.theme["text_color"])
        self.btn_trajectory.on_clicked(self.toggle_trajectories)

        # Toggle statistics button
        ax_stats = plt.axes(
            [0.82, 0.35, button_width, button_height],
            facecolor=self.theme["button_color"],
        )
        self.btn_stats = Button(
            ax_stats,
            "Statistics",
            color=self.theme["button_color"],
            hovercolor=self.theme["button_hover"],
        )
        self.btn_stats.label.set_color(self.theme["text_color"])
        self.btn_stats.on_clicked(self.toggle_statistics)

        # Previous/Next buttons
        ax_prev = plt.axes(
            [0.68, 0.30, 0.06, button_height], facecolor=self.theme["button_color"]
        )
        self.btn_prev = Button(
            ax_prev,
            "◀",
            color=self.theme["button_color"],
            hovercolor=self.theme["button_hover"],
        )
        self.btn_prev.label.set_color(self.theme["text_color"])
        self.btn_prev.on_clicked(self.prev_tilt)

        ax_next = plt.axes(
            [0.88, 0.30, 0.06, button_height], facecolor=self.theme["button_color"]
        )
        self.btn_next = Button(
            ax_next,
            "▶",
            color=self.theme["button_color"],
            hovercolor=self.theme["button_hover"],
        )
        self.btn_next.label.set_color(self.theme["text_color"])
        self.btn_next.on_clicked(self.next_tilt)

    def update_tilt(self, val):
        """Update display when slider value changes."""
        self.current_tilt_idx = int(self.slider.val)
        self.update_display()

    def update_display(self):
        """Update the main display with current tilt data."""
        if self.current_tilt_idx >= self.n_tilts:
            return

        # Update motion corrected image
        self.update_motion_image()

        # Update motion trajectory plot
        self.update_trajectory_plot()

        # Update statistics plot
        self.update_statistics_plot()

        # Update information panel
        self.update_info_panel()

        # Update title
        tilt_info = ""
        if self.tilt_angles and self.current_tilt_idx < len(self.tilt_angles):
            tilt_angle = self.tilt_angles[self.current_tilt_idx]
            tilt_info = f" (Stage: {tilt_angle:.1f}°)"

        self.ax_main.set_title(
            f"Motion Corrected Image - Tilt {self.current_tilt_idx + 1}/{self.n_tilts}{tilt_info}",
            color=self.theme["text_color"],
            fontsize=10,
        )

        self.fig.canvas.draw()

    def update_motion_image(self):
        """Update the motion corrected image display."""
        self.ax_main.clear()
        self.ax_main.set_facecolor(self.theme["bg_color"])

        if self.motion_images is not None and self.current_tilt_idx < len(
            self.motion_images
        ):
            # Display motion corrected image
            current_image = self.motion_images[self.current_tilt_idx]

            # Apply contrast enhancement
            vmin, vmax = np.percentile(current_image, [1, 99])

            self.im = self.ax_main.imshow(
                current_image,
                cmap="gray",
                origin="lower",
                vmin=vmin,
                vmax=vmax,
                interpolation="bilinear",
            )
            self.ax_main.set_aspect("equal")

            # Add scale bar if pixel size is known
            self._add_scale_bar()

        else:
            # No image available - show placeholder
            self.ax_main.text(
                0.5,
                0.5,
                "No Motion Corrected\nImage Available",
                transform=self.ax_main.transAxes,
                ha="center",
                va="center",
                color=self.theme["text_color"],
                fontsize=12,
            )

        self.ax_main.set_xticks([])
        self.ax_main.set_yticks([])

    def _add_scale_bar(self):
        """Add scale bar to the image if pixel size is known."""
        # This would require pixel size information from metadata
        # For now, skip scale bar implementation
        pass

    def update_trajectory_plot(self):
        """Update the motion trajectory plot."""
        self.ax_trajectory.clear()
        self.ax_trajectory.set_facecolor(self.theme["bg_color"])

        if not self.parameters.empty and self.show_trajectories:
            # Plot motion trajectory for current tilt
            if (
                "x_shift" in self.parameters.columns
                and "y_shift" in self.parameters.columns
            ):
                current_params = self.parameters.iloc[self.current_tilt_idx]

                # Create a simple trajectory visualization
                # This is a placeholder - real implementation would show
                # frame-by-frame motion
                x_shifts = [0, current_params.get("x_shift", 0)]
                y_shifts = [0, current_params.get("y_shift", 0)]

                self.ax_trajectory.plot(
                    x_shifts,
                    y_shifts,
                    "o-",
                    color=self.theme["trajectory_color"],
                    linewidth=2,
                    markersize=6,
                )

                # Add motion magnitude annotation
                total_motion = current_params.get("total_motion", 0)
                self.ax_trajectory.text(
                    0.02,
                    0.98,
                    f"Total Motion: {total_motion:.2f} Å",
                    transform=self.ax_trajectory.transAxes,
                    color=self.theme["text_color"],
                    fontsize=10,
                    verticalalignment="top",
                )

        # Style the plot
        self.ax_trajectory.set_title(
            "Motion Trajectory", color=self.theme["text_color"], fontsize=10
        )
        self.ax_trajectory.set_xlabel("X Shift (Å)", color=self.theme["text_color"])
        self.ax_trajectory.set_ylabel("Y Shift (Å)", color=self.theme["text_color"])
        self.ax_trajectory.tick_params(colors=self.theme["text_color"])
        self.ax_trajectory.grid(True, alpha=0.3, color=self.theme["grid_color"])

    def update_statistics_plot(self):
        """Update the statistics plot."""
        self.ax_stats.clear()
        self.ax_stats.set_facecolor(self.theme["bg_color"])

        if not self.parameters.empty and self.show_statistics:
            # Plot motion statistics across all tilts
            tilt_indices = range(len(self.parameters))
            total_motions = self.parameters["total_motion"].values

            # Color code based on motion quality
            colors = []
            for motion in total_motions:
                if motion < 2.0:
                    colors.append(self.theme["motion_good"])
                elif motion < 5.0:
                    colors.append(self.theme["motion_fair"])
                else:
                    colors.append(self.theme["motion_poor"])

            bars = self.ax_stats.bar(
                tilt_indices, total_motions, color=colors, alpha=0.7
            )

            # Highlight current tilt
            if self.current_tilt_idx < len(bars):
                bars[self.current_tilt_idx].set_edgecolor(self.theme["accent_color"])
                bars[self.current_tilt_idx].set_linewidth(2)

        # Style the plot
        self.ax_stats.set_title(
            "Motion Statistics", color=self.theme["text_color"], fontsize=10
        )
        self.ax_stats.set_xlabel("Tilt Index", color=self.theme["text_color"])
        self.ax_stats.set_ylabel("Total Motion (Å)", color=self.theme["text_color"])
        self.ax_stats.tick_params(colors=self.theme["text_color"])
        self.ax_stats.grid(True, alpha=0.3, color=self.theme["grid_color"])

    # TODO: Refactor function - Function 'update_info_panel' too long (65
    # lines)
    def update_info_panel(self):
        """Update the information panel with current motion data."""
        self.ax_info.clear()
        self.ax_info.axis("off")
        self.ax_info.set_facecolor(self.theme["bg_color"])

        if self.parameters.empty:
            return

        # Get current parameters
        current_params = self.parameters.iloc[self.current_tilt_idx]

        # Format information
        info_lines = [
            "📊 MOTION INFORMATION",
            "",
            f"Tilt: {self.current_tilt_idx + 1}/{self.n_tilts}",
            f"Total Motion: {current_params.get('total_motion', 0):.2f} Å",
            f"X Shift: {current_params.get('x_shift', 0):.2f} Å",
            f"Y Shift: {current_params.get('y_shift', 0):.2f} Å",
            "",
            "📈 QUALITY METRICS",
            "",
            f"Correlation: {current_params.get('correlation', 0):.3f}",
            f"Frame Count: {current_params.get('frame_count', 0):.0f}",
            "",
            "📋 SERIES STATISTICS",
            "",
            f"Mean Motion: {self.statistics.get('mean_total_motion', 0):.2f} Å",
            f"Max Motion: {self.statistics.get('max_total_motion', 0):.2f} Å",
            f"Quality: {self.statistics.get('quality_assessment', 'Unknown').title()}",
        ]

        # Add text with theme colors
        y_pos = 0.95
        for line in info_lines:
            if line.startswith(("📊", "📈", "📋")):
                # Header lines
                self.ax_info.text(
                    0.05,
                    y_pos,
                    line,
                    transform=self.ax_info.transAxes,
                    fontsize=10,
                    fontweight="bold",
                    verticalalignment="top",
                    color=self.theme["accent_color"],
                )
            elif line == "":
                # Empty lines for spacing
                pass
            else:
                # Regular text
                self.ax_info.text(
                    0.05,
                    y_pos,
                    line,
                    transform=self.ax_info.transAxes,
                    fontsize=9,
                    verticalalignment="top",
                    fontfamily="monospace",
                    color=self.theme["text_color"],
                )

            y_pos -= 0.05  # Move down for next line

    def toggle_trajectories(self, event):
        """Toggle motion trajectory display."""
        self.show_trajectories = not self.show_trajectories
        self.update_display()

    def toggle_statistics(self, event):
        """Toggle statistics display."""
        self.show_statistics = not self.show_statistics
        self.update_display()

    def prev_tilt(self, event):
        """Go to previous tilt."""
        if self.current_tilt_idx > 0:
            self.current_tilt_idx -= 1
            self.slider.set_val(self.current_tilt_idx)

    def next_tilt(self, event):
        """Go to next tilt."""
        if self.current_tilt_idx < self.n_tilts - 1:
            self.current_tilt_idx += 1
            self.slider.set_val(self.current_tilt_idx)

    def on_scroll(self, event):
        """Handle mouse scroll for zooming."""
        if event.inaxes != self.ax_main:
            return

        # Zoom in/out on main image
        scale_factor = 1.1 if event.button == "up" else 1 / 1.1

        xlim = self.ax_main.get_xlim()
        ylim = self.ax_main.get_ylim()

        # Get mouse position
        xdata, ydata = event.xdata, event.ydata

        # Calculate new limits
        new_width = (xlim[1] - xlim[0]) * scale_factor
        new_height = (ylim[1] - ylim[0]) * scale_factor

        relx = (xlim[1] - xdata) / (xlim[1] - xlim[0])
        rely = (ylim[1] - ydata) / (ylim[1] - ylim[0])

        self.ax_main.set_xlim(
            [xdata - new_width * (1 - relx), xdata + new_width * relx]
        )
        self.ax_main.set_ylim(
            [ydata - new_height * (1 - rely), ydata + new_height * rely]
        )

        self.fig.canvas.draw()

    def on_mouse_press(self, event):
        """Handle mouse press for panning."""
        if event.inaxes != self.ax_main:
            return
        self.last_mouse_pos = (event.xdata, event.ydata)

    def on_mouse_motion(self, event):
        """Handle mouse motion for panning."""
        if (
            event.inaxes != self.ax_main
            or not hasattr(self, "last_mouse_pos")
            or not event.button
        ):
            return

        if self.last_mouse_pos is None:
            return

        dx = event.xdata - self.last_mouse_pos[0]
        dy = event.ydata - self.last_mouse_pos[1]

        xlim = self.ax_main.get_xlim()
        ylim = self.ax_main.get_ylim()

        self.ax_main.set_xlim([xlim[0] - dx, xlim[1] - dx])
        self.ax_main.set_ylim([ylim[0] - dy, ylim[1] - dy])

        self.fig.canvas.draw()

    def show(self):
        """Display the interactive viewer."""
        if self.fig is None:
            self.create_interactive_viewer()
        plt.show()
        return self.fig


def test_motion_visualizer():
    """Test function for motion visualizer."""
    import sys

    from .motion_parser import MotionCorrectionParser

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        # Parse motion data
        parser = MotionCorrectionParser(test_path)
        motion_data = parser.parse_all()

        if not motion_data["has_motion_data"]:
            logger.info("No motion correction data available - cannot test visualizer")
            return False

        # Create visualizer
        visualizer = MotionCorrectionVisualizer(motion_data)

        logger.info(
            f"Created motion visualizer for {
                motion_data['series_name']}"
        )
        logger.info("Use slider to navigate through tilts")
        logger.info("Use mouse wheel to zoom, drag to pan")
        logger.info("Use buttons to toggle trajectories and statistics")

        # Show interactive viewer
        visualizer.show()

        return True

    except Exception as e:
        logger.info(f"Error testing motion visualizer: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_motion_visualizer()
