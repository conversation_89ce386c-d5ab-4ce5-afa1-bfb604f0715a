#!/usr/bin/env python3
"""
Enhanced Parameters Tab for AreTomo3 GUI.
Organizes parameters into General and Advanced subtabs based on AreTomo3 --help.
"""

import json
import logging
import os
from typing import Any, Dict

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import Q<PERSON>ont
from PyQt6.QtWidgets import (
    QApplication,
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QFormLayout,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QMessageBox,
    QPushButton,
    QScrollArea,
    QSpinBox,
    QTabWidget,
    QTextEdit,
    QToolTip,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)

# Parameter help texts from AreTomo3 --help
PARAMETER_HELP = {
    "in_prefix": """Prefix of input file name(s), together with Insuffix and InSkips, is used to form either a single or subset for file name(s), which are processed by AreTomo3.

• If the suffix is mdoc, any mdoc file that starts with the prefix string will be selected.
• If the suffix is mrc, any mrc file that starts with the prefix string will be selected and processed.
• The prefix can also be the path of a folder containing the movie files (tiff or eer) or tilt series (mrc).
• Note that movie files must be in the same directory as the mdoc files.""",
    "out_dir": """Path to output folder to store generated tilt series, tomograms, and alignment files.""",
    "pix_size": """Pixel size in Å of input stack in angstrom.""",
    "kv": """High tension in kV needed for dose weighting. Default is 300.""",
    "cs": """Spherical aberration in mm for CTF estimation.""",
    "fm_dose": """Per frame dose in e⁻/Å².""",
    "amp_contrast": """Amplitude contrast, default 0.07""",
    "tilt_axis": """User provided angle of tilt axis in degree. If users do not provide one, AreTomo3 will search in full range.

• If users provide one and do not want AreTomo3 to refine it, add -1 after the provided tilt axis.
• Otherwise, AreTomo3 refines the provided value in a smaller range.""",
    "vol_z": """Volume z height for reconstruction. It must be greater than 0 to reconstruct a volume.

• Default is 0, only aligned tilt series will be generated.""",
    "at_bin": """Binnings for tomograms with respect to motion corrected tilt series. Users can specify two floats.

• The first number is required and 1.0 is default.
• The second number is optional. By default, the second resolution is disabled.""",
    "gpu": """GPU IDs. Default 0.

For multiple GPUs, separate IDs by space.
For example, 0 1 2 3 specifies 4 GPUs.""",
    "split_sum": """Generate odd and even sums using odd and even frames.

• The default value is 1, which enables the split, 0 disables this function.
• When enabled, 3 tilt series and 3 tomograms will be generated.
• Tilt series and tomograms generated from odd and even sums are appended _ODD and _EVN in their file names, respectively.""",
    "flip_vol": """By giving a non-zero value, the reconstructed volume is saved in xyz fashion. The default is xzy.""",
    "out_imod": """It generates the Imod files needed by Relion4 or Warp for subtomogram averaging.

• 0: default, do not generate any IMod files.
• 1: generate IMod files needed for Relion 4.
• 2: generate IMod files needed for WARP.
• 3: generate IMod files when the aligned tilt series is used as the input for Relion 4 or WARP.""",
    "out_xf": """When set by giving non-zero value, IMOD compatible XF file will be generated.""",
    # Motion Correction Parameters
    "mc_patch_x": """Number of patches in x dimension for motion correction. The default values are 1 1, meaning only full-frame based alignment is performed.""",
    "mc_patch_y": """Number of patches in y dimension for motion correction. The default values are 1 1, meaning only full-frame based alignment is performed.""",
    "mc_iter": """Maximum iterations for iterative alignment, default 7 iterations.""",
    "mc_tol": """Tolerance for iterative alignment, default 0.5 pixel.""",
    "mc_bin": """Binning performed in Fourier space, default 1.0.""",
    # Gain/Dark References
    "gain_file": """MRC or TIFF file that stores the gain reference.

• Falcon camera produced .gain file can also be used since it is a TIFF file.""",
    "dark_file": """MRC file that stores the dark reference. If not specified, dark subtraction will be skipped.

• If -RotGain and/or -FlipGain is specified, the dark reference will also be rotated and/or flipped.""",
    "rot_gain": """Rotate gain reference counter-clockwise.

• 0 - no rotation, default
• 1 - rotate 90 degree
• 2 - rotate 180 degree
• 3 - rotate 270 degree""",
    "flip_gain": """Flip gain reference after gain rotation.

• 0 - no flipping, default
• 1 - flip upside down
• 2 - flip left right""",
    "inv_gain": """Inverse gain value at each pixel (1/f). If an original value is zero, the inversed value is set zero.

This option can be used together with flip and rotate gain reference.""",
    # Alignment Parameters
    "align_z": """Volume height for alignment, default 256""",
    "ext_z": """Extra volume z height for reconstruction. This is the space added to the estimated sample thickness for the final reconstruction of tomograms.

This setting is relevant only when -VolZ -1 is set, which means users want to use the estimated sample thickness.""",
    "tilt_cor": """Correct the offset of tilt angle.

• The first value can be -1, 0, or 1. Default is 0, indicating the tilt offset is measured for alignment only.
• When the value is 1, the offset is applied to reconstruction too.
• When a negative value is given, tilt is not measured nor applied.""",
    "recon_range": """It specifies the min and max tilt angles from which a 3D volume will be reconstructed.

Any tilt image whose tilt angle is outside this range is excluded in the reconstruction.""",
    # Reconstruction Parameters
    "sart": """Specify number of SART iterations. The default value is 15.""",
    "wbp": """By specifying 1, weighted back projection is enabled to reconstruct volume.""",
    "dark_tol": """Set tolerance for removing dark images. The range is in (0, 1). The default value is 0.7.

The higher value is more restrictive.""",
    "flip_int": """Flip the intensity of the volume to make structure white.

Default 0 means no flipping. Non-zero value flips.""",
    "intp_cor": """When enabled, the correction for information loss due to linear interpolation will be performed.

The default setting value 1 enables the correction.""",
    "corr_ctf": """When enabled, local CTF correction is performed on raw tilt series.

By default this function is enabled. Passing 0 disables this function.""",
    # Advanced Options
    "mag": """Correct anisotropic magnification by stretching image along the major axis.

Three inputs are needed including magnifications along major and minor axes and the angle of the major axis relative to the image x-axis in degree.

By default no correction is performed.""",
    "in_fm_motion": """Path to frame motion file for in-frame motion correction.""",
    "ext_phase": """Guess of phase shift and search range in degree.

Only required for CTF estimation with phase plate installed.""",
    "resume": """Resume processing from what are left by skipping all the mdoc files in MdocDone.txt file in the output folder.

• Default 0 processes all the data.
• Resume 1 starts from what are left.""",
    # Additional parameters from AreTomo3 --help
    "cmd": """Processing command mode.

• Default 0 starts processing from motion correction.
• Cmd 1 starts processing from tilt series alignment including CTF estimation, correction, tomographic alignment and reconstruction.
• Cmd 2 starts processing from CTF correction and then tomographic reconstruction.
• Cmd 1 and Cmd 2 ignore -Resume.""",
    "defect_file": """Defect file stores entries of defects on camera.

• Each entry corresponds to a rectangular region in image.
• The pixels in such a region are replaced by neighboring good pixel values.
• Each entry contains 4 integers x, y, w, h representing the x, y coordinates, width, and heights, respectively.""",
    "group": """Group every specified number of frames by adding them together.

• The alignment is then performed on the group sums.
• The so measured motion is interpolated to each raw frame.
• The 1st integer is for global alignment and the 2nd is for patch alignment.""",
    "fm_ref": """Specify a frame in the input movie stack to be the reference to which all other frames are aligned.

• The reference is 1-based index in the input movie stack regardless how many frames will be thrown.
• By default the reference is set to be the central frame.""",
    "align": """Skip alignment when followed by 0.

• This option is used when the input MRC file is an aligned tilt series.
• The default value is 1.""",
    "sart_projections": """Number of projections per SART update.

• Used together with SART iterations.
• Default value is 5.""",
    # Additional missing parameters
    "insuffix": """File extension for input files.

• .mdoc for MDOC files (live processing)
• .mrc, .mrcs, .st for MRC tilt series (batch processing)
• AreTomo3 determines processing mode based on suffix.""",
    "inskips": """Skip files containing this pattern.

• Useful to avoid processing duplicate files
• Example: _override skips files like Position_1_3_override.mdoc
• Prevents processing same movies twice.""",
    "serial": """Processing mode control.

• 0: Single mode (process one file)
• 1: Batch mode (process all matching files)
• >1: Live mode (wait N seconds for new files)""",
    "eer_sampling": """EER super-resolution sampling factor.

• 1: Normal resolution (default)
• 2: Super-resolution (use with -McBin 2)
• Higher values extract more information from EER encoding.""",
    "fm_int": """Number of raw frames summed to form rendered frame.

• 15: Default for EER movies
• 1-2: Recommended for TIFF movies
• Rendered frames used for motion measurement.""",
    "at_patch": """Local alignment patches.

• 0 0: Global alignment only (default)
• X Y: Enable X×Y patch-based local alignment
• Improves alignment for thick samples.""",
    "ctf_lowpass": """Lowpass filter for CTF correction.

• Default: 15.0 Å
• Applied during local CTF correction
• Higher values = more aggressive filtering.""",
    "at_bin_2nd": """Second binning factor for multi-resolution tomograms.

• 0: Disabled (default)
• >0: Generate additional tomogram with this binning
• Uses weighted back-projection without CTF correction.""",
    "at_bin_3rd": """Third binning factor for multi-resolution tomograms.

• 0: Disabled (default)
• >0: Generate additional tomogram with this binning
• Uses SART reconstruction method.""",
}


# TODO: Refactor class - Class 'EnhancedParametersTab' too long (1778 lines)
class EnhancedParametersTab(QWidget):
    """
    Reconstruction Parameters Tab - Comprehensive AreTomo3 parameter configuration.

    Features:
    - General parameters (microscopy settings, basic reconstruction)
    - Advanced parameters (motion correction, CTF estimation, alignment)
    - Parameter templates for common workflows
    - Real-time command preview
    - Parameter validation and tooltips
    """

    parameters_changed = pyqtSignal()  # Signal when parameters change

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        logger.info("Enhanced parameters tab created successfully")

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)

        # Create tab widget for General/Advanced
        self.param_tabs = QTabWidget()

        # Create General and Advanced tabs
        self.general_tab = self.create_general_tab()
        self.advanced_tab = self.create_advanced_tab()

        self.param_tabs.addTab(self.general_tab, "🔧 General")
        self.param_tabs.addTab(self.advanced_tab, "⚙️ Advanced")

        # Give parameters tab full space (100%)
        layout.addWidget(self.param_tabs, 1)

    # TODO: Refactor function - Function 'create_general_tab' too long (388
    # lines)
    def create_general_tab(self):
        """Create the General parameters tab."""
        tab = QWidget()

        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        layout.setSpacing(8)  # Reduce spacing between groups
        layout.setContentsMargins(8, 8, 8, 8)  # Reduce margins

        # Note about I/O configuration
        note_label = QLabel(
            "📋 Input/Output paths are configured in Batch Processing or Live Processing tabs"
        )
        note_label.setStyleSheet(
            """
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 4px;
                border-left: 3px solid #3498db;
                margin-bottom: 10px;
            }
        """
        )
        note_label.setWordWrap(True)
        layout.addWidget(note_label)

        # Microscope Parameters Group
        microscope_group = QGroupBox("🔬 Microscope Parameters")
        microscope_layout = QGridLayout(microscope_group)
        microscope_layout.setSpacing(6)  # Reduce internal spacing

        # Pixel size
        self.pix_size = QDoubleSpinBox()
        self.pix_size.setRange(0.1, 100.0)
        self.pix_size.setValue(1.91)
        self.pix_size.setSuffix(" Å")
        self.pix_size.setDecimals(3)
        help_pix_btn = self.create_help_button("pix_size")
        microscope_layout.addWidget(QLabel("Pixel Size:"), 0, 0)
        microscope_layout.addWidget(self.pix_size, 0, 1)
        microscope_layout.addWidget(help_pix_btn, 0, 2)

        # Voltage (kV)
        self.kv = QSpinBox()
        self.kv.setRange(60, 300)
        self.kv.setValue(300)
        self.kv.setSuffix(" kV")
        help_kv_btn = self.create_help_button("kv")
        microscope_layout.addWidget(QLabel("Voltage:"), 0, 3)
        microscope_layout.addWidget(self.kv, 0, 4)
        microscope_layout.addWidget(help_kv_btn, 0, 5)

        # Spherical aberration (Cs)
        self.cs = QDoubleSpinBox()
        self.cs.setRange(0.0, 10.0)
        self.cs.setValue(2.7)
        self.cs.setSuffix(" mm")
        self.cs.setDecimals(2)
        help_cs_btn = self.create_help_button("cs")
        microscope_layout.addWidget(QLabel("Cs:"), 1, 0)
        microscope_layout.addWidget(self.cs, 1, 1)
        microscope_layout.addWidget(help_cs_btn, 1, 2)

        # Frame dose
        self.fm_dose = QDoubleSpinBox()
        self.fm_dose.setRange(0.01, 100.0)
        self.fm_dose.setValue(0.14)
        self.fm_dose.setSuffix(" e⁻/Å²")
        self.fm_dose.setDecimals(3)
        help_fm_dose_btn = self.create_help_button("fm_dose")
        microscope_layout.addWidget(QLabel("Frame Dose:"), 1, 3)
        microscope_layout.addWidget(self.fm_dose, 1, 4)
        microscope_layout.addWidget(help_fm_dose_btn, 1, 5)

        # Amplitude contrast
        self.amp_contrast = QDoubleSpinBox()
        self.amp_contrast.setRange(0.0, 1.0)
        self.amp_contrast.setValue(0.07)
        self.amp_contrast.setDecimals(3)
        help_amp_btn = self.create_help_button("amp_contrast")
        microscope_layout.addWidget(QLabel("Amplitude Contrast:"), 2, 0)
        microscope_layout.addWidget(self.amp_contrast, 2, 1)
        microscope_layout.addWidget(help_amp_btn, 2, 2)

        layout.addWidget(microscope_group)

        # Processing Parameters Group
        processing_group = QGroupBox("⚙️ Processing Parameters")
        processing_layout = QGridLayout(processing_group)
        processing_layout.setSpacing(6)  # Reduce internal spacing

        # Tilt axis
        self.tilt_axis = QDoubleSpinBox()
        self.tilt_axis.setRange(-180.0, 180.0)
        self.tilt_axis.setValue(0.0)
        self.tilt_axis.setSuffix("°")
        self.tilt_axis.setDecimals(2)
        help_tilt_axis_btn = self.create_help_button("tilt_axis")
        processing_layout.addWidget(QLabel("Tilt Axis:"), 0, 0)
        processing_layout.addWidget(self.tilt_axis, 0, 1)
        processing_layout.addWidget(help_tilt_axis_btn, 0, 2)

        # Volume Z height
        self.vol_z = QSpinBox()
        self.vol_z.setRange(0, 8192)
        self.vol_z.setValue(2048)
        self.vol_z.setSpecialValueText("Auto")
        help_vol_z_btn = self.create_help_button("vol_z")
        processing_layout.addWidget(QLabel("Volume Z:"), 0, 3)
        processing_layout.addWidget(self.vol_z, 0, 4)
        processing_layout.addWidget(help_vol_z_btn, 0, 5)

        # Alignment binning
        self.at_bin = QDoubleSpinBox()
        self.at_bin.setRange(0.1, 16.0)
        self.at_bin.setValue(4.0)
        self.at_bin.setDecimals(1)
        help_at_bin_btn = self.create_help_button("at_bin")
        processing_layout.addWidget(QLabel("Alignment Binning:"), 1, 0)
        processing_layout.addWidget(self.at_bin, 1, 1)
        processing_layout.addWidget(help_at_bin_btn, 1, 2)

        # Command mode
        self.cmd = QComboBox()
        self.cmd.addItems(
            [
                "0 - Motion correction",
                "1 - Tilt series alignment",
                "2 - CTF correction + reconstruction",
                "3 - CTF estimation only",
                "4 - Rotate tilt axis 180°",
            ]
        )
        help_cmd_btn = self.create_help_button("cmd")
        processing_layout.addWidget(QLabel("Command Mode:"), 1, 3)
        processing_layout.addWidget(self.cmd, 1, 4)
        processing_layout.addWidget(help_cmd_btn, 1, 5)

        # File suffix and processing mode
        self.insuffix = QLineEdit(".mdoc")
        self.insuffix.setPlaceholderText("File extension (.mdoc, .mrc, .st)")
        help_insuffix_btn = self.create_help_button("insuffix")
        processing_layout.addWidget(QLabel("Input Suffix:"), 2, 0)
        processing_layout.addWidget(self.insuffix, 2, 1)
        processing_layout.addWidget(help_insuffix_btn, 2, 2)

        # Skip pattern
        self.inskips = QLineEdit("_override")
        self.inskips.setPlaceholderText("Skip files containing pattern")
        help_inskips_btn = self.create_help_button("inskips")
        processing_layout.addWidget(QLabel("Skip Pattern:"), 2, 3)
        processing_layout.addWidget(self.inskips, 2, 4)
        processing_layout.addWidget(help_inskips_btn, 2, 5)

        # Serial mode (processing mode control)
        self.serial = QSpinBox()
        self.serial.setRange(0, 10000)
        self.serial.setValue(0)
        self.serial.setSpecialValueText("Single mode")
        self.serial.setSuffix(" sec")
        help_serial_btn = self.create_help_button("serial")
        processing_layout.addWidget(QLabel("Serial Mode:"), 3, 0)
        processing_layout.addWidget(self.serial, 3, 1)
        processing_layout.addWidget(help_serial_btn, 3, 2)

        layout.addWidget(processing_group)

        # File References Group (moved from advanced)
        files_group = QGroupBox("📁 File References")
        files_layout = QGridLayout(files_group)

        # Gain reference
        self.gain_file = QLineEdit()
        self.gain_file.setPlaceholderText("Path to gain reference file")
        browse_gain_btn = QPushButton("📁")
        browse_gain_btn.setMaximumWidth(40)
        browse_gain_btn.clicked.connect(self.browse_gain_file)
        help_gain_btn = self.create_help_button("gain_file")
        files_layout.addWidget(QLabel("Gain Reference:"), 0, 0)
        files_layout.addWidget(self.gain_file, 0, 1)
        files_layout.addWidget(browse_gain_btn, 0, 2)
        files_layout.addWidget(help_gain_btn, 0, 3)

        # Dark reference
        self.dark_file = QLineEdit()
        self.dark_file.setPlaceholderText("Path to dark reference file")
        browse_dark_btn = QPushButton("📁")
        browse_dark_btn.setMaximumWidth(40)
        browse_dark_btn.clicked.connect(self.browse_dark_file)
        help_dark_btn = self.create_help_button("dark_file")
        files_layout.addWidget(QLabel("Dark Reference:"), 1, 0)
        files_layout.addWidget(self.dark_file, 1, 1)
        files_layout.addWidget(browse_dark_btn, 1, 2)
        files_layout.addWidget(help_dark_btn, 1, 3)

        # Frame motion file
        self.in_fm_motion = QLineEdit()
        self.in_fm_motion.setPlaceholderText("Path to frame motion file")
        browse_fm_btn = QPushButton("📁")
        browse_fm_btn.setMaximumWidth(40)
        browse_fm_btn.clicked.connect(self.browse_fm_motion)
        help_fm_btn = self.create_help_button("in_fm_motion")
        files_layout.addWidget(QLabel("Frame Motion File:"), 2, 0)
        files_layout.addWidget(self.in_fm_motion, 2, 1)
        files_layout.addWidget(browse_fm_btn, 2, 2)
        files_layout.addWidget(help_fm_btn, 2, 3)

        layout.addWidget(files_group)

        # Common Options Group
        options_group = QGroupBox("🔧 Common Options")
        options_layout = QGridLayout(options_group)
        options_layout.setSpacing(6)  # Reduce internal spacing

        # Split sum
        self.split_sum = QCheckBox("Generate odd/even sums")
        self.split_sum.setChecked(True)
        help_split_sum_btn = self.create_help_button("split_sum")
        options_layout.addWidget(self.split_sum, 0, 0)
        options_layout.addWidget(help_split_sum_btn, 0, 1)

        # Flip volume
        self.flip_vol = QCheckBox("Flip volume (xyz → xzy)")
        self.flip_vol.setChecked(False)
        help_flip_vol_btn = self.create_help_button("flip_vol")
        options_layout.addWidget(self.flip_vol, 0, 2)
        options_layout.addWidget(help_flip_vol_btn, 0, 3)

        # Output IMOD files
        self.out_imod = QComboBox()
        self.out_imod.addItems(
            [
                "0 - No IMOD files",
                "1 - Relion 4 files",
                "2 - WARP files",
                "3 - Aligned series files",
            ]
        )
        self.out_imod.setCurrentIndex(1)
        help_out_imod_btn = self.create_help_button("out_imod")
        options_layout.addWidget(QLabel("Output IMOD:"), 1, 0)
        options_layout.addWidget(self.out_imod, 1, 1)
        options_layout.addWidget(help_out_imod_btn, 1, 2)

        # Output XF files
        self.out_xf = QCheckBox("Output XF files")
        self.out_xf.setChecked(True)
        help_out_xf_btn = self.create_help_button("out_xf")
        options_layout.addWidget(self.out_xf, 1, 3)
        options_layout.addWidget(help_out_xf_btn, 1, 4)

        layout.addWidget(options_group)

        # Command Preview Group
        command_group = QGroupBox("📋 AreTomo3 Command Preview")
        command_layout = QVBoxLayout(command_group)
        command_layout.setSpacing(8)

        # Command text area with white background
        self.command_preview = QTextEdit()
        self.command_preview.setReadOnly(True)
        self.command_preview.setMinimumHeight(150)
        self.command_preview.setMaximumHeight(200)
        self.command_preview.setStyleSheet(
            """
            QTextEdit {
                background-color: white;
                color: black;
                font-family: 'Courier New', monospace;
                font-size: 10pt;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 10px;
                line-height: 1.4;
            }
        """
        )
        self.command_preview.setPlainText(
            "# AreTomo3 command will appear here\n# Parameters will be automatically updated as you change them"
        )
        command_layout.addWidget(self.command_preview)

        # Command actions
        actions_row = QHBoxLayout()

        self.update_preview_btn = QPushButton("🔄 Update Preview")
        self.update_preview_btn.clicked.connect(self.update_command_preview)
        self.update_preview_btn.setToolTip(
            "Update command preview with current parameters"
        )
        self.update_preview_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """
        )

        self.copy_command_btn = QPushButton("📋 Copy")
        self.copy_command_btn.clicked.connect(self.copy_command)
        self.copy_command_btn.setToolTip("Copy command to clipboard")
        self.copy_command_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """
        )

        self.save_command_btn = QPushButton("💾 Save")
        self.save_command_btn.clicked.connect(self.save_command)
        self.save_command_btn.setToolTip("Save command to file")
        self.save_command_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """
        )

        self.execute_btn = QPushButton("▶️ Execute")
        self.execute_btn.clicked.connect(self.execute_command)
        self.execute_btn.setToolTip("Execute AreTomo3 command")
        self.execute_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """
        )

        actions_row.addWidget(self.update_preview_btn)
        actions_row.addWidget(self.copy_command_btn)
        actions_row.addWidget(self.save_command_btn)
        actions_row.addWidget(self.execute_btn)
        actions_row.addStretch()

        command_layout.addLayout(actions_row)
        layout.addWidget(command_group)

        # Connect parameter change signals
        self.connect_general_signals()

        layout.addStretch()
        scroll.setWidget(scroll_widget)

        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)

        return tab

    # TODO: Refactor function - Function 'create_advanced_tab' too long (347
    # lines)
    def create_advanced_tab(self):
        """Create the Advanced parameters tab."""
        tab = QWidget()

        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        layout.setSpacing(8)  # Reduce spacing between groups
        layout.setContentsMargins(8, 8, 8, 8)  # Reduce margins

        # Motion Correction Group
        mc_group = QGroupBox("🎯 Motion Correction")
        mc_layout = QGridLayout(mc_group)
        mc_layout.setSpacing(6)  # Reduce internal spacing

        # MC Patch
        self.mc_patch_x = QSpinBox()
        self.mc_patch_x.setRange(1, 20)
        self.mc_patch_x.setValue(1)
        self.mc_patch_y = QSpinBox()
        self.mc_patch_y.setRange(1, 20)
        self.mc_patch_y.setValue(1)
        help_mc_patch_x_btn = self.create_help_button("mc_patch_x")
        help_mc_patch_y_btn = self.create_help_button("mc_patch_y")

        mc_layout.addWidget(QLabel("MC Patch X:"), 0, 0)
        mc_layout.addWidget(self.mc_patch_x, 0, 1)
        mc_layout.addWidget(help_mc_patch_x_btn, 0, 2)
        mc_layout.addWidget(QLabel("MC Patch Y:"), 0, 3)
        mc_layout.addWidget(self.mc_patch_y, 0, 4)
        mc_layout.addWidget(help_mc_patch_y_btn, 0, 5)

        # MC Iterations
        self.mc_iter = QSpinBox()
        self.mc_iter.setRange(1, 20)
        self.mc_iter.setValue(7)
        help_mc_iter_btn = self.create_help_button("mc_iter")
        mc_layout.addWidget(QLabel("MC Iterations:"), 1, 0)
        mc_layout.addWidget(self.mc_iter, 1, 1)
        mc_layout.addWidget(help_mc_iter_btn, 1, 2)

        # MC Tolerance
        self.mc_tol = QDoubleSpinBox()
        self.mc_tol.setRange(0.1, 5.0)
        self.mc_tol.setValue(0.5)
        self.mc_tol.setSuffix(" px")
        self.mc_tol.setDecimals(2)
        help_mc_tol_btn = self.create_help_button("mc_tol")
        mc_layout.addWidget(QLabel("MC Tolerance:"), 1, 3)
        mc_layout.addWidget(self.mc_tol, 1, 4)
        mc_layout.addWidget(help_mc_tol_btn, 1, 5)

        # MC Binning
        self.mc_bin = QDoubleSpinBox()
        self.mc_bin.setRange(0.1, 16.0)
        self.mc_bin.setValue(1.0)
        self.mc_bin.setDecimals(1)
        help_mc_bin_btn = self.create_help_button("mc_bin")
        mc_layout.addWidget(QLabel("MC Binning:"), 2, 0)
        mc_layout.addWidget(self.mc_bin, 2, 1)
        mc_layout.addWidget(help_mc_bin_btn, 2, 2)

        # Group frames
        self.group_global = QSpinBox()
        self.group_global.setRange(1, 20)
        self.group_global.setValue(1)
        self.group_patch = QSpinBox()
        self.group_patch.setRange(1, 20)
        self.group_patch.setValue(4)  # Default 4 for local motion
        help_group_btn = self.create_help_button("group")
        mc_layout.addWidget(QLabel("Group (Global):"), 2, 3)
        mc_layout.addWidget(self.group_global, 2, 4)
        mc_layout.addWidget(help_group_btn, 2, 5)
        mc_layout.addWidget(QLabel("Group (Patch):"), 3, 3)
        mc_layout.addWidget(self.group_patch, 3, 4)

        # EER Sampling
        self.eer_sampling = QSpinBox()
        self.eer_sampling.setRange(1, 4)
        self.eer_sampling.setValue(1)
        help_eer_btn = self.create_help_button("eer_sampling")
        mc_layout.addWidget(QLabel("EER Sampling:"), 3, 0)
        mc_layout.addWidget(self.eer_sampling, 3, 1)
        mc_layout.addWidget(help_eer_btn, 3, 2)

        # Frame Integration
        self.fm_int = QSpinBox()
        self.fm_int.setRange(1, 50)
        self.fm_int.setValue(15)  # Default for EER
        help_fm_int_btn = self.create_help_button("fm_int")
        mc_layout.addWidget(QLabel("Frame Integration:"), 4, 0)
        mc_layout.addWidget(self.fm_int, 4, 1)
        mc_layout.addWidget(help_fm_int_btn, 4, 2)

        # Frame reference
        self.fm_ref = QSpinBox()
        self.fm_ref.setRange(0, 100)
        self.fm_ref.setValue(0)
        self.fm_ref.setSpecialValueText("Auto (center)")
        help_fm_ref_btn = self.create_help_button("fm_ref")
        mc_layout.addWidget(QLabel("Frame Reference:"), 3, 0)
        mc_layout.addWidget(self.fm_ref, 3, 1)
        mc_layout.addWidget(help_fm_ref_btn, 3, 2)

        layout.addWidget(mc_group)

        # Gain Transformations Group (gain/dark files moved to general)
        gain_group = QGroupBox("📷 Gain Transformations")
        gain_layout = QGridLayout(gain_group)

        # Gain transformations
        self.rot_gain = QComboBox()
        self.rot_gain.addItems(["0 - No rotation", "1 - 90°", "2 - 180°", "3 - 270°"])
        help_rot_gain_btn = self.create_help_button("rot_gain")
        gain_layout.addWidget(QLabel("Rotate Gain:"), 0, 0)
        gain_layout.addWidget(self.rot_gain, 0, 1)
        gain_layout.addWidget(help_rot_gain_btn, 0, 2)

        self.flip_gain = QComboBox()
        self.flip_gain.addItems(["0 - No flip", "1 - Upside down", "2 - Left-right"])
        help_flip_gain_btn = self.create_help_button("flip_gain")
        gain_layout.addWidget(QLabel("Flip Gain:"), 0, 3)
        gain_layout.addWidget(self.flip_gain, 0, 4)
        gain_layout.addWidget(help_flip_gain_btn, 0, 5)

        self.inv_gain = QCheckBox("Inverse gain values")
        help_inv_gain_btn = self.create_help_button("inv_gain")
        gain_layout.addWidget(self.inv_gain, 1, 0)
        gain_layout.addWidget(help_inv_gain_btn, 1, 1)

        # Defect file
        self.defect_file = QLineEdit()
        self.defect_file.setPlaceholderText("Path to defect file")
        browse_defect_btn = QPushButton("📁")
        browse_defect_btn.setMaximumWidth(40)
        browse_defect_btn.clicked.connect(self.browse_defect_file)
        help_defect_btn = self.create_help_button("defect_file")

        gain_layout.addWidget(QLabel("Defect File:"), 1, 3)
        gain_layout.addWidget(self.defect_file, 1, 4)
        gain_layout.addWidget(browse_defect_btn, 1, 5)
        gain_layout.addWidget(help_defect_btn, 1, 6)

        layout.addWidget(gain_group)

        # Alignment Group
        align_group = QGroupBox("📐 Alignment")
        align_layout = QGridLayout(align_group)

        # Alignment Z height
        self.align_z = QSpinBox()
        self.align_z.setRange(0, 8192)
        self.align_z.setValue(800)
        self.align_z.setSpecialValueText("Auto")
        align_layout.addWidget(QLabel("Align Z:"), 0, 0)
        align_layout.addWidget(self.align_z, 0, 1)

        # Extended Z height
        self.ext_z = QSpinBox()
        self.ext_z.setRange(0, 8192)
        self.ext_z.setValue(0)
        self.ext_z.setSpecialValueText("Auto")
        align_layout.addWidget(QLabel("Extended Z:"), 0, 2)
        align_layout.addWidget(self.ext_z, 0, 3)

        # Tilt correction
        self.tilt_cor = QComboBox()
        self.tilt_cor.addItems(
            ["0 - No correction", "1 - Correct tilt axis", "2 - Correct tilt angles"]
        )
        self.tilt_cor.setCurrentIndex(1)
        align_layout.addWidget(QLabel("Tilt Correction:"), 1, 0)
        align_layout.addWidget(self.tilt_cor, 1, 1)

        # Reconstruction range
        self.recon_range = QLineEdit()
        self.recon_range.setPlaceholderText("e.g., -60 60")
        help_recon_range_btn = self.create_help_button("recon_range")
        align_layout.addWidget(QLabel("Recon Range:"), 1, 2)
        align_layout.addWidget(self.recon_range, 1, 3)
        align_layout.addWidget(help_recon_range_btn, 1, 4)

        # Alignment patches
        self.at_patch_x = QSpinBox()
        self.at_patch_x.setRange(0, 20)
        self.at_patch_x.setValue(0)
        self.at_patch_x.setSpecialValueText("Global only")
        self.at_patch_y = QSpinBox()
        self.at_patch_y.setRange(0, 20)
        self.at_patch_y.setValue(0)
        self.at_patch_y.setSpecialValueText("Global only")
        help_at_patch_btn = self.create_help_button("at_patch")
        align_layout.addWidget(QLabel("AtPatch X:"), 2, 0)
        align_layout.addWidget(self.at_patch_x, 2, 1)
        align_layout.addWidget(help_at_patch_btn, 2, 2)
        align_layout.addWidget(QLabel("AtPatch Y:"), 2, 3)
        align_layout.addWidget(self.at_patch_y, 2, 4)

        # Skip alignment option
        self.align = QCheckBox("Perform alignment")
        self.align.setChecked(True)
        help_align_btn = self.create_help_button("align")
        align_layout.addWidget(self.align, 3, 0)
        align_layout.addWidget(help_align_btn, 3, 1)

        layout.addWidget(align_group)

        # Reconstruction Group
        recon_group = QGroupBox("🔄 Reconstruction")
        recon_layout = QGridLayout(recon_group)

        # SART iterations
        self.sart = QSpinBox()
        self.sart.setRange(0, 50)
        self.sart.setValue(15)
        self.sart.setSpecialValueText("WBP only")
        help_sart_btn = self.create_help_button("sart")
        recon_layout.addWidget(QLabel("SART Iterations:"), 0, 0)
        recon_layout.addWidget(self.sart, 0, 1)
        recon_layout.addWidget(help_sart_btn, 0, 2)

        # SART projections
        self.sart_projections = QSpinBox()
        self.sart_projections.setRange(1, 20)
        self.sart_projections.setValue(5)
        help_sart_proj_btn = self.create_help_button("sart_projections")
        recon_layout.addWidget(QLabel("SART Projections:"), 0, 3)
        recon_layout.addWidget(self.sart_projections, 0, 4)
        recon_layout.addWidget(help_sart_proj_btn, 0, 5)

        # WBP
        self.wbp = QComboBox()
        self.wbp.addItems(["0 - No WBP", "1 - WBP reconstruction"])
        self.wbp.setCurrentIndex(1)
        recon_layout.addWidget(QLabel("WBP:"), 0, 2)
        recon_layout.addWidget(self.wbp, 0, 3)

        # Dark tolerance
        self.dark_tol = QDoubleSpinBox()
        self.dark_tol.setRange(0.0, 1.0)
        self.dark_tol.setValue(0.7)
        self.dark_tol.setDecimals(2)
        recon_layout.addWidget(QLabel("Dark Tolerance:"), 1, 0)
        recon_layout.addWidget(self.dark_tol, 1, 1)

        # Flip intensity
        self.flip_int = QCheckBox("Flip intensity")
        recon_layout.addWidget(self.flip_int, 1, 2)

        # Interpolation correction
        self.intp_cor = QComboBox()
        self.intp_cor.addItems(
            ["0 - No correction", "1 - Linear interpolation", "2 - Cubic interpolation"]
        )
        self.intp_cor.setCurrentIndex(1)
        recon_layout.addWidget(QLabel("Interpolation:"), 2, 0)
        recon_layout.addWidget(self.intp_cor, 2, 1)

        # CTF correction
        self.corr_ctf = QCheckBox("CTF correction")
        self.corr_ctf.setChecked(True)
        help_corr_ctf_btn = self.create_help_button("corr_ctf")
        recon_layout.addWidget(self.corr_ctf, 2, 2)
        recon_layout.addWidget(help_corr_ctf_btn, 2, 3)

        # CTF lowpass filter
        self.ctf_lowpass = QDoubleSpinBox()
        self.ctf_lowpass.setRange(5.0, 50.0)
        self.ctf_lowpass.setValue(15.0)
        self.ctf_lowpass.setSuffix(" Å")
        self.ctf_lowpass.setDecimals(1)
        help_ctf_lowpass_btn = self.create_help_button("ctf_lowpass")
        recon_layout.addWidget(QLabel("CTF Lowpass:"), 2, 4)
        recon_layout.addWidget(self.ctf_lowpass, 2, 5)
        recon_layout.addWidget(help_ctf_lowpass_btn, 2, 6)

        layout.addWidget(recon_group)

        # Advanced Options Group
        advanced_group = QGroupBox("🔬 Advanced Options")
        advanced_layout = QGridLayout(advanced_group)

        # Resume processing
        self.resume = QCheckBox("Resume processing")
        help_resume_btn = self.create_help_button("resume")
        advanced_layout.addWidget(self.resume, 0, 0)
        advanced_layout.addWidget(help_resume_btn, 0, 1)

        # Magnification
        self.mag = QDoubleSpinBox()
        self.mag.setRange(0.1, 10.0)
        self.mag.setValue(1.0)
        self.mag.setDecimals(3)
        help_mag_btn = self.create_help_button("mag")
        advanced_layout.addWidget(QLabel("Magnification:"), 0, 2)
        advanced_layout.addWidget(self.mag, 0, 3)
        advanced_layout.addWidget(help_mag_btn, 0, 4)

        # Extended phase
        self.ext_phase = QCheckBox("Extended phase")
        help_ext_phase_btn = self.create_help_button("ext_phase")
        advanced_layout.addWidget(self.ext_phase, 1, 0)
        advanced_layout.addWidget(help_ext_phase_btn, 1, 1)

        # GPU settings
        self.gpu = QLineEdit("0")
        self.gpu.setPlaceholderText("GPU IDs (space-separated: 0 1 2 3)")
        help_gpu_btn = self.create_help_button("gpu")
        advanced_layout.addWidget(QLabel("GPU IDs:"), 1, 2)
        advanced_layout.addWidget(self.gpu, 1, 3)
        advanced_layout.addWidget(help_gpu_btn, 1, 4)

        # Multi-resolution tomograms
        self.at_bin_2nd = QDoubleSpinBox()
        self.at_bin_2nd.setRange(0.0, 16.0)
        self.at_bin_2nd.setValue(0.0)
        self.at_bin_2nd.setSpecialValueText("Disabled")
        self.at_bin_2nd.setDecimals(1)
        help_at_bin_2nd_btn = self.create_help_button("at_bin_2nd")
        advanced_layout.addWidget(QLabel("2nd Binning:"), 2, 0)
        advanced_layout.addWidget(self.at_bin_2nd, 2, 1)
        advanced_layout.addWidget(help_at_bin_2nd_btn, 2, 2)

        self.at_bin_3rd = QDoubleSpinBox()
        self.at_bin_3rd.setRange(0.0, 16.0)
        self.at_bin_3rd.setValue(0.0)
        self.at_bin_3rd.setSpecialValueText("Disabled")
        self.at_bin_3rd.setDecimals(1)
        help_at_bin_3rd_btn = self.create_help_button("at_bin_3rd")
        advanced_layout.addWidget(QLabel("3rd Binning:"), 2, 3)
        advanced_layout.addWidget(self.at_bin_3rd, 2, 4)
        advanced_layout.addWidget(help_at_bin_3rd_btn, 2, 5)

        layout.addWidget(advanced_group)

        # Connect advanced parameter signals
        self.connect_advanced_signals()

        layout.addStretch()
        scroll.setWidget(scroll_widget)

        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)

        return tab

    def create_help_button(self, param_key):
        """Create a help button for a parameter."""
        help_btn = QPushButton("ℹ️")
        help_btn.setMaximumWidth(25)
        help_btn.setMaximumHeight(25)
        help_btn.setToolTip("Click for parameter help")
        help_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 12px;
                font-size: 12px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: #bbdefb;
            }
            QPushButton:pressed {
                background-color: #90caf9;
            }
        """
        )

        # Connect to show help dialog
        help_btn.clicked.connect(lambda: self.show_parameter_help(param_key))
        return help_btn

    def show_parameter_help(self, param_key):
        """Show help dialog for a parameter."""
        if param_key in PARAMETER_HELP:
            help_text = PARAMETER_HELP[param_key]

            # Create help dialog
            msg = QMessageBox(self)
            msg.setWindowTitle(
                f"Parameter Help: {
                    param_key.replace(
                        '_', ' ').title()}"
            )
            msg.setText(help_text)
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)

            # Make dialog larger for better readability
            msg.setStyleSheet(
                """
                QMessageBox {
                    min-width: 500px;
                    min-height: 300px;
                }
                QMessageBox QLabel {
                    font-size: 11pt;
                    line-height: 1.4;
                }
            """
            )

            msg.exec()
        else:
            # Fallback for parameters without help text
            QMessageBox.information(
                self,
                "Parameter Help",
                f"Help information for '{param_key}' is not available.",
            )

    def connect_general_signals(self):
        """Connect signals for general parameters."""
        # Connect all parameter widgets to update preview automatically
        widgets = [
            self.pix_size,
            self.kv,
            self.cs,
            self.fm_dose,
            self.amp_contrast,
            self.tilt_axis,
            self.vol_z,
            self.at_bin,
            self.cmd,
            self.gain_file,
            self.dark_file,
            self.in_fm_motion,
            self.split_sum,
            self.flip_vol,
            self.out_imod,
            self.out_xf,
            self.insuffix,
            self.inskips,
            self.serial,
        ]

        for widget in widgets:
            if hasattr(widget, "textChanged"):
                widget.textChanged.connect(self.parameters_changed.emit)
                widget.textChanged.connect(self.update_command_preview)
            elif hasattr(widget, "valueChanged"):
                widget.valueChanged.connect(self.parameters_changed.emit)
                widget.valueChanged.connect(self.update_command_preview)
            elif hasattr(widget, "toggled"):
                widget.toggled.connect(self.parameters_changed.emit)
                widget.toggled.connect(self.update_command_preview)
            elif hasattr(widget, "currentTextChanged"):
                widget.currentTextChanged.connect(self.parameters_changed.emit)
                widget.currentTextChanged.connect(self.update_command_preview)

    # TODO: Refactor function - Function 'connect_advanced_signals' too long
    # (54 lines)
    def connect_advanced_signals(self):
        """Connect signals for advanced parameters."""
        # Connect all advanced parameter widgets to update preview
        # automatically
        widgets = [
            self.mc_patch_x,
            self.mc_patch_y,
            self.mc_iter,
            self.mc_tol,
            self.mc_bin,
            self.group_global,
            self.group_patch,
            self.fm_ref,
            self.eer_sampling,
            self.fm_int,
            self.defect_file,
            self.rot_gain,
            self.flip_gain,
            self.inv_gain,
            self.align_z,
            self.ext_z,
            self.tilt_cor,
            self.recon_range,
            self.align,
            self.at_patch_x,
            self.at_patch_y,
            self.sart,
            self.sart_projections,
            self.wbp,
            self.dark_tol,
            self.flip_int,
            self.intp_cor,
            self.corr_ctf,
            self.ctf_lowpass,
            self.mag,
            self.ext_phase,
            self.resume,
            self.gpu,
            self.at_bin_2nd,
            self.at_bin_3rd,
        ]

        for widget in widgets:
            if hasattr(widget, "textChanged"):
                widget.textChanged.connect(self.parameters_changed.emit)
                widget.textChanged.connect(self.update_command_preview)
            elif hasattr(widget, "valueChanged"):
                widget.valueChanged.connect(self.parameters_changed.emit)
                widget.valueChanged.connect(self.update_command_preview)
            elif hasattr(widget, "toggled"):
                widget.toggled.connect(self.parameters_changed.emit)
                widget.toggled.connect(self.update_command_preview)
            elif hasattr(widget, "currentTextChanged"):
                widget.currentTextChanged.connect(self.parameters_changed.emit)
                widget.currentTextChanged.connect(self.update_command_preview)

    # I/O auto-population methods
    def set_io_paths(self, input_path: str, output_path: str):
        """Set I/O paths from Batch/Live processing tabs for command generation."""
        self._current_input_path = input_path
        self._current_output_path = output_path
        # Update command preview with new I/O paths
        self.update_command_preview()

    def get_io_paths(self):
        """Get current I/O paths for command generation."""
        return getattr(self, "_current_input_path", ""), getattr(
            self, "_current_output_path", ""
        )

    # File browser methods
    def browse_gain_file(self):
        """Browse for gain reference file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Gain Reference", "", "MRC Files (*.mrc);;All Files (*)"
        )
        if file_path:
            self.gain_file.setText(file_path)

    def browse_dark_file(self):
        """Browse for dark reference file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Dark Reference", "", "MRC Files (*.mrc);;All Files (*)"
        )
        if file_path:
            self.dark_file.setText(file_path)

    def browse_fm_motion(self):
        """Browse for frame motion file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Frame Motion File", "", "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            self.in_fm_motion.setText(file_path)

    def browse_defect_file(self):
        """Browse for defect file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Defect File", "", "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            self.defect_file.setText(file_path)

    # TODO: Refactor update_command_preview - complexity: 53 (target: <10)
    # TODO: Refactor function - Function 'update_command_preview' too long
    # (177 lines)
    def update_command_preview(self):
        """Update the command preview based on current parameters."""
        try:
            command_parts = ["AreTomo3"]

            # I/O paths from Batch/Live processing tabs
            input_path, output_path = self.get_io_paths()
            if input_path:
                command_parts.append(f"-InPrefix {input_path}")
            else:
                command_parts.append(
                    "# -InPrefix [will be set by Batch/Live processing]"
                )

            if output_path:
                command_parts.append(f"-OutDir {output_path}")
            else:
                command_parts.append("# -OutDir [will be set by Batch/Live processing]")

            if self.pix_size.value() != 1.91:
                command_parts.append(f"-PixSize {self.pix_size.value()}")

            if self.kv.value() != 300:
                command_parts.append(f"-kV {self.kv.value()}")

            if self.cs.value() != 2.7:
                command_parts.append(f"-Cs {self.cs.value()}")

            if self.fm_dose.value() != 0.14:
                command_parts.append(f"-FmDose {self.fm_dose.value()}")

            if self.amp_contrast.value() != 0.07:
                command_parts.append(f"-AmpContrast {self.amp_contrast.value()}")

            if self.tilt_axis.value() != 0.0:
                command_parts.append(f"-TiltAxis {self.tilt_axis.value()}")

            if self.vol_z.value() != 2048:
                command_parts.append(f"-VolZ {self.vol_z.value()}")

            if self.at_bin.value() != 4.0:
                command_parts.append(f"-AtBin {self.at_bin.value()}")

            # Command mode
            if self.cmd.currentIndex() != 0:
                command_parts.append(f"-Cmd {self.cmd.currentIndex()}")

            # File references (moved from advanced)
            if self.gain_file.text().strip():
                command_parts.append(f"-Gain {self.gain_file.text().strip()}")

            if self.dark_file.text().strip():
                command_parts.append(f"-Dark {self.dark_file.text().strip()}")

            if self.in_fm_motion.text().strip():
                command_parts.append(f"-InFmMotion {self.in_fm_motion.text().strip()}")

            # GPU selection (get from Control Center if available)
            if self.main_window and hasattr(self.main_window, "main_tab"):
                main_tab = self.main_window.main_tab
                if (
                    hasattr(main_tab, "gpu_selection")
                    and main_tab.gpu_selection.text().strip()
                ):
                    gpu_text = main_tab.gpu_selection.text().strip()
                    if (
                        gpu_text and gpu_text != "0"
                    ):  # Only add if not default single GPU
                        command_parts.append(f"-Gpu {gpu_text}")

            # Add checkboxes
            if not self.split_sum.isChecked():
                command_parts.append("-SplitSum 0")

            if self.flip_vol.isChecked():
                command_parts.append("-FlipVol 1")

            if self.out_xf.isChecked():
                command_parts.append("-OutXF 1")

            # Add IMOD output
            imod_value = self.out_imod.currentIndex()
            if imod_value != 1:
                command_parts.append(f"-OutImod {imod_value}")

            # Add advanced parameters if they differ from defaults
            if self.mc_patch_x.value() != 1 or self.mc_patch_y.value() != 1:
                command_parts.append(
                    f"-McPatch {self.mc_patch_x.value()} {self.mc_patch_y.value()}"
                )

            if self.mc_iter.value() != 7:
                command_parts.append(f"-McIter {self.mc_iter.value()}")

            if self.mc_tol.value() != 0.5:
                command_parts.append(f"-McTol {self.mc_tol.value()}")

            if self.mc_bin.value() != 1.0:
                command_parts.append(f"-McBin {self.mc_bin.value()}")

            # Group frames
            if self.group_global.value() != 1 or self.group_patch.value() != 1:
                command_parts.append(
                    f"-Group {self.group_global.value()} {self.group_patch.value()}"
                )

            # Frame reference
            if self.fm_ref.value() != 0:
                command_parts.append(f"-FmRef {self.fm_ref.value()}")

            # Resume processing
            if self.resume.isChecked():
                command_parts.append("-Resume 1")

            if self.defect_file.text().strip():
                command_parts.append(f"-DefectFile {self.defect_file.text().strip()}")

            if self.rot_gain.currentIndex() != 0:
                command_parts.append(f"-RotGain {self.rot_gain.currentIndex()}")

            if self.flip_gain.currentIndex() != 0:
                command_parts.append(f"-FlipGain {self.flip_gain.currentIndex()}")

            if self.inv_gain.isChecked():
                command_parts.append("-InvGain 1")

            if self.align_z.value() != 800:
                command_parts.append(f"-AlignZ {self.align_z.value()}")

            if self.ext_z.value() != 0:
                command_parts.append(f"-ExtZ {self.ext_z.value()}")

            if self.tilt_cor.currentIndex() != 1:
                command_parts.append(f"-TiltCor {self.tilt_cor.currentIndex()}")

            if self.recon_range.text().strip():
                command_parts.append(f"-ReconRange {self.recon_range.text().strip()}")

            # Skip alignment
            if not self.align.isChecked():
                command_parts.append("-Align 0")

            if self.sart.value() != 15:
                command_parts.append(f"-Sart {self.sart.value()}")

            # SART projections
            if self.sart_projections.value() != 5:
                command_parts.append(f"-SartProj {self.sart_projections.value()}")

            if self.wbp.currentIndex() != 1:
                command_parts.append(f"-Wbp {self.wbp.currentIndex()}")

            if self.dark_tol.value() != 0.7:
                command_parts.append(f"-DarkTol {self.dark_tol.value()}")

            if self.flip_int.isChecked():
                command_parts.append("-FlipInt 1")

            if self.intp_cor.currentIndex() != 1:
                command_parts.append(f"-IntpCor {self.intp_cor.currentIndex()}")

            if self.corr_ctf.isChecked():
                command_parts.append("-CorrCTF 1")

            if self.mag.value() != 1.0:
                command_parts.append(f"-Mag {self.mag.value()}")

            if self.ext_phase.isChecked():
                command_parts.append("-ExtPhase 1")

            # Format command with line breaks for readability
            command = " \\\n    ".join(command_parts)
            self.command_preview.setPlainText(command)

        except Exception as e:
            logger.error(f"Error updating command preview: {e}")
            self.command_preview.setPlainText(f"Error generating command: {e}")

    def copy_command(self):
        """Copy the command to clipboard."""
        from PyQt6.QtWidgets import QApplication

        clipboard = QApplication.clipboard()
        clipboard.setText(self.command_preview.toPlainText())

        if self.main_window:
            self.main_window.statusBar().showMessage(
                "Command copied to clipboard", 2000
            )

    # TODO: Refactor function - Function 'reset_parameters' too long (53 lines)
    def reset_parameters(self):
        """Reset all parameters to default values."""
        reply = QMessageBox.question(
            self,
            "Reset Parameters",
            "Are you sure you want to reset all parameters to default values?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Reset general parameters
            self.pix_size.setValue(1.91)
            self.kv.setValue(300)
            self.cs.setValue(2.7)
            self.fm_dose.setValue(0.14)
            self.amp_contrast.setValue(0.07)
            self.tilt_axis.setValue(0.0)
            self.vol_z.setValue(2048)
            self.at_bin.setValue(4.0)
            self.split_sum.setChecked(True)
            self.flip_vol.setChecked(False)
            self.out_imod.setCurrentIndex(1)
            self.out_xf.setChecked(True)

            # Reset advanced parameters
            self.mc_patch_x.setValue(1)
            self.mc_patch_y.setValue(1)
            self.mc_iter.setValue(7)
            self.mc_tol.setValue(0.5)
            self.mc_bin.setValue(1.0)
            self.gain_file.clear()
            self.dark_file.clear()
            self.rot_gain.setCurrentIndex(0)
            self.flip_gain.setCurrentIndex(0)
            self.inv_gain.setChecked(False)
            self.align_z.setValue(800)
            self.ext_z.setValue(0)
            self.tilt_cor.setCurrentIndex(1)
            self.recon_range.clear()
            self.sart.setValue(15)
            self.wbp.setCurrentIndex(1)
            self.dark_tol.setValue(0.7)
            self.flip_int.setChecked(False)
            self.intp_cor.setCurrentIndex(1)
            self.corr_ctf.setChecked(False)
            self.mag.setValue(1.0)
            self.in_fm_motion.clear()
            self.ext_phase.setChecked(False)
            self.resume.setChecked(False)

            self.update_command_preview()
            logger.info("Parameters reset to defaults")

    # TODO: Refactor function - Function 'get_parameters' too long (61 lines)
    def get_parameters(self) -> Dict[str, Any]:
        """Get all current parameter values as a dictionary."""
        params = {
            # General parameters (I/O handled by Batch/Live processing)
            "pix_size": self.pix_size.value(),
            "kv": self.kv.value(),
            "cs": self.cs.value(),
            "fm_dose": self.fm_dose.value(),
            "amp_contrast": self.amp_contrast.value(),
            "tilt_axis": self.tilt_axis.value(),
            "vol_z": self.vol_z.value(),
            "at_bin": self.at_bin.value(),
            "split_sum": self.split_sum.isChecked(),
            "flip_vol": self.flip_vol.isChecked(),
            "out_imod": self.out_imod.currentIndex(),
            "out_xf": self.out_xf.isChecked(),
            # Advanced parameters
            "mc_patch_x": self.mc_patch_x.value(),
            "mc_patch_y": self.mc_patch_y.value(),
            "mc_iter": self.mc_iter.value(),
            "mc_tol": self.mc_tol.value(),
            "mc_bin": self.mc_bin.value(),
            "gain_file": self.gain_file.text().strip(),
            "dark_file": self.dark_file.text().strip(),
            "rot_gain": self.rot_gain.currentIndex(),
            "flip_gain": self.flip_gain.currentIndex(),
            "inv_gain": self.inv_gain.isChecked(),
            "align_z": self.align_z.value(),
            "ext_z": self.ext_z.value(),
            "tilt_cor": self.tilt_cor.currentIndex(),
            "recon_range": self.recon_range.text().strip(),
            "sart": self.sart.value(),
            "wbp": self.wbp.currentIndex(),
            "dark_tol": self.dark_tol.value(),
            "flip_int": self.flip_int.isChecked(),
            "intp_cor": self.intp_cor.currentIndex(),
            "corr_ctf": self.corr_ctf.isChecked(),
            "mag": self.mag.value(),
            "in_fm_motion": self.in_fm_motion.text().strip(),
            "ext_phase": self.ext_phase.isChecked(),
            "resume": self.resume.isChecked(),
            # New parameters
            "cmd": self.cmd.currentIndex(),
            "insuffix": self.insuffix.text().strip(),
            "inskips": self.inskips.text().strip(),
            "serial": self.serial.value(),
            "eer_sampling": self.eer_sampling.value(),
            "fm_int": self.fm_int.value(),
            "group_global": self.group_global.value(),
            "group_patch": self.group_patch.value(),
            "at_patch_x": self.at_patch_x.value(),
            "at_patch_y": self.at_patch_y.value(),
            "ctf_lowpass": self.ctf_lowpass.value(),
            "gpu": self.gpu.text().strip(),
            "at_bin_2nd": self.at_bin_2nd.value(),
            "at_bin_3rd": self.at_bin_3rd.value(),
            "defect_file": self.defect_file.text().strip(),
            "sart_projections": self.sart_projections.value(),
            "align": self.align.isChecked(),
        }
        return params

    # TODO: Refactor set_parameters - complexity: 55 (target: <10)

    # TODO: Refactor function - Function 'set_parameters' too long (121 lines)
    def set_parameters(self, params: Dict[str, Any]):
        """Set parameter values from a dictionary."""
        try:
            # Set general parameters (I/O handled by Batch/Live processing)
            if "pix_size" in params:
                self.pix_size.setValue(params["pix_size"])
            if "kv" in params:
                self.kv.setValue(params["kv"])
            if "cs" in params:
                self.cs.setValue(params["cs"])
            if "fm_dose" in params:
                self.fm_dose.setValue(params["fm_dose"])
            if "amp_contrast" in params:
                self.amp_contrast.setValue(params["amp_contrast"])
            if "tilt_axis" in params:
                self.tilt_axis.setValue(params["tilt_axis"])
            if "vol_z" in params:
                self.vol_z.setValue(params["vol_z"])
            if "at_bin" in params:
                self.at_bin.setValue(params["at_bin"])
            if "split_sum" in params:
                self.split_sum.setChecked(params["split_sum"])
            if "flip_vol" in params:
                self.flip_vol.setChecked(params["flip_vol"])
            if "out_imod" in params:
                self.out_imod.setCurrentIndex(params["out_imod"])
            if "out_xf" in params:
                self.out_xf.setChecked(params["out_xf"])

            # Set advanced parameters
            if "mc_patch_x" in params:
                self.mc_patch_x.setValue(params["mc_patch_x"])
            if "mc_patch_y" in params:
                self.mc_patch_y.setValue(params["mc_patch_y"])
            if "mc_iter" in params:
                self.mc_iter.setValue(params["mc_iter"])
            if "mc_tol" in params:
                self.mc_tol.setValue(params["mc_tol"])
            if "mc_bin" in params:
                self.mc_bin.setValue(params["mc_bin"])
            if "gain_file" in params:
                self.gain_file.setText(params["gain_file"])
            if "dark_file" in params:
                self.dark_file.setText(params["dark_file"])
            if "rot_gain" in params:
                self.rot_gain.setCurrentIndex(params["rot_gain"])
            if "flip_gain" in params:
                self.flip_gain.setCurrentIndex(params["flip_gain"])
            if "inv_gain" in params:
                self.inv_gain.setChecked(params["inv_gain"])
            if "align_z" in params:
                self.align_z.setValue(params["align_z"])
            if "ext_z" in params:
                self.ext_z.setValue(params["ext_z"])
            if "tilt_cor" in params:
                self.tilt_cor.setCurrentIndex(params["tilt_cor"])
            if "recon_range" in params:
                self.recon_range.setText(params["recon_range"])
            if "sart" in params:
                self.sart.setValue(params["sart"])
            if "wbp" in params:
                self.wbp.setCurrentIndex(params["wbp"])
            if "dark_tol" in params:
                self.dark_tol.setValue(params["dark_tol"])
            if "flip_int" in params:
                self.flip_int.setChecked(params["flip_int"])
            if "intp_cor" in params:
                self.intp_cor.setCurrentIndex(params["intp_cor"])
            if "corr_ctf" in params:
                self.corr_ctf.setChecked(params["corr_ctf"])
            if "mag" in params:
                self.mag.setValue(params["mag"])
            if "in_fm_motion" in params:
                self.in_fm_motion.setText(params["in_fm_motion"])
            if "ext_phase" in params:
                self.ext_phase.setChecked(params["ext_phase"])
            if "resume" in params:
                self.resume.setChecked(params["resume"])

            # Set new parameters
            if "cmd" in params:
                self.cmd.setCurrentIndex(params["cmd"])
            if "insuffix" in params:
                self.insuffix.setText(params["insuffix"])
            if "inskips" in params:
                self.inskips.setText(params["inskips"])
            if "serial" in params:
                self.serial.setValue(params["serial"])
            if "eer_sampling" in params:
                self.eer_sampling.setValue(params["eer_sampling"])
            if "fm_int" in params:
                self.fm_int.setValue(params["fm_int"])
            if "group_global" in params:
                self.group_global.setValue(params["group_global"])
            if "group_patch" in params:
                self.group_patch.setValue(params["group_patch"])
            if "at_patch_x" in params:
                self.at_patch_x.setValue(params["at_patch_x"])
            if "at_patch_y" in params:
                self.at_patch_y.setValue(params["at_patch_y"])
            if "ctf_lowpass" in params:
                self.ctf_lowpass.setValue(params["ctf_lowpass"])
            if "gpu" in params:
                self.gpu.setText(params["gpu"])
            if "at_bin_2nd" in params:
                self.at_bin_2nd.setValue(params["at_bin_2nd"])
            if "at_bin_3rd" in params:
                self.at_bin_3rd.setValue(params["at_bin_3rd"])
            if "defect_file" in params:
                self.defect_file.setText(params["defect_file"])
            if "sart_projections" in params:
                self.sart_projections.setValue(params["sart_projections"])
            if "align" in params:
                self.align.setChecked(params["align"])

            self.update_command_preview()
            logger.info("Parameters loaded successfully")

        except Exception as e:
            logger.error(f"Error setting parameters: {e}")
            QMessageBox.warning(self, "Error", f"Error loading parameters: {e}")

    def update_from_mdoc_data(self, mdoc_params: Dict[str, Any]):
        """Update parameters specifically from mdoc file data."""
        try:
            # Convert mdoc parameter names to GUI parameter names
            param_mapping = {
                "pixel_size": "pix_size",
                "voltage": "kv",
                "tilt_axis": "tilt_axis",
                "cs": "cs",
                "amp_contrast": "amp_contrast",
                "frame_dose": "fm_dose",
            }

            # Convert and set parameters
            gui_params = {}
            for mdoc_key, gui_key in param_mapping.items():
                if mdoc_key in mdoc_params:
                    gui_params[gui_key] = mdoc_params[mdoc_key]

            if gui_params:
                self.set_parameters(gui_params)
                logger.info(f"Updated Parameters tab from .mdoc data: {gui_params}")

                # Show notification to user
                self.log_message(
                    f"📋 Auto-populated parameters from .mdoc file: {', '.join(gui_params.keys())}"
                )

        except Exception as e:
            logger.error(f"Error updating from mdoc data: {e}")

    def log_message(self, message: str):
        """Log message to main window if available."""
        if hasattr(self, "main_window") and hasattr(self.main_window, "log_message"):
            self.main_window.log_message(message)
        else:
            logger.info(message)

    def load_json_config(self):
        """Load parameters from JSON configuration file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load JSON Configuration", "", "JSON files (*.json);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path, "r") as f:
                    config = json.load(f)

                self.apply_json_config(config)
                self.update_command_preview()
                QMessageBox.information(
                    self,
                    "Success",
                    f"Configuration loaded from {os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to load configuration: {str(e)}"
                )

    def save_json_config(self):
        """Save current parameters to JSON configuration file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save JSON Configuration",
            "aretomo3_config.json",
            "JSON files (*.json);;All files (*.*)",
        )

        if file_path:
            try:
                config = self.get_current_config()
                with open(file_path, "w") as f:
                    json.dump(config, f, indent=2)

                QMessageBox.information(
                    self,
                    "Success",
                    f"Configuration saved to {os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to save configuration: {str(e)}"
                )

    def get_current_config(self):
        """Get current configuration as JSON-compatible dictionary."""
        return {
            "aretomo3_config": {
                "version": "1.0",
                "parameters": self.get_parameters(),
                "metadata": {
                    "created_by": "AreTomo3 GUI",
                    "timestamp": str(
                        os.path.getmtime(__file__)
                        if os.path.exists(__file__)
                        else "unknown"
                    ),
                },
            }
        }

    def apply_json_config(self, config):
        """Apply configuration from JSON data."""
        if "aretomo3_config" in config and "parameters" in config["aretomo3_config"]:
            params = config["aretomo3_config"]["parameters"]
            self.set_parameters(params)
        elif "parameters" in config:
            # Direct parameters format
            self.set_parameters(config["parameters"])
        else:
            # Assume the config itself contains parameters
            self.set_parameters(config)

    # TODO: Refactor function - Function 'load_template' too long (65 lines)
    def load_template(self, template_name):
        """Load predefined parameter template."""
        if template_name == "Custom Configuration":
            return

        templates = {
            "High Resolution Template": {
                "pix_size": 1.91,
                "kv": 300,
                "cs": 2.7,
                "fm_dose": 0.14,
                "amp_contrast": 0.07,
                "vol_z": 2048,
                "at_bin": 2.0,
                "sart": 15,
                "split_sum": True,
                "out_imod": 1,
            },
            "Fast Processing Template": {
                "pix_size": 1.91,
                "kv": 300,
                "cs": 2.7,
                "fm_dose": 0.14,
                "amp_contrast": 0.07,
                "vol_z": 1024,
                "at_bin": 4.0,
                "sart": 10,
                "split_sum": False,
                "out_imod": 1,
            },
            "Live Acquisition Template": {
                "pix_size": 1.91,
                "kv": 300,
                "cs": 2.7,
                "fm_dose": 0.14,
                "amp_contrast": 0.07,
                "vol_z": 1536,
                "at_bin": 3.0,
                "sart": 12,
                "split_sum": True,
                "out_imod": 1,
                "mc_patch_x": 2,
                "mc_patch_y": 2,
            },
            "Batch Processing Template": {
                "pix_size": 1.91,
                "kv": 300,
                "cs": 2.7,
                "fm_dose": 0.14,
                "amp_contrast": 0.07,
                "vol_z": 2048,
                "at_bin": 2.0,
                "sart": 15,
                "split_sum": True,
                "out_imod": 3,
                "gpu": "0 1 2 3",
            },
        }

        if template_name in templates:
            self.set_parameters(templates[template_name])
            self.update_command_preview()
            QMessageBox.information(
                self, "Template Loaded", f"Applied {template_name} settings"
            )

    def validate_configuration(self):
        """Validate current parameter configuration."""
        issues = []

        # Check required parameters
        if not self.in_prefix.text().strip():
            issues.append("Input prefix is required")

        if not self.out_dir.text().strip():
            issues.append("Output directory is required")

        # Check parameter ranges
        if self.pix_size.value() <= 0:
            issues.append("Pixel size must be greater than 0")

        if self.vol_z.value() == 0:
            issues.append("Volume Z height should be set for reconstruction")

        # GPU configuration is now handled in Control Center

        # Show validation results
        if issues:
            QMessageBox.warning(
                self,
                "Configuration Issues",
                "Found the following issues:\n\n"
                + "\n".join(f"• {issue}" for issue in issues),
            )
        else:
            QMessageBox.information(
                self,
                "Validation Passed",
                "✅ Configuration is valid and ready for processing!",
            )

    def _convert_param_name(self, param_name: str) -> str:
        """Convert internal parameter name to AreTomo3 command line format."""
        param_map = {
            "in_prefix": "InPrefix",
            "out_dir": "OutDir",
            "pix_size": "PixSize",
            "kv": "Kv",
            "cs": "Cs",
            "fm_dose": "FmDose",
            "amp_contrast": "AmpContrast",
            "tilt_axis": "TiltAxis",
            "vol_z": "VolZ",
            "at_bin": "AtBin",
            "gpu": "Gpu",
            "split_sum": "SplitSum",
            "flip_vol": "FlipVol",
            "out_imod": "OutImod",
            "out_xf": "OutXf",
            "mc_patch_x": "McPatchX",
            "mc_patch_y": "McPatchY",
            "mc_iter": "McIter",
            "mc_tol": "McTol",
            "mc_bin": "McBin",
            "gain_file": "GainFile",
            "dark_file": "DarkFile",
            "rot_gain": "RotGain",
            "flip_gain": "FlipGain",
            "inv_gain": "InvGain",
            "align_z": "AlignZ",
            "ext_z": "ExtZ",
            "tilt_cor": "TiltCor",
            "recon_range": "ReconRange",
            "sart": "Sart",
            "wbp": "Wbp",
            "dark_tol": "DarkTol",
            "flip_int": "FlipInt",
            "intp_cor": "IntpCor",
            "corr_ctf": "CorrCtf",
            "mag": "Mag",
            "in_fm_motion": "InFmMotion",
            "ext_phase": "ExtPhase",
            "resume": "Resume",
        }
        return param_map.get(param_name, "")

    def copy_command(self):
        """Copy command to clipboard."""
        try:
            clipboard = QApplication.clipboard()
            command_text = self.command_preview.toPlainText()

            # Extract just the command (remove comments)
            lines = command_text.split("\n")
            command_lines = [
                line
                for line in lines
                if not line.strip().startswith("#") and line.strip()
            ]
            command = "\n".join(command_lines)

            clipboard.setText(command)

            # Show temporary tooltip
            QToolTip.showText(
                self.copy_command_btn.mapToGlobal(
                    self.copy_command_btn.rect().center()
                ),
                "Command copied to clipboard!",
                self.copy_command_btn,
                self.copy_command_btn.rect(),
                2000,
            )

        except Exception as e:
            logger.error(f"Error copying command: {e}")
            QMessageBox.warning(self, "Copy Error", f"Failed to copy command: {e}")

    def save_command(self):
        """Save command to file."""
        try:
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save AreTomo3 Command",
                f"aretomo3_command_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sh",
                "Shell Scripts (*.sh);;Text Files (*.txt);;All Files (*)",
            )

            if filename:
                command_text = self.command_preview.toPlainText()

                # Add shebang for shell scripts
                if filename.endswith(".sh"):
                    command_text = "#!/bin/bash\n\n" + command_text

                with open(filename, "w") as f:
                    f.write(command_text)

                QMessageBox.information(
                    self, "Command Saved", f"Command saved to:\n{filename}"
                )

        except Exception as e:
            logger.error(f"Error saving command: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save command: {e}")

    def execute_command(self):
        """Execute the AreTomo3 command."""
        try:
            command_text = self.command_preview.toPlainText()

            # Extract just the command (remove comments)
            lines = command_text.split("\n")
            command_lines = [
                line
                for line in lines
                if not line.strip().startswith("#") and line.strip()
            ]
            command = " ".join(command_lines)

            if not command.strip():
                QMessageBox.warning(
                    self,
                    "Execute Error",
                    "No command to execute. Please update the preview first.",
                )
                return

            # Confirm execution
            reply = QMessageBox.question(
                self,
                "Execute Command",
                f"Execute the following command?\n\n{command[:200]}{'...' if len(command) > 200 else ''}",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Here you would integrate with the actual execution system
                logger.info(f"Executing command: {command}")
                QMessageBox.information(
                    self,
                    "Command Execution",
                    "Command execution started!\nCheck the processing monitor for progress.",
                )

        except Exception as e:
            logger.error(f"Error executing command: {e}")
            QMessageBox.critical(
                self, "Execution Error", f"Failed to execute command: {e}"
            )
