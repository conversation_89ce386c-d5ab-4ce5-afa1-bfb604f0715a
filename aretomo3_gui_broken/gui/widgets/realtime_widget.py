#!/usr/bin/env python3
"""
Real-time processing widget for AreTomo3 GUI.
Provides live monitoring and control of real-time processing.
"""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

import pyqtgraph as pg
from PyQt6.QtCore import Qt, QTimer, pyqtSlot
from PyQt6.QtGui import QColor, QFont, QPalette
from PyQt6.QtWidgets import (
    QFileDialog,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QProgressBar,
    QPushButton,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from ...core.realtime_processor import ProcessingJob, ProcessingStats, RealTimeProcessor


class RealTimeStatsWidget(QWidget):
    """Widget displaying real-time processing statistics."""

    def __init__(self):
        """Initialize the instance."""
        super().__init__()
        self.setup_ui()
        self.setup_plots()

    # TODO: Refactor function - Function 'setup_ui' too long (55 lines)
    def setup_ui(self):
        """Execute setup_ui operation."""
        layout = QGridLayout(self)

        # Statistics labels
        self.total_files_label = QLabel("0")
        self.processed_files_label = QLabel("0")
        self.failed_files_label = QLabel("0")
        self.queue_size_label = QLabel("0")
        self.active_jobs_label = QLabel("0")
        self.throughput_label = QLabel("0.0")
        self.avg_time_label = QLabel("0.0")

        # Style labels
        for label in [
            self.total_files_label,
            self.processed_files_label,
            self.failed_files_label,
            self.queue_size_label,
            self.active_jobs_label,
            self.throughput_label,
            self.avg_time_label,
        ]:
            label.setStyleSheet(
                """
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    background-color: #ecf0f1;
                    padding: 8px;
                    border-radius: 4px;
                    min-width: 60px;
                }
            """
            )
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Add to layout with descriptions
        layout.addWidget(QLabel("Total Files:"), 0, 0)
        layout.addWidget(self.total_files_label, 0, 1)
        layout.addWidget(QLabel("Processed:"), 0, 2)
        layout.addWidget(self.processed_files_label, 0, 3)
        layout.addWidget(QLabel("Failed:"), 0, 4)
        layout.addWidget(self.failed_files_label, 0, 5)

        layout.addWidget(QLabel("Queue Size:"), 1, 0)
        layout.addWidget(self.queue_size_label, 1, 1)
        layout.addWidget(QLabel("Active Jobs:"), 1, 2)
        layout.addWidget(self.active_jobs_label, 1, 3)
        layout.addWidget(QLabel("Files/Hour:"), 1, 4)
        layout.addWidget(self.throughput_label, 1, 5)

        layout.addWidget(QLabel("Avg Time (s):"), 2, 0)
        layout.addWidget(self.avg_time_label, 2, 1)

    def setup_plots(self):
        """Setup real-time plots."""
        # This will be added in the main widget
        pass

    def update_stats(self, stats: ProcessingStats):
        """Update statistics display."""
        self.total_files_label.setText(str(stats.total_files))
        self.processed_files_label.setText(str(stats.processed_files))
        self.failed_files_label.setText(str(stats.failed_files))
        self.queue_size_label.setText(str(stats.queue_size))
        self.active_jobs_label.setText(str(stats.active_jobs))
        self.throughput_label.setText(f"{stats.throughput_per_hour:.1f}")
        self.avg_time_label.setText(f"{stats.avg_processing_time:.1f}")

        # Color coding for failed files
        if stats.failed_files > 0:
            self.failed_files_label.setStyleSheet(
                """
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: white;
                    background-color: #e74c3c;
                    padding: 8px;
                    border-radius: 4px;
                    min-width: 60px;
                }
            """
            )


class JobListWidget(QTableWidget):
    """Widget displaying list of processing jobs."""

    def __init__(self):
        """Initialize the instance."""
        super().__init__()
        self.setup_table()
        self.jobs: Dict[str, ProcessingJob] = {}

    def setup_table(self):
        """Setup the job table."""
        headers = ["File", "Status", "Progress", "Time", "Result"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        # Configure table
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSortingEnabled(True)

        # Resize columns
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)

        self.setColumnWidth(2, 120)  # Progress column

    def add_job(self, job: ProcessingJob):
        """Add a new job to the table."""
        self.jobs[job.job_id] = job

        row = self.rowCount()
        self.insertRow(row)

        # File name
        self.setItem(row, 0, QTableWidgetItem(job.file_path.name))

        # Status
        status_item = QTableWidgetItem(job.status.title())
        self.setItem(row, 1, status_item)

        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setValue(int(job.progress))
        self.setCellWidget(row, 2, progress_bar)

        # Processing time
        time_text = f"{job.processing_time:.1f}s" if job.processing_time else "--"
        self.setItem(row, 3, QTableWidgetItem(time_text))

        # Result
        result_text = str(job.result_path.name) if job.result_path else "--"
        self.setItem(row, 4, QTableWidgetItem(result_text))

        # Store job_id in first column for reference
        self.item(row, 0).setData(Qt.ItemDataRole.UserRole, job.job_id)

        # Scroll to bottom to show new jobs
        self.scrollToBottom()

    def update_job(self, job: ProcessingJob):
        """Update an existing job in the table."""
        self.jobs[job.job_id] = job

        # Find the row for this job
        for row in range(self.rowCount()):
            item = self.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == job.job_id:
                # Update status
                status_item = self.item(row, 1)
                status_item.setText(job.status.title())

                # Color code status
                if job.status == "completed":
                    status_item.setBackground(QColor("#d4edda"))
                elif job.status == "failed":
                    status_item.setBackground(QColor("#f8d7da"))
                elif job.status == "processing":
                    status_item.setBackground(QColor("#d1ecf1"))

                # Update progress
                progress_bar = self.cellWidget(row, 2)
                if progress_bar:
                    progress_bar.setValue(int(job.progress))

                # Update time
                time_text = (
                    f"{job.processing_time:.1f}s" if job.processing_time else "--"
                )
                self.item(row, 3).setText(time_text)

                # Update result
                result_text = str(job.result_path.name) if job.result_path else "--"
                self.item(row, 4).setText(result_text)

                break


class RealTimeControlWidget(QWidget):
    """Widget for controlling real-time processing."""

    def __init__(self, processor: RealTimeProcessor):
        """Initialize the instance."""
        super().__init__()
        self.processor = processor
        self.setup_ui()

    # TODO: Refactor function - Function 'setup_ui' too long (80 lines)
    def setup_ui(self):
        """Execute setup_ui operation."""
        layout = QVBoxLayout(self)

        # Control buttons
        controls_group = QGroupBox("Real-Time Processing Control")
        controls_layout = QHBoxLayout(controls_group)

        self.start_button = QPushButton("▶️ Start Processing")
        self.start_button.clicked.connect(self.start_processing)
        self.start_button.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )

        self.stop_button = QPushButton("⏹️ Stop Processing")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet(
            """
            QPushButton {
                background-color: #dc3545;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """
        )

        self.pause_button = QPushButton("⏸️ Pause")
        self.pause_button.setEnabled(False)

        controls_layout.addWidget(self.start_button)
        controls_layout.addWidget(self.stop_button)
        controls_layout.addWidget(self.pause_button)
        controls_layout.addStretch()

        layout.addWidget(controls_group)

        # Watch directories
        dirs_group = QGroupBox("Watch Directories")
        dirs_layout = QVBoxLayout(dirs_group)

        self.dirs_list = QListWidget()
        for watch_dir in self.processor.watch_directories:
            self.dirs_list.addItem(str(watch_dir))

        dirs_buttons = QHBoxLayout()
        add_dir_btn = QPushButton("Add Directory")
        add_dir_btn.clicked.connect(self.add_watch_directory)
        remove_dir_btn = QPushButton("Remove Directory")
        remove_dir_btn.clicked.connect(self.remove_watch_directory)

        dirs_buttons.addWidget(add_dir_btn)
        dirs_buttons.addWidget(remove_dir_btn)
        dirs_buttons.addStretch()

        dirs_layout.addWidget(self.dirs_list)
        dirs_layout.addLayout(dirs_buttons)

        layout.addWidget(dirs_group)

    def start_processing(self):
        """Start real-time processing."""
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.pause_button.setEnabled(True)

        # Start processing in background
        asyncio.create_task(self.processor.start_processing())

    def stop_processing(self):
        """Stop real-time processing."""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.pause_button.setEnabled(False)

        # Stop processing
        asyncio.create_task(self.processor.stop_processing())

    def add_watch_directory(self):
        """Add a new watch directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Watch Directory")
        if directory:
            self.dirs_list.addItem(directory)
            # Add to processor (would need to implement this)

    def remove_watch_directory(self):
        """Remove selected watch directory."""
        current_row = self.dirs_list.currentRow()
        if current_row >= 0:
            self.dirs_list.takeItem(current_row)


class RealTimeProcessingWidget(QWidget):
    """Main real-time processing widget."""

    def __init__(self, watch_directories: List[Path], output_directory: Path):
        """Initialize the instance."""
        super().__init__()
        self.processor = RealTimeProcessor(watch_directories, output_directory)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Execute setup_ui operation."""
        layout = QVBoxLayout(self)

        # Create splitter for main layout
        splitter = QSplitter(Qt.Orientation.Vertical)

        # Top section: Statistics and controls
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)

        # Statistics
        self.stats_widget = RealTimeStatsWidget()
        top_layout.addWidget(self.stats_widget, 2)

        # Controls
        self.control_widget = RealTimeControlWidget(self.processor)
        top_layout.addWidget(self.control_widget, 1)

        splitter.addWidget(top_widget)

        # Bottom section: Job list and plots
        bottom_tabs = QTabWidget()

        # Job list tab
        self.job_list = JobListWidget()
        bottom_tabs.addTab(self.job_list, "📋 Processing Jobs")

        # Performance plots tab
        plots_widget = self.create_plots_widget()
        bottom_tabs.addTab(plots_widget, "📊 Performance")

        # Log tab
        self.log_widget = QTextEdit()
        self.log_widget.setReadOnly(True)
        # Note: setMaximumBlockCount is not available in PyQt6, we'll manage log size manually
        bottom_tabs.addTab(self.log_widget, "📝 Log")

        splitter.addWidget(bottom_tabs)

        # Set splitter proportions
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)

        layout.addWidget(splitter)

    def create_plots_widget(self) -> QWidget:
        """Create performance plots widget."""
        widget = QWidget()
        layout = QGridLayout(widget)

        # Throughput plot
        self.throughput_plot = pg.PlotWidget(title="Processing Throughput")
        self.throughput_plot.setLabel("left", "Files/Hour")
        self.throughput_plot.setLabel("bottom", "Time")
        layout.addWidget(self.throughput_plot, 0, 0)

        # Processing time plot
        self.time_plot = pg.PlotWidget(title="Processing Time")
        self.time_plot.setLabel("left", "Time (seconds)")
        self.time_plot.setLabel("bottom", "File Number")
        layout.addWidget(self.time_plot, 0, 1)

        # Queue size plot
        self.queue_plot = pg.PlotWidget(title="Queue Size")
        self.queue_plot.setLabel("left", "Number of Files")
        self.queue_plot.setLabel("bottom", "Time")
        layout.addWidget(self.queue_plot, 1, 0)

        # Success rate plot
        self.success_plot = pg.PlotWidget(title="Success Rate")
        self.success_plot.setLabel("left", "Success Rate (%)")
        self.success_plot.setLabel("bottom", "Time")
        layout.addWidget(self.success_plot, 1, 1)

        return widget

    def connect_signals(self):
        """Connect processor signals to GUI updates."""
        self.processor.job_queued.connect(self.on_job_queued)
        self.processor.job_started.connect(self.on_job_started)
        self.processor.job_progress.connect(self.on_job_progress)
        self.processor.job_completed.connect(self.on_job_completed)
        self.processor.job_failed.connect(self.on_job_failed)
        self.processor.stats_updated.connect(self.on_stats_updated)

    @pyqtSlot(str)
    def on_job_queued(self, job_id: str):
        """Handle job queued signal."""
        job = self.processor.get_job_status(job_id)
        if job:
            self.job_list.add_job(job)
            self.log_widget.append(
                f"[{datetime.now().strftime('%H:%M:%S')}] Queued: {job.file_path.name}"
            )

    @pyqtSlot(str)
    def on_job_started(self, job_id: str):
        """Handle job started signal."""
        job = self.processor.get_job_status(job_id)
        if job:
            self.job_list.update_job(job)
            self.log_widget.append(
                f"[{datetime.now().strftime('%H:%M:%S')}] Started: {job.file_path.name}"
            )

    @pyqtSlot(str, float)
    def on_job_progress(self, job_id: str, progress: float):
        """Handle job progress signal."""
        job = self.processor.get_job_status(job_id)
        if job:
            job.progress = progress
            self.job_list.update_job(job)

    @pyqtSlot(str, str)
    def on_job_completed(self, job_id: str, result_path: str):
        """Handle job completed signal."""
        job = self.processor.get_job_status(job_id)
        if job:
            self.job_list.update_job(job)
            self.log_widget.append(
                f"[{datetime.now().strftime('%H:%M:%S')}] Completed: {job.file_path.name}"
            )

    @pyqtSlot(str, str)
    def on_job_failed(self, job_id: str, error_message: str):
        """Handle job failed signal."""
        job = self.processor.get_job_status(job_id)
        if job:
            self.job_list.update_job(job)
            self.log_widget.append(
                f"[{datetime.now().strftime('%H:%M:%S')}] Failed: {job.file_path.name} - {error_message}"
            )

    @pyqtSlot(object)
    def on_stats_updated(self, stats: ProcessingStats):
        """Handle stats updated signal."""
        self.stats_widget.update_stats(stats)
        # Update plots here if needed
