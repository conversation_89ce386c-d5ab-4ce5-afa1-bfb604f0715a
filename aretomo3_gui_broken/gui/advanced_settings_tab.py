#!/usr/bin/env python3
"""
Advanced Settings Tab for AreTomo3 GUI.
Contains motion correction and reconstruction settings.
"""

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QFormLayout,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QScrollArea,
    QSlider,
    QSpinBox,
    QVBoxLayout,
    QWidget,
)

# Use standard PyQt6 spinboxes (enhanced spinbox was removed during cleanup)
# The enhanced styling is now handled via CSS in the theme manager


# TODO: Refactor class - Class 'AdvancedSettingsTab' too long (525 lines)
class AdvancedSettingsTab(QWidget):
    """Advanced settings tab combining motion correction and reconstruction settings."""

    # Signals for parameter changes
    settings_changed = pyqtSignal(dict)

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout with scroll area
        main_layout = QVBoxLayout(self)

        # Create scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Content widget for scroll area
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)

        # Create main sections
        self.create_motion_correction_section(content_layout)
        self.create_reconstruction_section(content_layout)
        self.create_advanced_options_section(content_layout)

        # Add stretch to push everything to top
        content_layout.addStretch()

        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)

        # Add scroll area to main layout
        main_layout.addWidget(scroll_area)

    # TODO: Refactor function - Function 'create_motion_correction_section'
    # too long (119 lines)
    def create_motion_correction_section(self, parent_layout):
        """Create motion correction settings section."""
        group = QGroupBox("Motion Correction Settings")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # Frame grouping settings
        frame_group = QGroupBox("Frame Grouping")
        frame_layout = QFormLayout(frame_group)

        self.group_start_spin = QSpinBox()
        self.group_start_spin.setRange(1, 100)
        self.group_start_spin.setValue(1)
        self.group_start_spin.setToolTip("First frame for grouping")
        frame_layout.addRow("Start Frame:", self.group_start_spin)

        self.group_end_spin = QSpinBox()
        self.group_end_spin.setRange(1, 100)
        self.group_end_spin.setValue(3)
        self.group_end_spin.setToolTip("Last frame for grouping")
        frame_layout.addRow("End Frame:", self.group_end_spin)

        # Patch-based alignment
        patch_group = QGroupBox("Patch-based Alignment")
        patch_layout = QFormLayout(patch_group)

        self.mc_patch_x_spin = QSpinBox()
        self.mc_patch_x_spin.setRange(0, 20)
        self.mc_patch_x_spin.setValue(0)
        self.mc_patch_x_spin.setToolTip(
            "Number of patches in X direction (0 = full frame)"
        )
        patch_layout.addRow("X Patches:", self.mc_patch_x_spin)

        self.mc_patch_y_spin = QSpinBox()
        self.mc_patch_y_spin.setRange(0, 20)
        self.mc_patch_y_spin.setValue(0)
        self.mc_patch_y_spin.setToolTip(
            "Number of patches in Y direction (0 = full frame)"
        )
        patch_layout.addRow("Y Patches:", self.mc_patch_y_spin)

        # Iteration and tolerance settings
        iter_group = QGroupBox("Iteration Control")
        iter_layout = QFormLayout(iter_group)

        self.mc_iter_spin = QSpinBox()
        self.mc_iter_spin.setRange(1, 100)
        self.mc_iter_spin.setValue(15)
        self.mc_iter_spin.setToolTip("Maximum iterations for motion correction")
        iter_layout.addRow("Max Iterations:", self.mc_iter_spin)

        self.mc_tol_spin = QDoubleSpinBox()
        self.mc_tol_spin.setRange(0.01, 1.0)
        self.mc_tol_spin.setValue(0.1)
        self.mc_tol_spin.setDecimals(3)
        self.mc_tol_spin.setSingleStep(0.01)
        self.mc_tol_spin.setToolTip("Tolerance for convergence")
        iter_layout.addRow("Tolerance:", self.mc_tol_spin)

        # Binning and reference settings
        ref_group = QGroupBox("Reference & Binning")
        ref_layout = QFormLayout(ref_group)

        self.mc_bin_spin = QDoubleSpinBox()
        self.mc_bin_spin.setRange(0.5, 8.0)
        self.mc_bin_spin.setValue(1.0)
        self.mc_bin_spin.setDecimals(1)
        self.mc_bin_spin.setSingleStep(0.5)
        self.mc_bin_spin.setToolTip("Binning factor for motion correction")
        ref_layout.addRow("MC Binning:", self.mc_bin_spin)

        self.fm_ref_spin = QSpinBox()
        self.fm_ref_spin.setRange(-1, 100)
        self.fm_ref_spin.setValue(-1)
        self.fm_ref_spin.setToolTip("Reference frame (-1 = auto)")
        ref_layout.addRow("Reference Frame:", self.fm_ref_spin)

        self.fm_int_spin = QSpinBox()
        self.fm_int_spin.setRange(1, 50)
        self.fm_int_spin.setValue(10)
        self.fm_int_spin.setToolTip("Frame integration interval")
        ref_layout.addRow("Frame Interval:", self.fm_int_spin)

        # Gain reference settings
        gain_group = QGroupBox("Gain Reference Processing")
        gain_layout = QFormLayout(gain_group)

        self.gain_file_edit = QLineEdit()
        self.gain_file_edit.setPlaceholderText("Select gain reference file...")
        self.gain_browse_btn = QPushButton("Browse")
        self.gain_browse_btn.clicked.connect(self.browse_gain_file)
        gain_file_layout = QHBoxLayout()
        gain_file_layout.addWidget(self.gain_file_edit)
        gain_file_layout.addWidget(self.gain_browse_btn)
        gain_layout.addRow("Gain File:", gain_file_layout)

        self.rot_gain_combo = QComboBox()
        self.rot_gain_combo.addItems(["0", "90", "180", "270"])
        self.rot_gain_combo.setToolTip("Rotation angle for gain reference")
        gain_layout.addRow("Rotate Gain:", self.rot_gain_combo)

        self.flip_gain_combo = QComboBox()
        self.flip_gain_combo.addItems(["None", "Horizontal", "Vertical", "Both"])
        self.flip_gain_combo.setToolTip("Flip gain reference")
        gain_layout.addRow("Flip Gain:", self.flip_gain_combo)

        self.inv_gain_check = QCheckBox("Invert Gain")
        self.inv_gain_check.setToolTip("Invert gain reference values")
        gain_layout.addRow("", self.inv_gain_check)

        # Add subgroups to main group
        layout.addWidget(frame_group, 0, 0)
        layout.addWidget(patch_group, 0, 1)
        layout.addWidget(iter_group, 1, 0)
        layout.addWidget(ref_group, 1, 1)
        layout.addWidget(gain_group, 2, 0, 1, 2)

        parent_layout.addWidget(group)

    # TODO: Refactor function - Function 'create_reconstruction_section' too
    # long (108 lines)
    def create_reconstruction_section(self, parent_layout):
        """Create reconstruction settings section."""
        group = QGroupBox("Reconstruction Settings")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # Tilt axis settings
        tilt_group = QGroupBox("Tilt Axis")
        tilt_layout = QFormLayout(tilt_group)

        self.tilt_axis_spin = QDoubleSpinBox()
        self.tilt_axis_spin.setRange(-180.0, 180.0)
        self.tilt_axis_spin.setValue(0.0)
        self.tilt_axis_spin.setDecimals(2)
        self.tilt_axis_spin.setSingleStep(0.1)
        self.tilt_axis_spin.setToolTip("Tilt axis angle in degrees")
        tilt_layout.addRow("Angle (°):", self.tilt_axis_spin)

        self.tilt_axis_search_check = QCheckBox("Auto Search")
        self.tilt_axis_search_check.setChecked(True)
        self.tilt_axis_search_check.setToolTip("Automatically search for tilt axis")
        tilt_layout.addRow("", self.tilt_axis_search_check)

        self.tilt_axis_refine_check = QCheckBox("Refine Angle")
        self.tilt_axis_refine_check.setChecked(True)
        self.tilt_axis_refine_check.setToolTip("Refine the tilt axis angle")
        tilt_layout.addRow("", self.tilt_axis_refine_check)

        # Volume dimensions
        vol_group = QGroupBox("Volume Dimensions")
        vol_layout = QFormLayout(vol_group)

        self.align_z_spin = QSpinBox()
        self.align_z_spin.setRange(0, 2048)
        self.align_z_spin.setValue(256)
        self.align_z_spin.setToolTip("Volume height for alignment")
        vol_layout.addRow("Align Z:", self.align_z_spin)

        self.vol_z_spin = QSpinBox()
        self.vol_z_spin.setRange(-1, 4096)
        self.vol_z_spin.setValue(-1)
        self.vol_z_spin.setToolTip("Final volume height (-1 = auto)")
        vol_layout.addRow("Volume Z:", self.vol_z_spin)

        self.ext_z_spin = QSpinBox()
        self.ext_z_spin.setRange(100, 1000)
        self.ext_z_spin.setValue(300)
        self.ext_z_spin.setToolTip("Extended Z range")
        vol_layout.addRow("Extended Z:", self.ext_z_spin)

        # Binning settings
        bin_group = QGroupBox("Multi-Resolution Binning")
        bin_layout = QFormLayout(bin_group)

        self.at_bin_1_spin = QDoubleSpinBox()
        self.at_bin_1_spin.setRange(0.5, 8.0)
        self.at_bin_1_spin.setValue(1.0)
        self.at_bin_1_spin.setDecimals(1)
        self.at_bin_1_spin.setSingleStep(0.5)
        self.at_bin_1_spin.setToolTip("First resolution binning")
        bin_layout.addRow("1st Resolution:", self.at_bin_1_spin)

        self.at_bin_2_spin = QDoubleSpinBox()
        self.at_bin_2_spin.setRange(0.0, 8.0)
        self.at_bin_2_spin.setValue(0.0)
        self.at_bin_2_spin.setDecimals(1)
        self.at_bin_2_spin.setSingleStep(0.5)
        self.at_bin_2_spin.setToolTip("Second resolution binning (0 = disabled)")
        bin_layout.addRow("2nd Resolution:", self.at_bin_2_spin)

        self.at_bin_3_spin = QDoubleSpinBox()
        self.at_bin_3_spin.setRange(0.0, 8.0)
        self.at_bin_3_spin.setValue(0.0)
        self.at_bin_3_spin.setDecimals(1)
        self.at_bin_3_spin.setSingleStep(0.5)
        self.at_bin_3_spin.setToolTip("Third resolution binning (0 = disabled)")
        bin_layout.addRow("3rd Resolution:", self.at_bin_3_spin)

        # CTF correction
        ctf_group = QGroupBox("CTF Correction")
        ctf_layout = QFormLayout(ctf_group)

        self.amp_contrast_spin = QDoubleSpinBox()
        self.amp_contrast_spin.setRange(0.0, 1.0)
        self.amp_contrast_spin.setValue(0.07)
        self.amp_contrast_spin.setDecimals(3)
        self.amp_contrast_spin.setSingleStep(0.01)
        self.amp_contrast_spin.setToolTip("Amplitude contrast")
        ctf_layout.addRow("Amplitude Contrast:", self.amp_contrast_spin)

        self.ctf_tile_size_combo = QComboBox()
        self.ctf_tile_size_combo.addItems(["256", "512", "1024", "2048"])
        self.ctf_tile_size_combo.setCurrentText("512")
        self.ctf_tile_size_combo.setToolTip("CTF tile size")
        ctf_layout.addRow("Tile Size:", self.ctf_tile_size_combo)

        self.corr_ctf_check = QCheckBox("Enable CTF Correction")
        self.corr_ctf_check.setChecked(True)
        self.corr_ctf_check.setToolTip("Apply CTF correction")
        ctf_layout.addRow("", self.corr_ctf_check)

        # Add subgroups to main group
        layout.addWidget(tilt_group, 0, 0)
        layout.addWidget(vol_group, 0, 1)
        layout.addWidget(bin_group, 1, 0)
        layout.addWidget(ctf_group, 1, 1)

        parent_layout.addWidget(group)

    # TODO: Refactor function - Function 'create_advanced_options_section' too
    # long (70 lines)
    def create_advanced_options_section(self, parent_layout):
        """Create advanced options section."""
        group = QGroupBox("Advanced Options")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # Reconstruction algorithms
        algo_group = QGroupBox("Reconstruction Algorithms")
        algo_layout = QFormLayout(algo_group)

        # SART parameters
        self.sart_iter_spin = QSpinBox()
        self.sart_iter_spin.setRange(1, 100)
        self.sart_iter_spin.setValue(20)
        self.sart_iter_spin.setToolTip("SART iterations")
        algo_layout.addRow("SART Iterations:", self.sart_iter_spin)

        self.sart_relax_spin = QSpinBox()
        self.sart_relax_spin.setRange(1, 20)
        self.sart_relax_spin.setValue(5)
        self.sart_relax_spin.setToolTip("SART relaxation factor")
        algo_layout.addRow("SART Relaxation:", self.sart_relax_spin)

        self.wbp_check = QCheckBox("Use WBP")
        self.wbp_check.setToolTip("Use Weighted Back Projection")
        algo_layout.addRow("", self.wbp_check)

        # Volume processing
        vol_proc_group = QGroupBox("Volume Processing")
        vol_proc_layout = QFormLayout(vol_proc_group)

        self.flip_vol_combo = QComboBox()
        self.flip_vol_combo.addItems(["None", "X", "Y", "Z", "XY", "XZ", "YZ", "XYZ"])
        self.flip_vol_combo.setToolTip("Flip volume along axes")
        vol_proc_layout.addRow("Flip Volume:", self.flip_vol_combo)

        self.flip_int_check = QCheckBox("Flip Intensity")
        self.flip_int_check.setToolTip("Flip intensity values")
        vol_proc_layout.addRow("", self.flip_int_check)

        self.dark_tol_spin = QDoubleSpinBox()
        self.dark_tol_spin.setRange(0.0, 1.0)
        self.dark_tol_spin.setValue(0.7)
        self.dark_tol_spin.setDecimals(2)
        self.dark_tol_spin.setSingleStep(0.1)
        self.dark_tol_spin.setToolTip("Dark region tolerance")
        vol_proc_layout.addRow("Dark Tolerance:", self.dark_tol_spin)

        # Output options
        output_group = QGroupBox("Output Options")
        output_layout = QFormLayout(output_group)

        self.out_xf_check = QCheckBox("Output Transform File")
        self.out_xf_check.setToolTip("Output transformation file")
        output_layout.addRow("", self.out_xf_check)

        self.out_imod_check = QCheckBox("IMOD Compatible")
        self.out_imod_check.setToolTip("Make output compatible with IMOD")
        output_layout.addRow("", self.out_imod_check)

        self.crop_vol_check = QCheckBox("Crop Volume")
        self.crop_vol_check.setToolTip("Enable volume cropping")
        output_layout.addRow("", self.crop_vol_check)

        # Add subgroups to main group
        layout.addWidget(algo_group, 0, 0)
        layout.addWidget(vol_proc_group, 0, 1)
        layout.addWidget(output_group, 1, 0, 1, 2)

        parent_layout.addWidget(group)

    # TODO: Refactor function - Function 'connect_signals' too long (57 lines)
    def connect_signals(self):
        """Connect signals for parameter changes."""
        # Connect all parameter change signals
        widgets = [
            self.group_start_spin,
            self.group_end_spin,
            self.mc_patch_x_spin,
            self.mc_patch_y_spin,
            self.mc_iter_spin,
            self.mc_tol_spin,
            self.mc_bin_spin,
            self.fm_ref_spin,
            self.fm_int_spin,
            self.tilt_axis_spin,
            self.align_z_spin,
            self.vol_z_spin,
            self.ext_z_spin,
            self.at_bin_1_spin,
            self.at_bin_2_spin,
            self.at_bin_3_spin,
            self.amp_contrast_spin,
            self.sart_iter_spin,
            self.sart_relax_spin,
            self.dark_tol_spin,
        ]

        for widget in widgets:
            if hasattr(widget, "valueChanged"):
                widget.valueChanged.connect(self.emit_settings_changed)

        # Connect checkboxes and comboxes
        checkboxes = [
            self.inv_gain_check,
            self.tilt_axis_search_check,
            self.tilt_axis_refine_check,
            self.corr_ctf_check,
            self.wbp_check,
            self.flip_int_check,
            self.out_xf_check,
            self.out_imod_check,
            self.crop_vol_check,
        ]

        for checkbox in checkboxes:
            checkbox.toggled.connect(self.emit_settings_changed)

        comboboxes = [
            self.rot_gain_combo,
            self.flip_gain_combo,
            self.ctf_tile_size_combo,
            self.flip_vol_combo,
        ]

        for combobox in comboboxes:
            combobox.currentTextChanged.connect(self.emit_settings_changed)

        self.gain_file_edit.textChanged.connect(self.emit_settings_changed)

    def browse_gain_file(self):
        """Browse for gain reference file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Gain Reference File",
            "",
            "Image Files (*.mrc *.tif *.tiff *.gain);;All Files (*)",
        )
        if file_path:
            self.gain_file_edit.setText(file_path)

    def emit_settings_changed(self):
        """Emit settings changed signal with current values."""
        settings = self.get_settings()
        self.settings_changed.emit(settings)

    def get_settings(self):
        """Get all current settings as a dictionary."""
        return {
            # Motion correction
            "group_start": self.group_start_spin.value(),
            "group_end": self.group_end_spin.value(),
            "mc_patch_x": self.mc_patch_x_spin.value(),
            "mc_patch_y": self.mc_patch_y_spin.value(),
            "mc_iter": self.mc_iter_spin.value(),
            "mc_tol": self.mc_tol_spin.value(),
            "mc_bin": self.mc_bin_spin.value(),
            "fm_ref": self.fm_ref_spin.value(),
            "fm_int": self.fm_int_spin.value(),
            "gain_file": self.gain_file_edit.text(),
            "rot_gain": self.rot_gain_combo.currentText(),
            "flip_gain": self.flip_gain_combo.currentText(),
            "inv_gain": self.inv_gain_check.isChecked(),
            # Reconstruction
            "tilt_axis": self.tilt_axis_spin.value(),
            "tilt_axis_search": self.tilt_axis_search_check.isChecked(),
            "tilt_axis_refine": self.tilt_axis_refine_check.isChecked(),
            "align_z": self.align_z_spin.value(),
            "vol_z": self.vol_z_spin.value(),
            "ext_z": self.ext_z_spin.value(),
            "at_bin_1": self.at_bin_1_spin.value(),
            "at_bin_2": self.at_bin_2_spin.value(),
            "at_bin_3": self.at_bin_3_spin.value(),
            "amp_contrast": self.amp_contrast_spin.value(),
            "ctf_tile_size": int(self.ctf_tile_size_combo.currentText()),
            "corr_ctf": self.corr_ctf_check.isChecked(),
            # Advanced options
            "sart_iter": self.sart_iter_spin.value(),
            "sart_relax": self.sart_relax_spin.value(),
            "wbp": self.wbp_check.isChecked(),
            "flip_vol": self.flip_vol_combo.currentText(),
            "flip_int": self.flip_int_check.isChecked(),
            "dark_tol": self.dark_tol_spin.value(),
            "out_xf": self.out_xf_check.isChecked(),
            "out_imod": self.out_imod_check.isChecked(),
            "crop_vol": self.crop_vol_check.isChecked(),
        }

    # TODO: Refactor function - Function 'set_settings' too long (67 lines)
    def set_settings(self, settings):
        """Set all settings from a dictionary."""
        # Block signals during update to avoid recursive calls
        self.blockSignals(True)

        try:
            # Motion correction
            self.group_start_spin.setValue(settings.get("group_start", 1))
            self.group_end_spin.setValue(settings.get("group_end", 3))
            self.mc_patch_x_spin.setValue(settings.get("mc_patch_x", 0))
            self.mc_patch_y_spin.setValue(settings.get("mc_patch_y", 0))
            self.mc_iter_spin.setValue(settings.get("mc_iter", 15))
            self.mc_tol_spin.setValue(settings.get("mc_tol", 0.1))
            self.mc_bin_spin.setValue(settings.get("mc_bin", 1.0))
            self.fm_ref_spin.setValue(settings.get("fm_ref", -1))
            self.fm_int_spin.setValue(settings.get("fm_int", 10))
            self.gain_file_edit.setText(settings.get("gain_file", ""))

            rot_gain = settings.get("rot_gain", "0")
            if rot_gain in ["0", "90", "180", "270"]:
                self.rot_gain_combo.setCurrentText(rot_gain)

            flip_gain = settings.get("flip_gain", "None")
            if flip_gain in ["None", "Horizontal", "Vertical", "Both"]:
                self.flip_gain_combo.setCurrentText(flip_gain)

            self.inv_gain_check.setChecked(settings.get("inv_gain", False))

            # Reconstruction
            self.tilt_axis_spin.setValue(settings.get("tilt_axis", 0.0))
            self.tilt_axis_search_check.setChecked(
                settings.get("tilt_axis_search", True)
            )
            self.tilt_axis_refine_check.setChecked(
                settings.get("tilt_axis_refine", True)
            )
            self.align_z_spin.setValue(settings.get("align_z", 256))
            self.vol_z_spin.setValue(settings.get("vol_z", -1))
            self.ext_z_spin.setValue(settings.get("ext_z", 300))
            self.at_bin_1_spin.setValue(settings.get("at_bin_1", 1.0))
            self.at_bin_2_spin.setValue(settings.get("at_bin_2", 0.0))
            self.at_bin_3_spin.setValue(settings.get("at_bin_3", 0.0))
            self.amp_contrast_spin.setValue(settings.get("amp_contrast", 0.07))

            ctf_tile_size = str(settings.get("ctf_tile_size", 512))
            if ctf_tile_size in ["256", "512", "1024", "2048"]:
                self.ctf_tile_size_combo.setCurrentText(ctf_tile_size)

            self.corr_ctf_check.setChecked(settings.get("corr_ctf", True))

            # Advanced options
            self.sart_iter_spin.setValue(settings.get("sart_iter", 20))
            self.sart_relax_spin.setValue(settings.get("sart_relax", 5))
            self.wbp_check.setChecked(settings.get("wbp", False))

            flip_vol = settings.get("flip_vol", "None")
            if flip_vol in ["None", "X", "Y", "Z", "XY", "XZ", "YZ", "XYZ"]:
                self.flip_vol_combo.setCurrentText(flip_vol)

            self.flip_int_check.setChecked(settings.get("flip_int", False))
            self.dark_tol_spin.setValue(settings.get("dark_tol", 0.7))
            self.out_xf_check.setChecked(settings.get("out_xf", False))
            self.out_imod_check.setChecked(settings.get("out_imod", False))
            self.crop_vol_check.setChecked(settings.get("crop_vol", False))

        finally:
            self.blockSignals(False)
