"""
Session manager for AreTomo3 GUI.
Handles project session saving, loading, and management.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from PyQt6.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)


class SessionManager(QObject):
    """Manages project session saving and loading."""

    # Signals
    session_saved = pyqtSignal(str)  # filepath
    session_loaded = pyqtSignal(dict)  # session_data
    session_error = pyqtSignal(str)  # error_message

    def __init__(self, main_window):
        """Initialize the session manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        super().__init__()
        self.main_window = main_window
        self.current_session_file: Optional[str] = None
        self.default_session_dir = Path.home() / ".aretomo3_gui" / "sessions"
        self.default_session_dir.mkdir(parents=True, exist_ok=True)

    def save_session(self, filepath: Optional[str] = None) -> bool:
        """Save current GUI state to a session file.

        Args:
            filepath: Optional path to save session. If None, uses current session file

        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            if not filepath:
                if self.current_session_file:
                    filepath = self.current_session_file
                else:
                    # Generate default filename
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filepath = str(
                        self.default_session_dir / f"session_{timestamp}.json"
                    )

            session_data = self._collect_session_data()

            with open(filepath, "w") as f:
                json.dump(session_data, f, indent=2)

            self.current_session_file = filepath
            self.session_saved.emit(filepath)
            logger.info(f"Session saved successfully: {filepath}")
            return True

        except Exception as e:
            error_msg = f"Error saving session: {str(e)}"
            logger.error(error_msg)
            self.session_error.emit(error_msg)
            return False

    def load_session(self, filepath: str) -> bool:
        """Load GUI state from a session file.

        Args:
            filepath: Path to the session file to load

        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            if not Path(filepath).exists():
                raise FileNotFoundError(f"Session file not found: {filepath}")

            with open(filepath, "r") as f:
                session_data = json.load(f)

            self._restore_session_data(session_data)

            self.current_session_file = filepath
            self.session_loaded.emit(session_data)
            logger.info(f"Session loaded successfully: {filepath}")
            return True

        except Exception as e:
            error_msg = f"Error loading session: {str(e)}"
            logger.error(error_msg)
            self.session_error.emit(error_msg)
            return False

    # TODO: Refactor _collect_session_data - complexity: 18 (target: <10)
    # TODO: Refactor function - Function '_collect_session_data' too long (103 lines)
    def _collect_session_data(self) -> Dict[str, Any]:
        """Collect current GUI state into a dictionary.

        Returns:
            Dict containing all relevant GUI state
        """
        session_data = {
            "version": "1.0",
            "timestamp": datetime.now().isoformat(),
            "gui_state": {},
            "parameters": {},
            "recent_files": [],
            "window_geometry": {},
        }

        try:
            # Collect main window geometry
            if hasattr(self.main_window, "geometry"):
                geom = self.main_window.geometry()
                session_data["window_geometry"] = {
                    "x": geom.x(),
                    "y": geom.y(),
                    "width": geom.width(),
                    "height": geom.height(),
                }

            # Collect GUI parameters
            gui_params = {}

            # AreTomo3 settings
            if hasattr(self.main_window, "aretomo_path"):
                gui_params["aretomo_path"] = self.main_window.aretomo_path.text()
            if hasattr(self.main_window, "gpu_index"):
                gui_params["gpu_index"] = self.main_window.gpu_index.value()

            # Input/Output directories
            if hasattr(self.main_window, "input_dir"):
                gui_params["input_dir"] = self.main_window.input_dir.text()
            if hasattr(self.main_window, "output_dir"):
                gui_params["output_dir"] = self.main_window.output_dir.text()

            # Microscope parameters
            microscope_params = {}
            for param in [
                "pixel_size",
                "voltage",
                "cs",
                "frame_dose",
                "tilt_axis",
                "amp_contrast",
                "volume_z",
                "lowpass",
                "dark_tol",
            ]:
                if hasattr(self.main_window, param):
                    widget = getattr(self.main_window, param)
                    if hasattr(widget, "value"):
                        microscope_params[param] = widget.value()
            gui_params["microscope"] = microscope_params

            # Motion correction parameters
            mc_params = {}
            for param in ["mc_bin", "mc_patch_x", "mc_patch_y", "fm_int"]:
                if hasattr(self.main_window, param):
                    widget = getattr(self.main_window, param)
                    if hasattr(widget, "value"):
                        mc_params[param] = widget.value()
            gui_params["motion_correction"] = mc_params

            # Tomogram parameters
            tomo_params = {}
            if hasattr(self.main_window, "at_bin"):
                tomo_params["at_bin"] = self.main_window.at_bin.value()

            # Checkbox states
            checkbox_params = {}
            for param in [
                "out_xf",
                "out_imod",
                "wbp",
                "tilt_cor",
                "flip_gain",
                "flip_volume",
                "correct_ctf",
            ]:
                if hasattr(self.main_window, param):
                    widget = getattr(self.main_window, param)
                    if hasattr(widget, "isChecked"):
                        checkbox_params[param] = widget.isChecked()
            gui_params["checkboxes"] = checkbox_params

            session_data["parameters"] = gui_params

            # Current tab
            if hasattr(self.main_window, "tabs"):
                session_data["gui_state"][
                    "current_tab"
                ] = self.main_window.tabs.currentIndex()

        except Exception as e:
            logger.warning(f"Error collecting some session data: {e}")

        return session_data

    # TODO: Refactor _restore_session_data - complexity: 27 (target: <10)

    # TODO: Refactor function - Function '_restore_session_data' too long (70 lines)
    def _restore_session_data(self, session_data: Dict[str, Any]) -> None:
        """Restore GUI state from session data.

        Args:
            session_data: Dictionary containing session data to restore
        """
        try:
            # Restore window geometry
            if "window_geometry" in session_data:
                geom = session_data["window_geometry"]
                self.main_window.setGeometry(
                    geom.get("x", 100),
                    geom.get("y", 100),
                    geom.get("width", 1200),
                    geom.get("height", 800),
                )

            # Restore parameters
            if "parameters" in session_data:
                params = session_data["parameters"]

                # AreTomo3 settings
                if "aretomo_path" in params and hasattr(
                    self.main_window, "aretomo_path"
                ):
                    self.main_window.aretomo_path.setText(params["aretomo_path"])
                if "gpu_index" in params and hasattr(self.main_window, "gpu_index"):
                    self.main_window.gpu_index.setValue(params["gpu_index"])

                # Input/Output directories
                if "input_dir" in params and hasattr(self.main_window, "input_dir"):
                    self.main_window.input_dir.setText(params["input_dir"])
                if "output_dir" in params and hasattr(self.main_window, "output_dir"):
                    self.main_window.output_dir.setText(params["output_dir"])

                # Microscope parameters
                if "microscope" in params:
                    microscope = params["microscope"]
                    for param, value in microscope.items():
                        if hasattr(self.main_window, param):
                            widget = getattr(self.main_window, param)
                            if hasattr(widget, "setValue"):
                                widget.setValue(value)

                # Motion correction parameters
                if "motion_correction" in params:
                    mc = params["motion_correction"]
                    for param, value in mc.items():
                        if hasattr(self.main_window, param):
                            widget = getattr(self.main_window, param)
                            if hasattr(widget, "setValue"):
                                widget.setValue(value)

                # Checkbox states
                if "checkboxes" in params:
                    checkboxes = params["checkboxes"]
                    for param, value in checkboxes.items():
                        if hasattr(self.main_window, param):
                            widget = getattr(self.main_window, param)
                            if hasattr(widget, "setChecked"):
                                widget.setChecked(value)

            # Restore tab
            if "gui_state" in session_data:
                gui_state = session_data["gui_state"]
                if "current_tab" in gui_state and hasattr(self.main_window, "tabs"):
                    self.main_window.tabs.setCurrentIndex(gui_state["current_tab"])

        except Exception as e:
            logger.warning(f"Error restoring some session data: {e}")

    def get_recent_sessions(self) -> list:
        """Get list of recent session files.

        Returns:
            List of recent session file paths
        """
        try:
            session_files = list(self.default_session_dir.glob("*.json"))
            # Sort by modification time, most recent first
            session_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            return [str(f) for f in session_files[:10]]  # Return 10 most recent
        except Exception as e:
            logger.error(f"Error getting recent sessions: {e}")
            return []

    def delete_session(self, filepath: str) -> bool:
        """Delete a session file.

        Args:
            filepath: Path to the session file to delete

        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            Path(filepath).unlink()
            logger.info(f"Session deleted: {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error deleting session {filepath}: {e}")
            return False
