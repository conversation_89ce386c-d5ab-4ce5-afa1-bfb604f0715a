/* Default theme for AreTomo3 GUI */

QMainWindow {
    background-color: #f0f0f0;
}

QWidget {
    color: #333333;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
}

QPushButton {
    background-color: #0078d4;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
}

QPushButton:hover {
    background-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
}

QLineEdit, QSpinBox, QDoubleSpinBox {
    padding: 4px;
    border: 1px solid #cccccc;
    border-radius: 4px;
    background-color: white;
}

QProgressBar {
    border: 1px solid #cccccc;
    border-radius: 4px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #0078d4;
}

QTabWidget::pane {
    border: 1px solid #cccccc;
    border-radius: 4px;
}

QTabBar::tab {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    padding: 6px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: none;
}
