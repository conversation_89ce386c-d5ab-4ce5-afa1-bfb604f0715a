#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Visualization Engine
Comprehensive visualization system for tomographic data, analysis results, and quality metrics.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np

# Import visualization libraries
try:
    import plotly.express as px
    import plotly.graph_objects as go
    import plotly.offline as pyo
    from plotly.subplots import make_subplots

    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import matplotlib.patches as patches
    import matplotlib.pyplot as plt
    import seaborn as sns
    from matplotlib.colors import LinearSegmentedColormap

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import vispy
    from vispy import app, scene
    from vispy.color import Colormap

    VISPY_AVAILABLE = True
except ImportError:
    VISPY_AVAILABLE = False

# 3D visualization imports
try:
    import vtk
    from vtk.util import numpy_support

    VTK_AVAILABLE = True
except ImportError:
    VTK_AVAILABLE = False

# Volume data imports
try:
    import mrcfile
    import tifffile

    MRC_AVAILABLE = True
except ImportError:
    MRC_AVAILABLE = False

logger = logging.getLogger(__name__)


# TODO: Refactor class - Class 'AdvancedVisualizer' too long (743 lines)
class AdvancedVisualizer:
    """
    Advanced visualization engine for AreTomo3 GUI.
    Provides comprehensive visualization capabilities for tomographic data and analysis results.
    """

    # TODO: Refactor function - Function '__init__' too long (56 lines)
    def __init__(self, output_dir: Union[str, Path] = None):
        """Initialize the advanced visualizer."""
        self.output_dir = (
            Path(output_dir) if output_dir else Path.cwd() / "visualizations"
        )
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Visualization themes
        self.themes = {
            "default": {
                "background": "white",
                "grid": True,
                "colorscale": "viridis",
                "font_family": "Arial",
                "font_size": 12,
            },
            "dark": {
                "background": "#2F2F2F",
                "grid": True,
                "colorscale": "plasma",
                "font_family": "Arial",
                "font_size": 12,
                "text_color": "white",
            },
            "scientific": {
                "background": "white",
                "grid": True,
                "colorscale": "RdBu",
                "font_family": "Times New Roman",
                "font_size": 10,
            },
        }

        self.current_theme = "default"

        # Volume data cache for 3D visualization
        self.volume_cache: Dict[str, np.ndarray] = {}
        self.volume_metadata: Dict[str, Dict[str, Any]] = {}

        # VTK components for 3D rendering
        self.vtk_renderer = None
        self.vtk_render_window = None
        self.vtk_interactor = None

        # Check available libraries
        self.capabilities = {
            "plotly": PLOTLY_AVAILABLE,
            "matplotlib": MATPLOTLIB_AVAILABLE,
            "vispy": VISPY_AVAILABLE,
            "vtk": VTK_AVAILABLE,
            "mrc": MRC_AVAILABLE,
        }

        logger.info(
            f"Advanced Visualizer initialized - Capabilities: {self.capabilities}"
        )

    def create_ctf_analysis_dashboard(
        self, ctf_data: Dict[str, Any], interactive: bool = True
    ) -> str:
        """Create comprehensive CTF analysis dashboard."""
        if not PLOTLY_AVAILABLE and interactive:
            logger.warning("Plotly not available, falling back to matplotlib")
            interactive = False

        if interactive:
            return self._create_interactive_ctf_dashboard(ctf_data)
        else:
            return self._create_static_ctf_dashboard(ctf_data)

    # TODO: Refactor _create_interactive_ctf_dashboard - complexity: 11 (target: <10)
    # TODO: Refactor function - Function '_create_interactive_ctf_dashboard'
    # too long (143 lines)
    def _create_interactive_ctf_dashboard(self, ctf_data: Dict[str, Any]) -> str:
        """Create interactive CTF analysis dashboard using Plotly."""
        try:
            # Create subplots
            fig = make_subplots(
                rows=2,
                cols=3,
                subplot_titles=[
                    "CTF Resolution vs Tilt Angle",
                    "Defocus Distribution",
                    "Astigmatism Analysis",
                    "Cross-Correlation Quality",
                    "CTF Fit Quality",
                    "Power Spectrum Gallery",
                ],
                specs=[
                    [{"type": "scatter"}, {"type": "histogram"}, {"type": "scatter"}],
                    [{"type": "scatter"}, {"type": "heatmap"}, {"type": "image"}],
                ],
            )

            # Extract data for plotting
            series_data = list(ctf_data.get("ctf_parameters", {}).values())[0]
            if "parameters" not in series_data:
                return self._create_no_data_message("No CTF parameters available")

            df = series_data["parameters"]

            # Plot 1: CTF Resolution vs Tilt Angle
            if "tilt_angle" in df.columns and "resolution_limit_A" in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df["tilt_angle"],
                        y=df["resolution_limit_A"],
                        mode="markers+lines",
                        name="Resolution",
                        marker=dict(
                            size=8,
                            color=(
                                df["cross_correlation"]
                                if "cross_correlation" in df.columns
                                else "blue"
                            ),
                            colorscale="viridis",
                            showscale=True,
                            colorbar=dict(title="Cross Correlation"),
                        ),
                    ),
                    row=1,
                    col=1,
                )

            # Plot 2: Defocus Distribution
            if "defocus1_A" in df.columns:
                defocus_um = df["defocus1_A"] / 10000  # Convert to micrometers
                fig.add_trace(
                    go.Histogram(
                        x=defocus_um,
                        nbinsx=20,
                        name="Defocus Distribution",
                        marker_color="lightblue",
                    ),
                    row=1,
                    col=2,
                )

            # Plot 3: Astigmatism Analysis
            if "astigmatism_A" in df.columns and "astigmatism_angle" in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df["astigmatism_angle"],
                        y=df["astigmatism_A"],
                        mode="markers",
                        name="Astigmatism",
                        marker=dict(size=10, color="red", opacity=0.7),
                    ),
                    row=1,
                    col=3,
                )

            # Plot 4: Cross-Correlation Quality
            if "cross_correlation" in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=list(range(len(df))),
                        y=df["cross_correlation"],
                        mode="lines+markers",
                        name="Cross Correlation",
                        line=dict(color="green", width=2),
                    ),
                    row=2,
                    col=1,
                )

            # Plot 5: CTF Fit Quality Heatmap
            if len(df) > 1:
                # Create correlation matrix of CTF parameters
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    fig.add_trace(
                        go.Heatmap(
                            z=corr_matrix.values,
                            x=corr_matrix.columns,
                            y=corr_matrix.columns,
                            colorscale="RdBu",
                            zmid=0,
                        ),
                        row=2,
                        col=2,
                    )

            # Update layout
            fig.update_layout(
                title="CTF Analysis Dashboard",
                height=800,
                showlegend=True,
                template=self._get_plotly_template(),
            )

            # Update axis labels
            fig.update_xaxes(title_text="Tilt Angle (°)", row=1, col=1)
            fig.update_yaxes(title_text="Resolution (Å)", row=1, col=1)

            fig.update_xaxes(title_text="Defocus (μm)", row=1, col=2)
            fig.update_yaxes(title_text="Count", row=1, col=2)

            fig.update_xaxes(title_text="Astigmatism Angle (°)", row=1, col=3)
            fig.update_yaxes(title_text="Astigmatism (Å)", row=1, col=3)

            fig.update_xaxes(title_text="Micrograph Index", row=2, col=1)
            fig.update_yaxes(title_text="Cross Correlation", row=2, col=1)

            # Save to file
            output_file = self.output_dir / "ctf_analysis_dashboard.html"
            fig.write_html(str(output_file))

            logger.info(f"Interactive CTF dashboard created: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"Error creating interactive CTF dashboard: {e}")
            return self._create_error_message(str(e))

    # TODO: Refactor _create_static_ctf_dashboard - complexity: 14 (target:
    # <10)

    # TODO: Refactor function - Function '_create_static_ctf_dashboard' too
    # long (103 lines)
    def _create_static_ctf_dashboard(self, ctf_data: Dict[str, Any]) -> str:
        """Create static CTF analysis dashboard using Matplotlib."""
        if not MATPLOTLIB_AVAILABLE:
            return self._create_no_data_message("Matplotlib not available")

        try:
            # Create figure with subplots
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            fig.suptitle("CTF Analysis Dashboard", fontsize=16)

            # Extract data
            series_data = list(ctf_data.get("ctf_parameters", {}).values())[0]
            if "parameters" not in series_data:
                return self._create_no_data_message("No CTF parameters available")

            df = series_data["parameters"]

            # Plot 1: CTF Resolution vs Tilt Angle
            if "tilt_angle" in df.columns and "resolution_limit_A" in df.columns:
                scatter = axes[0, 0].scatter(
                    df["tilt_angle"],
                    df["resolution_limit_A"],
                    c=(
                        df["cross_correlation"]
                        if "cross_correlation" in df.columns
                        else "blue"
                    ),
                    cmap="viridis",
                    alpha=0.7,
                )
                axes[0, 0].set_xlabel("Tilt Angle (°)")
                axes[0, 0].set_ylabel("Resolution (Å)")
                axes[0, 0].set_title("CTF Resolution vs Tilt Angle")
                if "cross_correlation" in df.columns:
                    plt.colorbar(scatter, ax=axes[0, 0], label="Cross Correlation")

            # Plot 2: Defocus Distribution
            if "defocus1_A" in df.columns:
                defocus_um = df["defocus1_A"] / 10000
                axes[0, 1].hist(defocus_um, bins=20, alpha=0.7, color="lightblue")
                axes[0, 1].set_xlabel("Defocus (μm)")
                axes[0, 1].set_ylabel("Count")
                axes[0, 1].set_title("Defocus Distribution")

            # Plot 3: Astigmatism Analysis
            if "astigmatism_A" in df.columns and "astigmatism_angle" in df.columns:
                axes[0, 2].scatter(
                    df["astigmatism_angle"], df["astigmatism_A"], color="red", alpha=0.7
                )
                axes[0, 2].set_xlabel("Astigmatism Angle (°)")
                axes[0, 2].set_ylabel("Astigmatism (Å)")
                axes[0, 2].set_title("Astigmatism Analysis")

            # Plot 4: Cross-Correlation Quality
            if "cross_correlation" in df.columns:
                axes[1, 0].plot(
                    df["cross_correlation"], "g-o", linewidth=2, markersize=4
                )
                axes[1, 0].set_xlabel("Micrograph Index")
                axes[1, 0].set_ylabel("Cross Correlation")
                axes[1, 0].set_title("Cross-Correlation Quality")

            # Plot 5: CTF Parameters Correlation
            if len(df) > 1:
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    im = axes[1, 1].imshow(corr_matrix, cmap="RdBu", vmin=-1, vmax=1)
                    axes[1, 1].set_xticks(range(len(corr_matrix.columns)))
                    axes[1, 1].set_yticks(range(len(corr_matrix.columns)))
                    axes[1, 1].set_xticklabels(corr_matrix.columns, rotation=45)
                    axes[1, 1].set_yticklabels(corr_matrix.columns)
                    axes[1, 1].set_title("Parameter Correlation")
                    plt.colorbar(im, ax=axes[1, 1])

            # Plot 6: Quality Summary
            if "resolution_limit_A" in df.columns:
                quality_bins = [0, 5, 10, 15, 20, np.inf]
                quality_labels = ["Excellent", "Good", "Fair", "Poor", "Bad"]
                quality_counts = pd.cut(
                    df["resolution_limit_A"], bins=quality_bins, labels=quality_labels
                ).value_counts()

                axes[1, 2].pie(
                    quality_counts.values,
                    labels=quality_counts.index,
                    autopct="%1.1f%%",
                )
                axes[1, 2].set_title("Resolution Quality Distribution")

            plt.tight_layout()

            # Save to file
            output_file = self.output_dir / "ctf_analysis_dashboard.png"
            plt.savefig(output_file, dpi=300, bbox_inches="tight")
            plt.close()

            logger.info(f"Static CTF dashboard created: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"Error creating static CTF dashboard: {e}")
            return self._create_error_message(str(e))

    def create_motion_analysis_plot(
        self, motion_data: Dict[str, Any], interactive: bool = True
    ) -> str:
        """Create motion analysis visualization."""
        if not PLOTLY_AVAILABLE and interactive:
            interactive = False

        if interactive:
            return self._create_interactive_motion_plot(motion_data)
        else:
            return self._create_static_motion_plot(motion_data)

    # TODO: Refactor function - Function '_create_interactive_motion_plot' too
    # long (83 lines)
    def _create_interactive_motion_plot(self, motion_data: Dict[str, Any]) -> str:
        """Create interactive motion analysis plot."""
        try:
            fig = make_subplots(
                rows=2,
                cols=2,
                subplot_titles=[
                    "Frame-by-Frame Motion",
                    "Cumulative Drift",
                    "Motion Magnitude Distribution",
                    "Motion Vector Field",
                ],
            )

            # Extract motion data
            for series_name, data in motion_data.get("motion_data", {}).items():
                if "motion_vectors" not in data:
                    continue

                motion_vectors = data["motion_vectors"]
                frames = list(range(len(motion_vectors)))

                # Calculate motion magnitudes
                magnitudes = [
                    np.sqrt(mv["x"] ** 2 + mv["y"] ** 2) for mv in motion_vectors
                ]

                # Plot 1: Frame-by-Frame Motion
                fig.add_trace(
                    go.Scatter(
                        x=frames,
                        y=magnitudes,
                        mode="lines+markers",
                        name=f"{series_name} Motion",
                        line=dict(width=2),
                    ),
                    row=1,
                    col=1,
                )

                # Plot 2: Cumulative Drift
                cumulative_x = np.cumsum([mv["x"] for mv in motion_vectors])
                cumulative_y = np.cumsum([mv["y"] for mv in motion_vectors])

                fig.add_trace(
                    go.Scatter(
                        x=cumulative_x,
                        y=cumulative_y,
                        mode="lines+markers",
                        name=f"{series_name} Drift Path",
                        line=dict(width=2),
                    ),
                    row=1,
                    col=2,
                )

                # Plot 3: Motion Magnitude Distribution
                fig.add_trace(
                    go.Histogram(
                        x=magnitudes, name=f"{series_name} Distribution", opacity=0.7
                    ),
                    row=2,
                    col=1,
                )

            # Update layout
            fig.update_layout(
                title="Motion Analysis Dashboard",
                height=800,
                showlegend=True,
                template=self._get_plotly_template(),
            )

            # Save to file
            output_file = self.output_dir / "motion_analysis.html"
            fig.write_html(str(output_file))

            logger.info(f"Interactive motion plot created: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"Error creating interactive motion plot: {e}")
            return self._create_error_message(str(e))

    # TODO: Refactor function - Function '_create_static_motion_plot' too long
    # (74 lines)
    def _create_static_motion_plot(self, motion_data: Dict[str, Any]) -> str:
        """Create static motion analysis plot."""
        if not MATPLOTLIB_AVAILABLE:
            return self._create_no_data_message("Matplotlib not available")

        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle("Motion Analysis Dashboard", fontsize=16)

            for series_name, data in motion_data.get("motion_data", {}).items():
                if "motion_vectors" not in data:
                    continue

                motion_vectors = data["motion_vectors"]
                frames = list(range(len(motion_vectors)))
                magnitudes = [
                    np.sqrt(mv["x"] ** 2 + mv["y"] ** 2) for mv in motion_vectors
                ]

                # Plot motion magnitude
                axes[0, 0].plot(
                    frames,
                    magnitudes,
                    "-o",
                    label=series_name,
                    linewidth=2,
                    markersize=4,
                )

                # Plot cumulative drift
                cumulative_x = np.cumsum([mv["x"] for mv in motion_vectors])
                cumulative_y = np.cumsum([mv["y"] for mv in motion_vectors])
                axes[0, 1].plot(
                    cumulative_x,
                    cumulative_y,
                    "-o",
                    label=series_name,
                    linewidth=2,
                    markersize=4,
                )

                # Motion distribution
                axes[1, 0].hist(magnitudes, alpha=0.7, label=series_name, bins=20)

            axes[0, 0].set_xlabel("Frame Number")
            axes[0, 0].set_ylabel("Motion Magnitude (pixels)")
            axes[0, 0].set_title("Frame-by-Frame Motion")
            axes[0, 0].legend()

            axes[0, 1].set_xlabel("Cumulative X Drift (pixels)")
            axes[0, 1].set_ylabel("Cumulative Y Drift (pixels)")
            axes[0, 1].set_title("Cumulative Drift Path")
            axes[0, 1].legend()

            axes[1, 0].set_xlabel("Motion Magnitude (pixels)")
            axes[1, 0].set_ylabel("Frequency")
            axes[1, 0].set_title("Motion Distribution")
            axes[1, 0].legend()

            # Remove empty subplot
            fig.delaxes(axes[1, 1])

            plt.tight_layout()

            output_file = self.output_dir / "motion_analysis.png"
            plt.savefig(output_file, dpi=300, bbox_inches="tight")
            plt.close()

            logger.info(f"Static motion plot created: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"Error creating static motion plot: {e}")
            return self._create_error_message(str(e))

    def _get_plotly_template(self) -> str:
        """Get Plotly template based on current theme."""
        theme_mapping = {
            "default": "plotly_white",
            "dark": "plotly_dark",
            "scientific": "simple_white",
        }
        return theme_mapping.get(self.current_theme, "plotly_white")

    def _create_no_data_message(self, message: str) -> str:
        """Create a no data message file."""
        html_content = f"""
        <html>
        <head><title>No Data Available</title></head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>No Data Available</h2>
                <p>{message}</p>
            </div>
        </body>
        </html>
        """

        output_file = self.output_dir / "no_data.html"
        output_file.write_text(html_content)
        return str(output_file)

    def _create_error_message(self, error: str) -> str:
        """Create an error message file."""
        html_content = f"""
        <html>
        <head><title>Visualization Error</title></head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>Visualization Error</h2>
                <p>Error: {error}</p>
            </div>
        </body>
        </html>
        """

        output_file = self.output_dir / "error.html"
        output_file.write_text(html_content)
        return str(output_file)

    def set_theme(self, theme_name: str):
        """Set visualization theme."""
        if theme_name in self.themes:
            self.current_theme = theme_name
            logger.info(f"Visualization theme set to: {theme_name}")
        else:
            logger.warning(f"Unknown theme: {theme_name}")

    def get_available_themes(self) -> List[str]:
        """Get list of available themes."""
        return list(self.themes.keys())

    def load_volume(self, file_path: Path, volume_id: str = None) -> Optional[str]:
        """Load volume data for 3D visualization."""
        try:
            if not volume_id:
                volume_id = file_path.stem

            # Load based on file extension
            if file_path.suffix.lower() in [".mrc", ".mrcs", ".rec"]:
                volume_data = self._load_mrc_volume(file_path)
            elif file_path.suffix.lower() in [".tif", ".tiff"]:
                volume_data = self._load_tiff_volume(file_path)
            else:
                logger.error(f"Unsupported file format: {file_path.suffix}")
                return None

            if volume_data is None:
                return None

            # Store in cache
            self.volume_cache[volume_id] = volume_data
            self.volume_metadata[volume_id] = {
                "file_path": str(file_path),
                "shape": volume_data.shape,
                "dtype": str(volume_data.dtype),
                "min_value": float(np.min(volume_data)),
                "max_value": float(np.max(volume_data)),
                "mean_value": float(np.mean(volume_data)),
                "std_value": float(np.std(volume_data)),
                "loaded_at": datetime.now().isoformat(),
            }

            logger.info(f"Loaded volume: {volume_id} - Shape: {volume_data.shape}")
            return volume_id

        except Exception as e:
            logger.error(f"Error loading volume: {e}")
            return None

    def _load_mrc_volume(self, file_path: Path) -> Optional[np.ndarray]:
        """Load MRC/REC volume."""
        if not MRC_AVAILABLE:
            logger.error("mrcfile not available for MRC loading")
            return None

        try:
            with mrcfile.open(file_path, mode="r") as mrc:
                volume_data = mrc.data.copy()

                # Handle different data layouts
                if volume_data.ndim == 3:
                    return volume_data.astype(np.float32)
                elif volume_data.ndim == 2:
                    return volume_data[np.newaxis, :, :].astype(np.float32)
                else:
                    logger.error(
                        f"Unsupported volume dimensions: {
                            volume_data.ndim}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Error loading MRC file: {e}")
            return None

    def _load_tiff_volume(self, file_path: Path) -> Optional[np.ndarray]:
        """Load TIFF volume stack."""
        try:
            volume_data = tifffile.imread(file_path)

            # Ensure 3D
            if volume_data.ndim == 2:
                volume_data = volume_data[np.newaxis, :, :]
            elif volume_data.ndim > 3:
                volume_data = (
                    volume_data[:, :, :, 0]
                    if volume_data.shape[-1] < volume_data.shape[0]
                    else volume_data[0]
                )

            return volume_data.astype(np.float32)

        except Exception as e:
            logger.error(f"Error loading TIFF file: {e}")
            return None

    # TODO: Refactor function - Function 'create_volume_rendering' too long
    # (52 lines)
    def create_volume_rendering(self, volume_id: str, **kwargs) -> Optional[str]:
        """Create 3D volume rendering."""
        if not VTK_AVAILABLE:
            logger.error("VTK not available for volume rendering")
            return None

        if volume_id not in self.volume_cache:
            logger.error(f"Volume not found: {volume_id}")
            return None

        try:
            volume_data = self.volume_cache[volume_id]

            # Create VTK volume
            vtk_volume = self._create_vtk_volume(volume_data)

            # Setup volume rendering
            volume_mapper = vtk.vtkGPUVolumeRayCastMapper()
            volume_mapper.SetInputData(vtk_volume)

            # Volume properties
            volume_property = vtk.vtkVolumeProperty()
            volume_property.SetInterpolationTypeToLinear()
            volume_property.ShadeOn()

            # Opacity and color functions
            opacity_func = self._create_opacity_function(volume_data)
            color_func = self._create_color_function(volume_data)
            volume_property.SetScalarOpacity(opacity_func)
            volume_property.SetColor(color_func)

            # Create volume actor
            volume_actor = vtk.vtkVolume()
            volume_actor.SetMapper(volume_mapper)
            volume_actor.SetProperty(volume_property)

            # Setup renderer
            if not self.vtk_renderer:
                self._setup_vtk_renderer()

            self.vtk_renderer.AddVolume(volume_actor)
            self.vtk_renderer.ResetCamera()

            if self.vtk_render_window:
                self.vtk_render_window.Render()

            logger.info(f"Created volume rendering for: {volume_id}")
            return f"volume_render_{volume_id}"

        except Exception as e:
            logger.error(f"Error creating volume rendering: {e}")
            return None

    def _create_vtk_volume(self, volume_data: np.ndarray):
        """Create VTK volume from numpy array."""
        vtk_data = numpy_support.numpy_to_vtk(
            volume_data.ravel(), deep=True, array_type=vtk.VTK_FLOAT
        )

        vtk_volume = vtk.vtkImageData()
        vtk_volume.SetDimensions(volume_data.shape[::-1])
        vtk_volume.SetSpacing(1.0, 1.0, 1.0)
        vtk_volume.SetOrigin(0.0, 0.0, 0.0)
        vtk_volume.GetPointData().SetScalars(vtk_data)

        return vtk_volume

    def _create_opacity_function(self, volume_data: np.ndarray):
        """Create opacity transfer function."""
        opacity_func = vtk.vtkPiecewiseFunction()

        min_val = np.min(volume_data)
        max_val = np.max(volume_data)

        opacity_func.AddPoint(min_val, 0.0)
        opacity_func.AddPoint(min_val + 0.2 * (max_val - min_val), 0.1)
        opacity_func.AddPoint(min_val + 0.8 * (max_val - min_val), 0.8)
        opacity_func.AddPoint(max_val, 1.0)

        return opacity_func

    def _create_color_function(self, volume_data: np.ndarray):
        """Create color transfer function."""
        color_func = vtk.vtkColorTransferFunction()

        min_val = np.min(volume_data)
        max_val = np.max(volume_data)

        color_func.AddRGBPoint(min_val, 0.0, 0.0, 0.0)
        color_func.AddRGBPoint(max_val, 1.0, 1.0, 1.0)

        return color_func

    def _setup_vtk_renderer(self):
        """Setup VTK renderer."""
        self.vtk_renderer = vtk.vtkRenderer()
        self.vtk_renderer.SetBackground(0.1, 0.1, 0.1)

        self.vtk_render_window = vtk.vtkRenderWindow()
        self.vtk_render_window.AddRenderer(self.vtk_renderer)
        self.vtk_render_window.SetSize(800, 600)

        self.vtk_interactor = vtk.vtkRenderWindowInteractor()
        self.vtk_interactor.SetRenderWindow(self.vtk_render_window)

        style = vtk.vtkInteractorStyleTrackballCamera()
        self.vtk_interactor.SetInteractorStyle(style)


# Global advanced visualizer instance
advanced_visualizer = AdvancedVisualizer()


def create_visualization(
    data: Dict[str, Any], viz_type: str, interactive: bool = True
) -> str:
    """Convenience function to create visualizations."""
    if viz_type == "ctf_analysis":
        return advanced_visualizer.create_ctf_analysis_dashboard(data, interactive)
    elif viz_type == "motion_analysis":
        return advanced_visualizer.create_motion_analysis_plot(data, interactive)
    else:
        logger.error(f"Unknown visualization type: {viz_type}")
        return advanced_visualizer._create_error_message(
            f"Unknown visualization type: {viz_type}"
        )
