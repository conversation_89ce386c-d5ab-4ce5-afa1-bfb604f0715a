#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Analytics Dashboard
Comprehensive analytics and insights for tomographic processing workflows.
"""

import json
import logging
import sqlite3
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

# Analytics and visualization imports
try:
    import plotly.express as px
    import plotly.graph_objects as go
    import plotly.offline as pyo
    from plotly.subplots import make_subplots

    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    from scipy import stats
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler

    SCIPY_SKLEARN_AVAILABLE = True
except ImportError:
    SCIPY_SKLEARN_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ProcessingSession:
    """Processing session data."""

    session_id: str
    start_time: datetime
    end_time: Optional[datetime]
    input_files: List[str]
    output_files: List[str]
    parameters: Dict[str, Any]
    quality_metrics: Dict[str, float]
    processing_time: float
    success: bool
    error_message: Optional[str]


@dataclass
class AnalyticsInsight:
    """Analytics insight or recommendation."""

    insight_type: str
    title: str
    description: str
    confidence: float
    impact: str
    data_source: str
    timestamp: datetime

    # TODO: Refactor class - Class 'AnalyticsDashboard' too long (682 lines)


class AnalyticsDashboard:
    """
    Advanced analytics dashboard for AreTomo3 GUI.
    Provides comprehensive insights into processing workflows and performance.
    """

    def __init__(self, data_dir: Path = None):
        """Initialize the analytics dashboard."""
        self.data_dir = data_dir or Path.home() / ".aretomo3_gui" / "analytics"
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Database for analytics data
        self.db_path = self.data_dir / "analytics.db"
        self._initialize_database()

        # Processing sessions
        self.sessions: List[ProcessingSession] = []

        # Analytics insights
        self.insights: List[AnalyticsInsight] = []

        # Load historical data
        self._load_historical_data()

        logger.info("Analytics Dashboard initialized")

    def _initialize_database(self):
        """Initialize SQLite database for analytics data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Create sessions table
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS sessions (
                        session_id TEXT PRIMARY KEY,
                        start_time TEXT,
                        end_time TEXT,
                        input_files TEXT,
                        output_files TEXT,
                        parameters TEXT,
                        quality_metrics TEXT,
                        processing_time REAL,
                        success BOOLEAN,
                        error_message TEXT
                    )
                """
                )

                # Create insights table
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS insights (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        insight_type TEXT,
                        title TEXT,
                        description TEXT,
                        confidence REAL,
                        impact TEXT,
                        data_source TEXT,
                        timestamp TEXT
                    )
                """
                )

                conn.commit()

        except Exception as e:
            logger.error(f"Error initializing analytics database: {e}")

    def record_processing_session(self, session: ProcessingSession):
        """Record a processing session for analytics."""
        try:
            self.sessions.append(session)

            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO sessions
                    (session_id, start_time, end_time, input_files, output_files,
                     parameters, quality_metrics, processing_time, success, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        session.session_id,
                        session.start_time.isoformat(),
                        session.end_time.isoformat() if session.end_time else None,
                        json.dumps(session.input_files),
                        json.dumps(session.output_files),
                        json.dumps(session.parameters),
                        json.dumps(session.quality_metrics),
                        session.processing_time,
                        session.success,
                        session.error_message,
                    ),
                )
                conn.commit()

            # Generate insights from new session
            self._analyze_session(session)

            logger.info(f"Recorded processing session: {session.session_id}")

        except Exception as e:
            logger.error(f"Error recording processing session: {e}")

    def _load_historical_data(self):
        """Load historical data from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Load sessions
                cursor.execute(
                    "SELECT * FROM sessions ORDER BY start_time DESC LIMIT 1000"
                )
                for row in cursor.fetchall():
                    session = ProcessingSession(
                        session_id=row[0],
                        start_time=datetime.fromisoformat(row[1]),
                        end_time=datetime.fromisoformat(row[2]) if row[2] else None,
                        input_files=json.loads(row[3]),
                        output_files=json.loads(row[4]),
                        parameters=json.loads(row[5]),
                        quality_metrics=json.loads(row[6]),
                        processing_time=row[7],
                        success=bool(row[8]),
                        error_message=row[9],
                    )
                    self.sessions.append(session)

                # Load insights
                cursor.execute(
                    "SELECT * FROM insights ORDER BY timestamp DESC LIMIT 100"
                )
                for row in cursor.fetchall():
                    insight = AnalyticsInsight(
                        insight_type=row[1],
                        title=row[2],
                        description=row[3],
                        confidence=row[4],
                        impact=row[5],
                        data_source=row[6],
                        timestamp=datetime.fromisoformat(row[7]),
                    )
                    self.insights.append(insight)

            logger.info(
                f"Loaded {len(self.sessions)} sessions and {len(self.insights)} insights"
            )

        except Exception as e:
            logger.error(f"Error loading historical data: {e}")

    def _analyze_session(self, session: ProcessingSession):
        """Analyze a processing session and generate insights."""
        try:
            # Success rate analysis
            recent_sessions = [s for s in self.sessions[-50:] if s.end_time]
            if len(recent_sessions) >= 10:
                success_rate = sum(1 for s in recent_sessions if s.success) / len(
                    recent_sessions
                )

                if success_rate < 0.8:
                    self._add_insight(
                        "quality",
                        "Low Success Rate Detected",
                        f"Recent success rate is {
                            success_rate:.1%}. Consider reviewing parameters.",
                        0.8,
                        "high",
                        "session_analysis",
                    )

            # Processing time analysis
            if session.processing_time > 0:
                similar_sessions = [
                    s
                    for s in self.sessions
                    if s.success and len(s.input_files) == len(session.input_files)
                ]

                if len(similar_sessions) >= 5:
                    avg_time = np.mean([s.processing_time for s in similar_sessions])
                    if session.processing_time > avg_time * 1.5:
                        self._add_insight(
                            "performance",
                            "Slow Processing Detected",
                            f"Processing took {
                                session.processing_time:.1f}s vs average {
                                avg_time:.1f}s",
                            0.7,
                            "medium",
                            "performance_analysis",
                        )

            # Quality metrics analysis
            if session.quality_metrics and session.success:
                self._analyze_quality_trends(session)

        except Exception as e:
            logger.error(f"Error analyzing session: {e}")

    def _analyze_quality_trends(self, session: ProcessingSession):
        """Analyze quality metric trends."""
        try:
            if "overall_score" in session.quality_metrics:
                score = session.quality_metrics["overall_score"]

                # Compare with recent sessions
                recent_scores = [
                    s.quality_metrics.get("overall_score", 0)
                    for s in self.sessions[-20:]
                    if s.success and s.quality_metrics
                ]

                if len(recent_scores) >= 5:
                    avg_score = np.mean(recent_scores)

                    if score < avg_score * 0.8:
                        self._add_insight(
                            "quality",
                            "Quality Score Below Average",
                            f"Quality score {
                                score:.1f} is below recent average {
                                avg_score:.1f}",
                            0.6,
                            "medium",
                            "quality_analysis",
                        )
                    elif score > avg_score * 1.2:
                        self._add_insight(
                            "quality",
                            "Excellent Quality Achieved",
                            f"Quality score {
                                score:.1f} exceeds recent average {
                                avg_score:.1f}",
                            0.8,
                            "positive",
                            "quality_analysis",
                        )

        except Exception as e:
            logger.error(f"Error analyzing quality trends: {e}")

    # TODO: Refactor function - Function '_add_insight' too long (54 lines)
    def _add_insight(
        self,
        insight_type: str,
        title: str,
        description: str,
        confidence: float,
        impact: str,
        data_source: str,
    ):
        """Add a new analytics insight."""
        try:
            insight = AnalyticsInsight(
                insight_type=insight_type,
                title=title,
                description=description,
                confidence=confidence,
                impact=impact,
                data_source=data_source,
                timestamp=datetime.now(),
            )

            self.insights.append(insight)

            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO insights
                    (
                        insight_type,
                        title,
                        description,
                        confidence,
                        impact,
                        data_source,
                        timestamp
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        insight.insight_type,
                        insight.title,
                        insight.description,
                        insight.confidence,
                        insight.impact,
                        insight.data_source,
                        insight.timestamp.isoformat(),
                    ),
                )
                conn.commit()

        except Exception as e:
            logger.error(f"Error adding insight: {e}")

    # TODO: Refactor function - Function 'generate_dashboard_html' too long
    # (96 lines)
    def generate_dashboard_html(self) -> str:
        """Generate comprehensive analytics dashboard HTML."""
        if not PLOTLY_AVAILABLE:
            return self._generate_basic_dashboard()

        try:
            # Create dashboard with multiple plots
            fig = make_subplots(
                rows=3,
                cols=2,
                subplot_titles=(
                    "Processing Success Rate Over Time",
                    "Processing Time Distribution",
                    "Quality Metrics Trends",
                    "Parameter Usage Analysis",
                    "Error Analysis",
                    "Performance Insights",
                ),
                specs=[
                    [{"secondary_y": False}, {"secondary_y": False}],
                    [{"secondary_y": False}, {"secondary_y": False}],
                    [{"secondary_y": False}, {"secondary_y": False}],
                ],
            )

            # Plot 1: Success rate over time
            self._add_success_rate_plot(fig, 1, 1)

            # Plot 2: Processing time distribution
            self._add_processing_time_plot(fig, 1, 2)

            # Plot 3: Quality metrics trends
            self._add_quality_trends_plot(fig, 2, 1)

            # Plot 4: Parameter usage
            self._add_parameter_analysis_plot(fig, 2, 2)

            # Plot 5: Error analysis
            self._add_error_analysis_plot(fig, 3, 1)

            # Plot 6: Performance insights
            self._add_performance_insights_plot(fig, 3, 2)

            # Update layout
            fig.update_layout(
                title=dict(
                    text="AreTomo3 GUI Analytics Dashboard", x=0.5, font=dict(size=24)
                ),
                height=1200,
                showlegend=True,
                template="plotly_white",
            )

            # Convert to HTML
            dashboard_html = pyo.plot(fig, output_type="div", include_plotlyjs=True)

            # Add summary statistics
            summary_stats = self._generate_summary_statistics()

            # Create complete HTML
            full_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>AreTomo3 Analytics Dashboard</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .summary {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .insight {{ background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }}
                    .insight.high {{ background-color: #ffebee; }}
                    .insight.positive {{ background-color: #e8f5e8; }}
                </style>
            </head>
            <body>
                <h1>AreTomo3 GUI Analytics Dashboard</h1>

                <div class="summary">
                    <h2>Summary Statistics</h2>
                    {summary_stats}
                </div>

                <div class="insights">
                    <h2>Recent Insights</h2>
                    {self._format_insights_html()}
                </div>

                {dashboard_html}
            </body>
            </html>
            """

            return full_html

        except Exception as e:
            logger.error(f"Error generating dashboard HTML: {e}")
            return self._generate_basic_dashboard()

    def _add_success_rate_plot(self, fig, row, col):
        """Add success rate plot to dashboard."""
        try:
            # Calculate daily success rates
            daily_data = {}
            for session in self.sessions:
                if session.end_time:
                    date = session.start_time.date()
                    if date not in daily_data:
                        daily_data[date] = {"total": 0, "success": 0}
                    daily_data[date]["total"] += 1
                    if session.success:
                        daily_data[date]["success"] += 1

            dates = sorted(daily_data.keys())
            success_rates = [
                daily_data[date]["success"] / daily_data[date]["total"] * 100
                for date in dates
            ]

            fig.add_trace(
                go.Scatter(
                    x=dates,
                    y=success_rates,
                    mode="lines+markers",
                    name="Success Rate",
                    line=dict(color="green", width=2),
                ),
                row=row,
                col=col,
            )

        except Exception as e:
            logger.error(f"Error adding success rate plot: {e}")

    def _add_processing_time_plot(self, fig, row, col):
        """Add processing time distribution plot."""
        try:
            processing_times = [
                s.processing_time
                for s in self.sessions
                if s.success and s.processing_time > 0
            ]

            if processing_times:
                fig.add_trace(
                    go.Histogram(
                        x=processing_times,
                        nbinsx=20,
                        name="Processing Time",
                        marker_color="blue",
                        opacity=0.7,
                    ),
                    row=row,
                    col=col,
                )

        except Exception as e:
            logger.error(f"Error adding processing time plot: {e}")

    def _add_quality_trends_plot(self, fig, row, col):
        """Add quality metrics trends plot."""
        try:
            quality_scores = []
            timestamps = []

            for session in self.sessions:
                if (
                    session.success
                    and session.quality_metrics
                    and "overall_score" in session.quality_metrics
                ):
                    quality_scores.append(session.quality_metrics["overall_score"])
                    timestamps.append(session.start_time)

            if quality_scores:
                fig.add_trace(
                    go.Scatter(
                        x=timestamps,
                        y=quality_scores,
                        mode="markers+lines",
                        name="Quality Score",
                        line=dict(color="purple", width=2),
                    ),
                    row=row,
                    col=col,
                )

        except Exception as e:
            logger.error(f"Error adding quality trends plot: {e}")

    def _add_parameter_analysis_plot(self, fig, row, col):
        """Add parameter usage analysis plot."""
        try:
            # Analyze most common parameters
            param_usage = {}
            for session in self.sessions:
                for key, value in session.parameters.items():
                    if key not in param_usage:
                        param_usage[key] = {}
                    str_value = str(value)
                    param_usage[key][str_value] = param_usage[key].get(str_value, 0) + 1

            # Show top parameter
            if param_usage:
                top_param = max(
                    param_usage.keys(), key=lambda k: sum(param_usage[k].values())
                )
                values = list(param_usage[top_param].keys())
                counts = list(param_usage[top_param].values())

                fig.add_trace(
                    go.Bar(
                        x=values[:10],  # Top 10 values
                        y=counts[:10],
                        name=f"{top_param} Usage",
                        marker_color="orange",
                    ),
                    row=row,
                    col=col,
                )

        except Exception as e:
            logger.error(f"Error adding parameter analysis plot: {e}")

    def _add_error_analysis_plot(self, fig, row, col):
        """Add error analysis plot."""
        try:
            error_types = {}
            for session in self.sessions:
                if not session.success and session.error_message:
                    # Categorize errors
                    error_key = (
                        session.error_message.split(":")[0]
                        if ":" in session.error_message
                        else "Unknown"
                    )
                    error_types[error_key] = error_types.get(error_key, 0) + 1

            if error_types:
                fig.add_trace(
                    go.Pie(
                        labels=list(error_types.keys()),
                        values=list(error_types.values()),
                        name="Error Types",
                    ),
                    row=row,
                    col=col,
                )

        except Exception as e:
            logger.error(f"Error adding error analysis plot: {e}")

    def _add_performance_insights_plot(self, fig, row, col):
        """Add performance insights plot."""
        try:
            # Show insights by type
            insight_types = {}
            for insight in self.insights[-50:]:  # Recent insights
                insight_types[insight.insight_type] = (
                    insight_types.get(insight.insight_type, 0) + 1
                )

            if insight_types:
                fig.add_trace(
                    go.Bar(
                        x=list(insight_types.keys()),
                        y=list(insight_types.values()),
                        name="Insight Types",
                        marker_color="red",
                    ),
                    row=row,
                    col=col,
                )

        except Exception as e:
            logger.error(f"Error adding performance insights plot: {e}")

    # TODO: Refactor function - Function '_generate_summary_statistics' too
    # long (54 lines)
    def _generate_summary_statistics(self) -> str:
        """Generate summary statistics HTML."""
        try:
            total_sessions = len(self.sessions)
            successful_sessions = sum(1 for s in self.sessions if s.success)
            success_rate = (
                (successful_sessions / total_sessions * 100)
                if total_sessions > 0
                else 0
            )

            avg_processing_time = np.mean(
                [
                    s.processing_time
                    for s in self.sessions
                    if s.success and s.processing_time > 0
                ]
            )

            recent_insights = len(
                [
                    i
                    for i in self.insights
                    if i.timestamp > datetime.now() - timedelta(days=7)
                ]
            )

            return f"""
            <div style="display: grid; grid-template-columns: repeat(
                4,
                1fr
            ); gap: 20px;">
                <div style="text-align: center;">
                    <h3>{total_sessions}</h3>
                    <p>Total Sessions</p>
                </div>
                <div style="text-align: center;">
                    <h3>{success_rate:.1f}%</h3>
                    <p>Success Rate</p>
                </div>
                <div style="text-align: center;">
                    <h3>{avg_processing_time:.1f}s</h3>
                    <p>Avg Processing Time</p>
                </div>
                <div style="text-align: center;">
                    <h3>{recent_insights}</h3>
                    <p>Recent Insights</p>
                </div>
            </div>
            """

        except Exception as e:
            logger.error(f"Error generating summary statistics: {e}")
            return "<p>Error generating statistics</p>"

    def _format_insights_html(self) -> str:
        """Format insights as HTML."""
        try:
            html = ""
            for insight in self.insights[-10:]:  # Recent 10 insights
                css_class = f"insight {insight.impact}"
                html += f"""
                <div class="{css_class}">
                    <h4>{insight.title}</h4>
                    <p>{insight.description}</p>
                    <small>Confidence: {insight.confidence:.1%} | {insight.timestamp.strftime('%Y-%m-%d %H:%M')}</small>
                </div>
                """

            return html if html else "<p>No recent insights available</p>"

        except Exception as e:
            logger.error(f"Error formatting insights: {e}")
            return "<p>Error formatting insights</p>"

    def _generate_basic_dashboard(self) -> str:
        """Generate basic dashboard without Plotly."""
        try:
            summary_stats = self._generate_summary_statistics()
            insights_html = self._format_insights_html()

            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>AreTomo3 Analytics Dashboard</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .summary {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .insight {{ background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }}
                </style>
            </head>
            <body>
                <h1>AreTomo3 GUI Analytics Dashboard</h1>
                <div class="summary">
                    <h2>Summary Statistics</h2>
                    {summary_stats}
                </div>
                <div class="insights">
                    <h2>Recent Insights</h2>
                    {insights_html}
                </div>
                <p><em>Install Plotly for advanced visualizations</em></p>
            </body>
            </html>
            """

        except Exception as e:
            logger.error(f"Error generating basic dashboard: {e}")
            return "<html><body><h1>Error generating dashboard</h1></body></html>"


# Global analytics dashboard instance
analytics_dashboard = AnalyticsDashboard()


def record_session(session: ProcessingSession):
    """Convenience function to record a processing session."""
    analytics_dashboard.record_processing_session(session)
