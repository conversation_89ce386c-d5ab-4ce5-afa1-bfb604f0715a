#!/usr/bin/env python3

import logging
import re
import subprocess
import threading
import time
from queue import Queue
from typing import Any, Dict, Optional, Tuple

import psutil

logger = logging.getLogger(__name__)


class GPUMonitor:
    """Class GPUMonitor implementation."""

    _gpu_info_cache = None
    _gpu_info_timestamp = 0
    _cache_duration = 5  # Cache GPU info for 5 seconds
    _gpu_check_done = False
    _has_gpu = False
    _check_lock = threading.Lock()

    @classmethod
    def _check_gpu_available(cls) -> bool:
        """Check if NVIDIA GPU is available without blocking"""
        if cls._gpu_check_done:
            return cls._has_gpu

        with cls._check_lock:
            if cls._gpu_check_done:  # Double-check pattern
                return cls._has_gpu

            try:
                # Give nvidia-smi a bit more time to respond
                result = subprocess.run(
                    ["nvidia-smi"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    timeout=2.0,
                )
                cls._has_gpu = result.returncode == 0
            except subprocess.TimeoutExpired:
                # If it times out, try one more time with a longer timeout
                try:
                    result = subprocess.run(
                        ["nvidia-smi"],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        timeout=5.0,
                    )
                    cls._has_gpu = result.returncode == 0
                except (subprocess.SubprocessError, FileNotFoundError):
                    cls._has_gpu = False
            except (subprocess.SubprocessError, FileNotFoundError):
                cls._has_gpu = False
            cls._gpu_check_done = True
            return cls._has_gpu

    @classmethod
    def get_gpu_info(cls) -> Optional[Dict[str, Any]]:
        """Get GPU information including memory usage, temperature and utilization"""
        # Quick check first
        if not cls._check_gpu_available():
            return None

        # Return cached info if available and recent
        current_time = time.time()
        if (
            cls._gpu_info_cache
            and (current_time - cls._gpu_info_timestamp) < cls._cache_duration
        ):
            return cls._gpu_info_cache

        try:
            # Use a very short timeout for the actual query
            result = subprocess.run(
                [
                    "nvidia-smi",
                    "--query-gpu=name,memory.total,memory.used,temperature.gpu,utilization.gpu",
                    "--format=csv,noheader",
                ],
                capture_output=True,
                text=True,
                timeout=2.0,
            )

            if result.returncode != 0:
                return None

            # Parse the output
            output = result.stdout.strip()
            name, mem_total, mem_used, temp, util = output.split(",")

            # Update cache
            cls._gpu_info_cache = {
                "name": name.strip(),
                "memory_total": int(mem_total.split()[0]),
                "memory_used": int(mem_used.split()[0]),
                "temperature": int(temp),
                "utilization": int(util.split()[0]),
            }
            cls._gpu_info_timestamp = current_time

            return cls._gpu_info_cache

        except (subprocess.SubprocessError, ValueError, FileNotFoundError, IndexError):
            return None

    @staticmethod
    def get_gpu_memory() -> Optional[Tuple[float, float]]:
        """Get basic GPU memory information"""
        if not GPUMonitor._check_gpu_available():
            return None

        try:
            output = subprocess.check_output(
                [
                    "nvidia-smi",
                    "--query-gpu=memory.used,memory.total",
                    "--format=csv,nounits,noheader",
                ],
                text=True,
                timeout=0.5,
            ).strip()

            used_mem, total_mem = map(float, output.split(","))
            return used_mem, total_mem
        except Exception:
            return None

    @staticmethod
    def get_available_gpus() -> list:
        """Get list of available CUDA devices"""
        if not GPUMonitor._check_gpu_available():
            return ["No GPU detected"]

        try:
            output = subprocess.check_output(
                ["nvidia-smi", "-L"], text=True, timeout=0.5
            ).strip()
            gpus = []
            for i, line in enumerate(output.split("\n")):
                if line:
                    # Extract GPU name and index for better display
                    match = re.search(r"GPU (\d+):\s*(.*)\s*\(", line)
                    if match:
                        idx, name = match.groups()
                        gpus.append(f"GPU {idx}: {name}")
                    else:
                        gpus.append(f"GPU {i}: {line}")
            return gpus if gpus else ["No GPU detected"]
        except Exception as e:
            return ["GPU detection error"]


class SystemMonitor:
    """Class SystemMonitor implementation."""

    def __init__(self, update_interval: float = 1.0):
        """Initialize the instance."""
        self.update_interval = update_interval
        self._stop_event = threading.Event()
        self._data_queue = Queue()
        self._monitor_thread = None
        self._last_update = 0

    def start(self) -> None:
        """Start the monitoring thread"""
        if self._monitor_thread is not None and self._monitor_thread.is_alive():
            return

        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("System monitoring started")

    def stop(self) -> None:
        """Stop the monitoring thread"""
        self._stop_event.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
            logger.info("System monitoring stopped")

    def _monitor_loop(self) -> None:
        """Background monitoring loop"""
        while not self._stop_event.is_set():
            try:
                # Get system metrics (use interval=None to avoid blocking)
                cpu_percent = psutil.cpu_percent(interval=None)
                mem = psutil.virtual_memory()
                disk = psutil.disk_usage("/")
                gpu_info = None  # Initially set to None

                # Try to get GPU info in a non-blocking way
                if GPUMonitor._check_gpu_available():
                    gpu_info = GPUMonitor.get_gpu_info()

                # Put data in queue
                self._data_queue.put(
                    {
                        "cpu": cpu_percent,
                        "memory": {
                            "total": mem.total,
                            "used": mem.used,
                            "percent": mem.percent,
                        },
                        "disk": {
                            "total": disk.total,
                            "used": disk.used,
                            "percent": disk.percent,
                        },
                        "gpu": gpu_info,
                    }
                )

            except Exception as e:
                logger.error(
                    f"Error in monitoring loop: {
                        str(e)}",
                    exc_info=True,
                )
                self._data_queue.put({"error": str(e)})

            # Sleep for update interval
            self._stop_event.wait(self.update_interval)

    def get_latest_data(self) -> Dict[str, Any]:
        """Get the latest monitoring data without blocking"""
        try:
            while not self._data_queue.empty():
                data = self._data_queue.get_nowait()
            return data
        except Exception:
            return {}
