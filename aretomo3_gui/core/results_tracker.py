#!/usr/bin/env python3
"""
Results tracking system for AreTomo3 processing.
Tracks detailed results from each processed tilt series.
"""

import json
import logging
import sqlite3
import threading
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class ProcessingResult:
    """Data class for storing processing results."""

    job_id: str
    tilt_series_name: str
    input_path: str
    output_path: str
    status: str  # "processing", "completed", "failed"
    start_time: datetime
    end_time: Optional[datetime] = None
    processing_time: Optional[float] = None  # seconds
    progress: float = 0.0
    error_message: Optional[str] = None

    # Processing parameters
    pixel_size: Optional[float] = None
    voltage: Optional[int] = None
    tilt_axis: Optional[float] = None
    tilt_range_min: Optional[float] = None
    tilt_range_max: Optional[float] = None
    num_tilts: Optional[int] = None

    # Output file information
    output_files: List[Dict[str, Any]] = None
    total_output_size: int = 0  # bytes

    # Quality metrics
    alignment_quality: Optional[float] = None
    ctf_resolution: Optional[float] = None

    def __post_init__(self):
        """Execute __post_init__ operation."""
        if self.output_files is None:
            self.output_files = []


class ResultsTracker:
    """Tracks and manages processing results."""

    def __init__(self, db_path: Optional[Path] = None):
        """Initialize the results tracker."""
        if db_path is None:
            db_path = Path.home() / ".aretomo3_gui" / "results.db"

        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Thread lock for database operations
        self._lock = threading.Lock()

        # Initialize database
        self._init_database()

        logger.info(
            f"Results tracker initialized with database: {
                self.db_path}"
        )

    def _init_database(self):
        """Initialize the SQLite database."""
        with self._lock:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Create results table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS processing_results (
                    job_id TEXT PRIMARY KEY,
                    tilt_series_name TEXT NOT NULL,
                    input_path TEXT NOT NULL,
                    output_path TEXT NOT NULL,
                    status TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    processing_time REAL,
                    progress REAL DEFAULT 0.0,
                    error_message TEXT,
                    pixel_size REAL,
                    voltage INTEGER,
                    tilt_axis REAL,
                    tilt_range_min REAL,
                    tilt_range_max REAL,
                    num_tilts INTEGER,
                    output_files TEXT,  -- JSON string
                    total_output_size INTEGER DEFAULT 0,
                    alignment_quality REAL,
                    ctf_resolution REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Create index for faster queries
            cursor.execute(
                "CREATE INDEX IF NOT EXISTS idx_status ON processing_results(status)"
            )
            cursor.execute(
                "CREATE INDEX IF NOT EXISTS idx_start_time ON processing_results(start_time)"
            )

            conn.commit()
            conn.close()

    # TODO: Refactor function - Function 'add_result' too long (78 lines)
    def add_result(self, result: ProcessingResult) -> bool:
        """Add a new processing result."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                # Convert result to database format
                data = {
                    "job_id": result.job_id,
                    "tilt_series_name": result.tilt_series_name,
                    "input_path": result.input_path,
                    "output_path": result.output_path,
                    "status": result.status,
                    "start_time": result.start_time.isoformat(),
                    "end_time": (
                        result.end_time.isoformat() if result.end_time else None
                    ),
                    "processing_time": result.processing_time,
                    "progress": result.progress,
                    "error_message": result.error_message,
                    "pixel_size": result.pixel_size,
                    "voltage": result.voltage,
                    "tilt_axis": result.tilt_axis,
                    "tilt_range_min": result.tilt_range_min,
                    "tilt_range_max": result.tilt_range_max,
                    "num_tilts": result.num_tilts,
                    "output_files": json.dumps(result.output_files),
                    "total_output_size": result.total_output_size,
                    "alignment_quality": result.alignment_quality,
                    "ctf_resolution": result.ctf_resolution,
                    "updated_at": datetime.now().isoformat(),
                }

                # Insert or replace
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO processing_results
                    (job_id, tilt_series_name, input_path, output_path, status, start_time,
                     end_time, processing_time, progress, error_message, pixel_size, voltage,
                     tilt_axis, tilt_range_min, tilt_range_max, num_tilts, output_files,
                     total_output_size, alignment_quality, ctf_resolution, updated_at)
                    VALUES (
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,
                        ?
                    )
                """,
                    tuple(data.values()),
                )

                conn.commit()
                conn.close()

                logger.info(f"Added/updated result for job {result.job_id}")
                return True

        except Exception as e:
            logger.error(f"Error adding result: {e}")
            return False

    def update_progress(self, job_id: str, progress: float, status: str = None) -> bool:
        """Update progress for a job."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                if status:
                    cursor.execute(
                        """
                        UPDATE processing_results
                        SET progress = ?, status = ?, updated_at = ?
                        WHERE job_id = ?
                    """,
                        (progress, status, datetime.now().isoformat(), job_id),
                    )
                else:
                    cursor.execute(
                        """
                        UPDATE processing_results
                        SET progress = ?, updated_at = ?
                        WHERE job_id = ?
                    """,
                        (progress, datetime.now().isoformat(), job_id),
                    )

                conn.commit()
                conn.close()
                return True

        except Exception as e:
            logger.error(f"Error updating progress: {e}")
            return False

    def complete_job(
        self, job_id: str, success: bool = True, error_message: str = None
    ) -> bool:
        """Mark a job as completed."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                # Get start time to calculate processing time
                cursor.execute(
                    "SELECT start_time FROM processing_results WHERE job_id = ?",
                    (job_id,),
                )
                row = cursor.fetchone()

                if row:
                    start_time = datetime.fromisoformat(row[0])
                    end_time = datetime.now()
                    processing_time = (end_time - start_time).total_seconds()

                    status = "completed" if success else "failed"

                    cursor.execute(
                        """
                        UPDATE processing_results
                        SET status = ?, end_time = ?, processing_time = ?, progress = ?,
                            error_message = ?, updated_at = ?
                        WHERE job_id = ?
                    """,
                        (
                            status,
                            end_time.isoformat(),
                            processing_time,
                            100.0 if success else 0.0,
                            error_message,
                            datetime.now().isoformat(),
                            job_id,
                        ),
                    )

                    conn.commit()
                    conn.close()

                    logger.info(f"Completed job {job_id} with status {status}")
                    return True

        except Exception as e:
            logger.error(f"Error completing job: {e}")
            return False

    def get_result(self, job_id: str) -> Optional[ProcessingResult]:
        """Get a specific result by job ID."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                cursor.execute(
                    "SELECT * FROM processing_results WHERE job_id = ?", (job_id,)
                )
                row = cursor.fetchone()
                conn.close()

                if row:
                    return self._row_to_result(row)
                return None

        except Exception as e:
            logger.error(f"Error getting result: {e}")
            return None

    def get_all_results(
        self, limit: int = None, status: str = None
    ) -> List[ProcessingResult]:
        """Get all results, optionally filtered by status."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                query = "SELECT * FROM processing_results"
                params = []

                if status:
                    query += " WHERE status = ?"
                    params.append(status)

                query += " ORDER BY start_time DESC"

                if limit:
                    query += " LIMIT ?"
                    params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()
                conn.close()

                return [self._row_to_result(row) for row in rows]

        except Exception as e:
            logger.error(f"Error getting results: {e}")
            return []

    # TODO: Refactor function - Function 'get_statistics' too long (60 lines)
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                # Basic counts
                cursor.execute("SELECT COUNT(*) FROM processing_results")
                total = cursor.fetchone()[0]

                cursor.execute(
                    "SELECT COUNT(*) FROM processing_results WHERE status = 'completed'"
                )
                completed = cursor.fetchone()[0]

                cursor.execute(
                    "SELECT COUNT(*) FROM processing_results WHERE status = 'failed'"
                )
                failed = cursor.fetchone()[0]

                cursor.execute(
                    "SELECT COUNT(*) FROM processing_results WHERE status = 'processing'"
                )
                processing = cursor.fetchone()[0]

                # Average processing time
                cursor.execute(
                    "SELECT AVG(processing_time) FROM processing_results WHERE status = 'completed'"
                )
                avg_time = cursor.fetchone()[0] or 0

                # Throughput (completed jobs in last 24 hours)
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM processing_results
                    WHERE status = 'completed' AND start_time > datetime(
                        'now',
                        '-1 day'
                    )
                """
                )
                recent_completed = cursor.fetchone()[0]

                conn.close()

                return {
                    "total_files": total,
                    "processed_files": completed,
                    "failed_files": failed,
                    "processing_files": processing,
                    "queue_size": processing,
                    "avg_processing_time": avg_time,
                    "throughput_per_hour": recent_completed / 24.0,
                    "success_rate": (completed / total * 100) if total > 0 else 0,
                }

        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}

    def _row_to_result(self, row) -> ProcessingResult:
        """Convert database row to ProcessingResult object."""
        # Database columns in order
        columns = [
            "job_id",
            "tilt_series_name",
            "input_path",
            "output_path",
            "status",
            "start_time",
            "end_time",
            "processing_time",
            "progress",
            "error_message",
            "pixel_size",
            "voltage",
            "tilt_axis",
            "tilt_range_min",
            "tilt_range_max",
            "num_tilts",
            "output_files",
            "total_output_size",
            "alignment_quality",
            "ctf_resolution",
            "created_at",
            "updated_at",
        ]

        data = dict(zip(columns, row))

        # Parse JSON fields
        if data["output_files"]:
            data["output_files"] = json.loads(data["output_files"])
        else:
            data["output_files"] = []

        # Parse datetime fields
        data["start_time"] = datetime.fromisoformat(data["start_time"])
        if data["end_time"]:
            data["end_time"] = datetime.fromisoformat(data["end_time"])

        # Remove database-only fields
        data.pop("created_at", None)
        data.pop("updated_at", None)

        return ProcessingResult(**data)

    def clear_old_results(self, days: int = 30) -> int:
        """Clear results older than specified days."""
        try:
            with self._lock:
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                cursor.execute(
                    """
                    DELETE FROM processing_results
                    WHERE start_time < datetime('now', '-{} days')
                """.format(
                        days
                    )
                )

                deleted = cursor.rowcount
                conn.commit()
                conn.close()

                logger.info(f"Cleared {deleted} old results")
                return deleted

        except Exception as e:
            logger.error(f"Error clearing old results: {e}")
            return 0
