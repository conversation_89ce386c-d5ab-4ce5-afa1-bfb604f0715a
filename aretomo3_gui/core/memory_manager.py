#!/usr/bin/env python3
"""
Advanced Memory Manager
Provides memory monitoring, leak detection, and automatic cleanup.
"""

import gc
import logging
import sys
import threading
import time
import tracemalloc
import weakref
from collections import OrderedDict, defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional, Set

import psutil

logger = logging.getLogger(__name__)


@dataclass
class MemorySnapshot:
    """Memory usage snapshot."""

    timestamp: datetime
    total_memory_mb: float
    available_memory_mb: float
    process_memory_mb: float
    memory_percent: float
    gc_objects: int
    gc_collections: Dict[int, int] = field(default_factory=dict)


@dataclass
class ObjectTracker:
    """Tracks object creation and destruction."""

    object_type: str
    creation_time: datetime
    creation_traceback: str
    size_bytes: int = 0
    still_alive: bool = True


class MemoryAwareCache:
    """LRU cache with memory pressure awareness."""

    def __init__(self, max_memory_mb: int = 500, max_items: int = 1000):
        """Initialize the instance."""
        self.max_memory = max_memory_mb * 1024 * 1024
        self.max_items = max_items
        self.cache = OrderedDict()
        self.memory_usage = 0
        self.lock = threading.RLock()

        logger.info(
            f"Memory-aware cache initialized: {max_memory_mb}MB, {max_items} items"
        )

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        with self.lock:
            if key in self.cache:
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None

    def put(self, key: str, value: Any) -> bool:
        """Put item in cache with memory check."""
        with self.lock:
            # Calculate size
            item_size = sys.getsizeof(value)

            # Remove existing item if present
            if key in self.cache:
                old_value = self.cache.pop(key)
                self.memory_usage -= sys.getsizeof(old_value)

            # Check if we need to evict items
            self._evict_if_needed(item_size)

            # Add new item
            self.cache[key] = value
            self.memory_usage += item_size

            return True

    def _evict_if_needed(self, new_item_size: int):
        """Evict items if memory limit would be exceeded."""
        # Check memory limit
        while (
            self.memory_usage + new_item_size > self.max_memory
            or len(self.cache) >= self.max_items
        ) and self.cache:

            # Remove least recently used item
            key, value = self.cache.popitem(last=False)
            self.memory_usage -= sys.getsizeof(value)
            logger.debug(f"Evicted cache item: {key}")

    def clear(self):
        """Clear all cache items."""
        with self.lock:
            self.cache.clear()
            self.memory_usage = 0

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            return {
                "items": len(self.cache),
                "memory_usage_mb": self.memory_usage / (1024 * 1024),
                "memory_limit_mb": self.max_memory / (1024 * 1024),
                "memory_utilization": (self.memory_usage / self.max_memory) * 100,
            }


class MemoryLeakDetector:
    """Detects potential memory leaks."""

    def __init__(self):
        """Initialize the instance."""
        self.object_trackers: Dict[int, ObjectTracker] = {}
        self.type_counts: Dict[str, int] = defaultdict(int)
        self.snapshots: List[MemorySnapshot] = []
        self.monitoring_active = False
        self.monitor_thread = None

        # Enable tracemalloc for detailed tracking
        if not tracemalloc.is_tracing():
            tracemalloc.start()

    def start_monitoring(self, interval_seconds: int = 60):
        """Start memory leak monitoring."""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, args=(interval_seconds,), daemon=True
        )
        self.monitor_thread.start()

        logger.info("Memory leak detection started")

    def stop_monitoring(self):
        """Stop memory leak monitoring."""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)

    def _monitoring_loop(self, interval_seconds: int):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                self._take_snapshot()
                self._analyze_trends()
                time.sleep(interval_seconds)
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                time.sleep(interval_seconds)

    def _take_snapshot(self):
        """Take a memory usage snapshot."""
        try:
            # System memory info
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()

            # Garbage collection info
            gc_stats = {}
            for i in range(3):
                gc_stats[i] = gc.get_count()[i]

            snapshot = MemorySnapshot(
                timestamp=datetime.now(),
                total_memory_mb=memory.total / (1024 * 1024),
                available_memory_mb=memory.available / (1024 * 1024),
                process_memory_mb=process_memory.rss / (1024 * 1024),
                memory_percent=memory.percent,
                gc_objects=len(gc.get_objects()),
                gc_collections=gc_stats,
            )

            self.snapshots.append(snapshot)

            # Keep only last 100 snapshots
            if len(self.snapshots) > 100:
                self.snapshots = self.snapshots[-100:]

        except Exception as e:
            logger.error(f"Error taking memory snapshot: {e}")

    def _analyze_trends(self):
        """Analyze memory usage trends for leaks."""
        if len(self.snapshots) < 10:
            return

        recent_snapshots = self.snapshots[-10:]

        # Check for consistent memory growth
        memory_values = [s.process_memory_mb for s in recent_snapshots]
        if len(set(memory_values)) > 1:  # Memory is changing
            growth_rate = (memory_values[-1] - memory_values[0]) / len(memory_values)

            if growth_rate > 10:  # More than 10MB per snapshot
                logger.warning(
                    f"Potential memory leak detected: {
                        growth_rate:.2f}MB growth per snapshot"
                )
                self._trigger_detailed_analysis()

    def _trigger_detailed_analysis(self):
        """Trigger detailed memory analysis."""
        try:
            # Get current tracemalloc snapshot
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics("lineno")

            logger.warning("Top 10 memory allocations:")
            for index, stat in enumerate(top_stats[:10], 1):
                logger.warning(f"{index}. {stat}")

            # Force garbage collection
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")

        except Exception as e:
            logger.error(f"Error in detailed memory analysis: {e}")

    def get_leak_report(self) -> Dict[str, Any]:
        """Get memory leak analysis report."""
        if not self.snapshots:
            return {"error": "No snapshots available"}

        latest = self.snapshots[-1]

        # Calculate trends
        if len(self.snapshots) >= 2:
            first = self.snapshots[0]
            memory_growth = latest.process_memory_mb - first.process_memory_mb
            time_span = (
                latest.timestamp - first.timestamp
            ).total_seconds() / 3600  # hours
            growth_rate = memory_growth / time_span if time_span > 0 else 0
        else:
            memory_growth = 0
            growth_rate = 0

        return {
            "current_memory_mb": latest.process_memory_mb,
            "memory_growth_mb": memory_growth,
            "growth_rate_mb_per_hour": growth_rate,
            "gc_objects": latest.gc_objects,
            "snapshots_count": len(self.snapshots),
            "potential_leak": growth_rate > 50,  # More than 50MB/hour
        }


class MemoryManager:
    """Comprehensive memory management system."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize memory manager."""
        self.config = config or {
            "memory_warning_threshold": 80,  # Percentage
            "memory_critical_threshold": 95,  # Percentage
            "auto_cleanup_enabled": True,
            "cache_max_memory_mb": 500,
            "monitoring_interval": 60,  # seconds
        }

        # Components
        self.leak_detector = MemoryLeakDetector()
        self.cache = MemoryAwareCache(max_memory_mb=self.config["cache_max_memory_mb"])

        # Monitoring
        self.monitoring_active = False
        self.monitor_thread = None
        self.cleanup_callbacks: List[Callable] = []

        # Weak references to track objects
        self.tracked_objects: Set[weakref.ref] = set()

        logger.info("Memory Manager initialized")

    def start_monitoring(self):
        """Start memory monitoring."""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitor_thread.start()

        # Start leak detection
        self.leak_detector.start_monitoring(self.config["monitoring_interval"])

        logger.info("Memory monitoring started")

    def stop_monitoring(self):
        """Stop memory monitoring."""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)

        self.leak_detector.stop_monitoring()
        logger.info("Memory monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                self._check_memory_pressure()
                self._cleanup_dead_references()
                time.sleep(self.config["monitoring_interval"])
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                time.sleep(self.config["monitoring_interval"])

    def _check_memory_pressure(self):
        """Check for memory pressure and trigger cleanup if needed."""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            if memory_percent >= self.config["memory_critical_threshold"]:
                logger.critical(f"Critical memory usage: {memory_percent}%")
                self._trigger_emergency_cleanup()
            elif memory_percent >= self.config["memory_warning_threshold"]:
                logger.warning(f"High memory usage: {memory_percent}%")
                if self.config["auto_cleanup_enabled"]:
                    self._trigger_cleanup()

        except Exception as e:
            logger.error(f"Error checking memory pressure: {e}")

    def _trigger_cleanup(self):
        """Trigger memory cleanup."""
        logger.info("Triggering memory cleanup")

        # Clear cache
        self.cache.clear()

        # Force garbage collection
        collected = gc.collect()
        logger.info(f"Garbage collection freed {collected} objects")

        # Call registered cleanup callbacks
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"Error in cleanup callback: {e}")

    def _trigger_emergency_cleanup(self):
        """Trigger emergency memory cleanup."""
        logger.critical("Triggering emergency memory cleanup")

        # More aggressive cleanup
        self._trigger_cleanup()

        # Force multiple GC cycles
        for _ in range(3):
            gc.collect()

        # Clear all caches
        self.cache.clear()

    def _cleanup_dead_references(self):
        """Clean up dead weak references."""
        dead_refs = [ref for ref in self.tracked_objects if ref() is None]
        for ref in dead_refs:
            self.tracked_objects.remove(ref)

    def track_object(self, obj: Any, cleanup_callback: Callable = None):
        """Track an object for memory management."""

        def cleanup_wrapper(ref):
            """Execute cleanup_wrapper operation."""
            if cleanup_callback:
                try:
                    cleanup_callback()
                except Exception as e:
                    logger.error(f"Error in object cleanup callback: {e}")
            self.tracked_objects.discard(ref)

        weak_ref = weakref.ref(obj, cleanup_wrapper)
        self.tracked_objects.add(weak_ref)

    def register_cleanup_callback(self, callback: Callable):
        """Register a cleanup callback."""
        self.cleanup_callbacks.append(callback)

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics."""
        try:
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()

            stats = {
                "system_memory": {
                    "total_mb": memory.total / (1024 * 1024),
                    "available_mb": memory.available / (1024 * 1024),
                    "used_percent": memory.percent,
                },
                "process_memory": {
                    "rss_mb": process_memory.rss / (1024 * 1024),
                    "vms_mb": process_memory.vms / (1024 * 1024),
                },
                "cache_stats": self.cache.get_stats(),
                "gc_stats": {
                    "objects": len(gc.get_objects()),
                    "collections": gc.get_stats(),
                },
                "tracked_objects": len(self.tracked_objects),
                "leak_report": self.leak_detector.get_leak_report(),
            }

            return stats

        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {"error": str(e)}

    def force_cleanup(self):
        """Force immediate memory cleanup."""
        self._trigger_cleanup()

    def get_cache(self) -> MemoryAwareCache:
        """Get the memory-aware cache instance."""
        return self.cache


# Global memory manager instance
memory_manager = MemoryManager()


def get_memory_manager() -> MemoryManager:
    """Get the global memory manager instance."""
    return memory_manager


def track_object(obj: Any, cleanup_callback: Callable = None):
    """Convenience function to track an object."""
    memory_manager.track_object(obj, cleanup_callback)


def register_cleanup_callback(callback: Callable):
    """Convenience function to register cleanup callback."""
    memory_manager.register_cleanup_callback(callback)
