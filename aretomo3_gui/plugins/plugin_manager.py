#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Plugin System
Extensible plugin architecture for third-party integrations and custom tools.
"""

import importlib
import inspect
import json
import logging
import sys
import traceback
from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Type

logger = logging.getLogger(__name__)


@dataclass
class PluginMetadata:
    """Plugin metadata information."""

    name: str
    version: str
    author: str
    description: str
    category: str
    dependencies: List[str]
    entry_point: str
    config_schema: Dict[str, Any]
    enabled: bool = True
    installed_at: Optional[datetime] = None


class PluginInterface(ABC):
    """Base interface for all plugins."""

    @abstractmethod
    def get_metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        pass

    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin with configuration."""
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """Clean up plugin resources."""
        pass

    def get_menu_items(self) -> List[Dict[str, Any]]:
        """Get menu items to add to GUI."""
        return []

    def get_toolbar_items(self) -> List[Dict[str, Any]]:
        """Get toolbar items to add to GUI."""
        return []

    def get_tab_widgets(self) -> List[Dict[str, Any]]:
        """Get tab widgets to add to GUI."""
        return []


class ProcessingPlugin(PluginInterface):
    """Base class for processing plugins."""

    @abstractmethod
    def process_data(self, input_data: Any, parameters: Dict[str, Any]) -> Any:
        """Process data with given parameters."""
        pass

    def get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for the processing function."""
        return {}


class VisualizationPlugin(PluginInterface):
    """Base class for visualization plugins."""

    @abstractmethod
    def create_visualization(self, data: Any, config: Dict[str, Any]) -> Any:
        """Create visualization from data."""
        pass

    def get_supported_data_types(self) -> List[str]:
        """Get list of supported data types."""
        return []


class AnalysisPlugin(PluginInterface):
    """Base class for analysis plugins."""

    @abstractmethod
    def analyze_data(self, data: Any, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze data and return results."""
        pass

    def get_analysis_types(self) -> List[str]:
        """Get list of analysis types provided."""
        return []


class PluginManager:
    """
    Advanced plugin manager for AreTomo3 GUI.
    Handles plugin discovery, loading, configuration, and lifecycle management.
    """

    def __init__(self, plugin_dirs: List[Path] = None):
        """Initialize the plugin manager."""
        self.plugin_dirs = plugin_dirs or [
            Path.cwd() / "plugins",
            Path.home() / ".aretomo3_gui" / "plugins",
            Path(__file__).parent / "builtin",
        ]

        # Create plugin directories
        for plugin_dir in self.plugin_dirs:
            plugin_dir.mkdir(parents=True, exist_ok=True)

        # Plugin registry
        self.plugins: Dict[str, PluginInterface] = {}
        self.plugin_metadata: Dict[str, PluginMetadata] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}

        # Plugin categories
        self.categories = {
            "processing": [],
            "visualization": [],
            "analysis": [],
            "utility": [],
            "integration": [],
        }

        # Event hooks
        self.event_hooks: Dict[str, List[Callable]] = {}

        # Configuration file
        self.config_file = Path.home() / ".aretomo3_gui" / "plugin_config.json"
        self.config_file.parent.mkdir(parents=True, exist_ok=True)

        # Load configuration
        self._load_config()

        logger.info(
            f"Plugin Manager initialized - Plugin dirs: {len(self.plugin_dirs)}"
        )

    def discover_plugins(self) -> List[PluginMetadata]:
        """Discover available plugins in plugin directories."""
        discovered = []

        for plugin_dir in self.plugin_dirs:
            if not plugin_dir.exists():
                continue

            # Look for plugin.json files
            for plugin_file in plugin_dir.rglob("plugin.json"):
                try:
                    metadata = self._load_plugin_metadata(plugin_file)
                    if metadata:
                        discovered.append(metadata)
                        logger.info(f"Discovered plugin: {metadata.name}")
                except Exception as e:
                    logger.error(f"Error discovering plugin {plugin_file}: {e}")

        return discovered

    def _load_plugin_metadata(self, plugin_file: Path) -> Optional[PluginMetadata]:
        """Load plugin metadata from plugin.json file."""
        try:
            with open(plugin_file, "r") as f:
                data = json.load(f)

            # Validate required fields
            required_fields = [
                "name",
                "version",
                "author",
                "description",
                "entry_point",
            ]
            for field in required_fields:
                if field not in data:
                    logger.error(f"Missing required field '{field}' in {plugin_file}")
                    return None

            metadata = PluginMetadata(
                name=data["name"],
                version=data["version"],
                author=data["author"],
                description=data["description"],
                category=data.get("category", "utility"),
                dependencies=data.get("dependencies", []),
                entry_point=data["entry_point"],
                config_schema=data.get("config_schema", {}),
                enabled=data.get("enabled", True),
            )

            return metadata

        except Exception as e:
            logger.error(f"Error loading plugin metadata from {plugin_file}: {e}")
            return None

    # TODO: Refactor function - Function 'load_plugin' too long (52 lines)
    def load_plugin(self, metadata: PluginMetadata, plugin_dir: Path) -> bool:
        """Load a plugin from its metadata."""
        try:
            # Check dependencies
            if not self._check_dependencies(metadata.dependencies):
                logger.error(
                    f"Dependencies not met for plugin: {
                        metadata.name}"
                )
                return False

            # Add plugin directory to Python path
            plugin_path = plugin_dir / metadata.name
            if str(plugin_path) not in sys.path:
                sys.path.insert(0, str(plugin_path))

            # Import plugin module
            module_name = metadata.entry_point.split(".")[0]
            class_name = metadata.entry_point.split(".")[1]

            module = importlib.import_module(module_name)
            plugin_class = getattr(module, class_name)

            # Validate plugin class
            if not issubclass(plugin_class, PluginInterface):
                logger.error(
                    f"Plugin class must inherit from PluginInterface: {
                        metadata.name}"
                )
                return False

            # Create plugin instance
            plugin_instance = plugin_class()

            # Initialize plugin
            plugin_config = self.plugin_configs.get(metadata.name, {})
            if plugin_instance.initialize(plugin_config):
                # Register plugin
                self.plugins[metadata.name] = plugin_instance
                self.plugin_metadata[metadata.name] = metadata

                # Add to category
                category = metadata.category
                if category in self.categories:
                    self.categories[category].append(metadata.name)

                logger.info(f"Plugin loaded successfully: {metadata.name}")
                return True
            else:
                logger.error(f"Plugin initialization failed: {metadata.name}")
                return False

        except Exception as e:
            logger.error(f"Error loading plugin {metadata.name}: {e}")
            logger.error(traceback.format_exc())
            return False

    def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a plugin."""
        try:
            if plugin_name not in self.plugins:
                logger.warning(f"Plugin not loaded: {plugin_name}")
                return False

            plugin = self.plugins[plugin_name]

            # Cleanup plugin
            if plugin.cleanup():
                # Remove from registry
                del self.plugins[plugin_name]

                # Remove from category
                metadata = self.plugin_metadata.get(plugin_name)
                if metadata and metadata.category in self.categories:
                    if plugin_name in self.categories[metadata.category]:
                        self.categories[metadata.category].remove(plugin_name)

                logger.info(f"Plugin unloaded: {plugin_name}")
                return True
            else:
                logger.error(f"Plugin cleanup failed: {plugin_name}")
                return False

        except Exception as e:
            logger.error(f"Error unloading plugin {plugin_name}: {e}")
            return False

    def reload_plugin(self, plugin_name: str) -> bool:
        """Reload a plugin."""
        try:
            if plugin_name in self.plugins:
                metadata = self.plugin_metadata[plugin_name]

                # Unload first
                if not self.unload_plugin(plugin_name):
                    return False

                # Find plugin directory
                plugin_dir = None
                for pdir in self.plugin_dirs:
                    if (pdir / plugin_name).exists():
                        plugin_dir = pdir
                        break

                if plugin_dir:
                    return self.load_plugin(metadata, plugin_dir)

            return False

        except Exception as e:
            logger.error(f"Error reloading plugin {plugin_name}: {e}")
            return False

    def _check_dependencies(self, dependencies: List[str]) -> bool:
        """Check if plugin dependencies are satisfied."""
        for dep in dependencies:
            try:
                importlib.import_module(dep)
            except ImportError:
                logger.error(f"Missing dependency: {dep}")
                return False
        return True

    def get_plugins_by_category(self, category: str) -> List[str]:
        """Get list of plugins in a category."""
        return self.categories.get(category, [])

    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """Get plugin instance by name."""
        return self.plugins.get(plugin_name)

    def call_plugin_method(
        self, plugin_name: str, method_name: str, *args, **kwargs
    ) -> Any:
        """Call a method on a plugin."""
        try:
            plugin = self.get_plugin(plugin_name)
            if plugin and hasattr(plugin, method_name):
                method = getattr(plugin, method_name)
                return method(*args, **kwargs)
            else:
                logger.error(f"Plugin or method not found: {plugin_name}.{method_name}")
                return None

        except Exception as e:
            logger.error(
                f"Error calling plugin method {plugin_name}.{method_name}: {e}"
            )
            return None

    def register_event_hook(self, event_name: str, callback: Callable):
        """Register an event hook."""
        if event_name not in self.event_hooks:
            self.event_hooks[event_name] = []

        self.event_hooks[event_name].append(callback)
        logger.info(f"Event hook registered: {event_name}")

    def trigger_event(self, event_name: str, *args, **kwargs):
        """Trigger an event and call all registered hooks."""
        if event_name in self.event_hooks:
            for callback in self.event_hooks[event_name]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Error in event hook {event_name}: {e}")

    def configure_plugin(self, plugin_name: str, config: Dict[str, Any]) -> bool:
        """Configure a plugin."""
        try:
            if plugin_name in self.plugins:
                # Validate configuration against schema
                metadata = self.plugin_metadata[plugin_name]
                if metadata.config_schema:
                    # Simple validation - could be enhanced with jsonschema
                    for key, value in config.items():
                        if key not in metadata.config_schema:
                            logger.warning(
                                f"Unknown config key for {plugin_name}: {key}"
                            )

                # Store configuration
                self.plugin_configs[plugin_name] = config
                self._save_config()

                # Reinitialize plugin with new config
                plugin = self.plugins[plugin_name]
                return plugin.initialize(config)

            return False

        except Exception as e:
            logger.error(f"Error configuring plugin {plugin_name}: {e}")
            return False

    def get_plugin_info(self) -> Dict[str, Any]:
        """Get information about loaded plugins."""
        return {
            "loaded_plugins": len(self.plugins),
            "categories": {
                cat: len(plugins) for cat, plugins in self.categories.items()
            },
            "plugin_list": [
                {
                    "name": name,
                    "version": self.plugin_metadata[name].version,
                    "category": self.plugin_metadata[name].category,
                    "enabled": self.plugin_metadata[name].enabled,
                }
                for name in self.plugins.keys()
            ],
        }

    def _load_config(self):
        """Load plugin configuration."""
        try:
            if self.config_file.exists():
                with open(self.config_file, "r") as f:
                    data = json.load(f)

                self.plugin_configs = data.get("plugin_configs", {})

        except Exception as e:
            logger.error(f"Error loading plugin config: {e}")

    def _save_config(self):
        """Save plugin configuration."""
        try:
            config_data = {"plugin_configs": self.plugin_configs}

            with open(self.config_file, "w") as f:
                json.dump(config_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving plugin config: {e}")

    def load_all_plugins(self):
        """Discover and load all available plugins."""
        try:
            discovered_plugins = self.discover_plugins()

            for metadata in discovered_plugins:
                if metadata.enabled:
                    # Find plugin directory
                    plugin_dir = None
                    for pdir in self.plugin_dirs:
                        plugin_path = pdir / metadata.name
                        if plugin_path.exists():
                            plugin_dir = pdir
                            break

                    if plugin_dir:
                        self.load_plugin(metadata, plugin_dir)
                    else:
                        logger.error(
                            f"Plugin directory not found: {
                                metadata.name}"
                        )

            logger.info(f"Loaded {len(self.plugins)} plugins")

        except Exception as e:
            logger.error(f"Error loading all plugins: {e}")

    # TODO: Refactor function - Function 'create_plugin_template' too long (87
    # lines)
    def create_plugin_template(
        self, plugin_name: str, category: str = "utility"
    ) -> Path:
        """Create a plugin template for development."""
        try:
            plugin_dir = self.plugin_dirs[0] / plugin_name
            plugin_dir.mkdir(parents=True, exist_ok=True)

            # Create plugin.json
            metadata = {
                "name": plugin_name,
                "version": "1.0.0",
                "author": "Plugin Developer",
                "description": f"Description for {plugin_name}",
                "category": category,
                "dependencies": [],
                "entry_point": f"{plugin_name}.{plugin_name.title()}Plugin",
                "config_schema": {},
                "enabled": True,
            }

            with open(plugin_dir / "plugin.json", "w") as f:
                json.dump(metadata, f, indent=2)

            # Create plugin Python file
            plugin_code = f'''#!/usr/bin/env python3
"""
{plugin_name.title()} Plugin for AreTomo3 GUI
"""

from aretomo3_gui.plugins.plugin_manager import PluginInterface, PluginMetadata
from typing import Dict, Any, List
from datetime import datetime

class {plugin_name.title()}Plugin(PluginInterface):
    """Example plugin implementation."""

    def get_metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        return PluginMetadata(
            name="{plugin_name}",
            version="1.0.0",
            author="Plugin Developer",
            description="Description for {plugin_name}",
            category="{category}",
            dependencies=[],
            entry_point="{plugin_name}.{plugin_name.title()}Plugin",
            config_schema={{}},
            enabled=True,
            installed_at=datetime.now()
        )

    def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin."""
        logger.info(f"Initializing {{self.__class__.__name__}} with config: {{config}}")
        return True

    def cleanup(self) -> bool:
        """Clean up plugin resources."""
        logger.info(f"Cleaning up {{self.__class__.__name__}}")
        return True

    def get_menu_items(self) -> List[Dict[str, Any]]:
        """Get menu items to add to GUI."""
        return [
            {{
                "text": "{plugin_name.title()} Action",
                "callback": self.plugin_action,
                "tooltip": "Execute {plugin_name} action"
            }}
        ]

    def plugin_action(self):
        """Example plugin action."""
        logger.info(f"{{self.__class__.__name__}} action executed!")
'''

            with open(plugin_dir / f"{plugin_name}.py", "w") as f:
                f.write(plugin_code)

            logger.info(f"Plugin template created: {plugin_dir}")
            return plugin_dir

        except Exception as e:
            logger.error(f"Error creating plugin template: {e}")
            return None


# Global plugin manager instance
plugin_manager = PluginManager()


def load_plugin(plugin_name: str) -> bool:
    """Convenience function to load a plugin."""
    discovered = plugin_manager.discover_plugins()
    for metadata in discovered:
        if metadata.name == plugin_name:
            for plugin_dir in plugin_manager.plugin_dirs:
                if (plugin_dir / plugin_name).exists():
                    return plugin_manager.load_plugin(metadata, plugin_dir)
    return False


def get_plugin(plugin_name: str) -> Optional[PluginInterface]:
    """Convenience function to get a plugin."""
    return plugin_manager.get_plugin(plugin_name)
