#!/usr/bin/env python3
"""
CTF Quality Assessment for AreTomo3

This module provides automated quality assessment for CTF estimation results:
- Quality scoring algorithms
- Outlier detection
- Resolution analysis
- Overall series assessment
"""

import logging
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)


class CTFQualityAssessment:
    """
    Automated CTF quality assessment and scoring.

    Provides:
    - Individual tilt quality scores
    - Overall series quality assessment
    - Outlier detection
    - Resolution analysis
    """

    def __init__(self, ctf_data: Dict):
        """
        Initialize quality assessment.

        Args:
            ctf_data: Dictionary containing parsed CTF data
        """
        self.ctf_data = ctf_data
        self.parameters = ctf_data.get("parameters")
        self.tilt_angles = ctf_data.get("tilt_angles", [])
        self.series_name = ctf_data.get("series_name", "Unknown")

        if self.parameters is None or self.parameters.empty:
            raise ValueError("No CTF parameters available for quality assessment")

        self.quality_scores = {}
        self.outliers = []
        self.overall_quality = None

        logger.info(
            f"Initialized CTF quality assessment for {
                self.series_name}"
        )

    def assess_all_quality(self) -> Dict:
        """
        Perform comprehensive quality assessment.

        Returns:
            Dictionary with all quality metrics
        """
        logger.info("Performing comprehensive CTF quality assessment")

        # Calculate individual quality scores
        self.calculate_individual_scores()

        # Detect outliers
        self.detect_outliers()

        # Assess overall quality
        self.assess_overall_quality()

        # Analyze resolution trends
        self.analyze_resolution_trends()

        return self.get_quality_summary()

    def calculate_individual_scores(self):
        """Calculate quality scores for individual tilts."""
        df = self.parameters.copy()

        # Initialize scores
        scores = {
            "cross_correlation_score": [],
            "resolution_score": [],
            "defocus_consistency_score": [],
            "astigmatism_score": [],
            "overall_score": [],
        }

        # Cross-correlation score (0-100)
        cc_scores = np.clip(df["cross_correlation"] * 100, 0, 100)
        scores["cross_correlation_score"] = cc_scores.tolist()

        # Resolution score (better resolution = higher score)
        # Typical good resolution: 3-8 Å, excellent: <5 Å
        resolution_scores = np.clip(100 - (df["resolution_limit_A"] - 3) * 10, 0, 100)
        scores["resolution_score"] = resolution_scores.tolist()

        # Defocus consistency score
        defocus_consistency = self._calculate_defocus_consistency()
        scores["defocus_consistency_score"] = defocus_consistency

        # Astigmatism score (lower astigmatism = higher score)
        astigmatism = np.abs(df["defocus1_A"] - df["defocus2_A"])
        astigmatism_scores = np.clip(
            100 - astigmatism / 100, 0, 100
        )  # Penalize >1000Å astigmatism
        scores["astigmatism_score"] = astigmatism_scores.tolist()

        # Overall score (weighted average)
        overall_scores = (
            0.3 * cc_scores
            + 0.3 * resolution_scores
            + 0.2 * defocus_consistency
            + 0.2 * astigmatism_scores
        )
        scores["overall_score"] = overall_scores.tolist()

        self.quality_scores = scores

        # Add scores to parameters dataframe
        for score_type, score_values in scores.items():
            self.parameters[score_type] = score_values

        logger.info(f"Calculated quality scores for {len(df)} tilts")

    def _calculate_defocus_consistency(self) -> List[float]:
        """Calculate defocus consistency scores based on expected defocus progression."""
        df = self.parameters

        if not self.tilt_angles:
            # No tilt angles available - use simple consistency
            return [50.0] * len(df)  # Neutral score

        # Expected defocus should follow a smooth progression with tilt angle
        # due to sample geometry changes
        defocus_values = df["defocus1_A"].values
        tilt_angles_rad = np.array(self.tilt_angles) * np.pi / 180

        # Fit polynomial to defocus vs tilt angle
        try:
            # Fit 2nd order polynomial
            coeffs = np.polyfit(tilt_angles_rad, defocus_values, 2)
            expected_defocus = np.polyval(coeffs, tilt_angles_rad)

            # Calculate residuals
            residuals = np.abs(defocus_values - expected_defocus)

            # Convert to scores (lower residual = higher score)
            max_residual = np.percentile(residuals, 95)  # Use 95th percentile as max
            consistency_scores = np.clip(100 - (residuals / max_residual) * 100, 0, 100)

            return consistency_scores.tolist()

        except Exception as e:
            logger.warning(f"Could not calculate defocus consistency: {e}")
            return [50.0] * len(df)  # Neutral score

    # TODO: Refactor function - Function 'detect_outliers' too long (57 lines)
    def detect_outliers(self):
        """Detect outlier tilts based on CTF parameters."""
        df = self.parameters

        # Features for outlier detection
        features = [
            "defocus1_A",
            "defocus2_A",
            "cross_correlation",
            "resolution_limit_A",
        ]

        # Prepare data
        X = df[features].values

        # Handle missing values
        X = np.nan_to_num(X)

        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # Use DBSCAN for outlier detection
        try:
            dbscan = DBSCAN(eps=1.5, min_samples=3)
            labels = dbscan.fit_predict(X_scaled)

            # Points labeled as -1 are outliers
            outlier_indices = np.where(labels == -1)[0]
            self.outliers = outlier_indices.tolist()

            logger.info(f"Detected {len(self.outliers)} outlier tilts")

        except Exception as e:
            logger.warning(f"Could not perform outlier detection: {e}")
            self.outliers = []

        # Also detect outliers based on quality scores
        if "overall_score" in self.quality_scores:
            scores = np.array(self.quality_scores["overall_score"])

            # Use IQR method for additional outlier detection
            Q1 = np.percentile(scores, 25)
            Q3 = np.percentile(scores, 75)
            IQR = Q3 - Q1

            lower_bound = Q1 - 1.5 * IQR
            quality_outliers = np.where(scores < lower_bound)[0]

            # Combine outliers
            all_outliers = set(self.outliers) | set(quality_outliers.tolist())
            self.outliers = sorted(list(all_outliers))

        # Mark outliers in parameters
        self.parameters["is_outlier"] = False
        if self.outliers:
            self.parameters.loc[self.outliers, "is_outlier"] = True

    def assess_overall_quality(self):
        """Assess overall series quality."""
        if "overall_score" not in self.quality_scores:
            self.overall_quality = {"score": 0, "grade": "Unknown"}
            return

        scores = np.array(self.quality_scores["overall_score"])

        # Calculate statistics
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        min_score = np.min(scores)

        # Penalize for outliers
        outlier_penalty = len(self.outliers) * 5  # 5 points per outlier

        # Penalize for high variability
        variability_penalty = min(std_score, 20)  # Max 20 points penalty

        # Final score
        final_score = max(0, mean_score - outlier_penalty - variability_penalty)

        # Assign grade
        if final_score >= 80:
            grade = "Excellent"
        elif final_score >= 65:
            grade = "Good"
        elif final_score >= 50:
            grade = "Fair"
        elif final_score >= 30:
            grade = "Poor"
        else:
            grade = "Very Poor"

        self.overall_quality = {
            "score": final_score,
            "grade": grade,
            "mean_individual_score": mean_score,
            "score_std": std_score,
            "min_score": min_score,
            "outlier_count": len(self.outliers),
            "outlier_penalty": outlier_penalty,
            "variability_penalty": variability_penalty,
        }

        logger.info(f"Overall quality: {grade} ({final_score:.1f}/100)")

    def analyze_resolution_trends(self):
        """Analyze resolution trends across the tilt series."""
        df = self.parameters

        if not self.tilt_angles:
            return

        # Resolution vs tilt angle analysis
        resolution = df["resolution_limit_A"].values
        tilt_angles = np.array(self.tilt_angles)

        # Find resolution trends
        # Typically, resolution degrades at higher tilt angles
        abs_tilt_angles = np.abs(tilt_angles)

        # Calculate correlation
        correlation, p_value = stats.pearsonr(abs_tilt_angles, resolution)

        # Expected: positive correlation (higher tilt = worse resolution)
        trend_quality = (
            "Good" if correlation > 0.3 else "Poor" if correlation < -0.1 else "Fair"
        )

        self.resolution_analysis = {
            "correlation_with_tilt": correlation,
            "correlation_p_value": p_value,
            "trend_quality": trend_quality,
            "mean_resolution": np.mean(resolution),
            "best_resolution": np.min(resolution),
            "worst_resolution": np.max(resolution),
            "resolution_range": np.max(resolution) - np.min(resolution),
        }

    def get_quality_summary(self) -> Dict:
        """Get comprehensive quality summary."""
        summary = {
            "series_name": self.series_name,
            "n_tilts": len(self.parameters),
            "overall_quality": self.overall_quality,
            "outliers": self.outliers,
            "quality_scores": self.quality_scores,
            "resolution_analysis": getattr(self, "resolution_analysis", {}),
            "recommendations": self._generate_recommendations(),
        }

        return summary

    # TODO: Refactor _generate_recommendations - complexity: 11 (target: <10)
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on quality assessment."""
        recommendations = []

        if not self.overall_quality:
            return recommendations

        # Overall quality recommendations
        if self.overall_quality["score"] < 50:
            recommendations.append(
                "Consider re-processing with different CTF parameters"
            )

        # Outlier recommendations
        if len(self.outliers) > len(self.parameters) * 0.1:  # >10% outliers
            recommendations.append(
                f"High number of outliers ({len(self.outliers)}) - check data quality"
            )

        # Resolution recommendations
        if hasattr(self, "resolution_analysis"):
            if self.resolution_analysis["mean_resolution"] > 10:
                recommendations.append(
                    "Poor average resolution - check focus and alignment"
                )

            if self.resolution_analysis["trend_quality"] == "Poor":
                recommendations.append(
                    "Unexpected resolution trend - check tilt series geometry"
                )

        # Cross-correlation recommendations
        if "cross_correlation_score" in self.quality_scores:
            mean_cc = np.mean(self.quality_scores["cross_correlation_score"])
            if mean_cc < 30:
                recommendations.append(
                    "Low CTF cross-correlation - check contrast and defocus range"
                )

        # Astigmatism recommendations
        if "astigmatism_score" in self.quality_scores:
            mean_astig = np.mean(self.quality_scores["astigmatism_score"])
            if mean_astig < 50:
                recommendations.append(
                    "High astigmatism detected - check objective lens stigmation"
                )

        return recommendations

    def get_tilt_quality_info(self, tilt_idx: int) -> Dict:
        """Get quality information for a specific tilt."""
        if tilt_idx >= len(self.parameters):
            return {}

        tilt_info = {
            "tilt_index": tilt_idx,
            "is_outlier": self.parameters.iloc[tilt_idx]["is_outlier"],
            "scores": {},
        }

        # Add all quality scores
        for score_type in self.quality_scores:
            tilt_info["scores"][score_type] = self.quality_scores[score_type][tilt_idx]

        # Add tilt angle if available
        if self.tilt_angles:
            tilt_info["tilt_angle"] = self.tilt_angles[tilt_idx]

        return tilt_info

    # TODO: Refactor function - Function 'test_ctf_quality' too long (52 lines)


def test_ctf_quality():
    """Test function for CTF quality assessment."""
    import sys

    from .ctf_parser import CTFDataParser

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        # Parse CTF data
        parser = CTFDataParser(test_path)
        ctf_data = parser.parse_all()

        # Assess quality
        quality_assessor = CTFQualityAssessment(ctf_data)
        quality_summary = quality_assessor.assess_all_quality()

        logger.info(
            f"CTF Quality Assessment for {
                quality_summary['series_name']}"
        )
        logger.info("=" * 50)

        # Overall quality
        overall = quality_summary["overall_quality"]
        logger.info(
            f"Overall Quality: {
                overall['grade']} ({
                overall['score']:.1f}/100)"
        )
        logger.info(
            f"Mean Individual Score: {
                overall['mean_individual_score']:.1f}"
        )
        logger.info(f"Score Std Dev: {overall['score_std']:.1f}")
        logger.info(f"Outliers: {overall['outlier_count']}")

        # Resolution analysis
        if quality_summary["resolution_analysis"]:
            res_analysis = quality_summary["resolution_analysis"]
            logger.info(f"\nResolution Analysis:")
            logger.info(
                f"Mean Resolution: {
                    res_analysis['mean_resolution']:.1f} Å"
            )
            logger.info(
                f"Best Resolution: {
                    res_analysis['best_resolution']:.1f} Å"
            )
            logger.info(f"Trend Quality: {res_analysis['trend_quality']}")

        # Recommendations
        if quality_summary["recommendations"]:
            logger.info(f"\nRecommendations:")
            for i, rec in enumerate(quality_summary["recommendations"], 1):
                logger.info(f"{i}. {rec}")

        return True

    except Exception as e:
        logger.info(f"Error testing CTF quality assessment: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_ctf_quality()
