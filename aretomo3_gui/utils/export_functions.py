import csv
import logging
import os
import traceback
from typing import Any, Dict, List, Union

import h5py
import mrcfile
import numpy as np
import tifffile
from PIL import Image

logger = logging.getLogger(__name__)


def export_to_mrc(self, input_path, output_path):
    """Export to IMOD MRC format"""
    try:
        self.export_status.setText("Exporting to MRC format...")

        # Get selected mode
        mode = 2  # 32-bit float by default
        if hasattr(self, "mrc_mode") and self.mrc_mode.currentIndex() == 0:
            mode = 1  # 16-bit integer

        # Use mrcfile to read input and write output
        with mrcfile.open(input_path, permissive=True) as mrc_in:
            # Create a new MRC file
            with mrcfile.new(output_path, overwrite=True) as mrc_out:
                # Copy data
                if mode == 1:
                    # Convert to 16-bit integer
                    data_min = mrc_in.data.min()
                    data_max = mrc_in.data.max()

                    # Normalize to use full int16 range
                    data = (
                        (mrc_in.data - data_min) / (data_max - data_min) * 65534 - 32767
                    ).astype(np.int16)
                    mrc_out.set_data(data)
                else:
                    # Copy as 32-bit float
                    mrc_out.set_data(mrc_in.data.astype(np.float32))

                # Copy header information
                if self.preserve_pixelsize.isChecked():
                    mrc_out.voxel_size = mrc_in.voxel_size

                # Update extended header if needed
                if self.include_tilt_angles.isChecked() and hasattr(
                    mrc_in, "extended_header"
                ):
                    mrc_out.set_extended_header(mrc_in.extended_header)

        self.export_status.setText(f"Exported to MRC: {output_path}")
        return True

    except Exception as e:
        self.log_message(f"Error exporting to MRC: {str(e)}")
        self.log_message(traceback.format_exc())
        return False

    # TODO: Refactor function - Function 'export_to_tiff' too long (92 lines)


def export_to_tiff(self, input_path, output_path):
    """Export to TIFF stack format"""
    try:
        self.export_status.setText("Exporting to TIFF stack...")

        # Get options
        depth_index = self.tif_depth.currentIndex()
        compression = self.tif_compression.currentText().lower()
        if compression == "none":
            compression = None

        # Open the MRC file
        # Try to import tifffile, but fall back to PIL if not available
        try:
            have_tifffile = True
        except ImportError:
            have_tifffile = False
            self.log_message("Warning: tifffile module not found, using PIL instead")

        with mrcfile.open(input_path, permissive=True) as mrc:
            # Get data and prepare for TIFF format
            data = mrc.data

            # Convert data based on selected bit depth
            if depth_index == 0:  # 8-bit
                data_min = data.min()
                data_max = data.max()
                data = ((data - data_min) / (data_max - data_min) * 255).astype(
                    np.uint8
                )
            elif depth_index == 1:  # 16-bit
                data_min = data.min()
                data_max = data.max()
                data = ((data - data_min) / (data_max - data_min) * 65535).astype(
                    np.uint16
                )
            else:  # 32-bit float
                data = data.astype(np.float32)

            # Save as TIFF stack
            if have_tifffile:
                # Use tifffile for better metadata support
                tifffile.imwrite(
                    output_path,
                    data,
                    compression=compression,
                    # Add metadata
                    metadata={
                        "Description": f"Exported from {os.path.basename(input_path)}",
                        "PixelSize": (
                            float(mrc.voxel_size.x)
                            if self.preserve_pixelsize.isChecked()
                            else 1.0
                        ),
                    },
                )
            else:
                # Use PIL as fallback
                if data.ndim == 3:
                    # For 3D data, save as a multi-page TIFF
                    img = Image.fromarray(data[0])
                    img.save(
                        output_path,
                        format="TIFF",
                        compression=compression if compression else "raw",
                    )

                    # Add subsequent slices
                    for i in range(1, data.shape[0]):
                        img = Image.fromarray(data[i])
                        img.save(
                            output_path,
                            format="TIFF",
                            compression=compression if compression else "raw",
                            append=True,
                        )
                else:
                    # For 2D data, save as a single page TIFF
                    img = Image.fromarray(data)
                    img.save(
                        output_path,
                        format="TIFF",
                        compression=compression if compression else "raw",
                    )

        self.export_status.setText(f"Exported to TIFF stack: {output_path}")
        return True

    except Exception as e:
        self.log_message(f"Error exporting to TIFF: {str(e)}")
        self.log_message(traceback.format_exc())
        return False

    # TODO: Refactor export_to_relion - complexity: 11 (target: <10)
    # TODO: Refactor function - Function 'export_to_relion' too long (63 lines)


def export_to_relion(self, input_path, output_path):
    """Export to Relion compatible format (MRC with STAR file)"""
    try:
        self.export_status.setText("Exporting to Relion format...")

        # First, copy the MRC file
        # Copy using mrcfile to ensure proper format
        with mrcfile.open(input_path, permissive=True) as mrc_in:
            with mrcfile.new(output_path, overwrite=True) as mrc_out:
                mrc_out.set_data(mrc_in.data)
                if self.preserve_pixelsize.isChecked():
                    mrc_out.voxel_size = mrc_in.voxel_size

        # Get pixel size and diameter
        pixel_size = 1.0
        with mrcfile.open(output_path, permissive=True) as mrc:
            if hasattr(mrc, "voxel_size") and mrc.voxel_size is not None:
                pixel_size = float(mrc.voxel_size.x)

        # Create STAR file if requested
        if self.relion_star.isChecked():
            star_path = os.path.splitext(output_path)[0] + ".star"
            with open(star_path, "w") as star_file:
                # Write STAR file header
                star_file.write("# Created by AreTomo3 GUI Export\n")
                star_file.write("data_\n\n")
                star_file.write("loop_\n")
                star_file.write("_rlnMicrographName #1\n")
                star_file.write("_rlnCoordinateX #2\n")
                star_file.write("_rlnCoordinateY #3\n")
                star_file.write("_rlnCoordinateZ #4\n")
                star_file.write("_rlnAngleRot #5\n")
                star_file.write("_rlnAngleTilt #6\n")
                star_file.write("_rlnAnglePsi #7\n")
                star_file.write("_rlnMagnification #8\n")
                star_file.write("_rlnDetectorPixelSize #9\n")
                star_file.write("_rlnCtfImage #10\n")
                star_file.write("_rlnImageName #11\n\n")

                # Example particle (middle of volume)
                with mrcfile.open(output_path, permissive=True) as mrc:
                    nx, ny, nz = mrc.data.shape
                    cx, cy, cz = nx // 2, ny // 2, nz // 2

                    star_file.write(
                        f"{os.path.basename(output_path)} {cx} {cy} {cz} 0.0 0.0 0.0 "
                    )
                    star_file.write(
                        f"10000.0 {pixel_size} {
                            os.path.basename(output_path)} {
                            os.path.basename(output_path)}\n"
                    )

            self.export_status.setText(
                f"Exported to Relion format: {output_path} and {star_path}"
            )
        else:
            self.export_status.setText(f"Exported to Relion format: {output_path}")

        return True

    except Exception as e:
        self.log_message(f"Error exporting to Relion format: {str(e)}")
        self.log_message(traceback.format_exc())
        return False

    # TODO: Refactor export_to_eman - complexity: 19 (target: <10)

    # TODO: Refactor function - Function 'export_to_eman' too long (97 lines)


def export_to_eman(self, input_path, output_path):
    """Export to EMAN2 HDF format"""
    try:
        self.export_status.setText("Exporting to EMAN2 HDF format...")

        # Try to import EMAN2 modules
        try:
            # First check if EMAN2 is in PYTHONPATH
            import sys

            eman_found = False
            for path in sys.path:
                if "EMAN2" in path:
                    eman_found = True
                    break

            if not eman_found:
                # EMAN2 typically has a special environment setup
                self.log_message(
                    "EMAN2 not found in PYTHONPATH. Will use alternative method."
                )
                have_eman = False
            else:
                # Try to import if it seems to be in the path
                from EMAN2 import EMData, EMUtil

                have_eman = True
        except ImportError:
            have_eman = False
            self.log_message(
                "Warning: EMAN2 module not found. Using alternate conversion method."
            )

        if have_eman:
            # Use native EMAN2 conversion
            # Get metadata
            pixel_size = 1.0
            with mrcfile.open(input_path, permissive=True) as mrc:
                data = mrc.data.copy()
                if hasattr(mrc, "voxel_size") and mrc.voxel_size is not None:
                    pixel_size = float(mrc.voxel_size.x)

            # Create EMData object
            volume = EMData(*data.shape[::-1])
            for z in range(data.shape[0]):
                for y in range(data.shape[1]):
                    for x in range(data.shape[2]):
                        volume.set_value_at(x, y, z, float(data[z, y, x]))

            # Set attributes
            volume.set_attr("apix_x", pixel_size)
            volume.set_attr("apix_y", pixel_size)
            volume.set_attr("apix_z", pixel_size)

            # Normalize if requested
            if self.eman_normalize.isChecked():
                volume.process_inplace("normalize")

            # Add metadata if requested
            if self.eman_metadata.isChecked():
                volume.set_attr("source_file", os.path.basename(input_path))
                volume.set_attr("data_source", "AreTomo3")

            # Write file
            volume.write_image(output_path)
        else:
            # Use alternative method with h5py
            with mrcfile.open(input_path, permissive=True) as mrc:
                data = mrc.data
                pixel_size = (
                    float(mrc.voxel_size.x) if hasattr(mrc, "voxel_size") else 1.0
                )

            # Normalize if requested
            if self.eman_normalize.isChecked():
                data = (data - data.mean()) / data.std()

            # Create HDF file
            with h5py.File(output_path, "w") as f:
                dataset = f.create_dataset("MDF/images/0", data=data, chunks=True)

                # Add metadata
                dataset.attrs["apix_x"] = pixel_size
                dataset.attrs["apix_y"] = pixel_size
                dataset.attrs["apix_z"] = pixel_size

                if self.eman_metadata.isChecked():
                    dataset.attrs["source_file"] = os.path.basename(input_path)
                    dataset.attrs["data_source"] = "AreTomo3"

        self.export_status.setText(f"Exported to EMAN2 format: {output_path}")
        return True

    except Exception as e:
        self.log_message(f"Error exporting to EMAN2 format: {str(e)}")
        self.log_message(traceback.format_exc())
        return False

    # TODO: Refactor function - Function 'export_to_imagej' too long (68 lines)


def export_to_imagej(self, input_path, output_path):
    """Export to ImageJ compatible TIFF format"""
    try:
        self.export_status.setText("Exporting to ImageJ format...")

        # Get the data
        with mrcfile.open(input_path, permissive=True) as mrc:
            data = mrc.data

            # Get pixel size
            pixel_size = 1.0
            if self.preserve_pixelsize.isChecked() and hasattr(mrc, "voxel_size"):
                pixel_size = float(mrc.voxel_size.x)

            # Adjust Z spacing if requested
            z_spacing = self.imagej_zspacing.value() * pixel_size

            if True:
                # Save as ImageJ compatible TIFF
                tifffile.imwrite(
                    output_path,
                    data,
                    imagej=True,
                    resolution=(1.0 / pixel_size, 1.0 / pixel_size),
                    metadata={"spacing": z_spacing, "unit": "angstrom", "axes": "ZYX"},
                )
            else:
                # Use PIL as fallback (with limited metadata support)
                if data.ndim == 3:
                    # For 3D data, save as a multi-page TIFF
                    img = Image.fromarray(data[0])
                    img.save(output_path, format="TIFF")

                    # Add subsequent slices
                    for i in range(1, data.shape[0]):
                        img = Image.fromarray(data[i])
                        img.save(output_path, format="TIFF", append=True)
                else:
                    # For 2D data, save as a single page TIFF
                    img = Image.fromarray(data)
                    img.save(output_path, format="TIFF")

            # Create calibration file if requested
            if self.imagej_calibration.isChecked():
                calib_path = os.path.splitext(output_path)[0] + "_calibration.txt"
                with open(calib_path, "w") as f:
                    f.write(
                        f"// ImageJ Calibration for {os.path.basename(output_path)}\n"
                    )
                    f.write(f"unit=angstrom\n")
                    f.write(f"pixel_width={pixel_size}\n")
                    f.write(f"pixel_height={pixel_size}\n")
                    f.write(f"pixel_depth={z_spacing}\n")
                    f.write(f"// Original file: {os.path.basename(input_path)}\n")
                    f.write(f"// Exported with AreTomo3 GUI\n")

                self.export_status.setText(
                    f"Exported to ImageJ format: {output_path} with calibration file"
                )
            else:
                self.export_status.setText(f"Exported to ImageJ format: {output_path}")

        return True

    except Exception as e:
        self.log_message(f"Error exporting to ImageJ format: {str(e)}")
        self.log_message(traceback.format_exc())
        return False


def export_results(data: Dict[str, Any], output_path: str, format: str = "mrc") -> bool:
    """
    Export processing results in the specified format.

    Args:
        data: Dictionary containing the data to export
        output_path: Path where to save the exported file
        format: Output format ('mrc', 'tiff', 'hdf', etc.)

    Returns:
        bool: True if export was successful, False otherwise
    """
    try:
        if format.lower() == "mrc":
            return _export_mrc(data, output_path)
        elif format.lower() == "tiff":
            return _export_tiff(data, output_path)
        elif format.lower() in ["hdf", "h5"]:
            return _export_hdf(data, output_path)
        else:
            logger.error(f"Unsupported export format: {format}")
            return False
    except Exception as e:
        logger.error(f"Error exporting data: {str(e)}")
        return False


def _export_mrc(data: Dict[str, Any], output_path: str) -> bool:
    """Export data in MRC format."""
    try:
        # Create a dummy array for testing
        arr = np.zeros((10, 10, 10), dtype=np.float32)

        with mrcfile.new(output_path, overwrite=True) as mrc:
            mrc.set_data(arr)
            if "pixel_size" in data:
                mrc.voxel_size = data["pixel_size"]
        return True
    except Exception as e:
        logger.error(f"Error exporting to MRC: {str(e)}")
        return False


def _export_tiff(data: Dict[str, Any], output_path: str) -> bool:
    """Export data in TIFF format."""
    try:
        # Create a dummy array for testing
        arr = np.zeros((10, 10, 10), dtype=np.float32)

        tifffile.imwrite(output_path, arr)
        return True
    except Exception as e:
        logger.error(f"Error exporting to TIFF: {str(e)}")
        return False


def _export_hdf(data: Dict[str, Any], output_path: str) -> bool:
    """Export data in HDF5 format."""
    try:
        # Create a dummy array for testing
        arr = np.zeros((10, 10, 10), dtype=np.float32)

        with h5py.File(output_path, "w") as f:
            f.create_dataset("data", data=arr)
            if "pixel_size" in data:
                f.attrs["pixel_size"] = data["pixel_size"]
        return True
    except Exception as e:
        logger.error(f"Error exporting to HDF: {str(e)}")
        return False
    # TODO: Refactor export_to_csv - complexity: 13 (target: <10)

    # TODO: Refactor function - Function 'export_to_csv' too long (61 lines)


def export_to_csv(
    data: Union[Dict[str, Any], List[Dict[str, Any]]], output_path: str
) -> bool:
    """
    Export data to CSV format.

    Args:
        data: Dictionary or list of dictionaries containing the data to export
        output_path: Path where to save the CSV file

    Returns:
        bool: True if export was successful, False otherwise
    """
    try:
        # Handle different data types
        if isinstance(data, dict):
            # Single dictionary - convert to list
            rows = [data]
        elif isinstance(data, list):
            # List of dictionaries
            rows = data
        else:
            logger.error(f"Unsupported data type for CSV export: {type(data)}")
            return False

        if not rows:
            logger.warning("No data to export to CSV")
            return False

        # Get all unique keys from all rows
        fieldnames = set()
        for row in rows:
            if isinstance(row, dict):
                fieldnames.update(row.keys())

        fieldnames = sorted(list(fieldnames))

        # Write CSV file
        with open(output_path, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for row in rows:
                if isinstance(row, dict):
                    # Convert numpy arrays to strings for CSV compatibility
                    clean_row = {}
                    for key, value in row.items():
                        if isinstance(value, np.ndarray):
                            clean_row[key] = str(value.tolist())
                        elif isinstance(value, (np.integer, np.floating)):
                            clean_row[key] = float(value)
                        else:
                            clean_row[key] = value
                    writer.writerow(clean_row)

        logger.info(
            f"Successfully exported {
                len(rows)} rows to CSV: {output_path}"
        )
        return True

    except Exception as e:
        logger.error(f"Error exporting to CSV: {str(e)}")
        return False
