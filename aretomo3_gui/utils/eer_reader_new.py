#!/usr/bin/env python3
"""
EER File Reader for AreTomo3 GUI

This module provides EER file reading capabilities using the tifffile library
to extract metadata from EER files stored in BigTIFF format.
"""

import logging
import os
from typing import Any, Dict, Optional

import numpy as np

try:
    import xmltodict
    from tifffile import TiffFile

    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"EER reader dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False

logger = logging.getLogger(__name__)


class EERHeader:
    """Class to hold EER file header information."""

    def __init__(self):
        """Initialize the instance."""
        self.width = 0
        self.height = 0
        self.frames = 0
        self.pixel_size = 0.0
        self.voltage = 0.0
        self.dose_rate = 0.0
        self.exposure_time = 0.0
        self.magnification = 0.0
        self.camera_name = ""
        self.acquisition_date = ""
        self.acquisition_time = ""
        self.microscope_name = ""
        self.operator = ""
        self.specimen = ""
        self.comments = ""
        self.sensor_pixel_width = 0.0
        self.sensor_pixel_height = 0.0
        self.raw_metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert header to dictionary."""
        return {
            "width": self.width,
            "height": self.height,
            "frames": self.frames,
            "pixel_size": self.pixel_size,
            "voltage": self.voltage,
            "dose_rate": self.dose_rate,
            "exposure_time": self.exposure_time,
            "magnification": self.magnification,
            "camera_name": self.camera_name,
            "acquisition_date": self.acquisition_date,
            "acquisition_time": self.acquisition_time,
            "microscope_name": self.microscope_name,
            "operator": self.operator,
            "specimen": self.specimen,
            "comments": self.comments,
            "sensor_pixel_width": self.sensor_pixel_width,
            "sensor_pixel_height": self.sensor_pixel_height,
        }


class EERReaderError(Exception):
    """Custom exception for EER reading errors."""

    pass


class EERReader:
    """
    EER file reader using tifffile library.

    This class provides EER file reading capabilities by extracting metadata
    from EER files stored in BigTIFF format with custom XML metadata.
    """

    def __init__(self):
        """Initialize the instance."""
        self.lib_loaded = DEPENDENCIES_AVAILABLE
        if not self.lib_loaded:
            logger.warning(
                "EER reading dependencies (xmltodict, tifffile) not available. "
                "Install with: pip install xmltodict tifffile"
            )
        else:
            logger.info("EER reader initialized with tifffile support")

    def is_available(self) -> bool:
        """Check if EER reading capabilities are available."""
        return self.lib_loaded

    def can_read_file(self, filepath: str) -> bool:
        """
        Check if a file can be read as an EER file.

        Args:
            filepath: Path to the file to check

        Returns:
            True if file exists and has .eer extension
        """
        if not os.path.exists(filepath):
            return False
        return filepath.lower().endswith(".eer")

    # TODO: Refactor read_eer_metadata - complexity: 11 (target: <10)
    # TODO: Refactor function - Function 'read_eer_metadata' too long (55
    # lines)
    def read_eer_metadata(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        Reads the metadata from an EER file.

        EER files are based on the BigTIFF format and store custom metadata,
        often in XML format within TIFF tag 65001.

        Args:
            filepath: The path to the EER file.

        Returns:
            Dictionary containing the parsed metadata, or None if an error occurs
        """
        if not self.lib_loaded:
            logger.error("EER reading dependencies not available")
            return None

        if not os.path.exists(filepath):
            logger.error(f"File not found at '{filepath}'")
            return None

        try:
            with TiffFile(filepath) as tif:
                if tif.pages and 65001 in tif.pages[0].tags:
                    tag = tif.pages[0].tags[65001]
                    data = tag.value.decode("UTF-8")

                    parsed_xml = xmltodict.parse(data)

                    metadata = {}
                    items = parsed_xml.get("metadata", {}).get("item", [])

                    if not isinstance(items, list):
                        items = [items]

                    for item in items:
                        key = item.get("@name")
                        value = item.get("#text")
                        if key is not None:
                            metadata[key] = value
                        unit = item.get("@unit")
                        if unit:
                            metadata[f"{key}.unit"] = unit

                    logger.info(f"Successfully read metadata from EER file: {filepath}")
                    return metadata
                else:
                    logger.warning(
                        f"No custom tag 65001 found in '{filepath}'. "
                        "This might not be a standard EER file or the metadata is stored differently."
                    )
                    return None
        except Exception as e:
            logger.error(f"An error occurred while reading '{filepath}': {e}")
            return None

    def read_metadata(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        Read metadata from an EER file (alias for read_eer_metadata).

        Args:
            filepath: Path to the EER file

        Returns:
            Dictionary with metadata or None if reading fails
        """
        return self.read_eer_metadata(filepath)

    # TODO: Refactor read_eer_header - complexity: 22 (target: <10)

    # TODO: Refactor function - Function 'read_eer_header' too long (108 lines)
    def read_eer_header(self, filepath: str) -> EERHeader:
        """
        Read EER file header information from metadata.

        Args:
            filepath: Path to the EER file

        Returns:
            EERHeader object with metadata

        Raises:
            EERReaderError: If file cannot be read
        """
        if not os.path.exists(filepath):
            raise EERReaderError(f"EER file not found: {filepath}")

        header = EERHeader()

        # Read metadata using tifffile
        metadata = self.read_eer_metadata(filepath)
        if metadata is None:
            raise EERReaderError(f"Failed to read metadata from EER file: {filepath}")

        # Store raw metadata
        header.raw_metadata = metadata

        # Extract common fields from metadata
        try:
            # Image dimensions
            if "image.width" in metadata:
                header.width = int(float(metadata["image.width"]))
            if "image.height" in metadata:
                header.height = int(float(metadata["image.height"]))

            # Frame count
            if "image.frameCount" in metadata:
                header.frames = int(float(metadata["image.frameCount"]))

            # Sensor pixel size
            sensor_pixel_width_str = metadata.get("sensorPixelSize.width")
            sensor_pixel_height_str = metadata.get("sensorPixelSize.height")

            if sensor_pixel_width_str and sensor_pixel_height_str:
                header.sensor_pixel_width = (
                    float(sensor_pixel_width_str) * 1e10
                )  # Convert to Angstrom
                header.sensor_pixel_height = (
                    float(sensor_pixel_height_str) * 1e10
                )  # Convert to Angstrom
                # Use average for pixel_size
                header.pixel_size = (
                    header.sensor_pixel_width + header.sensor_pixel_height
                ) / 2

            # Voltage
            if "electron.voltage" in metadata:
                header.voltage = (
                    float(metadata["electron.voltage"]) / 1000.0
                )  # Convert V to kV
            elif "accelerationVoltage" in metadata:
                header.voltage = float(metadata["accelerationVoltage"]) / 1000.0

            # Dose rate
            if "electron.doseRate" in metadata:
                header.dose_rate = float(metadata["electron.doseRate"])

            # Exposure time
            if "acquisition.exposureTime" in metadata:
                header.exposure_time = float(metadata["acquisition.exposureTime"])

            # Magnification
            if "optics.magnification" in metadata:
                header.magnification = float(metadata["optics.magnification"])

            # Camera name
            if "camera.name" in metadata:
                header.camera_name = metadata["camera.name"]
            elif "detector.name" in metadata:
                header.camera_name = metadata["detector.name"]

            # Acquisition date/time
            if "acquisition.startDate" in metadata:
                header.acquisition_date = metadata["acquisition.startDate"]
            if "acquisition.startTime" in metadata:
                header.acquisition_time = metadata["acquisition.startTime"]

            # Microscope name
            if "microscope.name" in metadata:
                header.microscope_name = metadata["microscope.name"]

            # Operator
            if "acquisition.operator" in metadata:
                header.operator = metadata["acquisition.operator"]

            # Specimen
            if "specimen.name" in metadata:
                header.specimen = metadata["specimen.name"]

            # Comments
            if "acquisition.comments" in metadata:
                header.comments = metadata["acquisition.comments"]

            logger.info(f"Successfully parsed EER header for {filepath}")

        except (ValueError, KeyError) as e:
            logger.warning(f"Error parsing some metadata fields: {e}")

        return header

    # TODO: Refactor function - Function 'get_eer_info' too long (52 lines)
    def get_eer_info(self, filepath: str) -> Dict[str, Any]:
        """
        Get comprehensive information about an EER file.

        Args:
            filepath: Path to the EER file

        Returns:
            Dictionary with file information
        """
        try:
            info = {
                "file_path": filepath,
                "file_size": os.path.getsize(filepath),
                "eer_reader_available": self.lib_loaded,
                "format": "EER (Electron Event Representation)",
                "compression": "Hardware-compressed",
            }

            # Try to read header
            try:
                header = self.read_eer_header(filepath)
                info.update(header.to_dict())

                # Calculate additional metrics
                if header.frames > 0 and header.exposure_time > 0:
                    info["frame_rate"] = header.frames / header.exposure_time

                if header.dose_rate > 0 and header.exposure_time > 0:
                    info["total_dose"] = header.dose_rate * header.exposure_time

                # Estimate compression ratio
                if header.width > 0 and header.height > 0 and header.frames > 0:
                    uncompressed_size = (
                        header.width * header.height * header.frames * 4
                    )  # 4 bytes per float
                    compression_ratio = uncompressed_size / info["file_size"]
                    info["estimated_compression_ratio"] = (
                        f"{
                        compression_ratio:.1f}:1"
                    )

            except EERReaderError as e:
                info["error"] = str(e)
                logger.warning(f"Could not read EER header: {e}")

            return info

        except Exception as e:
            logger.error(f"Error getting EER info: {e}")
            return {
                "file_path": filepath,
                "error": str(e),
                "format": "EER (Error reading file)",
            }

    # TODO: Refactor function - Function 'print_eer_metadata' too long (53
    # lines)
    def print_eer_metadata(self, filepath: str) -> None:
        """
        Print EER metadata in a formatted way (similar to the standalone script).

        Args:
            filepath: Path to the EER file
        """
        logger.info(f"Attempting to read metadata from: {filepath}")
        metadata = self.read_eer_metadata(filepath)

        if metadata:
            logger.info("\n--- EER File Metadata (Flattened) ---")
            for key, value in metadata.items():
                logger.info(f"  {key}: {value}")

            logger.info("\n--- Specific Metadata Fields ---")
            sensor_pixel_width_m_str = metadata.get("sensorPixelSize.width")
            sensor_pixel_height_m_str = metadata.get("sensorPixelSize.height")
            sensor_pixel_width_unit = metadata.get("sensorPixelSize.width.unit", "")
            sensor_pixel_height_unit = metadata.get("sensorPixelSize.height.unit", "")

            if sensor_pixel_width_m_str and sensor_pixel_height_m_str:
                try:
                    sensor_pixel_width_m = float(sensor_pixel_width_m_str)
                    sensor_pixel_height_m = float(sensor_pixel_height_m_str)

                    # Always print in meters (original unit)
                    logger.info(
                        f"Sensor Pixel Width: {sensor_pixel_width_m_str} {sensor_pixel_width_unit}"
                    )
                    logger.info(
                        f"Sensor Pixel Height: {sensor_pixel_height_m_str} {sensor_pixel_height_unit}"
                    )

                    # Add an additional line for Angstrom conversion
                    sensor_pixel_width_A = sensor_pixel_width_m * 1e10
                    sensor_pixel_height_A = sensor_pixel_height_m * 1e10
                    logger.info(
                        f"Sensor Pixel Width: {
                            sensor_pixel_width_A:.4f} Å"
                    )
                    logger.info(
                        f"Sensor Pixel Height: {
                            sensor_pixel_height_A:.4f} Å"
                    )

                except ValueError:
                    logger.info("Could not convert sensor pixel size to a number.")
            else:
                logger.info(
                    "Sensor pixel size information (sensorPixelSize.width/height) not found in metadata."
                )
                sensor_related_keys = [k for k in metadata if "sensor" in k.lower()]
                if sensor_related_keys:
                    logger.info(
                        "However, the following sensor-related keys were found:"
                    )
                    for key in sensor_related_keys:
                        logger.info(f"  {key}: {metadata[key]}")
        else:
            logger.info("\nFailed to read EER metadata.")


# Global EER reader instance
_eer_reader = None


def get_eer_reader() -> EERReader:
    """Get the global EER reader instance."""
    global _eer_reader
    if _eer_reader is None:
        _eer_reader = EERReader()
    return _eer_reader


def read_eer_header(filepath: str) -> EERHeader:
    """Convenience function to read EER file header."""
    return get_eer_reader().read_eer_header(filepath)


def read_eer_metadata(filepath: str) -> Optional[Dict[str, Any]]:
    """Convenience function to read EER metadata."""
    return get_eer_reader().read_eer_metadata(filepath)


def get_eer_info(filepath: str) -> Dict[str, Any]:
    """Convenience function to get EER file info."""
    return get_eer_reader().get_eer_info(filepath)


def is_eer_supported() -> bool:
    """Check if EER reading is supported."""
    return get_eer_reader().is_available()


def print_eer_metadata(filepath: str) -> None:
    """Convenience function to print EER metadata."""
    return get_eer_reader().print_eer_metadata(filepath)


# Main script functionality
if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        logger.info("Usage: python eer_reader.py <path_to_eer_file>")
        logger.info("Example: python eer_reader.py my_data.eer")
        sys.exit(1)

    eer_file_path = sys.argv[1]
    reader = get_eer_reader()
    reader.print_eer_metadata(eer_file_path)
