#!/usr/bin/env python3
"""
Enhanced MDOC file parser for AreTomo3 GUI.
Provides comprehensive parsing of SerialEM .mdoc files with tilt angle extraction.
"""

import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class MDOCParser:
    """Enhanced MDOC file parser with comprehensive data extraction."""

    def __init__(self):
        """Initialize the MDOC parser."""
        self.reset()

    def reset(self):
        """Reset parser state."""
        self.global_params = {}
        self.sections = []
        self.tilt_series_data = {}

    def parse_mdoc_file(self, file_path: str) -> Dict[str, Any]:
        """
        Parse a complete .mdoc file and extract all information.

        Args:
            file_path: Path to the .mdoc file

        Returns:
            Dictionary containing all parsed information
        """
        self.reset()

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Parse global parameters and sections
            self._parse_global_parameters(content)
            self._parse_sections(content)
            self._extract_tilt_series_data()

            # Compile comprehensive results
            result = {
                "file_path": file_path,
                "global_params": self.global_params,
                "sections": self.sections,
                "tilt_series": self.tilt_series_data,
                "summary": self._generate_summary(),
            }

            logger.info(f"Successfully parsed MDOC file: {file_path}")
            return result

        except Exception as e:
            logger.error(f"Error parsing MDOC file {file_path}: {e}")
            return self._create_error_result(file_path, str(e))

    def _parse_global_parameters(self, content: str):
        """Parse global parameters from MDOC content."""
        # Extract parameters before the first [ZValue section
        global_section = (
            content.split("[ZValue")[0] if "[ZValue" in content else content
        )

        # Define parameter patterns
        patterns = {
            "pixel_size": r"PixelSpacing\s*=\s*([\d.]+)",
            "voltage": r"Voltage\s*=\s*([\d.]+)",
            "cs": r"SphericalAberration\s*=\s*([\d.]+)",
            "total_dose": r"Dose\s*=\s*([\d.]+)",
            "tilt_axis": r"TiltAxis\s*=\s*([-\d.]+)",
            "magnification": r"Magnification\s*=\s*([\d.]+)",
            "defocus": r"TargetDefocus\s*=\s*([-\d.]+)",
            "exposure_time": r"ExposureTime\s*=\s*([\d.]+)",
            "binning": r"Binning\s*=\s*([\d]+)",
            "camera_length": r"CameraLength\s*=\s*([\d.]+)",
            "spot_size": r"SpotSize\s*=\s*([\d]+)",
            "intensity": r"Intensity\s*=\s*([\d.]+)",
            "image_file": r"ImageFile\s*=\s*(.+)",
            "data_mode": r"DataMode\s*=\s*([\d]+)",
            "image_size": r"ImageSize\s*=\s*([\d]+)\s+([\d]+)",
            "frame_dose_rate": r"FrameDoseRate\s*=\s*([\d.]+)",
            "pre_exposure_dose": r"PreExposureDose\s*=\s*([\d.]+)",
        }

        for param_name, pattern in patterns.items():
            match = re.search(pattern, global_section, re.IGNORECASE)
            if match:
                if param_name == "image_size":
                    self.global_params[param_name] = (
                        int(match.group(1)),
                        int(match.group(2)),
                    )
                elif param_name in ["image_file"]:
                    self.global_params[param_name] = match.group(1).strip()
                else:
                    try:
                        value = float(match.group(1))
                        if param_name in ["binning", "spot_size", "data_mode"]:
                            value = int(value)
                        elif param_name == "pixel_size":
                            value *= 10  # Convert nm to Angstrom
                        self.global_params[param_name] = value
                    except ValueError:
                        self.global_params[param_name] = match.group(1).strip()

    # TODO: Refactor function - Function '_parse_sections' too long (71 lines)
    def _parse_sections(self, content: str):
        """Parse individual sections (ZValue entries) from MDOC content."""
        # Split content into sections
        sections = re.split(r"\[ZValue\s*=\s*[\d]+\]", content)[
            1:
        ]  # Skip first empty part
        section_headers = re.findall(r"\[ZValue\s*=\s*([\d]+)\]", content)

        for i, (header, section_content) in enumerate(zip(section_headers, sections)):
            section_data = {"z_value": int(header)}

            # Define section-specific patterns
            patterns = {
                "tilt_angle": r"TiltAngle\s*=\s*([-\d.]+)",
                "stage_position": r"StagePosition\s*=\s*([-\d.]+)\s+([-\d.]+)",
                "stage_z": r"StageZ\s*=\s*([-\d.]+)",
                "magnification": r"Magnification\s*=\s*([\d.]+)",
                "intensity": r"Intensity\s*=\s*([\d.]+)",
                "exposure_dose": r"ExposureDose\s*=\s*([\d.]+)",
                "dose_rate": r"DoseRate\s*=\s*([\d.]+)",
                "pixel_size": r"PixelSpacing\s*=\s*([\d.]+)",
                "defocus": r"TargetDefocus\s*=\s*([-\d.]+)",
                "exposure_time": r"ExposureTime\s*=\s*([\d.]+)",
                "datetime": r"DateTime\s*=\s*(.+)",
                "sub_frame_path": r"SubFramePath\s*=\s*(.+)",
                "num_sub_frames": r"NumSubFrames\s*=\s*([\d]+)",
                "frame_dose_rate": r"FrameDoseRate\s*=\s*([\d.]+)",
                "accumulated_dose": r"AccumulatedDose\s*=\s*([\d.]+)",
                "prior_record_dose": r"PriorRecordDose\s*=\s*([\d.]+)",
                "image_shift": r"ImageShift\s*=\s*([-\d.]+)\s+([-\d.]+)",
                "beam_shift": r"BeamShift\s*=\s*([-\d.]+)\s+([-\d.]+)",
                "camera_index": r"CameraIndex\s*=\s*([\d]+)",
                "divide_by_2": r"DivideBy2\s*=\s*([\d]+)",
                "mag_index": r"MagIndex\s*=\s*([\d]+)",
                "counts_per_electron": r"CountsPerElectron\s*=\s*([\d.]+)",
                "target_defocus": r"TargetDefocus\s*=\s*([-\d.]+)",
                "rotation_angle": r"RotationAngle\s*=\s*([-\d.]+)",
                "energy_filter": r"EnergyFilter\s*=\s*([\d]+)",
                "slit_width": r"SlitWidth\s*=\s*([\d.]+)",
                "slit_in": r"SlitIn\s*=\s*([\d]+)",
            }

            for param_name, pattern in patterns.items():
                match = re.search(pattern, section_content, re.IGNORECASE)
                if match:
                    if param_name in ["stage_position", "image_shift", "beam_shift"]:
                        section_data[param_name] = (
                            float(match.group(1)),
                            float(match.group(2)),
                        )
                    elif param_name in ["datetime", "sub_frame_path"]:
                        section_data[param_name] = match.group(1).strip()
                    else:
                        try:
                            value = float(match.group(1))
                            if param_name in [
                                "num_sub_frames",
                                "camera_index",
                                "divide_by_2",
                                "mag_index",
                                "energy_filter",
                                "slit_in",
                            ]:
                                value = int(value)
                            elif param_name == "pixel_size":
                                value *= 10  # Convert nm to Angstrom
                            section_data[param_name] = value
                        except ValueError:
                            section_data[param_name] = match.group(1).strip()

            self.sections.append(section_data)

    def _extract_tilt_series_data(self):
        """Extract and organize tilt series specific data."""
        if not self.sections:
            return

        # Extract tilt angles and associated data
        tilt_angles = []
        exposure_doses = []
        accumulated_doses = []
        defocus_values = []
        stage_positions = []
        datetime_stamps = []

        for section in self.sections:
            if "tilt_angle" in section:
                tilt_angles.append(section["tilt_angle"])
                exposure_doses.append(section.get("exposure_dose", 0.0))
                accumulated_doses.append(section.get("accumulated_dose", 0.0))
                defocus_values.append(section.get("defocus", 0.0))
                stage_positions.append(section.get("stage_position", (0.0, 0.0)))
                datetime_stamps.append(section.get("datetime", ""))

        self.tilt_series_data = {
            "tilt_angles": tilt_angles,
            "exposure_doses": exposure_doses,
            "accumulated_doses": accumulated_doses,
            "defocus_values": defocus_values,
            "stage_positions": stage_positions,
            "datetime_stamps": datetime_stamps,
            "num_images": len(tilt_angles),
            "tilt_range": (
                (min(tilt_angles), max(tilt_angles)) if tilt_angles else (0, 0)
            ),
            "total_dose": sum(exposure_doses) if exposure_doses else 0.0,
        }

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate a summary of the parsed MDOC data."""
        summary = {
            "num_sections": len(self.sections),
            "has_tilt_series": bool(self.tilt_series_data.get("tilt_angles")),
            "global_params_count": len(self.global_params),
            "parsing_success": True,
        }

        if self.tilt_series_data.get("tilt_angles"):
            summary.update(
                {
                    "tilt_range": self.tilt_series_data["tilt_range"],
                    "num_tilts": self.tilt_series_data["num_images"],
                    "total_dose": self.tilt_series_data["total_dose"],
                    "average_dose_per_tilt": (
                        self.tilt_series_data["total_dose"]
                        / self.tilt_series_data["num_images"]
                        if self.tilt_series_data["num_images"] > 0
                        else 0
                    ),
                }
            )

        return summary

    def _create_error_result(self, file_path: str, error_msg: str) -> Dict[str, Any]:
        """Create error result structure."""
        return {
            "file_path": file_path,
            "global_params": {},
            "sections": [],
            "tilt_series": {},
            "summary": {
                "num_sections": 0,
                "has_tilt_series": False,
                "global_params_count": 0,
                "parsing_success": False,
                "error_message": error_msg,
            },
        }


# Legacy function for backward compatibility
def parse_mdoc(file_path: str) -> Dict[str, Any]:
    """
    Legacy function for backward compatibility.
    Parse .mdoc file to extract microscope parameters.

    Returns:
        dict: Parameters extracted from mdoc file
    """
    parser = MDOCParser()
    result = parser.parse_mdoc_file(file_path)

    # Convert to legacy format
    legacy_params = {
        "pixel_size": result["global_params"].get("pixel_size"),
        "voltage": result["global_params"].get("voltage"),
        "cs": result["global_params"].get("cs"),
        "total_dose": result["tilt_series"].get("total_dose", 0.0),
        "tilt_axis": result["global_params"].get("tilt_axis"),
        "tilt_angles": result["tilt_series"].get("tilt_angles", []),
        "num_images": result["tilt_series"].get("num_images", 0),
        "tilt_range": result["tilt_series"].get("tilt_range", (0, 0)),
    }

    return legacy_params


def extract_tilt_angles_from_mdoc(file_path: str) -> List[float]:
    """
    Extract tilt angles from MDOC file for plotting purposes.

    Args:
        file_path: Path to the .mdoc file

    Returns:
        List of tilt angles in degrees
    """
    parser = MDOCParser()
    result = parser.parse_mdoc_file(file_path)
    return result["tilt_series"].get("tilt_angles", [])


def get_mdoc_summary(file_path: str) -> Dict[str, Any]:
    """
    Get a quick summary of MDOC file contents.

    Args:
        file_path: Path to the .mdoc file

    Returns:
        Summary dictionary
    """
    parser = MDOCParser()
    result = parser.parse_mdoc_file(file_path)
    return result["summary"]
