"""
Parameter manager for AreTomo3 GUI.
Handles parameter validation, command preview, and parameter changes.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from PyQt6.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)


# TODO: Refactor class - Class 'ParameterManager' too long (515 lines)
class ParameterManager(QObject):
    """Manages parameter validation and command generation."""

    # Signals
    parameters_changed = pyqtSignal(dict)
    command_generated = pyqtSignal(str)
    validation_error = pyqtSignal(str)

    def __init__(self, main_window):
        """Initialize the parameter manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        super().__init__()
        self.main_window = main_window
        self.last_command = ""

    # TODO: Refactor connect_parameter_changes - complexity: 11 (target: <10)
    # TODO: Refactor function - Function 'connect_parameter_changes' too long (57 lines)
    def connect_parameter_changes(self) -> None:
        """Connect parameter change signals for automatic updates."""
        try:
            # Connect value change signals for automatic command preview
            parameter_widgets = [
                "pixel_size",
                "voltage",
                "cs",
                "frame_dose",
                "tilt_axis",
                "amp_contrast",
                "volume_z",
                "lowpass",
                "dark_tol",
                "mc_bin",
                "mc_patch_x",
                "mc_patch_y",
                "fm_int",
                "at_bin",
            ]

            for param_name in parameter_widgets:
                if hasattr(self.main_window, param_name):
                    widget = getattr(self.main_window, param_name)
                    if hasattr(widget, "valueChanged"):
                        widget.valueChanged.connect(self._on_parameter_changed)

            # Connect checkbox change signals
            checkbox_widgets = [
                "out_xf",
                "out_imod",
                "wbp",
                "tilt_cor",
                "flip_gain",
                "flip_volume",
                "correct_ctf",
            ]

            for param_name in checkbox_widgets:
                if hasattr(self.main_window, param_name):
                    widget = getattr(self.main_window, param_name)
                    if hasattr(widget, "toggled"):
                        widget.toggled.connect(self._on_parameter_changed)

            # Connect text change signals
            text_widgets = ["input_dir", "output_dir", "aretomo_path"]

            for param_name in text_widgets:
                if hasattr(self.main_window, param_name):
                    widget = getattr(self.main_window, param_name)
                    if hasattr(widget, "textChanged"):
                        widget.textChanged.connect(self._on_parameter_changed)

            logger.info("Parameter change signals connected successfully")

        except Exception as e:
            logger.error(f"Error connecting parameter changes: {e}")

    def _on_parameter_changed(self) -> None:
        """Handle parameter change events."""
        try:
            # Collect current parameters
            params = self.collect_current_parameters()

            # Emit parameters changed signal
            self.parameters_changed.emit(params)

            # Auto-update command preview if enabled
            if hasattr(self.main_window, "auto_update_preview"):
                if getattr(self.main_window, "auto_update_preview", False):
                    self.generate_command_preview()

        except Exception as e:
            logger.warning(f"Error handling parameter change: {e}")

    # TODO: Refactor collect_current_parameters - complexity: 16 (target: <10)

    # TODO: Refactor function - Function 'collect_current_parameters' too long (73 lines)
    def collect_current_parameters(self) -> Dict[str, Any]:
        """Collect all current parameter values.

        Returns:
            Dictionary containing all current parameter values
        """
        params = {}

        try:
            # Basic paths
            if hasattr(self.main_window, "aretomo_path"):
                params["aretomo_path"] = self.main_window.aretomo_path.text().strip()
            if hasattr(self.main_window, "input_dir"):
                params["input_dir"] = self.main_window.input_dir.text().strip()
            if hasattr(self.main_window, "output_dir"):
                params["output_dir"] = self.main_window.output_dir.text().strip()
            if hasattr(self.main_window, "gpu_index"):
                params["gpu_index"] = self.main_window.gpu_index.value()

            # Microscope parameters
            microscope_params = {}
            for param in [
                "pixel_size",
                "voltage",
                "cs",
                "frame_dose",
                "tilt_axis",
                "amp_contrast",
                "volume_z",
                "lowpass",
                "dark_tol",
            ]:
                if hasattr(self.main_window, param):
                    widget = getattr(self.main_window, param)
                    if hasattr(widget, "value"):
                        microscope_params[param] = widget.value()
            params["microscope"] = microscope_params

            # Motion correction parameters
            mc_params = {}
            for param in ["mc_bin", "mc_patch_x", "mc_patch_y", "fm_int"]:
                if hasattr(self.main_window, param):
                    widget = getattr(self.main_window, param)
                    if hasattr(widget, "value"):
                        mc_params[param] = widget.value()
            params["motion_correction"] = mc_params

            # Tomogram parameters
            if hasattr(self.main_window, "at_bin"):
                params["at_bin"] = self.main_window.at_bin.value()

            # Boolean options
            options = {}
            for param in [
                "out_xf",
                "out_imod",
                "wbp",
                "tilt_cor",
                "flip_gain",
                "flip_volume",
                "correct_ctf",
            ]:
                if hasattr(self.main_window, param):
                    widget = getattr(self.main_window, param)
                    if hasattr(widget, "isChecked"):
                        options[param] = widget.isChecked()
            params["options"] = options

        except Exception as e:
            logger.error(f"Error collecting parameters: {e}")

        # TODO: Refactor validate_parameters - complexity: 21 (target: <10)
        return params

    # TODO: Refactor function - Function 'validate_parameters' too long (95 lines)
    def validate_parameters(
        self, params: Optional[Dict[str, Any]] = None
    ) -> tuple[bool, List[str]]:
        """Validate current parameters.

        Args:
            params: Optional parameter dictionary. If None, collects current parameters.

        Returns:
            Tuple of (is_valid, error_messages)
        """
        if params is None:
            params = self.collect_current_parameters()

        errors = []

        try:
            # Validate AreTomo3 path
            aretomo_path = params.get("aretomo_path", "")
            if not aretomo_path:
                errors.append("AreTomo3 path is required")
            elif not Path(aretomo_path).exists():
                errors.append(f"AreTomo3 executable not found: {aretomo_path}")
            elif not Path(aretomo_path).is_file():
                errors.append(f"AreTomo3 path is not a file: {aretomo_path}")

            # Validate input directory
            input_dir = params.get("input_dir", "")
            if not input_dir:
                errors.append("Input directory is required")
            elif not Path(input_dir).exists():
                errors.append(f"Input directory not found: {input_dir}")
            elif not Path(input_dir).is_dir():
                errors.append(f"Input path is not a directory: {input_dir}")

            # Validate output directory
            output_dir = params.get("output_dir", "")
            if not output_dir:
                errors.append("Output directory is required")
            else:
                output_path = Path(output_dir)
                if not output_path.exists():
                    try:
                        output_path.mkdir(parents=True, exist_ok=True)
                    except Exception as e:
                        errors.append(f"Cannot create output directory: {e}")
                elif not output_path.is_dir():
                    errors.append(f"Output path is not a directory: {output_dir}")

            # Validate microscope parameters
            microscope = params.get("microscope", {})

            pixel_size = microscope.get("pixel_size", 0)
            if pixel_size <= 0:
                errors.append("Pixel size must be greater than 0")

            voltage = microscope.get("voltage", 0)
            if voltage <= 0:
                errors.append("Voltage must be greater than 0")

            cs = microscope.get("cs", 0)
            if cs < 0:
                errors.append("Spherical aberration (Cs) cannot be negative")

            frame_dose = microscope.get("frame_dose", 0)
            if frame_dose <= 0:
                errors.append("Frame dose must be greater than 0")

            # Validate motion correction parameters
            mc = params.get("motion_correction", {})

            mc_bin = mc.get("mc_bin", 0)
            if mc_bin <= 0:
                errors.append("Motion correction binning must be greater than 0")

            fm_int = mc.get("fm_int", 0)
            if fm_int <= 0:
                errors.append("Frame interval must be greater than 0")

            # Validate alignment binning
            at_bin = params.get("at_bin", 0)
            if at_bin <= 0:
                errors.append("Alignment binning must be greater than 0")

        except Exception as e:
            logger.error(f"Error during parameter validation: {e}")
            errors.append(f"Validation error: {str(e)}")

        is_valid = len(errors) == 0

        if errors:
            self.validation_error.emit("; ".join(errors))
        # TODO: Refactor generate_command_preview - complexity: 31 (target: <10)

        return is_valid, errors

    # TODO: Refactor function - Function 'generate_command_preview' too long (141 lines)
    def generate_command_preview(self) -> str:
        """Generate AreTomo3 command preview based on current parameters.

        Returns:
            String containing the AreTomo3 command
        """
        try:
            params = self.collect_current_parameters()

            # Validate parameters first
            is_valid, errors = self.validate_parameters(params)
            if not is_valid:
                command = f"# Invalid parameters: {'; '.join(errors[:3])}"
                self.last_command = command
                self.command_generated.emit(command)
                return command

            # Build command
            cmd_parts = []

            # AreTomo3 executable
            aretomo_path = params.get("aretomo_path", "")
            cmd_parts.append(f'"{aretomo_path}"')

            # Input/Output
            input_dir = params.get("input_dir", "")
            output_dir = params.get("output_dir", "")

            if input_dir:
                cmd_parts.append(f'-InMrc "{input_dir}"')
            if output_dir:
                cmd_parts.append(f'-OutMrc "{output_dir}"')

            # GPU
            gpu_index = params.get("gpu_index", 0)
            cmd_parts.append(f"-Gpu {gpu_index}")

            # Microscope parameters
            microscope = params.get("microscope", {})

            pixel_size = microscope.get("pixel_size")
            if pixel_size:
                cmd_parts.append(f"-PixSize {pixel_size}")

            voltage = microscope.get("voltage")
            if voltage:
                cmd_parts.append(f"-Kv {voltage}")

            cs = microscope.get("cs")
            if cs is not None:
                cmd_parts.append(f"-Cs {cs}")

            frame_dose = microscope.get("frame_dose")
            if frame_dose:
                cmd_parts.append(f"-FrameDose {frame_dose}")

            tilt_axis = microscope.get("tilt_axis")
            if tilt_axis is not None:
                cmd_parts.append(f"-TiltAxis {tilt_axis}")

            amp_contrast = microscope.get("amp_contrast")
            if amp_contrast is not None:
                cmd_parts.append(f"-AmpContrast {amp_contrast}")

            volume_z = microscope.get("volume_z")
            if volume_z:
                cmd_parts.append(f"-VolZ {volume_z}")

            # Motion correction parameters
            mc = params.get("motion_correction", {})

            mc_bin = mc.get("mc_bin")
            if mc_bin:
                cmd_parts.append(f"-McBin {mc_bin}")

            mc_patch_x = mc.get("mc_patch_x", 1)
            mc_patch_y = mc.get("mc_patch_y", 1)
            if mc_patch_x > 1 or mc_patch_y > 1:
                cmd_parts.append(f"-McPatch {mc_patch_x} {mc_patch_y}")

            fm_int = mc.get("fm_int")
            if fm_int:
                cmd_parts.append(f"-FmInt {fm_int}")

            # Alignment binning
            at_bin = params.get("at_bin")
            if at_bin:
                cmd_parts.append(f"-AlnBin {at_bin}")

            # Boolean options
            options = params.get("options", {})

            if options.get("out_xf"):
                cmd_parts.append("-OutXf 1")
            if options.get("out_imod"):
                cmd_parts.append("-OutImod 1")
            if options.get("wbp"):
                cmd_parts.append("-Wbp 1")
            if options.get("tilt_cor"):
                cmd_parts.append("-TiltCor 1")
            if options.get("flip_gain"):
                cmd_parts.append("-FlipGain 1")
            if options.get("flip_volume"):
                cmd_parts.append("-FlipVol 1")
            if options.get("correct_ctf"):
                cmd_parts.append("-CorrectCTF 1")

            # Join command parts
            command = " ".join(cmd_parts)

            # Format for display (add line breaks for readability)
            if len(command) > 80:
                formatted_parts = []
                current_line = ""
                for part in cmd_parts:
                    if len(current_line + " " + part) > 80 and current_line:
                        formatted_parts.append(current_line)
                        current_line = "    " + part  # Indent continuation lines
                    else:
                        if current_line:
                            current_line += " " + part
                        else:
                            current_line = part
                if current_line:
                    formatted_parts.append(current_line)
                command = " \\\n".join(formatted_parts)

            self.last_command = command
            self.command_generated.emit(command)

            # Update command preview widget if available
            if hasattr(self.main_window, "command_preview"):
                self.main_window.command_preview.setPlainText(command)

            return command

        except Exception as e:
            error_msg = f"Error generating command: {str(e)}"
            logger.error(error_msg)
            self.command_generated.emit(error_msg)
            return error_msg

    def get_last_command(self) -> str:
        """Get the last generated command.

        Returns:
            String containing the last generated command
        """
        return self.last_command

    def export_parameters(self, filepath: str) -> bool:
        """Export current parameters to a file.

        Args:
            filepath: Path to save parameters

        Returns:
            bool: True if exported successfully, False otherwise
        """
        try:
            import json

            params = self.collect_current_parameters()

            with open(filepath, "w") as f:
                json.dump(params, f, indent=2)

            logger.info(f"Parameters exported to: {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error exporting parameters: {e}")
            return False

    def import_parameters(self, filepath: str) -> bool:
        """Import parameters from a file.

        Args:
            filepath: Path to parameter file

        Returns:
            bool: True if imported successfully, False otherwise
        """
        try:
            import json

            with open(filepath, "r") as f:
                params = json.load(f)

            # Apply parameters to GUI
            self._apply_parameters_to_gui(params)

            logger.info(f"Parameters imported from: {filepath}")
            return True

        # TODO: Refactor _apply_parameters_to_gui - complexity: 24 (target: <10)
        except Exception as e:
            logger.error(f"Error importing parameters: {e}")
            return False

    def _apply_parameters_to_gui(self, params: Dict[str, Any]) -> None:
        """Apply parameter values to GUI widgets.

        Args:
            params: Dictionary containing parameter values
        """
        try:
            # Basic paths
            if "aretomo_path" in params and hasattr(self.main_window, "aretomo_path"):
                self.main_window.aretomo_path.setText(params["aretomo_path"])
            if "input_dir" in params and hasattr(self.main_window, "input_dir"):
                self.main_window.input_dir.setText(params["input_dir"])
            if "output_dir" in params and hasattr(self.main_window, "output_dir"):
                self.main_window.output_dir.setText(params["output_dir"])
            if "gpu_index" in params and hasattr(self.main_window, "gpu_index"):
                self.main_window.gpu_index.setValue(params["gpu_index"])

            # Microscope parameters
            if "microscope" in params:
                microscope = params["microscope"]
                for param, value in microscope.items():
                    if hasattr(self.main_window, param):
                        widget = getattr(self.main_window, param)
                        if hasattr(widget, "setValue"):
                            widget.setValue(value)

            # Motion correction parameters
            if "motion_correction" in params:
                mc = params["motion_correction"]
                for param, value in mc.items():
                    if hasattr(self.main_window, param):
                        widget = getattr(self.main_window, param)
                        if hasattr(widget, "setValue"):
                            widget.setValue(value)

            # Alignment binning
            if "at_bin" in params and hasattr(self.main_window, "at_bin"):
                self.main_window.at_bin.setValue(params["at_bin"])

            # Boolean options
            if "options" in params:
                options = params["options"]
                for param, value in options.items():
                    if hasattr(self.main_window, param):
                        widget = getattr(self.main_window, param)
                        if hasattr(widget, "setChecked"):
                            widget.setChecked(value)

        except Exception as e:
            logger.warning(f"Error applying some parameters to GUI: {e}")
