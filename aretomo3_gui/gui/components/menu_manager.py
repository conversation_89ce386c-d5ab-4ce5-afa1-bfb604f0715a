"""
Menu manager for AreTomo3 GUI.
Handles menu bar creation and menu actions.
"""

import logging
from typing import Optional

from PyQt6.QtGui import QAction
from PyQt6.QtWidgets import QMainWindow, QMenuBar

logger = logging.getLogger(__name__)


class MenuManager:
    """Manages menu bar setup and menu actions."""

    def __init__(self, main_window: QMainWindow):
        """Initialize the menu manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        self.menubar: Optional[QMenuBar] = None

    def setup_menubar(self) -> None:
        """Set up the main menu bar."""
        try:
            self.menubar = self.main_window.menuBar()

            # Create main menus
            self._setup_file_menu()
            self._setup_edit_menu()
            self._setup_view_menu()
            self._setup_tools_menu()
            self._setup_help_menu()

            logger.info("Menu bar setup completed successfully")

        except Exception as e:
            logger.error(f"Error setting up menu bar: {str(e)}", exc_info=True)
            raise

    def _setup_file_menu(self) -> None:
        """Setup the File menu."""
        file_menu = self.menubar.addMenu("&File")

        # New project
        new_action = QAction("&New Project...", self.main_window)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self._new_project)
        file_menu.addAction(new_action)

        # Open
        open_action = QAction("&Open...", self.main_window)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.main_window.open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # Save
        save_action = QAction("&Save", self.main_window)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.main_window.save_file)
        file_menu.addAction(save_action)

        # Save As
        save_as_action = QAction("Save &As...", self.main_window)
        save_as_action.setShortcut("Ctrl+Shift+S")
        save_as_action.triggered.connect(self._save_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        # Recent files submenu
        self.recent_menu = file_menu.addMenu("Recent Files")
        self._update_recent_files_menu()

        file_menu.addSeparator()

        # Exit
        exit_action = QAction("E&xit", self.main_window)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.main_window.close)
        file_menu.addAction(exit_action)

    def _setup_edit_menu(self) -> None:
        """Setup the Edit menu."""
        edit_menu = self.menubar.addMenu("&Edit")

        # Preferences
        prefs_action = QAction("&Preferences...", self.main_window)
        prefs_action.setShortcut("Ctrl+,")
        prefs_action.triggered.connect(self._show_preferences)
        edit_menu.addAction(prefs_action)

        edit_menu.addSeparator()

        # Clear recent files
        clear_recent_action = QAction("Clear Recent Files", self.main_window)
        clear_recent_action.triggered.connect(self._clear_recent_files)
        edit_menu.addAction(clear_recent_action)

    def _setup_view_menu(self) -> None:
        """Setup the View menu."""
        view_menu = self.menubar.addMenu("&View")

        # Toggle tabs
        tabs_action = QAction("Show/Hide Tabs", self.main_window)
        tabs_action.setShortcut("F11")
        tabs_action.triggered.connect(self._toggle_tabs)
        view_menu.addAction(tabs_action)

        view_menu.addSeparator()

        # Zoom actions
        zoom_in_action = QAction("Zoom In", self.main_window)
        zoom_in_action.setShortcut("Ctrl++")
        zoom_in_action.triggered.connect(self._zoom_in)
        view_menu.addAction(zoom_in_action)

        zoom_out_action = QAction("Zoom Out", self.main_window)
        zoom_out_action.setShortcut("Ctrl+-")
        zoom_out_action.triggered.connect(self._zoom_out)
        view_menu.addAction(zoom_out_action)

        reset_zoom_action = QAction("Reset Zoom", self.main_window)
        reset_zoom_action.setShortcut("Ctrl+0")
        reset_zoom_action.triggered.connect(self._reset_zoom)
        view_menu.addAction(reset_zoom_action)

    def _setup_tools_menu(self) -> None:
        """Setup the Tools menu."""
        tools_menu = self.menubar.addMenu("&Tools")

        # Validate installation
        validate_action = QAction("Validate AreTomo3 Installation", self.main_window)
        validate_action.triggered.connect(self._validate_installation)
        tools_menu.addAction(validate_action)

        tools_menu.addSeparator()

        # System information
        sysinfo_action = QAction("System Information", self.main_window)
        sysinfo_action.triggered.connect(self._show_system_info)
        tools_menu.addAction(sysinfo_action)

        # GPU information
        gpu_action = QAction("GPU Information", self.main_window)
        gpu_action.triggered.connect(self._show_gpu_info)
        tools_menu.addAction(gpu_action)

    def _setup_help_menu(self) -> None:
        """Setup the Help menu."""
        help_menu = self.menubar.addMenu("&Help")

        # Documentation
        docs_action = QAction("Documentation", self.main_window)
        docs_action.setShortcut("F1")
        docs_action.triggered.connect(self._show_documentation)
        help_menu.addAction(docs_action)

        # Keyboard shortcuts
        shortcuts_action = QAction("Keyboard Shortcuts", self.main_window)
        shortcuts_action.triggered.connect(self._show_shortcuts)
        help_menu.addAction(shortcuts_action)

        help_menu.addSeparator()

        # About
        about_action = QAction("About AreTomo3 GUI", self.main_window)
        about_action.triggered.connect(self.main_window._show_about)
        help_menu.addAction(about_action)

    def _update_recent_files_menu(self) -> None:
        """Update the recent files menu."""
        if hasattr(self, "recent_menu"):
            self.recent_menu.clear()
            # Add recent files logic here
            # This would integrate with the session manager

    # Menu action handlers
    def _new_project(self) -> None:
        """Handle new project action."""
        logger.info("Creating new project...")
        # Implementation would depend on project management system

    def _save_as(self) -> None:
        """Handle save as action."""
        logger.info("Save as requested...")
        # Implementation would use QFileDialog

    def _show_preferences(self) -> None:
        """Show preferences dialog."""
        logger.info("Showing preferences dialog...")
        # Implementation would create preferences dialog

    def _clear_recent_files(self) -> None:
        """Clear recent files list."""
        logger.info("Clearing recent files...")
        self._update_recent_files_menu()

    def _toggle_tabs(self) -> None:
        """Toggle tab bar visibility."""
        if hasattr(self.main_window, "tabs"):
            tabs = self.main_window.tabs
            visible = tabs.tabBar().isVisible()
            tabs.tabBar().setVisible(not visible)
            logger.info(f"Tabs {'hidden' if visible else 'shown'}")

    def _zoom_in(self) -> None:
        """Zoom in current view."""
        logger.info("Zoom in requested")
        # Implementation would depend on current active viewer

    def _zoom_out(self) -> None:
        """Zoom out current view."""
        logger.info("Zoom out requested")
        # Implementation would depend on current active viewer

    def _reset_zoom(self) -> None:
        """Reset zoom to default."""
        logger.info("Reset zoom requested")
        # Implementation would depend on current active viewer

    def _validate_installation(self) -> None:
        """Validate AreTomo3 installation."""
        try:
            from ...core.config.config_validation import validate_aretomo_installation

            # Use the existing validation function
            logger.info("Validating AreTomo3 installation...")
            # Implementation would show validation results
        except Exception as e:
            logger.error(f"Error validating installation: {e}")

    def _show_system_info(self) -> None:
        """Show system information dialog."""
        try:
            import platform

            import psutil
            from PyQt6.QtWidgets import QMessageBox

            info = f"""System Information:
OS: {platform.system()} {platform.release()} {platform.version()}
Architecture: {platform.machine()}
Processor: {platform.processor()}
Python: {platform.python_version()}
CPU Count: {psutil.cpu_count(logical=False)} physical, {psutil.cpu_count()} logical
Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB total
Available Memory: {psutil.virtual_memory().available / (1024**3):.1f} GB
"""
            QMessageBox.information(self.main_window, "System Information", info)
        except Exception as e:
            logger.error(f"Error showing system info: {e}")

    def _show_gpu_info(self) -> None:
        """Show GPU information dialog."""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # This would integrate with GPU monitoring
            info = "GPU information would be displayed here"
            QMessageBox.information(self.main_window, "GPU Information", info)
        except Exception as e:
            logger.error(f"Error showing GPU info: {e}")

    def _show_documentation(self) -> None:
        """Show documentation."""
        import webbrowser

        try:
            webbrowser.open("https://github.com/YourRepo/AreTomo3-GUI/wiki")
            logger.info("Opening documentation in web browser")
        except Exception as e:
            logger.error(f"Error opening documentation: {e}")

    def _show_shortcuts(self) -> None:
        """Show keyboard shortcuts dialog."""
        try:
            from PyQt6.QtWidgets import QMessageBox

            shortcuts = """Keyboard Shortcuts:

File Operations:
Ctrl+N - New Project
Ctrl+O - Open File
Ctrl+S - Save
Ctrl+Shift+S - Save As
Ctrl+Q - Exit

View:
F11 - Toggle Tabs
Ctrl++ - Zoom In
Ctrl+- - Zoom Out
Ctrl+0 - Reset Zoom

Help:
F1 - Documentation
"""
            QMessageBox.information(self.main_window, "Keyboard Shortcuts", shortcuts)
        except Exception as e:
            logger.error(f"Error showing shortcuts: {e}")
