#!/usr/bin/env python3
"""
Project Management Widget for AreTomo3 GUI.

This widget provides functionality to save and load complete project sessions,
including all processing parameters, file browser states, and results.
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from PyQt6.QtCore import Qt, QThread, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QPalette, QPixmap
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QFormLayout,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QScrollArea,
    QSizePolicy,
    QSpacerItem,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    Q<PERSON>abWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class ProjectLoadWorker(QThread):
    """Worker thread for loading project sessions."""

    progress_updated = pyqtSignal(int, str)
    session_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, session_file: str):
        """Initialize the instance."""
        super().__init__()
        self.session_file = session_file

    def run(self):
        """Execute run operation."""
        try:
            self.progress_updated.emit(10, "Reading session file...")

            with open(self.session_file, "r") as f:
                session_data = json.load(f)

            self.progress_updated.emit(30, "Validating session data...")

            # Validate required fields
            required_fields = ["metadata", "gui_state", "processing_parameters"]
            for field in required_fields:
                if field not in session_data:
                    raise ValueError(f"Missing required field: {field}")

            self.progress_updated.emit(60, "Checking file paths...")

            # Check if referenced files still exist
            file_checks = {}
            gui_state = session_data.get("gui_state", {})
            if "input_directory" in gui_state:
                input_dir = gui_state["input_directory"]
                file_checks["input_directory"] = (
                    os.path.exists(input_dir) if input_dir else False
                )

            if "output_directory" in gui_state:
                output_dir = gui_state["output_directory"]
                file_checks["output_directory"] = (
                    os.path.exists(output_dir) if output_dir else False
                )

            session_data["file_checks"] = file_checks

            self.progress_updated.emit(90, "Session loaded successfully")
            self.session_loaded.emit(session_data)

        except Exception as e:
            logger.error(f"Error loading session: {e}")
            self.error_occurred.emit(str(e))

    # TODO: Refactor class - Class 'ProjectManagementWidget' too long (1256 lines)


class ProjectManagementWidget(QWidget):
    """Widget for managing project sessions and file persistence."""

    # Signals
    session_saved = pyqtSignal(str)  # session_file_path
    session_loaded = pyqtSignal(dict)  # session_data
    project_opened = pyqtSignal(str)  # project_directory

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.parent_window = parent
        self.current_session_file = None
        self.recent_sessions = []
        self.auto_save_enabled = True
        self.load_worker = None

        # Session storage directory
        self.sessions_dir = Path.home() / ".aretomo3" / "sessions"
        self.sessions_dir.mkdir(parents=True, exist_ok=True)

        self.setup_ui()
        self.load_recent_sessions()

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # Create main splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Session Management
        left_panel = self.create_session_management_panel()
        main_splitter.addWidget(left_panel)

        # Right side - Project Details and Settings
        right_panel = self.create_project_details_panel()
        main_splitter.addWidget(right_panel)

        # Set splitter proportions (60% left, 40% right)
        main_splitter.setStretchFactor(0, 6)
        main_splitter.setStretchFactor(1, 4)

        layout.addWidget(main_splitter)

    # TODO: Refactor function - Function 'create_session_management_panel' too long (101 lines)
    def create_session_management_panel(self):
        """Create the session management panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # Session Actions
        actions_group = QGroupBox("Session Management")
        actions_layout = QGridLayout()
        actions_layout.setSpacing(10)

        # Quick Actions Row 1
        new_btn = QPushButton("🆕 New Project")
        new_btn.setToolTip("Create a new project session")
        new_btn.clicked.connect(self.new_session)
        new_btn.setMinimumHeight(35)

        save_btn = QPushButton("💾 Save Session")
        save_btn.setToolTip("Save current session state")
        save_btn.clicked.connect(self.save_current_session)
        save_btn.setMinimumHeight(35)

        save_as_btn = QPushButton("💾 Save As...")
        save_as_btn.setToolTip("Save session with new name")
        save_as_btn.clicked.connect(self.save_session_as)
        save_as_btn.setMinimumHeight(35)

        # Quick Actions Row 2
        load_btn = QPushButton("📂 Load Session")
        load_btn.setToolTip("Load an existing session")
        load_btn.clicked.connect(self.load_session)
        load_btn.setMinimumHeight(35)

        export_btn = QPushButton("📤 Export Project")
        export_btn.setToolTip("Export project with all files")
        export_btn.clicked.connect(self.export_project)
        export_btn.setMinimumHeight(35)

        import_btn = QPushButton("📥 Import Project")
        import_btn.setToolTip("Import a complete project")
        import_btn.clicked.connect(self.import_project)
        import_btn.setMinimumHeight(35)

        # Arrange buttons in grid
        actions_layout.addWidget(new_btn, 0, 0)
        actions_layout.addWidget(save_btn, 0, 1)
        actions_layout.addWidget(save_as_btn, 0, 2)
        actions_layout.addWidget(load_btn, 1, 0)
        actions_layout.addWidget(export_btn, 1, 1)
        actions_layout.addWidget(import_btn, 1, 2)

        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)

        # Recent Sessions
        recent_group = QGroupBox("Recent Sessions")
        recent_layout = QVBoxLayout()

        self.recent_list = QListWidget()
        self.recent_list.setMinimumHeight(200)
        self.recent_list.itemDoubleClicked.connect(self.load_recent_session)
        self.recent_list.setToolTip("Double-click to load a recent session")
        recent_layout.addWidget(self.recent_list)

        # Recent sessions controls
        recent_controls = QHBoxLayout()
        refresh_recent_btn = QPushButton("🔄 Refresh")
        refresh_recent_btn.clicked.connect(self.load_recent_sessions)
        clear_recent_btn = QPushButton("🗑️ Clear")
        clear_recent_btn.clicked.connect(self.clear_recent_sessions)

        recent_controls.addWidget(refresh_recent_btn)
        recent_controls.addWidget(clear_recent_btn)
        recent_controls.addStretch()

        recent_layout.addLayout(recent_controls)
        recent_group.setLayout(recent_layout)
        layout.addWidget(recent_group)

        # Auto-save Settings
        autosave_group = QGroupBox("Auto-Save Settings")
        autosave_layout = QFormLayout()

        self.auto_save_check = QCheckBox("Enable Auto-Save")
        self.auto_save_check.setChecked(self.auto_save_enabled)
        self.auto_save_check.toggled.connect(self.toggle_auto_save)
        autosave_layout.addRow("", self.auto_save_check)

        self.auto_save_interval = QComboBox()
        self.auto_save_interval.addItems(
            ["1 minute", "5 minutes", "10 minutes", "30 minutes"]
        )
        self.auto_save_interval.setCurrentText("5 minutes")
        autosave_layout.addRow("Interval:", self.auto_save_interval)

        autosave_group.setLayout(autosave_layout)
        layout.addWidget(autosave_group)

        layout.addStretch()

        return panel

    def create_project_details_panel(self):
        """Create the project details panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # Create tabbed interface for project details
        tabs = QTabWidget()

        # Session Info Tab
        info_tab = self.create_session_info_tab()
        tabs.addTab(info_tab, "📋 Session Info")

        # File Status Tab
        files_tab = self.create_file_status_tab()
        tabs.addTab(files_tab, "📁 Files")

        # Processing History Tab
        history_tab = self.create_processing_history_tab()
        tabs.addTab(history_tab, "📊 History")

        # Settings Tab
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "⚙️ Settings")

        layout.addWidget(tabs)

        return panel

    # TODO: Refactor function - Function 'create_session_info_tab' too long (61 lines)
    def create_session_info_tab(self):
        """Create the session information tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)

        # Current Session Info
        info_group = QGroupBox("Current Session")
        info_layout = QFormLayout()

        self.session_name_label = QLabel("No session loaded")
        self.session_name_label.setStyleSheet("font-weight: bold; color: #2e7d32;")
        info_layout.addRow("Name:", self.session_name_label)

        self.session_path_label = QLabel("N/A")
        self.session_path_label.setWordWrap(True)
        info_layout.addRow("Path:", self.session_path_label)

        self.session_created_label = QLabel("N/A")
        info_layout.addRow("Created:", self.session_created_label)

        self.session_modified_label = QLabel("N/A")
        info_layout.addRow("Modified:", self.session_modified_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # Session Description
        desc_group = QGroupBox("Description")
        desc_layout = QVBoxLayout()

        self.session_description = QTextEdit()
        self.session_description.setMaximumHeight(100)
        self.session_description.setPlaceholderText("Enter session description...")
        desc_layout.addWidget(self.session_description)

        desc_group.setLayout(desc_layout)
        layout.addWidget(desc_group)

        # Project Statistics
        stats_group = QGroupBox("Project Statistics")
        stats_layout = QFormLayout()

        self.total_files_label = QLabel("0")
        stats_layout.addRow("Total Files:", self.total_files_label)

        self.processed_files_label = QLabel("0")
        stats_layout.addRow("Processed Files:", self.processed_files_label)

        self.processing_time_label = QLabel("00:00:00")
        stats_layout.addRow("Total Processing Time:", self.processing_time_label)

        self.session_size_label = QLabel("0 MB")
        stats_layout.addRow("Session Size:", self.session_size_label)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        layout.addStretch()

        return tab

    def create_file_status_tab(self):
        """Create the file status tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # File Status Table
        self.file_status_table = QTableWidget()
        self.file_status_table.setColumnCount(4)
        self.file_status_table.setHorizontalHeaderLabels(
            ["File", "Status", "Size", "Modified"]
        )

        # Make table headers stretch
        header = self.file_status_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        layout.addWidget(self.file_status_table)

        # File controls
        file_controls = QHBoxLayout()
        refresh_files_btn = QPushButton("🔄 Refresh")
        refresh_files_btn.clicked.connect(self.refresh_file_status)

        check_paths_btn = QPushButton("🔍 Check Paths")
        check_paths_btn.clicked.connect(self.check_file_paths)

        file_controls.addWidget(refresh_files_btn)
        file_controls.addWidget(check_paths_btn)
        file_controls.addStretch()

        layout.addLayout(file_controls)

        return tab

    def create_processing_history_tab(self):
        """Create the processing history tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Processing History List
        self.history_list = QListWidget()
        self.history_list.setMinimumHeight(150)
        layout.addWidget(self.history_list)

        # History controls
        history_controls = QHBoxLayout()
        clear_history_btn = QPushButton("🗑️ Clear History")
        clear_history_btn.clicked.connect(self.clear_processing_history)

        export_history_btn = QPushButton("📤 Export History")
        export_history_btn.clicked.connect(self.export_processing_history)

        history_controls.addWidget(clear_history_btn)
        history_controls.addWidget(export_history_btn)
        history_controls.addStretch()

        layout.addLayout(history_controls)

        return tab

    # TODO: Refactor function - Function 'create_settings_tab' too long (55 lines)
    def create_settings_tab(self):
        """Create the settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)

        # Session Storage Settings
        storage_group = QGroupBox("Session Storage")
        storage_layout = QFormLayout()

        self.sessions_dir_label = QLabel(str(self.sessions_dir))
        self.sessions_dir_label.setWordWrap(True)
        storage_layout.addRow("Sessions Directory:", self.sessions_dir_label)

        change_dir_btn = QPushButton("Change...")
        change_dir_btn.clicked.connect(self.change_sessions_directory)
        storage_layout.addRow("", change_dir_btn)

        storage_group.setLayout(storage_layout)
        layout.addWidget(storage_group)

        # Backup Settings
        backup_group = QGroupBox("Backup Settings")
        backup_layout = QFormLayout()

        self.backup_enabled_check = QCheckBox("Enable Automatic Backups")
        self.backup_enabled_check.setChecked(True)
        backup_layout.addRow("", self.backup_enabled_check)

        self.backup_count = QComboBox()
        self.backup_count.addItems(["5", "10", "20", "50"])
        self.backup_count.setCurrentText("10")
        backup_layout.addRow("Keep Backups:", self.backup_count)

        backup_group.setLayout(backup_layout)
        layout.addWidget(backup_group)

        # Import/Export Settings
        import_export_group = QGroupBox("Import/Export")
        import_export_layout = QVBoxLayout()

        self.include_results_check = QCheckBox("Include processing results in exports")
        self.include_results_check.setChecked(True)
        import_export_layout.addWidget(self.include_results_check)

        self.compress_exports_check = QCheckBox("Compress exported projects")
        self.compress_exports_check.setChecked(True)
        import_export_layout.addWidget(self.compress_exports_check)

        import_export_group.setLayout(import_export_layout)
        layout.addWidget(import_export_group)

        layout.addStretch()

        return tab

    # TODO: Refactor function - Function 'new_session' too long (67 lines)
    def new_session(self):
        """Create a new project session."""
        try:
            # Prompt for session name
            from PyQt6.QtWidgets import QInputDialog

            name, ok = QInputDialog.getText(
                self,
                "New Session",
                "Enter session name:",
                text=f"Session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

            if not ok or not name:
                return

            # Create new session data structure
            session_data = {
                "metadata": {
                    "name": name,
                    "created": datetime.now().isoformat(),
                    "modified": datetime.now().isoformat(),
                    "version": "1.0",
                    "description": "",
                    "tags": [],
                },
                "gui_state": {
                    "input_directory": "",
                    "output_directory": "",
                    "aretomo_path": "",
                    "current_tab": 0,
                    "window_geometry": None,
                    "file_browser_state": {},
                },
                "processing_parameters": {
                    "microscope_settings": {},
                    "motion_correction": {},
                    "reconstruction": {},
                    "advanced_options": {},
                },
                "batch_queue": [],
                "processing_history": [],
                "file_list": [],
                "results": {},
            }

            # Save new session
            session_file = self.sessions_dir / f"{name}.json"
            self.save_session_data(session_data, session_file)

            # Update UI
            self.current_session_file = session_file
            self.update_session_info(session_data)
            self.load_recent_sessions()

            self.session_saved.emit(str(session_file))

            logger.info(f"Created new session: {name}")
            QMessageBox.information(
                self, "Success", f"New session '{name}' created successfully!"
            )

        except Exception as e:
            logger.error(f"Error creating new session: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to create new session:\n{str(e)}"
            )

    def save_current_session(self):
        """Save the current session state."""
        if not self.current_session_file:
            self.save_session_as()
            return

        try:
            session_data = self.collect_current_session_data()
            self.save_session_data(session_data, self.current_session_file)

            self.session_saved.emit(str(self.current_session_file))
            logger.info(f"Session saved: {self.current_session_file}")

        except Exception as e:
            logger.error(f"Error saving session: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save session:\n{str(e)}")

    def save_session_as(self):
        """Save session with a new name."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Session As",
                str(self.sessions_dir),
                "Session Files (*.json);;All Files (*)",
            )

            if not file_path:
                return

            session_data = self.collect_current_session_data()

            # Update metadata
            session_data["metadata"]["modified"] = datetime.now().isoformat()
            session_data["metadata"]["name"] = Path(file_path).stem

            self.save_session_data(session_data, file_path)

            self.current_session_file = file_path
            self.update_session_info(session_data)
            self.load_recent_sessions()

            self.session_saved.emit(file_path)
            logger.info(f"Session saved as: {file_path}")

        except Exception as e:
            logger.error(f"Error saving session as: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save session:\n{str(e)}")

    def load_session(self):
        """Load an existing session."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Load Session",
                str(self.sessions_dir),
                "Session Files (*.json);;All Files (*)",
            )

            if not file_path:
                return

            self.load_session_file(file_path)

        except Exception as e:
            logger.error(f"Error loading session: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load session:\n{str(e)}")

    def load_session_file(self, file_path: str):
        """Load session from file path."""
        try:
            # Create and start load worker
            self.load_worker = ProjectLoadWorker(file_path)
            self.load_worker.progress_updated.connect(self.on_load_progress)
            self.load_worker.session_loaded.connect(self.on_session_loaded)
            self.load_worker.error_occurred.connect(self.on_load_error)

            # Show progress dialog
            from PyQt6.QtWidgets import QProgressDialog

            self.load_progress = QProgressDialog(
                "Loading session...", "Cancel", 0, 100, self
            )
            self.load_progress.setWindowModality(Qt.WindowModality.WindowModal)
            self.load_progress.setMinimumDuration(0)

            self.load_worker.start()

        except Exception as e:
            logger.error(f"Error starting session load: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to start session load:\n{str(e)}"
            )

    @pyqtSlot(int, str)
    def on_load_progress(self, value: int, message: str):
        """Handle load progress update."""
        if hasattr(self, "load_progress"):
            self.load_progress.setValue(value)
            self.load_progress.setLabelText(message)

    @pyqtSlot(dict)
    def on_session_loaded(self, session_data: dict):
        """Handle successful session load."""
        try:
            if hasattr(self, "load_progress"):
                self.load_progress.close()

            # Update current session
            self.current_session_file = self.load_worker.session_file

            # Update UI
            self.update_session_info(session_data)
            self.update_file_status_from_session(session_data)
            self.add_to_recent_sessions(self.current_session_file)

            # Emit signal for main window to restore state
            self.session_loaded.emit(session_data)

            logger.info(f"Session loaded successfully: {self.current_session_file}")

            # Check for missing files
            file_checks = session_data.get("file_checks", {})
            missing_files = [path for path, exists in file_checks.items() if not exists]

            if missing_files:
                QMessageBox.warning(
                    self,
                    "Missing Files",
                    f"Some files referenced in the session are missing:\n"
                    + "\n".join(missing_files)
                    + "\n\nThe session has been loaded, but you may need to update file paths.",
                )
            else:
                QMessageBox.information(self, "Success", "Session loaded successfully!")

        except Exception as e:
            logger.error(f"Error handling loaded session: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to process loaded session:\n{str(e)}"
            )

    @pyqtSlot(str)
    def on_load_error(self, error_message: str):
        """Handle session load error."""
        if hasattr(self, "load_progress"):
            self.load_progress.close()

        QMessageBox.critical(
            self, "Load Error", f"Failed to load session:\n{error_message}"
        )

    def load_recent_session(self, item: QListWidgetItem):
        """Load a session from the recent sessions list."""
        session_path = item.data(Qt.ItemDataRole.UserRole)
        if session_path and os.path.exists(session_path):
            self.load_session_file(session_path)
        else:
            QMessageBox.warning(
                self, "File Not Found", f"Session file not found:\n{session_path}"
            )
            self.load_recent_sessions()  # Refresh the list

    def export_project(self):
        """Export complete project with all files."""
        try:
            if not self.current_session_file:
                QMessageBox.warning(
                    self, "No Session", "Please save a session first before exporting."
                )
                return

            # Choose export location
            export_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Project",
                f"{Path(self.current_session_file).stem}_export.zip",
                "ZIP Archives (*.zip);;All Files (*)",
            )

            if not export_path:
                return

            # Implementation for project export would go here
            # This would include copying all files and creating a portable project
            QMessageBox.information(
                self,
                "Export",
                "Project export functionality will be implemented in the next update.",
            )

        except Exception as e:
            logger.error(f"Error exporting project: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export project:\n{str(e)}")

    def import_project(self):
        """Import a complete project."""
        try:
            # Choose import file
            import_path, _ = QFileDialog.getOpenFileName(
                self,
                "Import Project",
                "",
                "ZIP Archives (*.zip);;Session Files (*.json);;All Files (*)",
            )

            if not import_path:
                return

            # Implementation for project import would go here
            QMessageBox.information(
                self,
                "Import",
                "Project import functionality will be implemented in the next update.",
            )

        except Exception as e:
            logger.error(f"Error importing project: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import project:\n{str(e)}")

    # TODO: Refactor function - Function 'collect_current_session_data' too long (90 lines)
    def collect_current_session_data(self) -> dict:
        """Collect current session data from the main window."""
        try:
            if not self.parent_window:
                raise ValueError("No parent window available")

            # Get current state from main window
            parent = self.parent_window

            # Use main window's state collection method if available
            gui_state = {}
            if hasattr(parent, "collect_current_gui_state"):
                gui_state = parent.collect_current_gui_state()
            else:
                # Fallback to direct access
                gui_state = {
                    "input_directory": (
                        getattr(parent, "input_dir", QLineEdit()).text()
                        if hasattr(parent, "input_dir")
                        else ""
                    ),
                    "output_directory": (
                        getattr(parent, "output_dir", QLineEdit()).text()
                        if hasattr(parent, "output_dir")
                        else ""
                    ),
                    "aretomo_path": (
                        getattr(parent, "aretomo_path", QLineEdit()).text()
                        if hasattr(parent, "aretomo_path")
                        else ""
                    ),
                    "current_tab": (
                        parent.tabs.currentIndex() if hasattr(parent, "tabs") else 0
                    ),
                    "window_geometry": (
                        bytes(parent.saveGeometry()).hex()
                        if hasattr(parent, "saveGeometry")
                        else None
                    ),
                }

            # Add file browser state
            gui_state["file_browser_state"] = self.get_file_browser_state()

            session_data = {
                "metadata": {
                    "name": (
                        Path(self.current_session_file).stem
                        if self.current_session_file
                        else "Untitled"
                    ),
                    "created": datetime.now().isoformat(),
                    "modified": datetime.now().isoformat(),
                    "version": "1.0",
                    "description": self.session_description.toPlainText(),
                    "tags": [],
                },
                "gui_state": gui_state,
                "processing_parameters": self.get_processing_parameters(),
                "batch_queue": (
                    getattr(parent, "batch_queue", [])
                    if hasattr(parent, "batch_queue")
                    else []
                ),
                "processing_history": self.get_processing_history(),
                "file_list": self.get_current_file_list(),
                "results": {},
            }

            return session_data

        except Exception as e:
            logger.error(f"Error collecting session data: {e}")
            # Return minimal session data
            return {
                "metadata": {
                    "name": "Recovery Session",
                    "created": datetime.now().isoformat(),
                    "modified": datetime.now().isoformat(),
                    "version": "1.0",
                    "description": "Recovered session data",
                    "tags": [],
                },
                "gui_state": {},
                "processing_parameters": {},
                "batch_queue": [],
                "processing_history": [],
                "file_list": [],
                "results": {},
            }

    def get_file_browser_state(self) -> dict:
        """Get current file browser state."""
        try:
            if (
                hasattr(self.parent_window, "file_browser")
                and self.parent_window.file_browser
            ):
                browser = self.parent_window.file_browser
                return {
                    "current_directory": getattr(browser, "current_directory", ""),
                    "selected_files": getattr(browser, "selected_files", []),
                    "view_mode": getattr(browser, "view_mode", "list"),
                    "sort_column": 0,
                    "sort_order": 0,
                }
        except Exception as e:
            logger.error(f"Error getting file browser state: {e}")

        return {}

    # TODO: Refactor function - Function 'get_processing_parameters' too long (102 lines)
    def get_processing_parameters(self) -> dict:
        """Get current processing parameters."""
        try:
            if not self.parent_window:
                return {}

            parent = self.parent_window

            # Collect parameters from various UI elements
            params = {
                "microscope_settings": {
                    "pixel_size": (
                        getattr(parent, "pixel_size", QLineEdit()).text()
                        if hasattr(parent, "pixel_size")
                        else ""
                    ),
                    "voltage": (
                        getattr(parent, "voltage", QLineEdit()).text()
                        if hasattr(parent, "voltage")
                        else ""
                    ),
                    "cs": (
                        getattr(parent, "cs", QLineEdit()).text()
                        if hasattr(parent, "cs")
                        else ""
                    ),
                    "amp_contrast": (
                        getattr(parent, "amp_contrast", QLineEdit()).text()
                        if hasattr(parent, "amp_contrast")
                        else ""
                    ),
                    "frame_dose": (
                        getattr(parent, "frame_dose", QLineEdit()).text()
                        if hasattr(parent, "frame_dose")
                        else ""
                    ),
                    "tilt_axis": (
                        getattr(parent, "tilt_axis", QLineEdit()).text()
                        if hasattr(parent, "tilt_axis")
                        else ""
                    ),
                },
                "motion_correction": {
                    "mc_bin": (
                        getattr(parent, "mc_bin", QLineEdit()).text()
                        if hasattr(parent, "mc_bin")
                        else ""
                    ),
                    "mc_patch_x": (
                        getattr(parent, "mc_patch_x", QLineEdit()).text()
                        if hasattr(parent, "mc_patch_x")
                        else ""
                    ),
                    "mc_patch_y": (
                        getattr(parent, "mc_patch_y", QLineEdit()).text()
                        if hasattr(parent, "mc_patch_y")
                        else ""
                    ),
                    "fm_int": (
                        getattr(parent, "fm_int", QLineEdit()).text()
                        if hasattr(parent, "fm_int")
                        else ""
                    ),
                },
                "reconstruction": {
                    "at_bin": (
                        getattr(parent, "at_bin", QLineEdit()).text()
                        if hasattr(parent, "at_bin")
                        else ""
                    ),
                    "volume_z": (
                        getattr(parent, "volume_z", QLineEdit()).text()
                        if hasattr(parent, "volume_z")
                        else ""
                    ),
                    "lowpass": (
                        getattr(parent, "lowpass", QLineEdit()).text()
                        if hasattr(parent, "lowpass")
                        else ""
                    ),
                    "dark_tol": (
                        getattr(parent, "dark_tol", QLineEdit()).text()
                        if hasattr(parent, "dark_tol")
                        else ""
                    ),
                },
                "advanced_options": {},
            }

            # Get advanced settings if available
            if hasattr(parent, "advanced_tab") and parent.advanced_tab:
                try:
                    advanced_settings = parent.advanced_tab.get_settings()
                    params["advanced_options"] = advanced_settings
                except Exception as e:
                    logger.error(f"Error getting advanced settings: {e}")

            return params

        except Exception as e:
            logger.error(f"Error getting processing parameters: {e}")
            return {}

    def get_processing_history(self) -> list:
        """Get processing history."""
        # This would integrate with the processing monitor
        return []

    def get_current_file_list(self) -> list:
        """Get current file list."""
        # This would get files from the file browser
        return []

    def save_session_data(self, session_data: dict, file_path: str):
        """Save session data to file."""
        try:
            with open(file_path, "w") as f:
                json.dump(session_data, f, indent=2)

            # Update metadata
            if session_data.get("metadata"):
                session_data["metadata"]["modified"] = datetime.now().isoformat()

        except Exception as e:
            raise Exception(f"Failed to save session data: {e}")

    def update_session_info(self, session_data: dict):
        """Update session info display."""
        try:
            metadata = session_data.get("metadata", {})

            self.session_name_label.setText(metadata.get("name", "Unknown"))
            self.session_path_label.setText(
                str(self.current_session_file) if self.current_session_file else "N/A"
            )
            self.session_created_label.setText(metadata.get("created", "N/A"))
            self.session_modified_label.setText(metadata.get("modified", "N/A"))
            self.session_description.setPlainText(metadata.get("description", ""))

            # Update statistics
            file_list = session_data.get("file_list", [])
            processing_history = session_data.get("processing_history", [])

            self.total_files_label.setText(str(len(file_list)))
            self.processed_files_label.setText(str(len(processing_history)))

            # Calculate session size
            if self.current_session_file and os.path.exists(self.current_session_file):
                size_mb = os.path.getsize(self.current_session_file) / (1024 * 1024)
                self.session_size_label.setText(f"{size_mb:.2f} MB")

        except Exception as e:
            logger.error(f"Error updating session info: {e}")

    # TODO: Refactor function - Function 'update_file_status_from_session' too long (63 lines)
    def update_file_status_from_session(self, session_data: dict):
        """Update file status table from session data."""
        try:
            self.file_status_table.setRowCount(0)

            gui_state = session_data.get("gui_state", {})
            file_checks = session_data.get("file_checks", {})

            # Add directories to table
            for key, path in gui_state.items():
                if key.endswith("_directory") and path:
                    row = self.file_status_table.rowCount()
                    self.file_status_table.insertRow(row)

                    # File name
                    self.file_status_table.setItem(row, 0, QTableWidgetItem(path))

                    # Status
                    exists = file_checks.get(
                        key, os.path.exists(path) if path else False
                    )
                    status = "✅ Found" if exists else "❌ Missing"
                    status_item = QTableWidgetItem(status)
                    if not exists:
                        status_item.setBackground(
                            QPalette().color(QPalette.ColorRole.Base)
                        )
                    self.file_status_table.setItem(row, 1, status_item)

                    # Size and modified (for directories, show as folder)
                    if exists and os.path.exists(path):
                        if os.path.isdir(path):
                            self.file_status_table.setItem(
                                row, 2, QTableWidgetItem("Folder")
                            )
                            modified = datetime.fromtimestamp(
                                os.path.getmtime(path)
                            ).strftime("%Y-%m-%d %H:%M")
                            self.file_status_table.setItem(
                                row, 3, QTableWidgetItem(modified)
                            )
                        else:
                            size = os.path.getsize(path)
                            size_str = (
                                f"{size / (1024*1024):.1f} MB"
                                if size > 1024 * 1024
                                else f"{size / 1024:.1f} KB"
                            )
                            self.file_status_table.setItem(
                                row, 2, QTableWidgetItem(size_str)
                            )
                            modified = datetime.fromtimestamp(
                                os.path.getmtime(path)
                            ).strftime("%Y-%m-%d %H:%M")
                            self.file_status_table.setItem(
                                row, 3, QTableWidgetItem(modified)
                            )
                    else:
                        self.file_status_table.setItem(row, 2, QTableWidgetItem("N/A"))
                        self.file_status_table.setItem(row, 3, QTableWidgetItem("N/A"))

        except Exception as e:
            logger.error(f"Error updating file status: {e}")

    def refresh_file_status(self):
        """Refresh the file status table."""
        # TODO: Implement actual file status refresh logic
        self.file_status_table.clearContents()
        self.file_status_table.setRowCount(0)
        # Optionally, repopulate the table with current file info

    # TODO: Refactor function - Function 'load_recent_sessions' too long (53 lines)
    def load_recent_sessions(self):
        """Load and display recent sessions."""
        try:
            self.recent_list.clear()

            # Get all session files
            session_files = list(self.sessions_dir.glob("*.json"))

            # Sort by modification time (newest first)
            session_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Add to list (limit to 20 most recent)
            for session_file in session_files[:20]:
                try:
                    # Read session metadata
                    with open(session_file, "r") as f:
                        session_data = json.load(f)

                    metadata = session_data.get("metadata", {})
                    name = metadata.get("name", session_file.stem)
                    modified = metadata.get("modified", "Unknown")

                    # Create list item
                    if modified != "Unknown":
                        try:
                            mod_time = datetime.fromisoformat(modified)
                            time_str = mod_time.strftime("%Y-%m-%d %H:%M")
                        except Exception:
                            time_str = modified
                    else:
                        time_str = "Unknown"

                    item_text = f"{name} ({time_str})"
                    item = QListWidgetItem(item_text)
                    item.setData(Qt.ItemDataRole.UserRole, str(session_file))
                    item.setToolTip(f"Path: {session_file}\nModified: {time_str}")

                    # Highlight current session
                    if self.current_session_file and str(session_file) == str(
                        self.current_session_file
                    ):
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)

                    self.recent_list.addItem(item)

                except Exception as e:
                    logger.error(f"Error reading session file {session_file}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error loading recent sessions: {e}")

    # TODO: Refactor function - Function '_update_recent_sessions_list' too long (82 lines)
    def _update_recent_sessions_list(self) -> None:
        """Update the recent sessions list display."""
        try:
            self.recent_list.clear()

            # Load recent sessions from settings
            self._load_recent_sessions()

            for session_file in self.recent_sessions:
                if os.path.exists(session_file):
                    try:
                        # Load session metadata
                        with open(session_file, "r") as f:
                            import json

                            session_data = json.load(f)

                        metadata = session_data.get("metadata", {})
                        session_name = metadata.get(
                            "name", os.path.splitext(os.path.basename(session_file))[0]
                        )
                        created_time = metadata.get("created", "Unknown")

                        # Format creation time
                        try:
                            if created_time != "Unknown":
                                created_dt = datetime.fromisoformat(
                                    created_time.replace("Z", "+00:00")
                                )
                                created_str = created_dt.strftime("%Y-%m-%d %H:%M")
                            else:
                                created_str = "Unknown"
                        except Exception:
                            created_str = (
                                str(created_time)[:16]
                                if created_time != "Unknown"
                                else "Unknown"
                            )

                        # Get file modification time as fallback
                        modified_time = datetime.fromtimestamp(
                            os.path.getmtime(session_file)
                        )

                        # Count files in session
                        file_count = 0
                        gui_state = session_data.get("gui_state", {})
                        tilt_series_data = session_data.get("tilt_series_data", {})

                        for series_data in tilt_series_data.values():
                            file_count += len(series_data.get("files", []))

                        # Create rich item text
                        item_text = f"📁 {session_name}\n"
                        item_text += f"   📍 {os.path.dirname(session_file)}\n"
                        item_text += f"   📄 {file_count} files in {len(tilt_series_data)} series\n"
                        item_text += f"   🕒 Created: {created_str}\n"
                        item_text += f"   📝 Modified: {modified_time.strftime('%Y-%m-%d %H:%M')}"

                        item = QListWidgetItem(item_text)
                        item.setData(Qt.ItemDataRole.UserRole, session_file)

                        # Set tooltip with full path
                        item.setToolTip(
                            f"Session file: {session_file}\nClick to load this session"
                        )

                        self.recent_list.addItem(item)

                    except Exception as e:
                        logger.error(f"Error reading session file {session_file}: {e}")
                        # Add basic item if we can't read the session
                        session_name = os.path.splitext(os.path.basename(session_file))[
                            0
                        ]
                        item_text = f"⚠️ {session_name}\n   {session_file}\n   (Error reading session)"
                        item = QListWidgetItem(item_text)
                        item.setData(Qt.ItemDataRole.UserRole, session_file)
                        self.recent_list.addItem(item)

        except Exception as e:
            logger.error(f"Error updating recent sessions list: {e}")

    def _load_recent_sessions(self) -> None:
        """Load recent sessions from settings file."""
        try:
            settings_file = self.sessions_dir / "recent_sessions.json"
            if settings_file.exists():
                with open(settings_file, "r") as f:
                    import json

                    data = json.load(f)
                    self.recent_sessions = data.get("recent_sessions", [])
            else:
                # Initialize with all existing session files
                self.recent_sessions = [
                    str(f)
                    for f in self.sessions_dir.glob("*.json")
                    if f.name != "recent_sessions.json"
                ]
                self.recent_sessions.sort(
                    key=lambda x: os.path.getmtime(x), reverse=True
                )
                self.recent_sessions = self.recent_sessions[:10]  # Keep last 10
                self._save_recent_sessions()
        except Exception as e:
            logger.error(f"Error loading recent sessions: {e}")
            self.recent_sessions = []

    def _save_recent_sessions(self) -> None:
        """Save recent sessions to settings file."""
        try:
            settings_file = self.sessions_dir / "recent_sessions.json"
            with open(settings_file, "w") as f:
                import json

                json.dump({"recent_sessions": self.recent_sessions}, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving recent sessions: {e}")

    def add_to_recent_sessions(self, session_file: str) -> None:
        """Add session to recent sessions list."""
        try:
            session_file = str(session_file)
            if session_file in self.recent_sessions:
                self.recent_sessions.remove(session_file)

            self.recent_sessions.insert(0, session_file)
            self.recent_sessions = self.recent_sessions[:10]  # Keep only last 10

            self._save_recent_sessions()
            self._update_recent_sessions_list()

        except Exception as e:
            logger.error(f"Error adding to recent sessions: {e}")

    def clear_recent_sessions(self):
        """Clear the list of recent sessions."""
        self.recent_sessions = []
        self._save_recent_sessions()
        self._update_recent_sessions_list()

    def toggle_auto_save(self, checked):
        """Enable or disable auto-save feature."""
        self.auto_save_enabled = checked

    def check_file_paths(self):
        """Check the validity of file paths in the project."""
        # TODO: Implement actual file path checking logic
        QMessageBox.information(
            self, "Check Paths", "File path check not yet implemented."
        )

    def clear_processing_history(self):
        """Clear the processing history list."""
        self.history_list.clear()
        QMessageBox.information(
            self, "Processing History", "Processing history cleared."
        )

    def export_processing_history(self):
        """Export the processing history to a file."""
        try:
            if not hasattr(self, "history_list") or self.history_list.count() == 0:
                QMessageBox.information(
                    self, "Export History", "No processing history to export."
                )
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Processing History",
                str(self.sessions_dir / "processing_history.txt"),
                "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)",
            )

            if not file_path:
                return

            with open(file_path, "w") as f:
                f.write(
                    f"AreTomo3 Processing History - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                )
                f.write("-" * 80 + "\n\n")

                for i in range(self.history_list.count()):
                    item = self.history_list.item(i)
                    f.write(f"{i+1}. {item.text()}\n")

            logger.info(f"Processing history exported to: {file_path}")
            QMessageBox.information(
                self,
                "Export Complete",
                f"Processing history has been exported to:\n{file_path}",
            )

        except Exception as e:
            logger.error(f"Error exporting processing history: {e}")
            QMessageBox.critical(
                self, "Export Error", f"Failed to export processing history:\n{str(e)}"
            )

    def change_sessions_directory(self):
        """Change the directory where sessions are stored."""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox

        dir_path = QFileDialog.getExistingDirectory(
            self, "Select Sessions Directory", str(self.sessions_dir)
        )
        if dir_path:
            self.sessions_dir = Path(dir_path)
            self.sessions_dir_label.setText(str(self.sessions_dir))
            self.sessions_dir.mkdir(parents=True, exist_ok=True)
            self.load_recent_sessions()
            QMessageBox.information(
                self,
                "Sessions Directory Changed",
                f"Sessions will now be saved in:\n{self.sessions_dir}",
            )
