#!/usr/bin/env python3
"""
Smart File Organization System
Automatic project structure creation, intelligent file naming conventions,
and metadata extraction with tagging.
"""

import hashlib
import json
import os
import re
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

try:
    import mrcfile

    MRC_AVAILABLE = True
except ImportError:
    MRC_AVAILABLE = False

try:
    from PIL import Image

    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


@dataclass
class FileMetadata:
    """Comprehensive file metadata container."""

    filename: str
    filepath: str
    size_bytes: int
    created: datetime
    modified: datetime
    file_type: str
    hash_md5: str

    # Scientific metadata
    microscope_info: Dict[str, Any]
    acquisition_params: Dict[str, Any]
    processing_history: List[Dict[str, Any]]
    quality_metrics: Dict[str, Any]
    tags: List[str]

    # Derived properties
    tilt_series_group: Optional[str] = None
    position_name: Optional[str] = None
    tilt_angle: Optional[float] = None
    defocus_value: Optional[float] = None
    dose_rate: Optional[float] = None


@dataclass
class ProjectStructure:
    """Standardized project directory structure."""

    root_path: Path
    raw_data_path: Path
    processed_data_path: Path
    analysis_path: Path
    exports_path: Path
    logs_path: Path
    metadata_path: Path
    temp_path: Path

    @classmethod
    def create_standard_structure(
        cls, root_path: Union[str, Path]
    ) -> "ProjectStructure":
        """Create a standardized project structure."""
        root = Path(root_path)

        structure = cls(
            root_path=root,
            raw_data_path=root / "01_raw_data",
            processed_data_path=root / "02_processed",
            analysis_path=root / "03_analysis",
            exports_path=root / "04_exports",
            logs_path=root / "05_logs",
            metadata_path=root / "06_metadata",
            temp_path=root / ".temp",
        )

        # Create directories
        for path in [
            structure.raw_data_path,
            structure.processed_data_path,
            structure.analysis_path,
            structure.exports_path,
            structure.logs_path,
            structure.metadata_path,
            structure.temp_path,
        ]:
            path.mkdir(parents=True, exist_ok=True)

        return structure


class MetadataExtractor:
    """Extract metadata from various file types."""

    def __init__(self):
        """Initialize the instance."""
        self.extractors = {
            ".mrc": self._extract_mrc_metadata,
            ".mrcs": self._extract_mrc_metadata,
            ".rec": self._extract_mrc_metadata,
            ".st": self._extract_mrc_metadata,
            ".mdoc": self._extract_mdoc_metadata,
            ".tif": self._extract_tiff_metadata,
            ".tiff": self._extract_tiff_metadata,
            ".dm3": self._extract_dm_metadata,
            ".dm4": self._extract_dm_metadata,
        }

    def extract_metadata(self, filepath: Union[str, Path]) -> FileMetadata:
        """Extract comprehensive metadata from a file."""
        filepath = Path(filepath)

        if not filepath.exists():
            raise FileNotFoundError(f"File not found: {filepath}")

        # Basic file info
        stat = filepath.stat()
        file_hash = self._calculate_file_hash(filepath)

        # Extract file-type specific metadata
        file_ext = filepath.suffix.lower()
        extractor = self.extractors.get(file_ext, self._extract_generic_metadata)

        scientific_metadata = extractor(filepath)

        # Create metadata object
        metadata = FileMetadata(
            filename=filepath.name,
            filepath=str(filepath.absolute()),
            size_bytes=stat.st_size,
            created=datetime.fromtimestamp(stat.st_ctime),
            modified=datetime.fromtimestamp(stat.st_mtime),
            file_type=file_ext,
            hash_md5=file_hash,
            **scientific_metadata,
        )

        return metadata

    # TODO: Refactor function - Function '_extract_mrc_metadata' too long (65 lines)
    def _extract_mrc_metadata(self, filepath: Path) -> Dict[str, Any]:
        """Extract metadata from MRC files."""
        metadata = {
            "microscope_info": {},
            "acquisition_params": {},
            "processing_history": [],
            "quality_metrics": {},
            "tags": [],
        }

        if not MRC_AVAILABLE:
            metadata["tags"].append("mrc_reader_unavailable")
            return metadata

        try:
            with mrcfile.open(filepath, mode="r", permissive=True) as mrc:
                header = mrc.header

                # Basic acquisition parameters
                metadata["acquisition_params"] = {
                    "nx": int(header.nx),
                    "ny": int(header.ny),
                    "nz": int(header.nz),
                    "pixel_size_x": (
                        float(header.cella.x / header.nx) if header.nx > 0 else 0
                    ),
                    "pixel_size_y": (
                        float(header.cella.y / header.ny) if header.ny > 0 else 0
                    ),
                    "pixel_size_z": (
                        float(header.cella.z / header.nz) if header.nz > 0 else 0
                    ),
                    "voltage": getattr(header, "voltage", None),
                    "cs": getattr(header, "cs", None),
                }

                # Extended header analysis
                if hasattr(mrc, "extended_header") and mrc.extended_header is not None:
                    metadata["tags"].append("has_extended_header")

                # Quality metrics
                data = mrc.data
                if data is not None:
                    metadata["quality_metrics"] = {
                        "mean_intensity": float(data.mean()),
                        "std_intensity": float(data.std()),
                        "min_intensity": float(data.min()),
                        "max_intensity": float(data.max()),
                        "data_type": str(data.dtype),
                    }

                # Determine file type from dimensions
                if header.nz == 1:
                    metadata["tags"].append("2d_image")
                elif header.nz > 1:
                    metadata["tags"].append("3d_volume")
                    if header.nz > 50:
                        metadata["tags"].append("tomogram")
                    else:
                        metadata["tags"].append("tilt_series")

        except Exception as e:
            metadata["tags"].append(f"mrc_error_{type(e).__name__}")

        return metadata

    def _extract_mdoc_metadata(self, filepath: Path) -> Dict[str, Any]:
        """Extract metadata from MDOC files."""
        metadata = {
            "microscope_info": {},
            "acquisition_params": {},
            "processing_history": [],
            "quality_metrics": {},
            "tags": ["mdoc", "serialem"],
        }

        try:
            mdoc_data = self._parse_mdoc_file(filepath)

            # Extract global parameters
            global_params = mdoc_data.get("global", {})
            metadata["acquisition_params"].update(global_params)

            # Extract tilt information
            tilt_sections = [
                section
                for section in mdoc_data.values()
                if isinstance(section, dict) and "TiltAngle" in section
            ]

            if tilt_sections:
                metadata["tags"].append("tilt_series")
                metadata["acquisition_params"]["num_tilts"] = len(tilt_sections)

                # Analyze tilt angles
                tilt_angles = [
                    float(section["TiltAngle"])
                    for section in tilt_sections
                    if "TiltAngle" in section
                ]
                if tilt_angles:
                    metadata["acquisition_params"]["tilt_range"] = {
                        "min": min(tilt_angles),
                        "max": max(tilt_angles),
                        "step": (
                            (max(tilt_angles) - min(tilt_angles)) / len(tilt_angles)
                            if len(tilt_angles) > 1
                            else 0
                        ),
                    }

        except Exception as e:
            metadata["tags"].append(f"mdoc_parse_error_{type(e).__name__}")

        return metadata

    def _extract_tiff_metadata(self, filepath: Path) -> Dict[str, Any]:
        """Extract metadata from TIFF files."""
        metadata = {
            "microscope_info": {},
            "acquisition_params": {},
            "processing_history": [],
            "quality_metrics": {},
            "tags": ["tiff"],
        }

        if not PIL_AVAILABLE:
            metadata["tags"].append("pil_unavailable")
            return metadata

        try:
            with Image.open(filepath) as img:
                metadata["acquisition_params"] = {
                    "width": img.width,
                    "height": img.height,
                    "mode": img.mode,
                    "format": img.format,
                }

                # Extract EXIF/TIFF tags
                if hasattr(img, "tag"):
                    metadata["microscope_info"] = dict(img.tag)

        except Exception as e:
            metadata["tags"].append(f"tiff_error_{type(e).__name__}")

        return metadata

    def _extract_dm_metadata(self, filepath: Path) -> Dict[str, Any]:
        """Extract metadata from DigitalMicrograph files."""
        metadata = {
            "microscope_info": {},
            "acquisition_params": {},
            "processing_history": [],
            "quality_metrics": {},
            "tags": ["digital_micrograph"],
        }

        # DM file parsing would require specialized library
        metadata["tags"].append("dm_parser_needed")
        return metadata

    def _extract_generic_metadata(self, filepath: Path) -> Dict[str, Any]:
        """Extract generic metadata for unknown file types."""
        return {
            "microscope_info": {},
            "acquisition_params": {},
            "processing_history": [],
            "quality_metrics": {},
            "tags": ["unknown_format"],
        }

    def _parse_mdoc_file(self, filepath: Path) -> Dict[str, Any]:
        """Parse MDOC file format."""
        data = {}
        current_section = "global"

        with open(filepath, "r", encoding="utf-8", errors="ignore") as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # Section headers
                if line.startswith("["):
                    current_section = line.strip("[]")
                    data[current_section] = {}
                    continue

                # Key-value pairs
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()

                    # Try to convert to appropriate type
                    try:
                        if "." in value:
                            value = float(value)
                        else:
                            value = int(value)
                    except ValueError:
                        pass  # Keep as string

                    if current_section not in data:
                        data[current_section] = {}
                    data[current_section][key] = value

        return data

    def _calculate_file_hash(self, filepath: Path) -> str:
        """Calculate MD5 hash of file."""
        hash_md5 = hashlib.md5()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return "hash_error"


class IntelligentFileNamer:
    """Intelligent file naming conventions."""

    def __init__(self):
        """Initialize the instance."""
        self.naming_patterns = {
            "tilt_series": "{date}_{position}_{magnification}x_{tilt_range}deg",
            "tomogram": "{date}_{position}_tomogram_{binning}bin",
            "analysis": "{date}_{position}_{analysis_type}",
            "export": "{date}_{position}_{format}_{timestamp}",
        }

    def generate_filename(
        self,
        file_type: str,
        metadata: FileMetadata,
        additional_params: Dict[str, Any] = None,
    ) -> str:
        """Generate intelligent filename based on metadata."""
        params = additional_params or {}

        # Extract relevant information from metadata
        date_str = metadata.created.strftime("%Y%m%d")
        timestamp = metadata.created.strftime("%H%M%S")

        # Try to extract position name
        position = metadata.position_name or self._extract_position_from_filename(
            metadata.filename
        )

        # Get naming pattern
        pattern = self.naming_patterns.get(file_type, "{filename}_{timestamp}")

        # Build parameters
        format_params = {
            "date": date_str,
            "timestamp": timestamp,
            "position": position,
            "filename": Path(metadata.filename).stem,
            "magnification": params.get("magnification", "unknown"),
            "binning": params.get("binning", "1"),
            "tilt_range": self._format_tilt_range(metadata),
            "analysis_type": params.get("analysis_type", "analysis"),
            "format": params.get("format", "mrc"),
        }

        # Generate filename
        try:
            filename = pattern.format(**format_params)
            # Clean up the filename
            filename = re.sub(r"[^\w\-_.]", "_", filename)
            filename = re.sub(r"_+", "_", filename)  # Remove multiple underscores
            return filename
        except KeyError as e:
            # Fallback naming
            return f"{date_str}_{position}_{timestamp}"

    def _extract_position_from_filename(self, filename: str) -> str:
        """Extract position name from filename using common patterns."""
        # Common position naming patterns
        patterns = [
            r"pos(\d+)",
            r"position(\d+)",
            r"pos_(\d+)",
            r"grid(\d+)",
            r"hole(\d+)",
            r"area(\d+)",
        ]

        filename_lower = filename.lower()
        for pattern in patterns:
            match = re.search(pattern, filename_lower)
            if match:
                return f"pos{match.group(1)}"

        # Fallback - use first part of filename
        parts = Path(filename).stem.split("_")
        return parts[0] if parts else "unknown"

    def _format_tilt_range(self, metadata: FileMetadata) -> str:
        """Format tilt range from metadata."""
        tilt_range = metadata.acquisition_params.get("tilt_range")
        if tilt_range and isinstance(tilt_range, dict):
            min_tilt = tilt_range.get("min", 0)
            max_tilt = tilt_range.get("max", 0)
            return f"{min_tilt:+.0f}to{max_tilt:+.0f}"
        return "unknown"


class SmartFileOrganizer:
    """
    Smart File Organization System

    Features:
    - Automatic project structure creation
    - Intelligent file naming conventions
    - Metadata extraction and tagging
    - File categorization and grouping
    """

    def __init__(self, project_root: Union[str, Path]):
        """Initialize the instance."""
        self.project_root = Path(project_root)
        self.metadata_extractor = MetadataExtractor()
        self.file_namer = IntelligentFileNamer()
        self.project_structure = None
        self.file_catalog: Dict[str, FileMetadata] = {}

    def initialize_project(self, project_name: str = None) -> ProjectStructure:
        """Initialize a new organized project."""
        if not project_name:
            project_name = f"at3gui_project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        project_path = self.project_root / project_name
        self.project_structure = ProjectStructure.create_standard_structure(
            project_path
        )

        # Create project info file
        project_info = {
            "name": project_name,
            "created": datetime.now().isoformat(),
            "structure_version": "1.0",
            "description": "AT3GUI organized project",
        }

        info_file = self.project_structure.metadata_path / "project_info.json"
        with open(info_file, "w") as f:
            json.dump(project_info, f, indent=2)

        return self.project_structure

    def scan_and_organize_directory(
        self, source_dir: Union[str, Path], copy_files: bool = True
    ) -> Dict[str, List[FileMetadata]]:
        """Scan directory and organize files intelligently."""
        source_dir = Path(source_dir)
        organized_files = defaultdict(list)

        if not self.project_structure:
            self.initialize_project()

        # Scan all files
        for filepath in source_dir.rglob("*"):
            if filepath.is_file() and not filepath.name.startswith("."):
                try:
                    metadata = self.metadata_extractor.extract_metadata(filepath)
                    self.file_catalog[metadata.hash_md5] = metadata

                    # Categorize file
                    category = self._categorize_file(metadata)
                    organized_files[category].append(metadata)

                    # Organize file
                    if copy_files:
                        self._organize_file(metadata, category)

                except Exception as e:
                    logger.info(f"Error processing {filepath}: {e}")

        # Save catalog
        self._save_file_catalog()

        return dict(organized_files)

    def _categorize_file(self, metadata: FileMetadata) -> str:
        """Categorize file based on metadata and tags."""
        tags = metadata.tags
        file_ext = metadata.file_type.lower()

        # Categorization logic
        if "mdoc" in tags:
            return "metadata"
        elif "tilt_series" in tags:
            return "tilt_series"
        elif "tomogram" in tags:
            return "tomograms"
        elif "2d_image" in tags:
            return "images"
        elif file_ext in [".log", ".txt"]:
            return "logs"
        elif file_ext in [".json", ".xml"]:
            return "metadata"
        else:
            return "other"

    def _organize_file(self, metadata: FileMetadata, category: str):
        """Organize file into appropriate directory."""
        if not self.project_structure:
            return

        # Determine target directory
        target_base = {
            "tilt_series": self.project_structure.raw_data_path / "tilt_series",
            "tomograms": self.project_structure.processed_data_path / "tomograms",
            "images": self.project_structure.raw_data_path / "images",
            "metadata": self.project_structure.metadata_path / "acquisition",
            "logs": self.project_structure.logs_path,
            "other": self.project_structure.raw_data_path / "other",
        }.get(category, self.project_structure.raw_data_path / "unsorted")

        target_base.mkdir(parents=True, exist_ok=True)

        # Generate organized filename
        new_filename = self.file_namer.generate_filename(category, metadata)
        if not new_filename.endswith(metadata.file_type):
            new_filename += metadata.file_type

        # Create subdirectory by position if applicable
        if metadata.position_name:
            target_dir = target_base / metadata.position_name
            target_dir.mkdir(exist_ok=True)
        else:
            target_dir = target_base

        target_path = target_dir / new_filename

        # Copy file
        try:
            import shutil

            if not target_path.exists():
                shutil.copy2(metadata.filepath, target_path)
                logger.info(
                    f"Organized: {metadata.filename} -> {target_path.relative_to(self.project_structure.root_path)}"
                )
        except Exception as e:
            logger.info(f"Error organizing {metadata.filename}: {e}")

    def _save_file_catalog(self):
        """Save file catalog to disk."""
        if not self.project_structure:
            return

        catalog_file = self.project_structure.metadata_path / "file_catalog.json"

        # Convert metadata to serializable format
        catalog_data = {}
        for file_hash, metadata in self.file_catalog.items():
            catalog_data[file_hash] = asdict(metadata)
            # Convert datetime objects to ISO strings
            catalog_data[file_hash]["created"] = metadata.created.isoformat()
            catalog_data[file_hash]["modified"] = metadata.modified.isoformat()

        with open(catalog_file, "w") as f:
            json.dump(catalog_data, f, indent=2)

    def search_files(
        self, query: str, search_tags: bool = True, search_filenames: bool = True
    ) -> List[FileMetadata]:
        """Search files by various criteria."""
        results = []
        query_lower = query.lower()

        for metadata in self.file_catalog.values():
            match = False

            if search_filenames and query_lower in metadata.filename.lower():
                match = True

            if search_tags and any(query_lower in tag.lower() for tag in metadata.tags):
                match = True

            if match:
                results.append(metadata)

        return results

    def generate_organization_report(self) -> Dict[str, Any]:
        """Generate comprehensive organization report."""
        if not self.file_catalog:
            return {"error": "No files cataloged"}

        # Statistics
        total_files = len(self.file_catalog)
        total_size = sum(m.size_bytes for m in self.file_catalog.values())

        # Category breakdown
        categories = defaultdict(int)
        for metadata in self.file_catalog.values():
            category = self._categorize_file(metadata)
            categories[category] += 1

        # File type breakdown
        file_types = defaultdict(int)
        for metadata in self.file_catalog.values():
            file_types[metadata.file_type] += 1

        # Tag analysis
        all_tags = []
        for metadata in self.file_catalog.values():
            all_tags.extend(metadata.tags)
        tag_counts = defaultdict(int)
        for tag in all_tags:
            tag_counts[tag] += 1

        return {
            "summary": {
                "total_files": total_files,
                "total_size_gb": total_size / (1024**3),
                "organization_date": datetime.now().isoformat(),
            },
            "categories": dict(categories),
            "file_types": dict(file_types),
            "top_tags": dict(
                sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            ),
            "project_structure": (
                str(self.project_structure.root_path)
                if self.project_structure
                else None
            ),
        }


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    organizer = SmartFileOrganizer("/tmp/test_organization")

    # Initialize project
    project = organizer.initialize_project("example_tomography_project")
    logger.info(f"Created project structure at: {project.root_path}")

    # Example of organizing files (would scan actual directory)
    # organized = organizer.scan_and_organize_directory("/path/to/data")

    # Generate report
    report = organizer.generate_organization_report()
    logger.info("Organization Report:")
    logger.info(json.dumps(report, indent=2))
