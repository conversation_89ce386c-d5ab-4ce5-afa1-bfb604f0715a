#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher with Qt environment setup.
"""

import os
import sys

# Force PyQt6 usage
os.environ['QT_API'] = 'pyqt6'
os.environ['NAPARI_QT_BACKEND'] = 'pyqt6'

# Import and run the main application
try:
    from aretomo3_gui.main import main
    sys.exit(main())
except Exception as e:
    print(f"Error launching AreTomo3 GUI: {e}")
    print("\nTroubleshooting:")
    print("1. Ensure PyQt6 is installed: pip install PyQt6")
    print("2. Check system requirements")
    print("3. Try running in a clean environment")
    sys.exit(1)
