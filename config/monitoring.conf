# AreTomo3 GUI Professional Edition - Performance Monitoring Configuration
# This file configures comprehensive performance monitoring and alerting

[monitoring]
# Enable/disable monitoring
enabled = true

# Performance logging
log_performance = true
log_file = logs/performance.log
log_level = INFO
log_rotation = daily
log_retention_days = 30

# Metrics collection
track_memory = true
track_cpu = true
track_disk_io = true
track_network = true
track_gpu = true

# Reporting intervals (seconds)
report_interval = 60
detailed_report_interval = 300
summary_report_interval = 3600

# Data retention
metrics_retention_hours = 168  # 7 days
detailed_retention_hours = 24  # 1 day

[thresholds]
# Memory thresholds (MB)
max_memory_mb = 2048
warning_memory_mb = 1536
critical_memory_mb = 1792

# CPU thresholds (percentage)
max_cpu_percent = 85
warning_cpu_percent = 70
critical_cpu_percent = 80

# Response time thresholds (milliseconds)
max_response_time_ms = 2000
warning_response_time_ms = 1000
critical_response_time_ms = 1500

# Disk space thresholds (percentage)
max_disk_usage_percent = 90
warning_disk_usage_percent = 75
critical_disk_usage_percent = 85

# GPU thresholds (percentage)
max_gpu_usage_percent = 95
warning_gpu_usage_percent = 80
critical_gpu_usage_percent = 90

[alerts]
# Alert mechanisms
email_notifications = false
log_alerts = true
system_notifications = true
webhook_alerts = false

# Alert settings
alert_cooldown_minutes = 15
max_alerts_per_hour = 10
escalation_enabled = true

# Email configuration (if enabled)
smtp_server = smtp.example.com
smtp_port = 587
smtp_username = <EMAIL>
smtp_password =
alert_recipients = <EMAIL>,<EMAIL>

# Webhook configuration (if enabled)
webhook_url = https://hooks.example.com/alerts
webhook_timeout_seconds = 10
webhook_retry_attempts = 3
