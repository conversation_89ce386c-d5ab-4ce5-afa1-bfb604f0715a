# AreTomo3 GUI - Compressed Packages

## Overview
This directory contains different package formats for AreTomo3 GUI distribution.

## Package Types

### 1. Minimal Package (`*_minimal_*.zip`)
- **Purpose**: Source code only for developers
- **Contents**: Essential source files, setup scripts, documentation
- **Use case**: Development, custom installations, building from source
- **Installation**: Extract and run `pip install .`

### 2. Installed Package (`*_installed_*.zip`)  
- **Purpose**: Complete ready-to-run installation
- **Contents**: Virtual environment with AreTomo3 GUI pre-installed
- **Use case**: End users who want immediate usage
- **Installation**: Extract and run launcher scripts

### 3. Distribution Package (`*_distribution_*.zip`)
- **Purpose**: Professional distribution with wheel package
- **Contents**: Wheel file, installation scripts, documentation
- **Use case**: System administrators, package managers
- **Installation**: Extract and run installation scripts

## Quick Start Guide

### For Developers (Minimal Package)
```bash
unzip aretomo3_gui_minimal_*.zip
cd minimal_package/
pip install .
export QT_API=pyqt6
aretomo3-gui
```

### For End Users (Installed Package)
```bash
unzip aretomo3_gui_installed_*.zip
cd AT3GUI_installed/
python launch_aretomo3_gui.py
```

### For System Installation (Distribution Package)
```bash
unzip aretomo3_gui_distribution_*.zip
cd aretomo3_gui_distribution_*/
pip install *.whl
python aretomo3_gui_launcher.py
```

## System Requirements
- Python 3.8 or higher
- 4GB RAM (8GB recommended)
- Graphics card with OpenGL support
- 2GB free disk space

## Package Verification
Each package includes verification tools to test the installation.

## Support
See individual package README files for detailed instructions and troubleshooting.
