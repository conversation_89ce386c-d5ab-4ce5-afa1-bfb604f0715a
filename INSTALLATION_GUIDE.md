# AreTomo3 GUI Installation Guide

## Overview
This guide provides comprehensive instructions for installing AreTomo3 GUI on a fresh system.

## System Requirements

### Minimum Requirements
- **Operating System**: Linux, macOS, or Windows
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Graphics**: OpenGL-compatible graphics card (recommended)

### Python Dependencies
The package automatically installs the following dependencies:
- PyQt6 (GUI framework)
- numpy (numerical computing)
- matplotlib (plotting)
- mrcfile (MRC file handling)
- scipy (scientific computing)
- h5py (HDF5 file support)
- napari (image visualization)
- And other scientific computing libraries

## Installation Methods

### Method 1: Install from Wheel (Recommended)

1. **Download the wheel file**:
   ```bash
   # The wheel file is located in the dist/ directory
   # aretomo3_gui-1.0.0-py3-none-any.whl
   ```

2. **Install the package**:
   ```bash
   pip install aretomo3_gui-1.0.0-py3-none-any.whl
   ```

3. **Set Qt environment** (Important for GUI functionality):
   ```bash
   export QT_API=pyqt6
   export NAPARI_QT_BACKEND=pyqt6
   ```

4. **Verify installation**:
   ```bash
   aretomo3-gui --version
   ```

### Method 2: Install from Source

1. **Clone or download the source code**
2. **Navigate to the project directory**:
   ```bash
   cd AT3GUI_devel
   ```

3. **Install in development mode**:
   ```bash
   pip install -e .
   ```

4. **Set Qt environment**:
   ```bash
   export QT_API=pyqt6
   ```

### Method 3: Using the Launcher Script

1. **Use the provided launcher** (automatically sets Qt environment):
   ```bash
   python aretomo3_gui_launcher.py
   ```

## Quick Start

### Launch the GUI
```bash
# Method 1: Using environment variables
export QT_API=pyqt6
aretomo3-gui

# Method 2: Using the launcher
python aretomo3_gui_launcher.py

# Method 3: One-line command
QT_API=pyqt6 aretomo3-gui
```

### Basic Usage
1. Launch the application using one of the methods above
2. Load your MRC files through the File menu
3. Configure processing parameters
4. Start processing
5. View results in the integrated viewer

## Troubleshooting

### Common Issues

#### 1. PyQt5/PyQt6 Conflicts
**Error**: `RuntimeError: Refusing to import PyQt5 because PyQt6.QtCore is already imported`

**Solution**:
```bash
# Remove PyQt5 to avoid conflicts
pip uninstall PyQt5 PyQt5-Qt5 PyQt5-sip -y

# Set Qt environment
export QT_API=pyqt6
export NAPARI_QT_BACKEND=pyqt6

# Launch application
aretomo3-gui
```

#### 2. Entry Point Not Found
**Error**: `aretomo3-gui: command not found`

**Solution**:
```bash
# Ensure the package is installed
pip install aretomo3_gui-1.0.0-py3-none-any.whl

# Check if pip bin directory is in PATH
pip show -f aretomo3-gui

# Alternative: Use Python module execution
python -m aretomo3_gui.main
```

#### 3. Import Errors
**Error**: `ModuleNotFoundError: No module named 'aretomo3_gui'`

**Solution**:
```bash
# Reinstall the package
pip install --force-reinstall aretomo3_gui-1.0.0-py3-none-any.whl

# Verify installation
python -c "import aretomo3_gui; print('Success')"
```

#### 4. GUI Display Issues
**Error**: GUI doesn't display or crashes

**Solution**:
```bash
# For headless systems, install virtual display
sudo apt-get install xvfb  # Ubuntu/Debian
# or
sudo yum install xorg-x11-server-Xvfb  # CentOS/RHEL

# Run with virtual display
xvfb-run -a aretomo3-gui

# For remote systems, enable X11 forwarding
ssh -X username@hostname
```

### Dependency Issues

#### PyQt6 Installation Problems
```bash
# On Ubuntu/Debian
sudo apt-get install python3-pyqt6

# On CentOS/RHEL
sudo yum install python3-qt6

# On macOS with Homebrew
brew install pyqt6

# Using conda
conda install pyqt6
```

#### OpenGL Issues
```bash
# Install OpenGL libraries
# Ubuntu/Debian:
sudo apt-get install libgl1-mesa-glx libglu1-mesa

# CentOS/RHEL:
sudo yum install mesa-libGL mesa-libGLU
```

## Verification

### Test Installation
Run the verification script to ensure everything is working:

```bash
python test_basic_functionality.py
```

Expected output:
```
🧪 AreTomo3 GUI Basic Functionality Test
==================================================
✅ aretomo3_gui package imported successfully
✅ All dependencies available
✅ Configuration loaded successfully
✅ File utilities available
✅ Processing core available

🎉 All basic functionality tests PASSED!
```

### Test GUI Components
```bash
# Test with Qt environment set
QT_API=pyqt6 python -c "
from PyQt6.QtWidgets import QApplication
app = QApplication([])
print('✅ Qt GUI components working')
"
```

## Advanced Configuration

### Environment Variables
```bash
# Qt backend selection
export QT_API=pyqt6
export NAPARI_QT_BACKEND=pyqt6

# Logging level
export ARETOMO3_LOG_LEVEL=INFO

# Data directory
export ARETOMO3_DATA_DIR=/path/to/data

# Temporary directory
export ARETOMO3_TEMP_DIR=/tmp/aretomo3
```

### Configuration Files
The application creates configuration files in:
- Linux: `~/.config/aretomo3_gui/`
- macOS: `~/Library/Application Support/aretomo3_gui/`
- Windows: `%APPDATA%\aretomo3_gui\`

## Support

### Getting Help
1. Check this installation guide
2. Run the verification scripts
3. Check the application logs
4. Review the troubleshooting section

### Reporting Issues
When reporting issues, please include:
1. Operating system and version
2. Python version
3. Installation method used
4. Complete error messages
5. Output of verification scripts

## Distribution

### Creating a Distribution Package
The wheel file `aretomo3_gui-1.0.0-py3-none-any.whl` in the `dist/` directory contains everything needed for installation on a fresh system.

### Package Contents
- Source code
- Dependencies specification
- Entry points
- Configuration files
- Documentation

This package can be distributed and installed on any compatible system using the installation methods described above.
