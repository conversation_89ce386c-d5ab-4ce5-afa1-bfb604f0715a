#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AreTomo3 GUI - Professional Tomographic Reconstruction Interface

A comprehensive graphical user interface for AreTomo3 tomographic reconstruction
software, providing advanced analysis, visualization, and workflow management
capabilities for cryo-electron tomography.

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License

Usage:
    aretomo3-gui [options] [files...]

Options:
    --help, -h          Show this help message and exit
    --version, -v       Show version information and exit
    --debug             Enable debug logging
    --config FILE       Use custom configuration file
    --theme THEME       Set UI theme (dark, light, auto)
    --no-splash         Disable splash screen
    --safe-mode         Start in safe mode with minimal features

Examples:
    aretomo3-gui                    # Start with default settings
    aretomo3-gui --debug            # Start with debug logging
    aretomo3-gui data.mrc           # Open specific file
    aretomo3-gui --theme dark       # Use dark theme
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add the package directory to Python path
package_dir = Path(__file__).parent.parent
sys.path.insert(0, str(package_dir))

def setup_logging(debug=False):
    """Configure logging for the application."""
    level = logging.DEBUG if debug else logging.INFO
    format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(Path.home() / ".aretomo3_gui" / "application.log")
        ]
    )

def check_system_requirements():
    """Check system requirements and dependencies."""
    logger = logging.getLogger(__name__)
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    # Check required packages
    required_packages = {
        'PyQt6': 'PyQt6',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'mrcfile': 'mrcfile'
    }
    
    missing_packages = []
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.info("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def create_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        prog='aretomo3-gui',
        description='AreTomo3 GUI - Professional Tomographic Reconstruction Interface',
        epilog='For more information, visit: https://github.com/aretomo3/gui'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='AreTomo3 GUI v2.0.0'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        metavar='FILE',
        help='Use custom configuration file'
    )
    
    parser.add_argument(
        '--theme',
        choices=['dark', 'light', 'auto'],
        default='auto',
        help='Set UI theme (default: auto)'
    )
    
    parser.add_argument(
        '--no-splash',
        action='store_true',
        help='Disable splash screen'
    )
    
    parser.add_argument(
        '--safe-mode',
        action='store_true',
        help='Start in safe mode with minimal features'
    )
    
    parser.add_argument(
        'files',
        nargs='*',
        help='Files to open on startup'
    )
    
    return parser

def main():
    """Main application entry point."""
    # Parse command line arguments
    parser = create_parser()
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting AreTomo3 GUI v2.0.0")
    
    # Check system requirements
    if not check_system_requirements():
        logger.error("System requirements not met")
        sys.exit(1)
    
    try:
        # Import GUI components
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from aretomo3_gui.gui.main_window import AreTomoGUI
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("AreTomo3 GUI")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("AreTomo3 GUI Development Team")
        app.setOrganizationDomain("aretomo3-gui.org")
        
        # Configure high DPI support (Qt6 handles this automatically)
        # Note: AA_EnableHighDpiScaling and AA_UseHighDpiPixmaps are deprecated in Qt6
        try:
            # Only set these attributes if they exist (Qt5 compatibility)
            if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
                app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
            if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
                app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # Qt6 handles high DPI automatically, no action needed
            pass
        
        # Create main window
        main_window = AreTomoGUI()
        
        # Apply theme
        if args.theme != 'auto':
            main_window.set_theme(args.theme)
        
        # Configure safe mode
        if args.safe_mode:
            main_window.enable_safe_mode()
        
        # Load files if provided
        if args.files:
            for file_path in args.files:
                if Path(file_path).exists():
                    main_window.load_file(file_path)
                else:
                    logger.warning(f"File not found: {file_path}")
        
        # Show main window
        main_window.show()
        
        logger.info("AreTomo3 GUI started successfully")
        
        # Run application
        sys.exit(app.exec())
        
    except ImportError as e:
        logger.error(f"Failed to import GUI components: {e}")
        logger.error("Please ensure all dependencies are properly installed")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
